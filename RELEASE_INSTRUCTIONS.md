# Releasing a New Version of Ticket Reader

### Table of Contents
1. [Overview](#1-overview)
2. [Creating a Release Candidate (RC)](#2-creating-a-release-candidate-rc)
3. [Testing the RC](#3-testing-the-rc)
4. [Uploading the Final Build to the Stores](#4-uploading-the-final-build-to-the-stores)
5. [Submitting and Releasing the App](#5-submitting-and-releasing-the-app)

## 1. Overview 
In most cases, multiple pieces of work in this repo will be bundled and released together as an app update. These instructions will walk you through the steps of building, testing, and releasing changes to the Ticket Reader app.

### rn-ticketing
The structure of rn-ticketing branches is as follow:

main\
&ensp;&ensp;└── develop\
&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;└── feature branches

As work is done in rn-ticketing, feature branches will be created off of `develop`, and merged in once they have been completed and tested. Once it is time to cut a release, a release candidate (RC) branch will be created off of `develop`. The RC branch will be used to test the release, and once it is ready, it will be merged into `main`. The `main` branch will be used to create the build that will be uploaded to the stores.

## 2. Creating a Release Candidate (RC)
At this point, you should have merged all branches that are to be included in this release into `develop`.

### Creating the RC
1. Open [rn-ticketing's Actions tab](https://github.com/hudl/rn-ticketing/actions)
2. Click on the [`Create RC` workflow](https://github.com/hudl/rn-ticketing/actions/workflows/create-rc.yml)
3. Click the `Run workflow` button
    1. `develop` should be selected as the branch to use the workflow from. If it is not, select it from the dropdown.
    2. Select the Semantic Versioning type to use for this release
    3. Leave the `Automatically merge version bump PR` and `Create Release Candidate PR` boxes checked, they automatically bump the version, and create the pull request for the RC
7. Click the green `Run workflow` button
8. Once the workflow is complete, open the [Pull Requests](https://github.com/hudl/rn-ticketing/pulls) tab in GitHub, and find the RC pull request that was created. It should be named `Release Candidate vX.X.X`

## 3. Testing the RC
At this point, you have created the RC, and it is ready to be tested. The owning squad should have an RC regression testing plan that cover's all the main workflows in the app. Testing should include both this regression test plan, as well as specific testing for the features/fixes that are included in this release.

### Installing the RC on Device
- See the [Installing Builds](https://github.com/hudl/rn-ticketing#installing-builds) section of the README for instructions on installing the RC on device.
- You will be downloading the latest `RC-X.X.X` build from firebase

### Addressing Issues Found in Testing
- If any issues are found, they can be fixed in the RC branch itself.

Once the RC has been tested, and it is ready to be uploaded to the stores, continue to the next step.

## 4. Uploading the Final Build to the Stores
At this point the RC has been fully tested, and is ready to be uploaded to the stores.

### Uploading the build
1. Merge the RC pull request
2. Upon merge, the [Release Apps](https://github.com/hudl/rn-ticketing/actions/workflows/release-build.yml) workflow will automatically run
3. Once that workflow is complete, the build has been uploaded to App Store Connect and the Google Play Console

## 5. Submitting and Releasing the App
See the  [App Release Runbook](https://sync.hudlnet.com/display/Mobile/App+Release+Runbook) in sync, or follow the instructions below:

### App Store Connect (iOS)

#### Submitting the App
1. Open App Store Connect
2. Click on the `Hudl Ticket Reader` app
3. On the sidebar in the left, you should see a section called `iOS App`. Beneath that header you will see a list of app versions with green check marks or yellow clock icons. The version with the green check mark is the version that is currently live in the store. The version with the yellow clock icon is the version that was just uploaded. Click on the version with the yellow clock icon titled `x.x.x Ready for Review`
4. You should not have to change any of the information on this page, as it is uploaded automatically  by fastlane. It is OK that there are no screenshots for `iPhone 6.7" Display`, the other screenshots scale automatically for it
5. Scroll down to the `Build` section, click on the `Select a build before you submit your app` button, and select the build that was just uploaded
6. Under the `Version Release` section, make sure that `Manually release this version` is selected
7. We do not use a phased release for Hudl Tickets, so you can leave the `Phased Release` section set to `Release update to all users immediately`
8. Click the `Save` button in the top right corner
9. Click the `Submit for Review` button in the top right corner
10. You will be taken to the `Confirm Submission` page. Click the `Submit to App Review` button in the top right corner
11. The app has now been submitted for review. Once Apple has reviewed and approved you will be able to release the app to the store

#### Releasing the App
1. Wait until Apple has reviewed and approved the app (you will receive an email when this happens, or can check in App Store Connect)
2. Open App Store Connect
3. Click on the `x.x.x Ready for Sale` version of the app in the left hand sidebar
4. Click the `Release This Version` button in the top right corner


### Google Play Console (Android)

#### Submitting the App
1. Open the Google Play Console
2. In the left hand sidebar, click on `Testing > Open Testing`. You should see your app version under the `Releases` header
3. Click `Promote Release` and promote it to the `Production` track
4. Release notes and the build should all have been uploaded automatically. You should see your version of the app under the `Included` header, and the old version of the app under the `Not Included` header
5. Click the `Review release` button in the bottom right corner
6. Review the release. We do not use a staged rollout for Hudl Tickets, so you can leave the `Release to production` section set to `Immediately`
7. Submit the app for review by clicking the `Send changes for review` button

#### Releasing the App
1. Wait until Google has reviewed and approved the app (you will receive an email when this happens, or can check in the Google Play Console)
2. Open the Google Play Console
3. In the left hand sidebar, click on `Publishing Overview`
4. Click the `Review and rollout release` button
5. Under `Changes ready to publish` you should see your new version of the app. Click on it and publish the new version of the app to the Play Store
