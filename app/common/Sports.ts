import { Sport } from '../gql/hudl/__generated__/graphql';

export const SportFromName: { [key: string]: Sport } = {
  Football: Sport.Football,
  Basketball: Sport.Basketball,
  Wrestling: Sport.Wrestling,
  Volleyball: Sport.Volleyball,
  Baseball: Sport.Baseball,
  Soccer: Sport.Soccer,
  Lacrosse: Sport.Lacrosse,
  Golf: Sport.Golf,
  Gymnastics: Sport.Gymnastics,
  Softball: Sport.Softball,
  SwimmingAndDiving: Sport.SwimmingAndDiving,
  Track: Sport.Track,
  IceHockey: Sport.IceHockey,
  FieldHockey: Sport.FieldHockey,
  WaterPolo: Sport.WaterPolo,
  CheerAndSpirit: Sport.CheerAndSpirit,
  DanceAndDrill: Sport.DanceAndDrill,
  Cricket: Sport.Cricket,
  CrossCountry: Sport.CrossCountry,
  PerformingArts: Sport.PerformingArts,
  Rugby: Sport.Rugby,
  Tennis: Sport.Tennis,
  AustralianRulesFootball: Sport.AustralianRulesFootball,
  RugbyLeague: Sport.RugbyLeague,
  RugbyUnion: Sport.RugbyUnion,
  Netball: Sport.Netball,
  Surfing: Sport.Surfing,
  TenpinBowling: Sport.TenpinBowling,
  Badminton: Sport.Badminton,
  Cycling: Sport.Cycling,
  SailingAndYachting: Sport.SailingAndYachting,
  Fencing: Sport.Fencing,
  Handball: Sport.Handball,
  Squash: Sport.Squash,
  Other: Sport.Other,
  NoSport: Sport.NoSport,
  FootballRecruiting: Sport.FootballRecruiting,
  BasketballRecruiting: Sport.BasketballRecruiting,
  WrestlingRecruiting: Sport.WrestlingRecruiting,
  VolleyballRecruiting: Sport.VolleyballRecruiting,
  BaseballRecruiting: Sport.BaseballRecruiting,
  SoccerRecruiting: Sport.SoccerRecruiting,
  LacrosseRecruiting: Sport.LacrosseRecruiting,
  GolfRecruiting: Sport.GolfRecruiting,
  GymnasticsRecruiting: Sport.GymnasticsRecruiting,
  SoftballRecruiting: Sport.SoftballRecruiting,
  SwimmingAndDivingRecruiting: Sport.SwimmingAndDivingRecruiting,
  TrackRecruiting: Sport.TrackRecruiting,
  IceHockeyRecruiting: Sport.IceHockeyRecruiting,
  FieldHockeyRecruiting: Sport.FieldHockeyRecruiting,
  WaterPoloRecruiting: Sport.WaterPoloRecruiting,
  CheerAndSpiritRecruiting: Sport.CheerAndSpiritRecruiting,
  DanceAndDrillRecruiting: Sport.DanceAndDrillRecruiting,
  CricketRecruiting: Sport.CricketRecruiting,
  CrossCountryRecruiting: Sport.CrossCountryRecruiting,
  PerformingArtsRecruiting: Sport.PerformingArtsRecruiting,
  RugbyRecruiting: Sport.RugbyRecruiting,
  TennisRecruiting: Sport.TennisRecruiting,
  AustralianRulesFootballRecruiting: Sport.AustralianRulesFootballRecruiting,
  RugbyLeagueRecruiting: Sport.RugbyLeagueRecruiting,
  RugbyUnionRecruiting: Sport.RugbyUnionRecruiting,
  NetballRecruiting: Sport.NetballRecruiting,
  SurfingRecruiting: Sport.SurfingRecruiting,
  TenpinBowlingRecruiting: Sport.TenpinBowlingRecruiting,
  BadmintonRecruiting: Sport.BadmintonRecruiting,
  CyclingRecruiting: Sport.CyclingRecruiting,
  SailingAndYachtingRecruiting: Sport.SailingAndYachtingRecruiting,
  FencingRecruiting: Sport.FencingRecruiting,
  HandballRecruiting: Sport.HandballRecruiting,
  SquashRecruiting: Sport.SquashRecruiting,
};

export const SportFromId: { [key: number]: Sport } = {
  1: Sport.Football,
  2: Sport.Basketball,
  3: Sport.Wrestling,
  4: Sport.Volleyball,
  5: Sport.Baseball,
  6: Sport.Soccer,
  7: Sport.Lacrosse,
  8: Sport.Golf,
  9: Sport.Gymnastics,
  10: Sport.Softball,
  11: Sport.SwimmingAndDiving,
  12: Sport.Track,
  13: Sport.IceHockey,
  14: Sport.FieldHockey,
  15: Sport.WaterPolo,
  16: Sport.CheerAndSpirit,
  17: Sport.DanceAndDrill,
  18: Sport.Cricket,
  19: Sport.CrossCountry,
  20: Sport.PerformingArts,
  21: Sport.Rugby,
  22: Sport.Tennis,
  23: Sport.AustralianRulesFootball,
  24: Sport.RugbyLeague,
  25: Sport.RugbyUnion,
  26: Sport.Netball,
  27: Sport.Surfing,
  28: Sport.TenpinBowling,
  29: Sport.Badminton,
  30: Sport.Cycling,
  31: Sport.SailingAndYachting,
  32: Sport.Fencing,
  33: Sport.Handball,
  34: Sport.Squash,
  100: Sport.Other,
  101: Sport.NoSport,
  1001: Sport.FootballRecruiting,
  1002: Sport.BasketballRecruiting,
  1003: Sport.WrestlingRecruiting,
  1004: Sport.VolleyballRecruiting,
  1005: Sport.BaseballRecruiting,
  1006: Sport.SoccerRecruiting,
  1007: Sport.LacrosseRecruiting,
  1008: Sport.GolfRecruiting,
  1009: Sport.GymnasticsRecruiting,
  1010: Sport.SoftballRecruiting,
  1011: Sport.SwimmingAndDivingRecruiting,
  1012: Sport.TrackRecruiting,
  1013: Sport.IceHockeyRecruiting,
  1014: Sport.FieldHockeyRecruiting,
  1015: Sport.WaterPoloRecruiting,
  1016: Sport.CheerAndSpiritRecruiting,
  1017: Sport.DanceAndDrillRecruiting,
  1018: Sport.CricketRecruiting,
  1019: Sport.CrossCountryRecruiting,
  1020: Sport.PerformingArtsRecruiting,
  1021: Sport.RugbyRecruiting,
  1022: Sport.TennisRecruiting,
  1023: Sport.AustralianRulesFootballRecruiting,
  1024: Sport.RugbyLeagueRecruiting,
  1025: Sport.RugbyUnionRecruiting,
  1026: Sport.NetballRecruiting,
  1027: Sport.SurfingRecruiting,
  1028: Sport.TenpinBowlingRecruiting,
  1029: Sport.BadmintonRecruiting,
  1030: Sport.CyclingRecruiting,
  1031: Sport.SailingAndYachtingRecruiting,
  1032: Sport.FencingRecruiting,
  1033: Sport.HandballRecruiting,
  1034: Sport.SquashRecruiting,
};
