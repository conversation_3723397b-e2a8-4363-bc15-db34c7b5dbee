import AsyncStorage from '@react-native-async-storage/async-storage';

export default class PersistedItem {
  private key: string;
  private defaultValue: string;

  public constructor(key: string, defaultValue: string) {
    this.key = key;
    this.defaultValue = defaultValue;
  }

  public async get(): Promise<string> {
    return (await AsyncStorage.getItem(this.key)) ?? this.defaultValue;
  }

  public async set(value: string): Promise<void> {
    await AsyncStorage.setItem(this.key, value);
  }
}
