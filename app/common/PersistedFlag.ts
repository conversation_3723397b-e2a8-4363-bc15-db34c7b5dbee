import PersistedItem from './PersistedItem';

export default class PersistedFlag {
  private item: PersistedItem;

  public constructor(key: string, defaultValue: boolean) {
    this.item = new PersistedItem(key, String(defaultValue));
  }

  public async get(): Promise<boolean> {
    return (await this.item.get()) === 'true';
  }

  public async set(value: boolean): Promise<void> {
    await this.item.set(String(value));
  }
}
