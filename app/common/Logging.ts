import { useEffect, useRef } from 'react';

import { HudlLogger } from '@hudl/jarvis/logging';

import { useAccessContext } from '../context/AccessContext';
import { useAppSession } from '../session/AppSession';

const APP_NAME = 'TicketReader';
let accessCode: string;
let organizationId: string;

export function useConfigureHudlLogger(): void {
  const session = useAppSession();
  const accessContext = useAccessContext();

  const loggerBaseURL = useRef<string | undefined>(undefined);

  useEffect(() => {
    const { baseURL } = session;

    // Only initialize the logger once (or when the baseURL changes)
    if (loggerBaseURL === undefined || loggerBaseURL.current !== baseURL) {
      HudlLogger.init({
        baseURL,
        isReleaseBuild: !__DEV__,
        appName: APP_NAME,
      });

      loggerBaseURL.current = baseURL;
    }

    accessCode = accessContext.volunteerInfo?.accessCode ?? '';
    organizationId = accessContext.organization?.id ?? '';

    return () => {
      HudlLogger.flushAndUpload();
    };
  }, [accessContext.organization?.id, accessContext.volunteerInfo?.accessCode, session]);
}

export function logError(
  error: string,
  method: string,
  attributes?: Record<string, unknown>
): void {
  HudlLogger.logError(error, method, {
    ...attributes,
    accessCode: accessCode,
    organizationId: organizationId,
  });
}

export function logWarn(
  message: string,
  method: string,
  attributes?: Record<string, unknown>
): void {
  HudlLogger.logWarn(message, method, {
    ...attributes,
    accessCode: accessCode,
    organizationId: organizationId,
  });
}

export function logInfo(
  message: string,
  method: string,
  attributes?: Record<string, unknown>
): void {
  HudlLogger.logInfo(message, method, {
    ...attributes,
    accessCode: accessCode,
    organizationId: organizationId,
  });
}
