import { PermissionStatus, PermissionsAndroid, Platform } from 'react-native';

// import { PERMISSIONS, PermissionStatus, check, request } from 'react-native-permissions';

export async function checkCameraPermissions(): Promise<boolean> {
  if (Platform.OS === 'android') {
    return await PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.CAMERA);
  } else if (Platform.OS === 'ios') {
    return false;
  }
  return false;
}

export async function requestCameraPermission(): Promise<PermissionStatus> {
  if (Platform.OS === 'android') {
    return await PermissionsAndroid.request('android.permission.CAMERA');
  } else if (Platform.OS === 'ios') {
    return 'denied';
  }
  return 'denied';
}
