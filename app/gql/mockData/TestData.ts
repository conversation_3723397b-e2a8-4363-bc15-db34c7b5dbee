import { PaymentIntent, Reader } from '@stripe/stripe-terminal-react-native';

import { LineItemType } from '../../enums/shared';
import { EventInfo } from '../../models/EventInfo';
import { Volunteer } from '../../models/Volunteer';
import { LineItemSelection } from '../../types/shared';
import {
  School,
  TicketType,
  TicketedEvent,
  TicketingPricingSummary,
} from '../public/__generated__/graphql';

export const mockVolunteerInfo: Volunteer = {
  accessCode: 'accessCode',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
};

export const mockOrganization: School = {
  id: 'schoolId',
  fullName: 'School Name',
  canUserEditSchool: false,
  isActive: true,
  isEnabled: true,
  orgClassificationId: 0,
  organizationPrograms: [],
};

export const mockEventInfo: EventInfo = {
  id: 'eventId',
  name: 'Event Name',
  description: '',
  date: '2022-01-01T00:00:00Z',
  timezoneIdentifier: 'America/Chicago',
  organizationId: 'schoolId',
  participatingTeamIds: ['testId'],
  associatedPassConfigIds: [],
  ticketTypePricingSummaries: [
    {
      ticketTypeId: 'ticketTypeId',
      hudlFeeInCents: 100,
      shouldShowFee: true,
      priceInCents: 200,
      priceInCentsWithHudlFee: 300,
      ticketedEventId: 'eventId',
    },
  ],
};

export const mockReader: Reader.Type = {
  id: 'testReaderIds',
  serialNumber: 'testSerialNumber',
  locationStatus: 'set',
  deviceType: 'stripeM2',
  status: 'offline',
  batteryStatus: 'nominal',
};

export const mockTicketType: TicketType = {
  id: 'ticketTypeId',
  name: 'Ticket Type Name',
  priceInCents: 200,
  organizationId: 'schoolId',
  quantity: 10,
  createdAt: '2022-01-01T00:00:00Z',
  updatedAt: '2022-01-01T00:00:00Z',
};

export const mockTicketedEvent: TicketedEvent = {
  id: 'ticketedEventId',
  name: 'Ticketed Event Name',
  date: '2022-01-01T00:00:00Z',
  timeZoneIdentifier: 'America/Chicago',
  organizationId: 'schoolId',
  participatingTeamIds: ['testId'],
  ticketTypeReferences: [
    {
      ticketTypeId: 'ticketTypeId',
      priceOverride: 300,
    },
  ],
  createdAt: '2022-01-01T00:00:00Z',
  updatedAt: '2022-01-01T00:00:00Z',
};

export const mockLineItemSelection: LineItemSelection = {
  lineItemId: 'lineItemId',
  lineItemType: LineItemType.TicketType,
  unitPriceInCents: 200,
  quantityRemaining: 10,
  quantitySelected: 2,
  name: 'Ticket Type Name',
};

export const mockPricingSummary: TicketingPricingSummary = {
  currency: 'USD',
  subtotalInCents: 190,
  feesInCents: 10,
  totalInCents: 200,
  hudlFeeInCents: 5,
  subtotalWithHudlFeeInCents: 195,
  paymentProcessingFeeInCents: 5,
  shouldShowFee: true,
};

export const mockPaymentIntentGQLResult = {
  data: {
    createPaymentIntent: {
      paymentIntentId: 'paymentIntentId',
      status: 'requires_payment_method',
      clientSecret: 'clientSecret',
      __typename: 'PaymentOutput',
    },
  },
};

export const mockStripePaymentIntent: Partial<PaymentIntent.Type> = {
  id: 'paymentIntentId',
  amount: 200,
  charges: [],
  created: '2022-01-01T00:00:00Z',
  currency: 'USD',
  status: 'requiresPaymentMethod',
  sdkUuid: 'sdkUuid',
  paymentMethodId: 'paymentMethodId',
};

export const mockTicketingPaymentTokenGQLResult = {
  data: { createTicketingPaymentToken: 'paymentTokenId' },
};

export const mockSchool = {
  data: { schoolData: mockOrganization },
};
