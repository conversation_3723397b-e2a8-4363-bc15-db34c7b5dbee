/* eslint-disable @typescript-eslint/no-explicit-any -- needed for Apollo */
import {
  LazyQueryExecFunction,
  OperationVariables,
  QueryHookOptions,
  useLazyQuery,
} from '@apollo/client';

import { getTicketingPricingSummary } from '../public/queries/TicketingPricingSummary';

export default function useLazyGetTicketingPricingSummary(
  onError?: () => void,
  onCompleted?: (data: any) => void,
  options?: QueryHookOptions<any, OperationVariables>
): { executePricingSummaryQuery: LazyQueryExecFunction<any, OperationVariables> } {
  const [executePricingSummaryQuery, {}] = useLazyQuery(getTicketingPricingSummary(), {
    ...options,
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'no-cache',
    onError() {
      onError?.();
    },
    onCompleted: (data) => {
      onCompleted?.(data);
    },
  });

  return {
    executePricingSummaryQuery,
  };
}
