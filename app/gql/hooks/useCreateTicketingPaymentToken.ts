/* eslint-disable @typescript-eslint/no-explicit-any */
import { FetchResult, MutationHookOptions, OperationVariables, useMutation } from '@apollo/client';

import { logError } from '../../common/Logging';
import { useAccessContext } from '../../context/AccessContext';
import { useAppSession } from '../../session/AppSession';
import { LineItemSelection } from '../../types/shared';
import { Source } from '../../utils/Enums';
import { lineItemDictToPricingSummaryInput } from '../../utils/LineItemUtils';
import { CreateTicketingPaymentTokenInput } from '../public/__generated__/graphql';
import { getCreateTicketingPaymentToken } from '../public/mutations/CreateTicketingPaymentToken';

export default function useCreateTicketingPaymentToken(
  lineItemSelections: LineItemSelection[],
  expectedTotalInCents: number,
  eventId: string,
  options?: MutationHookOptions<any, OperationVariables>
): {
  createTicketingPaymentToken: () => Promise<FetchResult<any>>;
} {
  const session = useAppSession();
  const accessContext = useAccessContext();
  const { organization } = accessContext;
  const organizationId = organization?.id ?? '';

  const pricingSummaryInput = lineItemDictToPricingSummaryInput(
    lineItemSelections,
    organizationId,
    eventId
  );

  const input: CreateTicketingPaymentTokenInput = {
    expectedTotalInCents: expectedTotalInCents,
    lineItems: pricingSummaryInput,
    organizationId: organizationId,
    source: Source.POS,
  };

  const [createTicketingPaymentToken] = useMutation(getCreateTicketingPaymentToken(), {
    client: session.gqlClients.public,
    variables: {
      input: input,
    },
    onError: (error) => {
      logError(
        'CreateTicketingPaymentToken failed: ' + error.message,
        'useCreateTicketingPaymentToken'
      );
    },
    ...options,
  });

  return { createTicketingPaymentToken };
}
