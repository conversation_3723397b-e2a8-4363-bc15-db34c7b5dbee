/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';

import { FetchResult, MutationHookOptions, OperationVariables, useMutation } from '@apollo/client';

import { logError } from '../../common/Logging';
import { useAccessContext } from '../../context/AccessContext';
import { useAppSession } from '../../session/AppSession';
import { LineItemSelection } from '../../types/shared';
import { CheckoutMutationRetryLimit } from '../../utils/Constants';
import { Source } from '../../utils/Enums';
import { lineItemDictToPricingSummaryInput } from '../../utils/LineItemUtils';
import { TicketingCheckoutInput, TicketingCheckoutResult } from '../public/__generated__/graphql';
import { getCheckoutWithTicketingLineItems } from '../public/mutations/CheckoutWithTicketingLineItems';

interface CheckoutWithTicketingLineItemsResult {
  checkoutWithTicketingLineItems: TicketingCheckoutResult;
}

export default function useCheckoutWithTicketingLineItems(
  hudlPaymentTokenId: string,
  lineItemSelections: LineItemSelection[],
  totalInCents: number,
  eventId: string,
  purchaserEmail: string,
  options?: MutationHookOptions<any, OperationVariables>
): {
  checkoutWithTicketingLineItems: () => Promise<FetchResult<CheckoutWithTicketingLineItemsResult>>;
} {
  const session = useAppSession();
  const [retryCount, setRetryCount] = useState(0);
  const accessContext = useAccessContext();
  const { organization } = accessContext;
  const organizationId = organization?.id ?? '';

  const pricingSummaryInput = lineItemDictToPricingSummaryInput(
    lineItemSelections,
    organizationId,
    eventId
  );

  const input: TicketingCheckoutInput = {
    email: purchaserEmail,
    hudlPaymentTokenId: hudlPaymentTokenId,
    lineItems: pricingSummaryInput,
    totalInCents: totalInCents,
    source: Source.POS,
    emailOrder: purchaserEmail.length > 0,
    tosAgreement: false,
    autoRedeemTickets: true,
  };

  const [checkoutWithTicketingLineItems] = useMutation(getCheckoutWithTicketingLineItems(), {
    client: session.gqlClients.public,
    variables: {
      input: input,
    },
    onCompleted: () => {
      setRetryCount(0);
    },
    onError: (error) => {
      if (retryCount < CheckoutMutationRetryLimit) {
        setRetryCount(retryCount + 1);
        checkoutWithTicketingLineItems();
      } else {
        logError(
          'CheckoutWithTicketingLineItems failed after retrying: ' + error.message,
          'useCheckoutWithTicketingLineItems'
        );
      }
    },
    ...options,
  });

  return { checkoutWithTicketingLineItems };
}
