/* eslint-disable @typescript-eslint/no-explicit-any */
import { FetchResult, MutationHookOptions, OperationVariables, useMutation } from '@apollo/client';

import { logError } from '../../common/Logging';
import { useAccessContext } from '../../context/AccessContext';
import { useAppSession } from '../../session/AppSession';
import { PointOfSale, USIsoAlpha2 } from '../../utils/Constants';
import {
  CreatePaymentIntentInput,
  getCreatePaymentIntent,
} from '../payments/mutations/CreatePaymentIntent';

export default function useCreatePaymentIntent(
  paymentTokenId: string,
  description?: string,
  options?: MutationHookOptions<any, OperationVariables>
): {
  createPaymentIntent: () => Promise<FetchResult<any>>;
} {
  const session = useAppSession();
  const accessContext = useAccessContext();
  const { organization } = accessContext;

  const createPaymentIntentInput: CreatePaymentIntentInput = {
    input: {
      paymentTokenId: paymentTokenId,
      description: description ?? PointOfSale,
      countryIso: organization?.countryIsoAlpha2 ?? USIsoAlpha2,
      isPointOfSaleTransaction: true,
    },
  };

  const [createPaymentIntent] = useMutation(getCreatePaymentIntent(), {
    client: session.gqlClients.payments,
    variables: createPaymentIntentInput,
    onError: (error) => {
      logError('CreatePaymentIntent failed: ' + error.message, 'useCreatePaymentIntent');
    },
    ...options,
  });

  return { createPaymentIntent };
}
