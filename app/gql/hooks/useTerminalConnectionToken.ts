/* eslint-disable @typescript-eslint/no-explicit-any */
import { FetchResult, MutationHookOptions, OperationVariables, useMutation } from '@apollo/client';

import { logError } from '../../common/Logging';
import { useAccessContext } from '../../context/AccessContext';
import { useAppSession } from '../../session/AppSession';
import { getCreateStripeConnectionToken } from '../public/mutations/CreateStripeConnectionToken';

export default function useCreateTerminalConnectionToken(
  options?: MutationHookOptions<any, OperationVariables>
): { createStripeConnectionToken: () => Promise<FetchResult<any>> } {
  const session = useAppSession();
  const { organization, volunteerInfo } = useAccessContext();

  const createTerminalConnectionTokenInput = {
    accessCode: volunteerInfo?.accessCode,
    organizationId: organization?.id,
  };

  const [createStripeConnectionToken] = useMutation(getCreateStripeConnectionToken(), {
    client: session.gqlClients.public,
    variables: {
      input: createTerminalConnectionTokenInput,
    },
    ...options,
    onError: (error) => {
      logError(
        `CreateStripeConnectionToken error. Error: ${error.message}`,
        'useCreateTerminalConnectionToken'
      );
    },
  });

  return { createStripeConnectionToken };
}
