/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ApolloError,
  ApolloQueryResult,
  NetworkStatus,
  OperationVariables,
  QueryHookOptions,
  useQuery,
} from '@apollo/client';

import { useAppSession } from '../../session/AppSession';
import { TicketedEvent } from '../public/__generated__/graphql';
import { getTicketedEventWithAnalytics } from '../public/queries/TicketedEventWithAnalytics';

export default function useTicketedEventByIdWithAnalytics(
  ticketedEventId?: string,
  options?: QueryHookOptions<any, OperationVariables>
): {
  ticketedEvent: TicketedEvent | undefined;
  ticketedEventLoading: boolean;
  ticketedEventError: ApolloError | undefined;
  ticketedEventNetworkStatus: NetworkStatus;
  refetchTicketedEvent: () => Promise<ApolloQueryResult<TicketedEvent>>;
} {
  const session = useAppSession();

  const {
    data: ticketedEventData,
    error: ticketedEventError,
    loading: ticketedEventLoading,
    networkStatus: ticketedEventNetworkStatus,
    refetch: refetchTicketedEvent,
  } = useQuery(getTicketedEventWithAnalytics(), {
    client: session.gqlClients.public,
    variables: { ticketedEventId },
    skip: !ticketedEventId,
    ...options,
  });

  const ticketedEvent: TicketedEvent = ticketedEventData?.ticketedEventById;

  return {
    ticketedEvent,
    ticketedEventLoading,
    ticketedEventError,
    ticketedEventNetworkStatus,
    refetchTicketedEvent,
  };
}
