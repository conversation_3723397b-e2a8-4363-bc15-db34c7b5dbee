/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ApolloError,
  ApolloQueryResult,
  NetworkStatus,
  OperationVariables,
  QueryHookOptions,
  useQuery,
} from '@apollo/client';

import { useAppSession } from '../../session/AppSession';
import {
  GetAssociatedTicketGroupsWithAccessCodeInput,
  TicketGroup,
} from '../public/__generated__/graphql';
import { getAssociatedTicketGroupsForTicketedEventId } from '../public/queries/GetAllAssociatedTicketGroupsForTicketedEventId';

export default function useGetAllAssociatedTicketGroupsForTicketedEventId(
  input: GetAssociatedTicketGroupsWithAccessCodeInput,
  options?: QueryHookOptions<any, OperationVariables>
): {
  ticketGroups: TicketGroup[];
  ticketGroupsLoading: boolean;
  ticketGroupError: ApolloError | undefined;
  ticketGroupNetworkStatus: NetworkStatus;
  refetchTicketGroups: () => Promise<ApolloQueryResult<TicketGroup>>;
  fetchMoreTicketGroups: any;
} {
  const session = useAppSession();

  const {
    data: ticketGroupData,
    error: ticketGroupError,
    loading: ticketGroupsLoading,
    networkStatus: ticketGroupNetworkStatus,
    refetch: refetchTicketGroups,
    fetchMore: fetchMoreTicketGroups,
  } = useQuery(getAssociatedTicketGroupsForTicketedEventId(), {
    client: session.gqlClients.public,
    variables: { input: input },
    ...options,
  });

  const ticketGroups: TicketGroup[] =
    ticketGroupData?.associatedTicketGroupsForTicketedEventId?.items;

  return {
    ticketGroups,
    ticketGroupsLoading,
    ticketGroupError,
    ticketGroupNetworkStatus,
    refetchTicketGroups,
    fetchMoreTicketGroups,
  };
}
