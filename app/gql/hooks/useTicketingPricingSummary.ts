/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  ApolloError,
  ApolloQueryResult,
  NetworkStatus,
  OperationVariables,
  QueryHookOptions,
  useQuery,
} from '@apollo/client';

import { useAppSession } from '../../session/AppSession';
import { LineItemSelection } from '../../types/shared';
import { Source } from '../../utils/Enums';
import { lineItemDictToPricingSummaryInput } from '../../utils/LineItemUtils';
import { TicketingPricingSummary } from '../public/__generated__/graphql';
import { getTicketingPricingSummary } from '../public/queries/TicketingPricingSummary';

export default function useTicketingPricingSummary(
  lineItemSelections: LineItemSelection[],
  referenceId: string,
  organizationId: string,
  options?: QueryHookOptions<any, OperationVariables>
): {
  pricingSummary: TicketingPricingSummary | undefined;
  pricingSummaryLoading: boolean;
  pricingSummaryError: ApolloError | undefined;
  refetchPricingSummary: () => Promise<ApolloQueryResult<TicketingPricingSummary>>;
  networkStatus: NetworkStatus;
} {
  const session = useAppSession();

  const lineItemInput = lineItemDictToPricingSummaryInput(
    lineItemSelections,
    organizationId,
    referenceId
  );

  const {
    data: pricingSummaryData,
    loading: pricingSummaryLoading,
    error: pricingSummaryError,
    refetch: refetchPricingSummary,
    networkStatus,
  } = useQuery(getTicketingPricingSummary(), {
    client: session.gqlClients.public,
    variables: {
      lineItems: lineItemInput,
      source: Source.POS,
    },
    fetchPolicy: 'network-only',
    ...options,
  });

  const pricingSummary: TicketingPricingSummary = pricingSummaryData?.ticketingPricingSummary;
  return {
    pricingSummary,
    pricingSummaryLoading,
    pricingSummaryError,
    refetchPricingSummary,
    networkStatus,
  };
}
