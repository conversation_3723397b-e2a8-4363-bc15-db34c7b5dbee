import { useCallback } from 'react';

import { useQuery } from '@apollo/client';

import {
  GetTicketedEventsByOrganizationIdInput,
  TicketedEvent,
} from '../public/__generated__/graphql';
import GetTicketedEventsByOrganizationId from '../public/queries/GetTicketedEventsByOrganizationId';

export function useTicketedEventsByOrganizationId({
  input,
  staticItems,
  onCompleted,
}: {
  input: GetTicketedEventsByOrganizationIdInput;
  staticItems?: TicketedEvent[];
  onCompleted?: () => void;
}): {
  events?: TicketedEvent[];
  hasError: boolean;
  isLoading: boolean;
  fetchMore: () => void;
  refetch: () => void;
} {
  const { data, error, loading, fetchMore, refetch } = useQuery(GetTicketedEventsByOrganizationId, {
    variables: {
      input: input,
    },
    onCompleted: () => {
      onCompleted?.();
    },
  });

  const fetchMoreEvents = useCallback(async () => {
    const hasNextPage = data?.ticketedEventsByOrganizationId?.pageInfo.hasNextPage ?? false;
    if (!hasNextPage) {
      return;
    }
    await fetchMore({
      variables: {
        input: {
          ...input,
          after: data?.ticketedEventsByOrganizationId?.pageInfo?.endCursor,
        },
      },
      updateQuery: (prev, { fetchMoreResult }) => {
        return fetchMoreResult?.ticketedEventsByOrganizationId?.items?.length
          ? {
              ticketedEventsByOrganizationId: {
                __typename: prev.ticketedEventsByOrganizationId?.__typename,
                items: [
                  ...(prev.ticketedEventsByOrganizationId?.items ?? []),
                  ...(fetchMoreResult.ticketedEventsByOrganizationId?.items ?? []),
                ],
                pageInfo: fetchMoreResult?.ticketedEventsByOrganizationId?.pageInfo,
                totalCount: fetchMoreResult?.ticketedEventsByOrganizationId?.totalCount,
                edges: fetchMoreResult.ticketedEventsByOrganizationId?.edges,
              },
            }
          : prev;
      },
    }).catch(console.log);
  }, [
    data?.ticketedEventsByOrganizationId?.pageInfo?.endCursor,
    data?.ticketedEventsByOrganizationId?.pageInfo.hasNextPage,
    fetchMore,
    input,
  ]);

  if (staticItems) {
    return {
      events: staticItems,
      hasError: false,
      isLoading: false,
      fetchMore: console.log,
      refetch: () => {},
    };
  }

  return {
    events: data?.ticketedEventsByOrganizationId?.items as TicketedEvent[],
    hasError: error !== undefined,
    isLoading: loading,
    fetchMore: fetchMoreEvents,
    refetch,
  };
}
