/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState } from 'react';

import { FetchResult, MutationHookOptions, OperationVariables, useMutation } from '@apollo/client';
import CryptoES from 'crypto-es';
import uuid from 'react-native-uuid';

import { logError } from '../../common/Logging';
import { useAccessContext } from '../../context/AccessContext';
import { useAppSession } from '../../session/AppSession';
import { LineItemSelection } from '../../types/shared';
import { CheckoutMutationRetryLimit } from '../../utils/Constants';
import { Source } from '../../utils/Enums';
import { lineItemDictToPricingSummaryInput } from '../../utils/LineItemUtils';
import {
  TicketingCashCheckoutInput,
  TicketingCheckoutResult,
} from '../public/__generated__/graphql';
import { getCashCheckoutWithTicketingLineItems } from '../public/mutations/CashCheckoutWithTicketingLineItems';

interface CashCheckoutWithTicketingLineItemsResult {
  cashCheckoutWithTicketingLineItems: TicketingCheckoutResult;
}

const cashCheckoutSeed =
  'MBeI6sDJrkQkHtLETU11fXl9hbLS0+kXm092HaRewX7Ewo/pEtNfMjpxS6YYouOCULuji6K4eT/K9X9d6T6K8w==';

export default function useCheckoutWithTicketingLineItems(
  lineItemSelections: LineItemSelection[],
  totalInCents: number,
  eventId: string,
  purchaserEmail: string,
  options?: MutationHookOptions<any, OperationVariables>
): {
  cashCheckoutWithTicketingLineItems: () => Promise<
    FetchResult<CashCheckoutWithTicketingLineItemsResult>
  >;
} {
  const session = useAppSession();
  const [retryCount, setRetryCount] = useState(0);
  const accessContext = useAccessContext();
  const { organization, volunteerInfo } = accessContext;
  const organizationId = organization?.id ?? '';

  const pricingSummaryInput = lineItemDictToPricingSummaryInput(
    lineItemSelections,
    organizationId,
    eventId
  );

  const checkoutSessionId = uuid.v4();
  const lineItemCount = lineItemSelections.length;
  const lineItemQuantitySum = lineItemSelections.reduce(
    (sum, item) => sum + item.quantitySelected,
    0
  );
  const lineItemNumber = lineItemCount + lineItemQuantitySum;
  const checksum = calculateChecksumForCashSale(
    volunteerInfo?.accessCode ?? '',
    checkoutSessionId,
    lineItemNumber
  );

  const input: TicketingCashCheckoutInput = {
    email: purchaserEmail,
    checksum: checksum,
    lineItems: pricingSummaryInput,
    totalInCents: totalInCents,
    source: Source.POS,
    emailOrder: purchaserEmail.length > 0,
    tosAgreement: false,
    autoRedeemTickets: true,
    accessCode: volunteerInfo?.accessCode ?? '',
    checkoutSessionId: checkoutSessionId,
  };

  const [cashCheckoutWithTicketingLineItems] = useMutation(
    getCashCheckoutWithTicketingLineItems(),
    {
      client: session.gqlClients.public,
      variables: {
        input: input,
      },
      onCompleted: () => {
        setRetryCount(0);
      },
      onError: (error) => {
        if (retryCount < CheckoutMutationRetryLimit) {
          setRetryCount(retryCount + 1);
          cashCheckoutWithTicketingLineItems();
        } else {
          logError(
            'CashCheckoutWithTicketingLineItems failed after retrying: ' + error.message,
            'useCashCheckoutWithTicketingLineItems'
          );
        }
      },
      ...options,
    }
  );

  return { cashCheckoutWithTicketingLineItems };
}

function calculateChecksumForCashSale(
  accessCode: string,
  checkoutSessionId: string,
  lineItemNumber: number
): string {
  const key = CryptoES.enc.Utf8.parse(cashCheckoutSeed);
  const message = CryptoES.enc.Utf8.parse(`${accessCode}-${checkoutSessionId}-${lineItemNumber}`);

  const hashedData = CryptoES.HmacSHA256(message, key);
  return hashedData.toString(CryptoES.enc.Base64);
}
