/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

const documents = {
    "\n  query RN_Ticketing_GetMe_r1 {\n    me {\n      firstName\n      lastName\n    }\n  }\n": types.Rn_Ticketing_GetMe_R1Document,
};

export function graphql(source: "\n  query RN_Ticketing_GetMe_r1 {\n    me {\n      firstName\n      lastName\n    }\n  }\n"): (typeof documents)["\n  query RN_Ticketing_GetMe_r1 {\n    me {\n      firstName\n      lastName\n    }\n  }\n"];

export function graphql(source: string): unknown;
export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;