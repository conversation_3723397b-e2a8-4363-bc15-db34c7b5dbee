import { DocumentNode, gql } from '@apollo/client';

export type CreatePaymentIntentInput = {
  input: {
    paymentTokenId: string;
    description: string;
    countryIso: string;
    isPointOfSaleTransaction: boolean;
  };
};

export type CreatePaymentIntentResult = {
  createPaymentIntent: {
    paymentIntentId: string;
    status: string;
    clientSecret: string;
  };
};

export const getCreatePaymentIntent = (): DocumentNode => {
  return gql`
    mutation CreatePaymentIntent($input: CreatePaymentIntentInput!) {
      createPaymentIntent(input: $input) {
        paymentIntentId
        status
        clientSecret
      }
    }
  `;
};
