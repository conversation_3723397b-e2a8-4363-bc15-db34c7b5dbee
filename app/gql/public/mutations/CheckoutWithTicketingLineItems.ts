import { gql } from '@apollo/client';
import { DocumentNode } from 'graphql';

export const getCheckoutWithTicketingLineItems = (): DocumentNode => {
  return gql`
    mutation RN_POS_CheckoutWithTicketingLineItems_r1($input: TicketingCheckoutInput!) {
      checkoutWithTicketingLineItems(input: $input) {
        pricingSummary {
          currency
          feesInCents
          lineItems {
            lineItemId
            lineItemType
            quantity
            referenceId
            totalPriceInCents
            unitPriceInCents
          }
          subtotalInCents
          totalInCents
        }
        ticketGroup {
          createdAt
          deletedAt
          email
          id
          ticketGroupReference
          tickets {
            createdAt
            deletedAt
            id
            updatedAt
            email
            passId
            redeemedAt
            refundable
            ticketedEventId
            ticketStatus
            ticketTypeId
            qrCodeUrl
          }
          passes {
            id
            createdAt
            deletedAt
            email
            passConfigId
            qrCodeUrl
            updatedAt
          }
          updatedAt
        }
      }
    }
  `;
};
