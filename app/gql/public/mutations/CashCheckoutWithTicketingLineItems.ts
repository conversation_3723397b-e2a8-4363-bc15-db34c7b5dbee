import { gql } from '@apollo/client';
import { DocumentNode } from 'graphql';

export const getCashCheckoutWithTicketingLineItems = (): DocumentNode => {
  return gql`
    mutation RN_POS_CashCheckoutWithTicketingLineItems_r1($input: TicketingCashCheckoutInput!) {
      cashCheckoutWithTicketingLineItems(input: $input) {
        ticketGroup {
          createdAt
          deletedAt
          email
          id
          ticketGroupReference
          updatedAt
        }
      }
    }
  `;
};
