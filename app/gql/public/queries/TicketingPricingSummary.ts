import { gql } from '@apollo/client';
import { DocumentNode } from 'graphql';

export const getTicketingPricingSummary = (): DocumentNode => {
  return gql`
    query RN_GetTicketingPricingSummary_r1(
      $lineItems: [TicketingPricingLineItemInput]!
      $source: String
    ) {
      ticketingPricingSummary(lineItems: $lineItems, source: $source) {
        currency
        feesInCents
        lineItems {
          lineItemId
          lineItemType
          quantity
          referenceId
          totalPriceInCents
          unitPriceInCents
        }
        shouldShowFee
        subtotalInCents
        totalInCents
        hudlFeeInCents
        paymentProcessingFeeInCents
        subtotalWithHudlFeeInCents
      }
    }
  `;
};
