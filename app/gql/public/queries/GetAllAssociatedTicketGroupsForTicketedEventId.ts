import { gql } from '@apollo/client';
import { DocumentNode } from 'graphql';

export const getAssociatedTicketGroupsForTicketedEventId = (): DocumentNode => {
  return gql`
    query RN_Scanner_GetAssociatedTicketGroupsForTicketedEventId_r1(
      $input: GetAssociatedTicketGroupsWithAccessCodeInput!
    ) {
      associatedTicketGroupsForTicketedEventId(input: $input) {
        pageInfo {
          startCursor
          endCursor
          hasNextPage
          hasPreviousPage
        }
        items {
          id
          ticketGroupReference
          userId
          firstName
          lastName
          email
          tickets {
            id
          }
          passes {
            id
          }
        }
      }
    }
  `;
};
