import { graphql } from '../__generated__/gql';

export default graphql(`
  query RN_Scanner_GetTicketedEventAccessCode_r1($input: GetTicketedEventAccessCodeInput!) {
    ticketedEventAccessCode(input: $input) {
      organization {
        id
        internalId
        abbreviation
        fullName
        shortName
        mascot
        primaryColor
        secondaryColor
        countryIsoAlpha2
      }
    }
  }
`);
