import { graphql } from '../__generated__/gql';

export default graphql(`
  query RN_Scanner_GetTicketedEventsByOrganizationId_r1(
    $input: GetTicketedEventsByOrganizationIdInput!
  ) {
    ticketedEventsByOrganizationId(input: $input) {
      totalCount
      edges {
        node {
          id
        }
      }
      pageInfo {
        startCursor
        endCursor
        hasNextPage
        hasPreviousPage
      }
      items {
        id
        date
        eventStatus
        gender
        name
        organizationId
        participatingTeamIds
        sport
        timeZoneIdentifier
        venueConfigurationId
        associatedPassConfigs {
          id
        }
        ticketTypePricingSummaries {
          hudlFeeInCents
          priceInCents
          priceInCentsWithHudlFee
          ticketedEventId
          ticketTypeId
          shouldShowFee
          currency
        }
      }
    }
  }
`);
