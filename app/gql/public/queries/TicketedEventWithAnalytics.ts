import { gql } from '@apollo/client';
import { DocumentNode } from 'graphql';

export const getTicketedEventWithAnalytics = (): DocumentNode => {
  return gql`
    query RN_TicketedEventByIdWithAnalytics_r1($ticketedEventId: ID!) {
      ticketedEventById(ticketedEventId: $ticketedEventId) {
        id
        name
        description
        date
        timeZoneIdentifier
        organizationId
        participatingTeamIds
        venueConfigurationId
        associatedPassConfigs {
          id
        }
        ticketTypeReferences {
          priceOverride
          quantityOverride
          ticketTypeId
        }
        ticketTypes {
          createdAt
          deletedAt
          id
          name
          organizationId
          priceInCents
          quantity
          updatedAt
        }
        quantityRemainingForTicketTypes {
          quantityRemaining
          referenceId
        }
        ticketTypePricingSummaries {
          hudlFeeInCents
          priceInCents
          priceInCentsWithHudlFee
          ticketedEventId
          ticketTypeId
          shouldShowFee
          currency
        }
      }
    }
  `;
};
