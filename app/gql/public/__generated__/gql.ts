/* eslint-disable */
import * as types from './graphql';
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

const documents = {
    "\n    mutation RN_POS_CashCheckoutWithTicketingLineItems_r1($input: TicketingCashCheckoutInput!) {\n      cashCheckoutWithTicketingLineItems(input: $input) {\n        ticketGroup {\n          createdAt\n          deletedAt\n          email\n          id\n          ticketGroupReference\n          updatedAt\n        }\n      }\n    }\n  ": types.Rn_Pos_CashCheckoutWithTicketingLineItems_R1Document,
    "\n    mutation RN_POS_CheckoutWithTicketingLineItems_r1($input: TicketingCheckoutInput!) {\n      checkoutWithTicketingLineItems(input: $input) {\n        pricingSummary {\n          currency\n          feesInCents\n          lineItems {\n            lineItemId\n            lineItemType\n            quantity\n            referenceId\n            totalPriceInCents\n            unitPriceInCents\n          }\n          subtotalInCents\n          totalInCents\n        }\n        ticketGroup {\n          createdAt\n          deletedAt\n          email\n          id\n          ticketGroupReference\n          tickets {\n            createdAt\n            deletedAt\n            id\n            updatedAt\n            email\n            passId\n            redeemedAt\n            refundable\n            ticketedEventId\n            ticketStatus\n            ticketTypeId\n            qrCodeUrl\n          }\n          passes {\n            id\n            createdAt\n            deletedAt\n            email\n            passConfigId\n            qrCodeUrl\n            updatedAt\n          }\n          updatedAt\n        }\n      }\n    }\n  ": types.Rn_Pos_CheckoutWithTicketingLineItems_R1Document,
    "\n    mutation RN_POS_CreateTerminalConnectionToken_r1($input: CreateTerminalConnectionTokenInput!) {\n      createTerminalConnectionToken(input: $input) {\n        clientSecret\n      }\n    }\n  ": types.Rn_Pos_CreateTerminalConnectionToken_R1Document,
    "\n    mutation RN_POS_CreateTicketingPaymentToken_r1($input: CreateTicketingPaymentTokenInput!) {\n      createTicketingPaymentToken(input: $input)\n    }\n  ": types.Rn_Pos_CreateTicketingPaymentToken_R1Document,
    "\n  mutation RN_Scanner_QueueBatchRedemption_r1($input: ScanningRedemptionInput!) {\n    queueBatchRedemption(input: $input)\n  }\n": types.Rn_Scanner_QueueBatchRedemption_R1Document,
    "\n  query RN_Scanner_GetTicketedEventAccessCode_r1($input: GetTicketedEventAccessCodeInput!) {\n    ticketedEventAccessCode(input: $input) {\n      organization {\n        id\n        internalId\n        abbreviation\n        fullName\n        shortName\n        mascot\n        primaryColor\n        secondaryColor\n        countryIsoAlpha2\n      }\n    }\n  }\n": types.Rn_Scanner_GetTicketedEventAccessCode_R1Document,
    "\n    query RN_Scanner_GetAssociatedTicketGroupsForTicketedEventId_r1(\n      $input: GetAssociatedTicketGroupsWithAccessCodeInput!\n    ) {\n      associatedTicketGroupsForTicketedEventId(input: $input) {\n        pageInfo {\n          startCursor\n          endCursor\n          hasNextPage\n          hasPreviousPage\n        }\n        items {\n          id\n          ticketGroupReference\n          userId\n          firstName\n          lastName\n          email\n          tickets {\n            id\n          }\n          passes {\n            id\n          }\n        }\n      }\n    }\n  ": types.Rn_Scanner_GetAssociatedTicketGroupsForTicketedEventId_R1Document,
    "\n    query Web_Fan_GetSchool_r1($schoolId: ID, $internalSchoolId: String) {\n      school(schoolId: $schoolId, internalSchoolId: $internalSchoolId) {\n        id\n        countryIsoAlpha2\n      }\n    }\n  ": types.Web_Fan_GetSchool_R1Document,
    "\n  query RN_Scanner_GetTicketedEventsByOrganizationId_r1(\n    $input: GetTicketedEventsByOrganizationIdInput!\n  ) {\n    ticketedEventsByOrganizationId(input: $input) {\n      totalCount\n      edges {\n        node {\n          id\n        }\n      }\n      pageInfo {\n        startCursor\n        endCursor\n        hasNextPage\n        hasPreviousPage\n      }\n      items {\n        id\n        date\n        eventStatus\n        gender\n        name\n        organizationId\n        participatingTeamIds\n        sport\n        timeZoneIdentifier\n        venueConfigurationId\n        associatedPassConfigs {\n          id\n        }\n        ticketTypePricingSummaries {\n          hudlFeeInCents\n          priceInCents\n          priceInCentsWithHudlFee\n          ticketedEventId\n          ticketTypeId\n          shouldShowFee\n          currency\n        }\n      }\n    }\n  }\n": types.Rn_Scanner_GetTicketedEventsByOrganizationId_R1Document,
    "\n    query RN_TicketedEventByIdWithAnalytics_r1($ticketedEventId: ID!) {\n      ticketedEventById(ticketedEventId: $ticketedEventId) {\n        id\n        name\n        description\n        date\n        timeZoneIdentifier\n        organizationId\n        participatingTeamIds\n        venueConfigurationId\n        associatedPassConfigs {\n          id\n        }\n        ticketTypeReferences {\n          priceOverride\n          quantityOverride\n          ticketTypeId\n        }\n        ticketTypes {\n          createdAt\n          deletedAt\n          id\n          name\n          organizationId\n          priceInCents\n          quantity\n          updatedAt\n        }\n        quantityRemainingForTicketTypes {\n          quantityRemaining\n          referenceId\n        }\n        ticketTypePricingSummaries {\n          hudlFeeInCents\n          priceInCents\n          priceInCentsWithHudlFee\n          ticketedEventId\n          ticketTypeId\n          shouldShowFee\n          currency\n        }\n      }\n    }\n  ": types.Rn_TicketedEventByIdWithAnalytics_R1Document,
    "\n    query RN_GetTicketingPricingSummary_r1(\n      $lineItems: [TicketingPricingLineItemInput]!\n      $source: String\n    ) {\n      ticketingPricingSummary(lineItems: $lineItems, source: $source) {\n        currency\n        feesInCents\n        lineItems {\n          lineItemId\n          lineItemType\n          quantity\n          referenceId\n          totalPriceInCents\n          unitPriceInCents\n        }\n        shouldShowFee\n        subtotalInCents\n        totalInCents\n        hudlFeeInCents\n        paymentProcessingFeeInCents\n        subtotalWithHudlFeeInCents\n      }\n    }\n  ": types.Rn_GetTicketingPricingSummary_R1Document,
};

export function graphql(source: "\n    mutation RN_POS_CashCheckoutWithTicketingLineItems_r1($input: TicketingCashCheckoutInput!) {\n      cashCheckoutWithTicketingLineItems(input: $input) {\n        ticketGroup {\n          createdAt\n          deletedAt\n          email\n          id\n          ticketGroupReference\n          updatedAt\n        }\n      }\n    }\n  "): (typeof documents)["\n    mutation RN_POS_CashCheckoutWithTicketingLineItems_r1($input: TicketingCashCheckoutInput!) {\n      cashCheckoutWithTicketingLineItems(input: $input) {\n        ticketGroup {\n          createdAt\n          deletedAt\n          email\n          id\n          ticketGroupReference\n          updatedAt\n        }\n      }\n    }\n  "];
export function graphql(source: "\n    mutation RN_POS_CheckoutWithTicketingLineItems_r1($input: TicketingCheckoutInput!) {\n      checkoutWithTicketingLineItems(input: $input) {\n        pricingSummary {\n          currency\n          feesInCents\n          lineItems {\n            lineItemId\n            lineItemType\n            quantity\n            referenceId\n            totalPriceInCents\n            unitPriceInCents\n          }\n          subtotalInCents\n          totalInCents\n        }\n        ticketGroup {\n          createdAt\n          deletedAt\n          email\n          id\n          ticketGroupReference\n          tickets {\n            createdAt\n            deletedAt\n            id\n            updatedAt\n            email\n            passId\n            redeemedAt\n            refundable\n            ticketedEventId\n            ticketStatus\n            ticketTypeId\n            qrCodeUrl\n          }\n          passes {\n            id\n            createdAt\n            deletedAt\n            email\n            passConfigId\n            qrCodeUrl\n            updatedAt\n          }\n          updatedAt\n        }\n      }\n    }\n  "): (typeof documents)["\n    mutation RN_POS_CheckoutWithTicketingLineItems_r1($input: TicketingCheckoutInput!) {\n      checkoutWithTicketingLineItems(input: $input) {\n        pricingSummary {\n          currency\n          feesInCents\n          lineItems {\n            lineItemId\n            lineItemType\n            quantity\n            referenceId\n            totalPriceInCents\n            unitPriceInCents\n          }\n          subtotalInCents\n          totalInCents\n        }\n        ticketGroup {\n          createdAt\n          deletedAt\n          email\n          id\n          ticketGroupReference\n          tickets {\n            createdAt\n            deletedAt\n            id\n            updatedAt\n            email\n            passId\n            redeemedAt\n            refundable\n            ticketedEventId\n            ticketStatus\n            ticketTypeId\n            qrCodeUrl\n          }\n          passes {\n            id\n            createdAt\n            deletedAt\n            email\n            passConfigId\n            qrCodeUrl\n            updatedAt\n          }\n          updatedAt\n        }\n      }\n    }\n  "];
export function graphql(source: "\n    mutation RN_POS_CreateTerminalConnectionToken_r1($input: CreateTerminalConnectionTokenInput!) {\n      createTerminalConnectionToken(input: $input) {\n        clientSecret\n      }\n    }\n  "): (typeof documents)["\n    mutation RN_POS_CreateTerminalConnectionToken_r1($input: CreateTerminalConnectionTokenInput!) {\n      createTerminalConnectionToken(input: $input) {\n        clientSecret\n      }\n    }\n  "];
export function graphql(source: "\n    mutation RN_POS_CreateTicketingPaymentToken_r1($input: CreateTicketingPaymentTokenInput!) {\n      createTicketingPaymentToken(input: $input)\n    }\n  "): (typeof documents)["\n    mutation RN_POS_CreateTicketingPaymentToken_r1($input: CreateTicketingPaymentTokenInput!) {\n      createTicketingPaymentToken(input: $input)\n    }\n  "];
export function graphql(source: "\n  mutation RN_Scanner_QueueBatchRedemption_r1($input: ScanningRedemptionInput!) {\n    queueBatchRedemption(input: $input)\n  }\n"): (typeof documents)["\n  mutation RN_Scanner_QueueBatchRedemption_r1($input: ScanningRedemptionInput!) {\n    queueBatchRedemption(input: $input)\n  }\n"];
export function graphql(source: "\n  query RN_Scanner_GetTicketedEventAccessCode_r1($input: GetTicketedEventAccessCodeInput!) {\n    ticketedEventAccessCode(input: $input) {\n      organization {\n        id\n        internalId\n        abbreviation\n        fullName\n        shortName\n        mascot\n        primaryColor\n        secondaryColor\n        countryIsoAlpha2\n      }\n    }\n  }\n"): (typeof documents)["\n  query RN_Scanner_GetTicketedEventAccessCode_r1($input: GetTicketedEventAccessCodeInput!) {\n    ticketedEventAccessCode(input: $input) {\n      organization {\n        id\n        internalId\n        abbreviation\n        fullName\n        shortName\n        mascot\n        primaryColor\n        secondaryColor\n        countryIsoAlpha2\n      }\n    }\n  }\n"];
export function graphql(source: "\n    query RN_Scanner_GetAssociatedTicketGroupsForTicketedEventId_r1(\n      $input: GetAssociatedTicketGroupsWithAccessCodeInput!\n    ) {\n      associatedTicketGroupsForTicketedEventId(input: $input) {\n        pageInfo {\n          startCursor\n          endCursor\n          hasNextPage\n          hasPreviousPage\n        }\n        items {\n          id\n          ticketGroupReference\n          userId\n          firstName\n          lastName\n          email\n          tickets {\n            id\n          }\n          passes {\n            id\n          }\n        }\n      }\n    }\n  "): (typeof documents)["\n    query RN_Scanner_GetAssociatedTicketGroupsForTicketedEventId_r1(\n      $input: GetAssociatedTicketGroupsWithAccessCodeInput!\n    ) {\n      associatedTicketGroupsForTicketedEventId(input: $input) {\n        pageInfo {\n          startCursor\n          endCursor\n          hasNextPage\n          hasPreviousPage\n        }\n        items {\n          id\n          ticketGroupReference\n          userId\n          firstName\n          lastName\n          email\n          tickets {\n            id\n          }\n          passes {\n            id\n          }\n        }\n      }\n    }\n  "];
export function graphql(source: "\n    query Web_Fan_GetSchool_r1($schoolId: ID, $internalSchoolId: String) {\n      school(schoolId: $schoolId, internalSchoolId: $internalSchoolId) {\n        id\n        countryIsoAlpha2\n      }\n    }\n  "): (typeof documents)["\n    query Web_Fan_GetSchool_r1($schoolId: ID, $internalSchoolId: String) {\n      school(schoolId: $schoolId, internalSchoolId: $internalSchoolId) {\n        id\n        countryIsoAlpha2\n      }\n    }\n  "];
export function graphql(source: "\n  query RN_Scanner_GetTicketedEventsByOrganizationId_r1(\n    $input: GetTicketedEventsByOrganizationIdInput!\n  ) {\n    ticketedEventsByOrganizationId(input: $input) {\n      totalCount\n      edges {\n        node {\n          id\n        }\n      }\n      pageInfo {\n        startCursor\n        endCursor\n        hasNextPage\n        hasPreviousPage\n      }\n      items {\n        id\n        date\n        eventStatus\n        gender\n        name\n        organizationId\n        participatingTeamIds\n        sport\n        timeZoneIdentifier\n        venueConfigurationId\n        associatedPassConfigs {\n          id\n        }\n        ticketTypePricingSummaries {\n          hudlFeeInCents\n          priceInCents\n          priceInCentsWithHudlFee\n          ticketedEventId\n          ticketTypeId\n          shouldShowFee\n          currency\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query RN_Scanner_GetTicketedEventsByOrganizationId_r1(\n    $input: GetTicketedEventsByOrganizationIdInput!\n  ) {\n    ticketedEventsByOrganizationId(input: $input) {\n      totalCount\n      edges {\n        node {\n          id\n        }\n      }\n      pageInfo {\n        startCursor\n        endCursor\n        hasNextPage\n        hasPreviousPage\n      }\n      items {\n        id\n        date\n        eventStatus\n        gender\n        name\n        organizationId\n        participatingTeamIds\n        sport\n        timeZoneIdentifier\n        venueConfigurationId\n        associatedPassConfigs {\n          id\n        }\n        ticketTypePricingSummaries {\n          hudlFeeInCents\n          priceInCents\n          priceInCentsWithHudlFee\n          ticketedEventId\n          ticketTypeId\n          shouldShowFee\n          currency\n        }\n      }\n    }\n  }\n"];
export function graphql(source: "\n    query RN_TicketedEventByIdWithAnalytics_r1($ticketedEventId: ID!) {\n      ticketedEventById(ticketedEventId: $ticketedEventId) {\n        id\n        name\n        description\n        date\n        timeZoneIdentifier\n        organizationId\n        participatingTeamIds\n        venueConfigurationId\n        associatedPassConfigs {\n          id\n        }\n        ticketTypeReferences {\n          priceOverride\n          quantityOverride\n          ticketTypeId\n        }\n        ticketTypes {\n          createdAt\n          deletedAt\n          id\n          name\n          organizationId\n          priceInCents\n          quantity\n          updatedAt\n        }\n        quantityRemainingForTicketTypes {\n          quantityRemaining\n          referenceId\n        }\n        ticketTypePricingSummaries {\n          hudlFeeInCents\n          priceInCents\n          priceInCentsWithHudlFee\n          ticketedEventId\n          ticketTypeId\n          shouldShowFee\n          currency\n        }\n      }\n    }\n  "): (typeof documents)["\n    query RN_TicketedEventByIdWithAnalytics_r1($ticketedEventId: ID!) {\n      ticketedEventById(ticketedEventId: $ticketedEventId) {\n        id\n        name\n        description\n        date\n        timeZoneIdentifier\n        organizationId\n        participatingTeamIds\n        venueConfigurationId\n        associatedPassConfigs {\n          id\n        }\n        ticketTypeReferences {\n          priceOverride\n          quantityOverride\n          ticketTypeId\n        }\n        ticketTypes {\n          createdAt\n          deletedAt\n          id\n          name\n          organizationId\n          priceInCents\n          quantity\n          updatedAt\n        }\n        quantityRemainingForTicketTypes {\n          quantityRemaining\n          referenceId\n        }\n        ticketTypePricingSummaries {\n          hudlFeeInCents\n          priceInCents\n          priceInCentsWithHudlFee\n          ticketedEventId\n          ticketTypeId\n          shouldShowFee\n          currency\n        }\n      }\n    }\n  "];
export function graphql(source: "\n    query RN_GetTicketingPricingSummary_r1(\n      $lineItems: [TicketingPricingLineItemInput]!\n      $source: String\n    ) {\n      ticketingPricingSummary(lineItems: $lineItems, source: $source) {\n        currency\n        feesInCents\n        lineItems {\n          lineItemId\n          lineItemType\n          quantity\n          referenceId\n          totalPriceInCents\n          unitPriceInCents\n        }\n        shouldShowFee\n        subtotalInCents\n        totalInCents\n        hudlFeeInCents\n        paymentProcessingFeeInCents\n        subtotalWithHudlFeeInCents\n      }\n    }\n  "): (typeof documents)["\n    query RN_GetTicketingPricingSummary_r1(\n      $lineItems: [TicketingPricingLineItemInput]!\n      $source: String\n    ) {\n      ticketingPricingSummary(lineItems: $lineItems, source: $source) {\n        currency\n        feesInCents\n        lineItems {\n          lineItemId\n          lineItemType\n          quantity\n          referenceId\n          totalPriceInCents\n          unitPriceInCents\n        }\n        shouldShowFee\n        subtotalInCents\n        totalInCents\n        hudlFeeInCents\n        paymentProcessingFeeInCents\n        subtotalWithHudlFeeInCents\n      }\n    }\n  "];

export function graphql(source: string): unknown;
export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;