/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** The `Byte` scalar type represents non-fractional whole numeric values. Byte can represent values between 0 and 255. */
  Byte: any;
  /** Represents a cursor */
  Cursor: any;
  /** The `Date` scalar represents an ISO-8601 compliant date type. */
  Date: any;
  /** The `DateTimeType` scalar represents an ISO-8601 compliant date time type */
  DateTime: any;
  /** The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1. */
  Long: any;
  /** The `Short` scalar type represents non-fractional signed whole 16-bit numeric values. Short can represent values between -(2^15) and 2^15 - 1. */
  Short: any;
  /** The `TimeSpan` scalar represents an ISO-8601 compliant duration type. */
  TimeSpan: any;
  /** The UnsignedShort scalar type represents a unsigned 16-bit numeric non-fractional value greater than or equal to 0. */
  UnsignedShort: any;
};

/** Ad targeting model. */
export type AdTargeting = {
  __typename?: 'AdTargeting';
  /** The targeting context for the ad. */
  adContext?: Maybe<Scalars['String']>;
  /** Toggle to opt out of targeted ads. */
  canReceivePersonalizedAds: Scalars['Boolean'];
};

/** Representation of a key-value pair input for additional properties. */
export type AdditionalPropertiesKeyValuePairInput = {
  /** Represents the additonal property to set. */
  key?: InputMaybe<Scalars['String']>;
  /** Represents the value of the additional property. */
  value?: InputMaybe<Scalars['String']>;
};

/** Representation of an athlete and guardian relationship. */
export type AthleteAndGuardianRelationship = {
  __typename?: 'AthleteAndGuardianRelationship';
  /** GraphQL encoded user ID for the athlete. */
  athleteUserId: Scalars['ID'];
  /** Athlete's birth date. */
  birthDate?: Maybe<Scalars['Date']>;
  /** Athlete's last name. */
  familyName: Scalars['String'];
  /** Athlete's gender. */
  gender?: Maybe<Gender>;
  /** Athlete's first name. */
  givenName: Scalars['String'];
  /** Athlete's graduation year. */
  graduationYear?: Maybe<Scalars['Int']>;
  /** The guardian relationship. */
  guardianRelationship: GuardianRelationship;
};

export type AthleteProfile = {
  __typename?: 'AthleteProfile';
  /** The athlete's team details. */
  athleteTeamDetails?: Maybe<Array<Maybe<AthleteProfileTeamDetails>>>;
  /** The athlete's banner images */
  bannerImages?: Maybe<AthleteProfileImages>;
  /** The athlete's bio. */
  bio?: Maybe<Scalars['String']>;
  /** The athlete's first name. */
  firstName?: Maybe<Scalars['String']>;
  /** The athlete's follower count. */
  followerCount: Scalars['Int'];
  /** The athlete's graduation year. */
  graduationYear: Scalars['Int'];
  /** The athlete's height. */
  height: Scalars['Long'];
  /** The athlete's highlight view count. */
  highlightViews: Scalars['Int'];
  /** The GraphQL id of the athlete. */
  id: Scalars['ID'];
  /** The internal id of the athlete. */
  internalId?: Maybe<Scalars['String']>;
  /** The athlete's internal school ids. */
  internalSchoolIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The athlete's internal team ids. */
  internalTeamIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The athlete's last name. */
  lastName?: Maybe<Scalars['String']>;
  /** The athlete's lastest GraphQL TeamIds for each sport they played. */
  latestTeamIdForEachSport?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** The athlete's latest internal teamIds for each sport they played. */
  latestTeamInternalIdForEachSport?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The athlete's primary graphql teamId. */
  primaryTeamId: Scalars['ID'];
  /** The athlete's primary internal teamId. */
  primaryTeamInternalId?: Maybe<Scalars['String']>;
  /** The athlete's profile images */
  profileImages?: Maybe<AthleteProfileImages>;
  /** The athlete's school ids. */
  schoolIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** The schools for the athlete */
  schools?: Maybe<Array<Maybe<School>>>;
  /** The athlete's team ids. */
  teamIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** The athlete's Twitter Handle */
  twitterHandle?: Maybe<Scalars['String']>;
  /** The athlete's vitals. */
  vitals?: Maybe<AthleteProfileVitals>;
  /** The athlete's weight. */
  weight: Scalars['Long'];
};

export type AthleteProfileImages = {
  __typename?: 'AthleteProfileImages';
  /** Full sized image URL */
  full?: Maybe<Scalars['String']>;
  /** Normal sized image URL */
  normal?: Maybe<Scalars['String']>;
  /** Retina sized image URL */
  retina?: Maybe<Scalars['String']>;
  /** Thumbnail sized image URL */
  thumbnail?: Maybe<Scalars['String']>;
};

export type AthleteProfileTeamDetails = {
  __typename?: 'AthleteProfileTeamDetails';
  /** The gender of the team */
  gender: Gender;
  /** The athlete's graduation year */
  graduationYear?: Maybe<Scalars['Short']>;
  /** The internal Id of the team */
  internalTeamId?: Maybe<Scalars['String']>;
  /** Whether the athlete is disabled */
  isAthleteDisabled: Scalars['Boolean'];
  /** The athlete's jersey number */
  jersey?: Maybe<Scalars['Byte']>;
  /** The level of the team */
  level: TeamLevel;
  /** The full name of the organization */
  orgFullName?: Maybe<Scalars['String']>;
  /** The short name of the organization */
  orgShortName?: Maybe<Scalars['String']>;
  /** The athlete's positions on the team */
  positions?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The GraphQL Id of the school */
  schoolId: Scalars['ID'];
  /** The sport of the team */
  sport: Sport;
  /** The GraphQL Id of the team */
  teamId: Scalars['ID'];
  /** The name of the team */
  teamName?: Maybe<Scalars['String']>;
};

export type AthleteProfileVitals = {
  __typename?: 'AthleteProfileVitals';
  /** The athlete's Achievements. */
  achievements?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The athlete's Approaching Jump Touch with One Arm. */
  approachJumpTouchOneArm?: Maybe<Scalars['String']>;
  /** The athlete's Bench. */
  bench?: Maybe<Scalars['String']>;
  /** The athlete's Bench Press Reps. */
  benchPressReps?: Maybe<Scalars['String']>;
  /** Whether the athlete's Bench Press Reps is verified. */
  benchPressRepsVerified: Scalars['Boolean'];
  /** Whether the athlete's Bench is verified. */
  benchVerified: Scalars['Boolean'];
  /** The athlete's Clean. */
  clean?: Maybe<Scalars['String']>;
  /** The athlete's Dead Lift. */
  deadLift?: Maybe<Scalars['String']>;
  /** The athlete's Forty. */
  forty?: Maybe<Scalars['String']>;
  /** Whether the athlete's Forty is verified. */
  fortyVerified: Scalars['Boolean'];
  /** The athlete's 100 Meter Time. */
  meter100?: Maybe<Scalars['String']>;
  /** The athlete's 400 Meter Time. */
  meter400?: Maybe<Scalars['String']>;
  /** The athlete's 1600 Meter Time. */
  meter1600?: Maybe<Scalars['String']>;
  /** The athlete's 3200 Meter Time. */
  meter3200?: Maybe<Scalars['String']>;
  /** The athlete's Nike Football rating */
  nikeFootballRating?: Maybe<Scalars['Float']>;
  /** Whether the athlete's Nike Football rating is verified */
  nikeFootballRatingVerified: Scalars['Boolean'];
  /** The athlete's Powerball. */
  powerball?: Maybe<Scalars['String']>;
  /** Whether the athlete's Powerball is verified. */
  powerballVerified: Scalars['Boolean'];
  /** The athlete's Pro Agility. */
  proAgility?: Maybe<Scalars['String']>;
  /** The athlete's Shuttle. */
  shuttle?: Maybe<Scalars['String']>;
  /** Whether the athlete's Shuttle is verified. */
  shuttleVerified: Scalars['Boolean'];
  /** The athlete's Six Touches Sideline to Sideline. */
  sixTouchesSidelineToSideline?: Maybe<Scalars['String']>;
  /** The athlete's Squat. */
  squat?: Maybe<Scalars['String']>;
  /** The athlete's Standing Blocking Reach. */
  standingBlockingReach?: Maybe<Scalars['String']>;
  /** The athlete's Standing Reach. */
  standingReach?: Maybe<Scalars['String']>;
  /** The athlete's Vertical. */
  vertical?: Maybe<Scalars['String']>;
  /** The athlete's Vertical Jump with One Arm. */
  verticalJumpOneArm?: Maybe<Scalars['String']>;
  /** The athlete's Vertical Jumping Block with Two Arms. */
  verticalJumpingBlockTwoArms?: Maybe<Scalars['String']>;
  /** Whether the athlete's Vertical is verified. */
  verticalVerified: Scalars['Boolean'];
};

export type AuthenticationPayload = {
  __typename?: 'AuthenticationPayload';
  endDate?: Maybe<Scalars['DateTime']>;
  licenseType?: Maybe<LicenseType>;
  machine?: Maybe<Scalars['String']>;
  publicHash?: Maybe<Scalars['String']>;
  registration?: Maybe<Scalars['String']>;
  schoolName?: Maybe<Scalars['String']>;
  signature?: Maybe<Scalars['String']>;
  special?: Maybe<Scalars['String']>;
  startDate?: Maybe<Scalars['DateTime']>;
  subscriptionEndDate?: Maybe<Scalars['DateTime']>;
  subscriptionStartDate?: Maybe<Scalars['DateTime']>;
  token?: Maybe<Scalars['String']>;
};

export type BackgroundImage = {
  __typename?: 'BackgroundImage';
  /** The image content server id */
  contentServerId?: Maybe<Scalars['String']>;
  /** The image height */
  height?: Maybe<Scalars['Int']>;
  /** Returns true if contentServerId or path is defined */
  isValid: Scalars['Boolean'];
  /** The image path */
  path?: Maybe<Scalars['String']>;
  /** The image secure url */
  secureUrl?: Maybe<Scalars['String']>;
  /** The image size */
  size: ImageSize;
  /** The image url */
  url?: Maybe<Scalars['String']>;
  /** The image width */
  width?: Maybe<Scalars['Int']>;
};

export type Broadcast = {
  __typename?: 'Broadcast';
  /** List of access passes for the broadcast. */
  accessPassIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Broadcast availability. */
  available: Scalars['Boolean'];
  /** The time the broadcast will begin in the UTC timezone. */
  broadcastDateUtc: Scalars['DateTime'];
  /** The internal broadcastId of the broadcast. */
  broadcastId?: Maybe<Scalars['String']>;
  /** The Id of the user or device that created the broadcast. This value could represent a userId for a mobile broadcast, an installationId for a focus broadcast, etc */
  broadcastOriginatorId?: Maybe<Scalars['String']>;
  /** The name of the user or device that created the broadcast. This value could represent a user's name for a mobile broadcast, a device name for a focus broadcast, etc */
  broadcastOriginatorName?: Maybe<Scalars['String']>;
  /** The internal BsonId of the broadcast. Refers to the Id originating from hudl-broadcasts. */
  bsonId?: Maybe<Scalars['String']>;
  /** The date the broadcast was last modified. */
  dateModified: Scalars['DateTime'];
  /** The description of the broadcast. */
  description?: Maybe<Scalars['String']>;
  /** Indicates if the broadcast has domain blocking rules. */
  domainBlocking: Scalars['Boolean'];
  /** String url to start the download process for the broadcast. */
  downloadUrl?: Maybe<Scalars['String']>;
  /** The current duration of the broadcast. */
  duration: Scalars['Float'];
  /** String HTML code used to embed the broadcast into a webpage using an iFrame. */
  embedCode?: Maybe<Scalars['String']>;
  /** The src string from the embed code. */
  embedCodeSrc?: Maybe<Scalars['String']>;
  /** Determines if the broadcast should be hidden from the user. */
  hidden: Scalars['Boolean'];
  /** The Id of the broadcast. Refers to the encoded BroadcastId originating from vCloud. */
  id: Scalars['ID'];
  /** The internal Id of the broadcast. Refers to the BroadcastId originating from vCloud. */
  internalId?: Maybe<Scalars['String']>;
  /** String url to poster image uploaded to broadcast. Large version. */
  largeThumbnail?: Maybe<Scalars['String']>;
  /** The duration of the broadcast when it was live. */
  liveDuration: Scalars['Float'];
  /** Related media stream for a broadcast. */
  mediaStreamId?: Maybe<Scalars['String']>;
  /** String url to poster image uploaded to broadcast. Medium version. */
  mediumThumbnail?: Maybe<Scalars['String']>;
  /** The number of syndication endpoints for the broadcast. */
  numSyndicationEndpoints: Scalars['Int'];
  /** Indicates if the broadcast has geoblocking rules. */
  regionBlocking: Scalars['Boolean'];
  /** Indicates whether the broadcast requires login for pay-per-view. */
  requireLogin?: Maybe<Scalars['String']>;
  /** The identifier of the schedule entry associated with this broadcast. */
  scheduleEntryId?: Maybe<Scalars['ID']>;
  /** The public schedule entry summary for the broadcast */
  scheduleEntrySummary?: Maybe<ScheduleEntryPublicSummary>;
  /** The identifier of the school that owns this broadcast. */
  schoolId?: Maybe<Scalars['ID']>;
  /** The Scoring Session for the broadcast. */
  scoreboardSession?: Maybe<ScoreboardSession>;
  /** The internalId of the season associated with this broadcast. */
  seasonId?: Maybe<Scalars['ID']>;
  /** The sectionId that classifies this broadcast. Sections correlate to Hudl genders and sports. */
  sectionId: Scalars['Int'];
  /** The title of the section that classifies this broadcasts. Sections correlate to Hudl genders and sports. */
  sectionTitle?: Maybe<Scalars['String']>;
  /** Indicates that the broadcast was shared to a site that doesn't own it. */
  shared: Scalars['Boolean'];
  /** List of site slugs that this broadcast is shared with. */
  sharedSites?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The siteId of the site that owns the broadcast. */
  siteId?: Maybe<Scalars['String']>;
  /** The site slug of the site that owns the broadcast. */
  siteSlug?: Maybe<Scalars['String']>;
  /** The title of the site that owns this broadcast. */
  siteTitle?: Maybe<Scalars['String']>;
  /** String url to poster image uploaded to broadcast. Small version. */
  smallThumbnail?: Maybe<Scalars['String']>;
  /** If set, the GraphQL encoded broadcastId that this broadcast uses video data from. This broadcast would be considered a simulcast of the source broadcast. */
  sourceBroadcastId?: Maybe<Scalars['ID']>;
  /** The status of the broadcast. */
  status?: Maybe<Scalars['String']>;
  /** The identifier of the team that owns this broadcast. */
  teamId?: Maybe<Scalars['ID']>;
  /** The timezone the broadcast will be recorded in. Used to convert BroadcastDateUtc. */
  timezone?: Maybe<Scalars['String']>;
  /** The title of the broadcast. */
  title?: Maybe<Scalars['String']>;
  /** The upload source of the livestream */
  uploadSource: PublishSessionUploadSource;
};

/** A connection to a list of items. */
export type BroadcastConnection = {
  __typename?: 'BroadcastConnection';
  /** A list of edges. */
  edges?: Maybe<Array<BroadcastEdge>>;
  items?: Maybe<Array<Broadcast>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<Broadcast>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type BroadcastEdge = {
  __typename?: 'BroadcastEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<Broadcast>;
};

export enum BroadcastFilter {
  All = 'ALL',
  Archived = 'ARCHIVED',
  Live = 'LIVE',
  Scheduled = 'SCHEDULED',
  Streaming = 'STREAMING',
  Upcoming = 'UPCOMING',
  UpcomingOrStreaming = 'UPCOMING_OR_STREAMING'
}

export enum BroadcastSortType {
  BroadcastDate = 'BROADCAST_DATE'
}

export type Carrier = {
  __typename?: 'Carrier';
  /** Description for the phone carrier */
  description?: Maybe<Scalars['String']>;
  /** Carrier value of the enum */
  value?: Maybe<Scalars['String']>;
};

export enum CommunityContentContainerPlayer {
  EmbeddablePlayer = 'EMBEDDABLE_PLAYER',
  Immersive = 'IMMERSIVE',
  NativeSocial = 'NATIVE_SOCIAL',
  Partner = 'PARTNER',
  TimelineCard = 'TIMELINE_CARD',
  Unknown = 'UNKNOWN',
  VideoManagementPlayer = 'VIDEO_MANAGEMENT_PLAYER',
  VideoPage = 'VIDEO_PAGE'
}

export enum CommunityContentContainerType {
  EmbeddablePlayer = 'EMBEDDABLE_PLAYER',
  Explore = 'EXPLORE',
  Home = 'HOME',
  OwnedSocial = 'OWNED_SOCIAL',
  Partner = 'PARTNER',
  PerformanceCenter = 'PERFORMANCE_CENTER',
  Profile = 'PROFILE',
  Unknown = 'UNKNOWN',
  VideoManagement = 'VIDEO_MANAGEMENT',
  VideoPage = 'VIDEO_PAGE'
}

/** Used to link metadata to community content. */
export type CommunityContentIdInput = {
  /** Specifies the unique ID of this content, e.g. the reelId for a highlight. */
  relatedId?: InputMaybe<Scalars['String']>;
  /** Specifies a secondary ID that may be necessary when querying for a piece of content, e.g. the ownerId for a highlight. */
  secondaryRelatedId?: InputMaybe<Scalars['String']>;
  /** Specifies the type of content referenced. */
  type: CommunityContentType;
};

export enum CommunityContentRequestingApp {
  Android = 'ANDROID',
  Ios = 'IOS',
  Unknown = 'UNKNOWN',
  Web = 'WEB'
}

export enum CommunityContentType {
  FeedContent = 'FEED_CONTENT',
  HudlHighlight = 'HUDL_HIGHLIGHT',
  Message = 'MESSAGE',
  PerformanceCenter = 'PERFORMANCE_CENTER',
  TeamHighlight = 'TEAM_HIGHLIGHT',
  Unknown = 'UNKNOWN',
  UserHighlight = 'USER_HIGHLIGHT'
}

export type CompetitionHeader = {
  __typename?: 'CompetitionHeader';
  /** The competition periods for the competition. */
  competitionPeriods?: Maybe<Array<Maybe<CompetitionPeriodHeader>>>;
  /** The ISO 3166-1 alpha-3 code representing the country of the Competition */
  countryIso?: Maybe<Scalars['String']>;
  /** The current competition period for the competition */
  currentCompetitionPeriod?: Maybe<CompetitionPeriodHeader>;
  /** The gender of the Competition. */
  gender: Gender;
  /** The Id of the Governing Body the Competition belongs to */
  governingBodyId?: Maybe<Scalars['String']>;
  /** The Id of the Competition */
  id: Scalars['ID'];
  /** The internal Id of the Competition */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the Competition */
  name?: Maybe<Scalars['String']>;
  /** The sport of the Competition. */
  sport: Sport;
  /** The Id of the Subdivision the Competition belongs to */
  subdivisionId?: Maybe<Scalars['String']>;
  /** The ISO 3166-2 code representing the state or province of the Competition */
  subdivisionIso?: Maybe<Scalars['String']>;
};

export type CompetitionPeriodHeader = {
  __typename?: 'CompetitionPeriodHeader';
  /** The Id of the Comeptition the period belongs to */
  competitionId?: Maybe<Scalars['String']>;
  /** The UTC time that the Competition Period ends */
  endDateUtc?: Maybe<Scalars['DateTime']>;
  /** The graphQL Id of the Competition Period */
  id: Scalars['ID'];
  /** The internal Id of the Competition Period */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the Competition Period */
  name?: Maybe<Scalars['String']>;
  /** The UTC time that the Competition Period starts */
  startDateUtc?: Maybe<Scalars['DateTime']>;
  /** The list of team Ids associated with the Competition Period */
  teamIds?: Maybe<Array<Maybe<Scalars['String']>>>;
};

/** Representation of an input to create a new provisional athlete and guardian relationship. */
export type CreateProvisionalAthleteAndGuardianRelationshipInput = {
  /** Provisional athlete's birth date. */
  birthDate: Scalars['Date'];
  /** User ID of the person creating the provisional athlete. */
  creatingUserId: Scalars['String'];
  /** Provisional athlete's last name. */
  familyName: Scalars['String'];
  /** Provisional athlete's gender. */
  gender: Gender;
  /** Provisional athlete's first name. */
  givenName: Scalars['String'];
  /** Provisional athlete's graduation year. */
  graduationYear: Scalars['Int'];
};

/** Representation of an input to create a terminal connection token. */
export type CreateTerminalConnectionTokenInput = {
  /** The access code for the org that is using terminal payments. */
  accessCode: Scalars['String'];
  /** The ID of the org that is using terminal payments */
  organizationId: Scalars['ID'];
};

/** Input to create a payment token for a cart of ticketing items. */
export type CreateTicketingPaymentTokenInput = {
  /** The expected total amount in cents to charge for the token. */
  expectedTotalInCents: Scalars['Int'];
  /** A list of ticketing items to include in the total for the token. */
  lineItems: Array<InputMaybe<TicketingPricingLineItemInput>>;
  /** The organization ID to use for the token. */
  organizationId: Scalars['ID'];
  /** The source of the cart of ticketing items. */
  source?: InputMaybe<Scalars['String']>;
};

/** Input parameters for a deregistration operation. */
export type DeregistrationInput = {
  /** The client mutation identifier. */
  clientMutationId?: InputMaybe<Scalars['String']>;
  /** The machine identifier. */
  machineIdentifier: Scalars['String'];
  /** The machine name. */
  machineName?: InputMaybe<Scalars['String']>;
  /** The registration code. */
  registrationCode: Scalars['String'];
  /** The License security token. */
  token: Scalars['String'];
  /** The installed version of Sportscode. */
  userVersion: Scalars['String'];
};

/** The result of a deregistration operation. */
export type DeregistrationPayload = {
  __typename?: 'DeregistrationPayload';
  /** The client mutation identifier. */
  clientMutationId?: Maybe<Scalars['String']>;
  /** Indication of whether the operation was successful or not. */
  isSuccessful?: Maybe<Scalars['Boolean']>;
};

/** A connection to a list of items. */
export type DiscoverablePassConfigsConnection = {
  __typename?: 'DiscoverablePassConfigsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<DiscoverablePassConfigsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<PassConfig>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type DiscoverablePassConfigsEdge = {
  __typename?: 'DiscoverablePassConfigsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<PassConfig>;
};

/** A connection to a list of items. */
export type DiscoverableTicketedEventsConnection = {
  __typename?: 'DiscoverableTicketedEventsConnection';
  /** A list of edges. */
  edges?: Maybe<Array<DiscoverableTicketedEventsEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<TicketedEvent>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type DiscoverableTicketedEventsEdge = {
  __typename?: 'DiscoverableTicketedEventsEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<TicketedEvent>;
};

/** Representation of an embedded organization program type. */
export type EmbeddedOrganizationProgramType = {
  __typename?: 'EmbeddedOrganizationProgramType';
  /** The Id of the Organization Program Type. */
  id: Scalars['ID'];
  /** The name of the program type. */
  name: Scalars['String'];
};

export enum EntityType {
  Athlete = 'ATHLETE',
  Organization = 'ORGANIZATION',
  Team = 'TEAM',
  User = 'USER'
}

export type FanBroadcast = {
  __typename?: 'FanBroadcast';
  /** Broadcast availability. */
  available: Scalars['Boolean'];
  /** The time the broadcast will begin in the UTC timezone. */
  broadcastDateUtc: Scalars['DateTime'];
  /** The internal broadcastId of the broadcast. */
  broadcastId?: Maybe<Scalars['String']>;
  /** The description of the broadcast. */
  description?: Maybe<Scalars['String']>;
  /** The current duration of the broadcast. */
  duration: Scalars['Float'];
  /** String HTML code used to embed the broadcast into a webpage using an iFrame. */
  embedCode?: Maybe<Scalars['String']>;
  /** The src string from the embed code. */
  embedCodeSrc?: Maybe<Scalars['String']>;
  /** Determines if the broadcast should be hidden from the user. */
  hidden: Scalars['Boolean'];
  /** The broadcast id. */
  id: Scalars['ID'];
  /** Determines if the broadcasts corresponding team profile is hidden */
  isTeamProfileHidden: Scalars['Boolean'];
  /** String url to poster image uploaded to broadcast. Large version. */
  largeThumbnail?: Maybe<Scalars['String']>;
  /** String url to poster image uploaded to broadcast. Medium version. */
  mediumThumbnail?: Maybe<Scalars['String']>;
  /** The number of syndication endpoints for the broadcast. */
  numSyndicationEndpoints: Scalars['Int'];
  /** The opponent organization of the broadcast. */
  opponentOrganization?: Maybe<FanContentOrganization>;
  /** The organization of the broadcast. */
  organization?: Maybe<FanContentOrganization>;
  /** Indicates whether the broadcast requires login for pay-per-view. */
  requireLogin?: Maybe<Scalars['String']>;
  /** The identifier of the schedule entry associated with this broadcast. */
  scheduleEntryId?: Maybe<Scalars['ID']>;
  /** The identifier of the school that owns this broadcast. */
  schoolId?: Maybe<Scalars['ID']>;
  /** List of site slugs that this broadcast is shared with. */
  sharedSites?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The siteId of the site that owns the broadcast. */
  siteId?: Maybe<Scalars['String']>;
  /** String url to poster image uploaded to broadcast. Small version. */
  smallThumbnail?: Maybe<Scalars['String']>;
  /** The status of the broadcast. */
  status?: Maybe<Scalars['String']>;
  /** The team header of the broadcast. */
  teamHeader?: Maybe<FanContentTeamHeader>;
  /** The identifier of the team that owns this broadcast. */
  teamId?: Maybe<Scalars['ID']>;
  /** The title of the broadcast. */
  title?: Maybe<Scalars['String']>;
};

/** A connection to a list of items. */
export type FanBroadcastConnection = {
  __typename?: 'FanBroadcastConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FanBroadcastEdge>>;
  items?: Maybe<Array<FanBroadcast>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<FanBroadcast>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type FanBroadcastEdge = {
  __typename?: 'FanBroadcastEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<FanBroadcast>;
};

export enum FanBroadcastFilter {
  All = 'ALL',
  Archived = 'ARCHIVED',
  Live = 'LIVE',
  Scheduled = 'SCHEDULED',
  Streaming = 'STREAMING',
  Upcoming = 'UPCOMING',
  UpcomingOrStreaming = 'UPCOMING_OR_STREAMING'
}

export enum FanBroadcastSortType {
  BroadcastDate = 'BROADCAST_DATE'
}

export type FanContentOrganization = {
  __typename?: 'FanContentOrganization';
  /** The abbreviation of the organization. */
  abbreviation?: Maybe<Scalars['String']>;
  /** First address line of the organization. */
  addressLine1?: Maybe<Scalars['String']>;
  /** Second address line of the organization. */
  addressLine2?: Maybe<Scalars['String']>;
  /** The banner image uri of the organization. */
  bannerImageUri?: Maybe<Scalars['String']>;
  /** The city where the organization is located. */
  city?: Maybe<Scalars['String']>;
  /** The country where the organization is located. */
  country?: Maybe<Scalars['String']>;
  /** The full name of the organization. */
  fullName?: Maybe<Scalars['String']>;
  /** The internal id of the organization. */
  internalOrganizationId?: Maybe<Scalars['String']>;
  /** The mascot of the organization. */
  mascot?: Maybe<Scalars['String']>;
  /** Maps to how an organization is classified */
  orgClassificationId: Scalars['Int'];
  /** The fan x profile url of the organization. */
  organizationProfileUrl?: Maybe<Scalars['String']>;
  /** The primary color of the organization. */
  primaryColor?: Maybe<Scalars['String']>;
  /** The uri to the profile image of the organization. */
  profileImageUri?: Maybe<Scalars['String']>;
  /** The secondary color of the organization. */
  secondaryColor?: Maybe<Scalars['String']>;
  /** The short name of the organization. */
  shortName?: Maybe<Scalars['String']>;
  /** The state where the organization is located. */
  state?: Maybe<Scalars['String']>;
  /** The zip code where the organization is located. */
  zipCode?: Maybe<Scalars['String']>;
};

export type FanContentTeamHeader = {
  __typename?: 'FanContentTeamHeader';
  /** Current season year */
  currentSeasonYear?: Maybe<Scalars['Int']>;
  /** The gender of the team */
  gender: Gender;
  /** Internal Id of the team */
  internalTeamId?: Maybe<Scalars['String']>;
  /** The logo of the team */
  logo?: Maybe<Scalars['String']>;
  /** The name of the team */
  name?: Maybe<Scalars['String']>;
  /** The primary color of the team */
  primaryColor?: Maybe<Scalars['String']>;
  /** The sport of the team */
  sport: Sport;
  /** The level of the team */
  teamLevel: TeamLevel;
  /** The fan x profile url of the team. */
  teamProfileUrl?: Maybe<Scalars['String']>;
};

export type FanHighlightReel = {
  __typename?: 'FanHighlightReel';
  /** The creation time of the highlight reel. */
  createdAt: Scalars['DateTime'];
  /** The description of the highlight reel. */
  description?: Maybe<Scalars['String']>;
  /** The duration of the highlight reel. */
  duration?: Maybe<Scalars['String']>;
  /** The duration of the highlight reel in milliseconds, expressed as a number. */
  durationInMs: Scalars['Float'];
  /** The GraphQL encoded highlight reel Id. */
  id: Scalars['ID'];
  /** The internal highlight reel Id. */
  internalId?: Maybe<Scalars['String']>;
  /** Indicates if any of the corresponding teams have their profile hidden. */
  isAnyTeamProfileHidden: Scalars['Boolean'];
  /** Indicates if the highlight reel was auto generated */
  isAutoGen: Scalars['Boolean'];
  /** The internal Id of the highlight reel owner. */
  ownerId?: Maybe<Scalars['String']>;
  /** The type of owner of the highlight reel. */
  ownerType: HighlightReelOwnerType;
  /** The list of teams related to the highlight reel. */
  teamInfo?: Maybe<Array<Maybe<FanHighlightReelTeamInfo>>>;
  /** The thumbnail for the highlight reel. */
  thumbnail?: Maybe<Scalars['String']>;
  /** The title of the highlight reel. */
  title?: Maybe<Scalars['String']>;
  /** The video url for the highlight reel. */
  videoUrl?: Maybe<Scalars['String']>;
  /** The number of views on the highlight reel. */
  views: Scalars['Long'];
};

/** A connection to a list of items. */
export type FanHighlightReelConnection = {
  __typename?: 'FanHighlightReelConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FanHighlightReelEdge>>;
  items?: Maybe<Array<FanHighlightReel>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<FanHighlightReel>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type FanHighlightReelEdge = {
  __typename?: 'FanHighlightReelEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<FanHighlightReel>;
};

export type FanHighlightReelTeamInfo = {
  __typename?: 'FanHighlightReelTeamInfo';
  /** The organization of the highlight reel. */
  organization?: Maybe<FanContentOrganization>;
  /** The GraphQL encoded scheduleEntryIds of the games referenced by the highlight reel. */
  scheduleEntryIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** The team header of the team. */
  teamHeader?: Maybe<FanContentTeamHeader>;
};

export enum FanHighlightSortType {
  CreationDate = 'CREATION_DATE',
  ViewCount = 'VIEW_COUNT'
}

export enum FanLeagueEntityType {
  Competition = 'COMPETITION',
  CompetitionPeriod = 'COMPETITION_PERIOD',
  GoverningBody = 'GOVERNING_BODY',
  Municipality = 'MUNICIPALITY',
  RegionalAlignment = 'REGIONAL_ALIGNMENT',
  Subdivision = 'SUBDIVISION',
  Unknown = 'UNKNOWN'
}

export enum FanLeagueMemberSortType {
  OrganizationName = 'ORGANIZATION_NAME'
}

export enum FanLeagueMemberType {
  Organization = 'ORGANIZATION',
  Unknown = 'UNKNOWN'
}

export type FanScheduleEntry = {
  __typename?: 'FanScheduleEntry';
  /** The status of the schedule entries' corresponding broadcast. */
  broadcastStatus?: Maybe<Scalars['String']>;
  /** The numeric value correlating to the GameType enum from hudl-schedules */
  gameType: Scalars['Int'];
  /** The numeric value correlating to the Gender enum */
  genderId: Scalars['Int'];
  /** The GraphQL encoded fan schedule entry id. */
  id: Scalars['ID'];
  /** The internal schedule entry id. */
  internalScheduleEntryId?: Maybe<Scalars['String']>;
  /** Determines if the team that owns the schedule entry is hidden */
  isTeamProfileHidden: Scalars['Boolean'];
  /** The location of the schedule entry. Corresponds to the ScheduleEntryLocation enum. */
  location: Scalars['Int'];
  /** The opponent name. */
  opponentName?: Maybe<Scalars['String']>;
  /** The opponent organization. */
  opponentOrganization?: Maybe<FanContentOrganization>;
  /** The GraphQL encoded opponent school id. */
  opponentSchoolId?: Maybe<Scalars['ID']>;
  /** The opponent team header. */
  opponentTeamHeader?: Maybe<FanContentTeamHeader>;
  /** The GraphQL encoded opponent team id. */
  opponentTeamId?: Maybe<Scalars['ID']>;
  /** The owning organization of the schedule entry. */
  organization?: Maybe<FanContentOrganization>;
  /** The outcome of the schedule entry. Corresponds to the ScheduleEntryOutcome enum. */
  outcome: Scalars['Int'];
  /** The projected end time of the schedule entry. */
  projectedEndTime?: Maybe<Scalars['DateTime']>;
  /** The GraphQL encoded schedule entry id. */
  scheduleEntryId: Scalars['ID'];
  /** The GraphQL encoded id of the school that owns the schedule entry */
  schoolId: Scalars['ID'];
  /** One of the team scores for the schedule entry. Typically correlates to the owning team score. */
  score1?: Maybe<Scalars['Short']>;
  /** One of the team scores for the schedule entry. Typically correlates to the opponent team score. */
  score2?: Maybe<Scalars['Short']>;
  /** The GraphQL encoded season id of the schedule entry. */
  seasonId: Scalars['ID'];
  /** The sport id of the schedule entry. */
  sportId: Scalars['Int'];
  /** The team header of the team that owns the schedule entry. */
  teamHeader?: Maybe<FanContentTeamHeader>;
  /** The GraphQL encoded team id of the team that owns the schedule entry. */
  teamId: Scalars['ID'];
  /** The GraphQL encoded id of the ticketed event */
  ticketedEventId?: Maybe<Scalars['ID']>;
  /** The event status of the ticketed event */
  ticketedEventStatus?: Maybe<Scalars['String']>;
  /** The time in UTC that the schedule entry starts. */
  timeUtc: Scalars['DateTime'];
};

/** A connection to a list of items. */
export type FanScheduleEntryConnection = {
  __typename?: 'FanScheduleEntryConnection';
  /** A list of edges. */
  edges?: Maybe<Array<FanScheduleEntryEdge>>;
  items?: Maybe<Array<FanScheduleEntry>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<FanScheduleEntry>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type FanScheduleEntryEdge = {
  __typename?: 'FanScheduleEntryEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<FanScheduleEntry>;
};

export enum FanScheduleEntrySortType {
  ScheduleEntryDate = 'SCHEDULE_ENTRY_DATE'
}

export type FanSearchAthlete = {
  __typename?: 'FanSearchAthlete';
  /** The abbreviated name of the athlete. */
  abbreviation?: Maybe<Scalars['String']>;
  /** The city of their primary team. */
  city?: Maybe<Scalars['String']>;
  /** The country of their primary team. */
  country?: Maybe<Scalars['String']>;
  /** The profile image uri of the athlete. */
  iconUri?: Maybe<Scalars['String']>;
  /** The GraphQL Id of the athlete. */
  id: Scalars['ID'];
  /** The internal Id of the athlete. */
  internalId?: Maybe<Scalars['String']>;
  /** The jersey number on their primary team. */
  jersey?: Maybe<Scalars['Int']>;
  /** The full name of the athlete. */
  name?: Maybe<Scalars['String']>;
  /** A list of the org types associated with the team that the athlete belongs to. */
  organizationClassificationIds?: Maybe<Array<Maybe<Scalars['Int']>>>;
  /** The main position on their primary team. */
  position?: Maybe<Scalars['String']>;
  /** The primary color of their primary team. */
  primaryColor?: Maybe<Scalars['String']>;
  /** The full name of the athletes primary organization. */
  primaryOrganizationName?: Maybe<Scalars['String']>;
  /** The Id of the athletes primary team. */
  primaryTeamId: Scalars['ID'];
  /** The secondary color of their primary team. */
  secondaryColor?: Maybe<Scalars['String']>;
  /** The Id of the sport of the primary team of the athlete. */
  sportId: Scalars['Int'];
  /** The state of their primary team. */
  state?: Maybe<Scalars['String']>;
};

export type FanSearchLeague = {
  __typename?: 'FanSearchLeague';
  /** The abbreviated name of the league. */
  abbreviation?: Maybe<Scalars['String']>;
  /** The city of the league. */
  city?: Maybe<Scalars['String']>;
  /** The country of the league. */
  country?: Maybe<Scalars['String']>;
  /** The profile picture of the league. */
  iconUri?: Maybe<Scalars['String']>;
  /** The GraphQL Id of the league. */
  id: Scalars['ID'];
  /** The internal Id of the league. */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the league. */
  name?: Maybe<Scalars['String']>;
  /** The primary color of the league. */
  primaryColor?: Maybe<Scalars['String']>;
  /** The secondary color of the league. */
  secondaryColor?: Maybe<Scalars['String']>;
  /** The subdivision of the league. */
  subdivision?: Maybe<Scalars['String']>;
};

export type FanSearchOrganization = {
  __typename?: 'FanSearchOrganization';
  /** The abbreviated name of the organization. */
  abbreviation?: Maybe<Scalars['String']>;
  /** The city of the organization. */
  city?: Maybe<Scalars['String']>;
  /** The country of the organization. */
  country?: Maybe<Scalars['String']>;
  /** The profile picture of the organization. */
  iconUri?: Maybe<Scalars['String']>;
  /** The GraphQL Id of the organization. */
  id: Scalars['ID'];
  /** The internal Id of the organization. */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the organization. */
  name?: Maybe<Scalars['String']>;
  /** The list of classifcation Ids of the organization. */
  organizationClassificationId: Scalars['Int'];
  /** The primary color of the organization. */
  primaryColor?: Maybe<Scalars['String']>;
  /** The secondary color of the organization. */
  secondaryColor?: Maybe<Scalars['String']>;
  /** The state of the organization. */
  state?: Maybe<Scalars['String']>;
};

export type FanSearchRequestInput = {
  /** The OrganizationClassificationIds to filter by. */
  organizationClassificationIds: Array<Scalars['Int']>;
  /** The number of results to return for each type. Used for pagination. */
  resultSize: Scalars['Int'];
  /** The 0-based index to start from when getting search results. Used for pagination. */
  resultStartIndex: Scalars['Int'];
  /** The term or terms to search by. */
  searchTerm: Scalars['String'];
  /** The entity types you want to return. */
  types: Array<SearchItemType>;
};

export type FanSearchResult = {
  __typename?: 'FanSearchResult';
  /** The result for an athlete. Will be populated when the Type equals 'Athlete' */
  athlete?: Maybe<FanSearchAthlete>;
  /** The result for a club. Will be populated when the Type equals 'Club'. You can also retrieve clubs from the organization type */
  club?: Maybe<FanSearchOrganization>;
  /** The result for a league. Will be populated when the Type equals 'Municipality', 'RegionalAlignment' or 'GoverningBody' */
  league?: Maybe<FanSearchLeague>;
  /** The result for an organization. Will be populated when the Type equals 'Organization' */
  organization?: Maybe<FanSearchOrganization>;
  /** The entity type that will be returned. */
  type: SearchItemResultType;
};

export enum FeeResponsibility {
  Customer = 'CUSTOMER',
  Organization = 'ORGANIZATION',
  Unset = 'UNSET'
}

export enum FeedUserType {
  Hudl = 'HUDL',
  Team = 'TEAM',
  Unknown = 'UNKNOWN',
  User = 'USER'
}

/** A field on an event or pass to be filled out by the user. */
export type FormField = {
  __typename?: 'FormField';
  /** The time the Form Field was created. */
  createdAt: Scalars['DateTime'];
  /** The time the Form Field was deleted, if it was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The type of the Form Field. */
  fieldType: Scalars['String'];
  /** The help text for the Form Field. */
  helpText?: Maybe<Scalars['String']>;
  /** The ID of the Form Field. */
  id: Scalars['ID'];
  /** If the Form Field is a required field. */
  isRequired: Scalars['Boolean'];
  /** The label for the Form Field. */
  label: Scalars['String'];
  /** The unique identifier for the Organization that the Form Field belongs to. */
  organizationId: Scalars['ID'];
  /** The time the Form Field was last updated. */
  updatedAt: Scalars['DateTime'];
};

/** A field on an event or pass to be filled out by the user. */
export type FormFieldResponse = {
  __typename?: 'FormFieldResponse';
  /** The unique identifier for the Form Field this response is for. */
  formFieldId: Scalars['ID'];
  /** The response to the Form Field. */
  response: Array<Scalars['String']>;
};

export type FormFieldResponseInput = {
  /** The ID of the form field. */
  formFieldId: Scalars['ID'];
  /** Response to the form field. */
  response?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

/** Representation of a form reference. */
export type FormRef = {
  __typename?: 'FormRef';
  /** The display order of the form reference. */
  displayOrder: Scalars['Int'];
  /** The form ID. */
  formId: Scalars['ID'];
  /** Whether the form reference is enabled. */
  isEnabled: Scalars['Boolean'];
};

export enum Gender {
  Coed = 'COED',
  Mens = 'MENS',
  Womens = 'WOMENS'
}

/** Representation of a generic Scoreboard Entry. */
export type GenericScoreboardEntry = {
  __typename?: 'GenericScoreboardEntry';
  /** The current game clock in seconds in the generic scoreboard entry. */
  gameClock: Scalars['Int'];
  /** The GraphQL id of the generic scoreboard entry. */
  id: Scalars['ID'];
  /** The current period in the generic scoreboard entry. */
  period?: Maybe<Scalars['String']>;
  /** The id of the scoreboard session the generic scoreboard entry belongs to. */
  scoreboardSessionId: Scalars['String'];
  /** Team 1's current score in the generic scoreboard entry. */
  team1Score: Scalars['Int'];
  /** Team 2's current score in the generic scoreboard entry. */
  team2Score: Scalars['Int'];
  /** The UTC timestamp of the generic scoreboard entry. */
  timeUtc: Scalars['DateTime'];
};

/** Representation of a generic Scoreboard Settings object. */
export type GenericScoreboardSettings = {
  __typename?: 'GenericScoreboardSettings';
  /** Whether or not the game clock is displayed on the scoreboard overlay. */
  gameClockOn: Scalars['Boolean'];
  /** Whether or not the team names are displayed on the scoreboard overlay. */
  namesOn: Scalars['Boolean'];
  /** Whether or not the scoreboard overlay should be displayed on the video. */
  overlayOn: Scalars['Boolean'];
  /** Whether or not the period is displayed on the scoreboard overlay. */
  periodOn: Scalars['Boolean'];
  /** Whether or not the scores are displayed on the scoreboard overlay. */
  scoresOn: Scalars['Boolean'];
  /** Team 1's name that is displayed on the scoreboard overlay. */
  team1Name: Scalars['String'];
  /** Team 1's primary color that is displayed on the scoreboard overlay. */
  team1PrimaryColor: Scalars['String'];
  /** Team 2's name that is displayed on the scoreboard overlay. */
  team2Name: Scalars['String'];
  /** Team 2's primary color that is displayed on the scoreboard overlay. */
  team2PrimaryColor: Scalars['String'];
  /** The UTC timestamp of the scoreboard settings. */
  timeUtc: Scalars['DateTime'];
};

/** Input to retrieve ad context information. */
export type GetAdTargetingInput = {
  /** IP Address for user to get ad context for. */
  ip: Scalars['String'];
};

/** Input to retrieve all ticket groups associated with a ticketed event */
export type GetAssociatedTicketGroupsWithAccessCodeInput = {
  /** The access code associated with the organization */
  accessCode: Scalars['String'];
  /** Value used to start a page of ticket groups. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of ticket groups. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Number of ticket groups requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Number of ticket groups requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** GraphQL encoded Id of the organization the access code associated with */
  organizationId: Scalars['ID'];
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** The sort type to use for the ticket groups. */
  sortType: TicketGroupSortType;
  /** GraphQL encoded Id of the ticketed event to retrieve ticket groups for */
  ticketedEventId: Scalars['ID'];
  /** A list of source types to filter the ticket groups by. */
  validSources?: InputMaybe<Array<Scalars['String']>>;
};

/** Input to get guardian relationships and athlete info. */
export type GetAthleteAndGuardianRelationshipsInput = {
  /** The user ID used to retrieve guardian relationships and athlete info. */
  userId: Scalars['ID'];
};

/** Representation of an input to fetch broadcasts. */
export type GetBroadcastsPaginatedInput = {
  /** Value used to start a page of broadcasts. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of broadcasts. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Used to further filter broadcasts by status. */
  broadcastStatusFilter: BroadcastFilter;
  /** If set, will return broadcasts that happened before the requested date. Can be used with StartDate to create a date range. */
  endDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of broadcasts requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** If true, will include broadcasts that are hidden */
  includeHiddenBroadcasts?: InputMaybe<Scalars['Boolean']>;
  /** If true, will include broadcasts from hidden teams */
  includeHiddenTeams?: InputMaybe<Scalars['Boolean']>;
  /** If true, will include broadcasts that are unavailable */
  includeUnavailableBroadcasts?: InputMaybe<Scalars['Boolean']>;
  /** Number of broadcasts requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** Filters broadcasts by GraphQL encoded scheduleEntryIds. */
  scheduleEntryIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded schoolIds. */
  schoolIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by vCloud siteIds. */
  siteIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting broadcasts. */
  sortType: BroadcastSortType;
  /** If set, will return broadcasts that happened after the requested date. Can be used with EndDate to create a date range. */
  startDate?: InputMaybe<Scalars['DateTime']>;
  /** Filters broadcasts by GraphQL encoded teamIds. */
  teamIds?: InputMaybe<Array<Scalars['ID']>>;
};

export type GetCanonicalUrlForVanityUrlInput = {
  /** The slug of the vanity url. The slug is the last, unique tail segment of the url. Optional if vanityUrl is provided. */
  vanitySlug?: InputMaybe<Scalars['String']>;
  /** The vanity url. The slug is assumed to be the last unique tail segment of the url. Optional if vanitySlug is provided. */
  vanityUrl?: InputMaybe<Scalars['String']>;
};

/** Representation of an input to fetch discoverable league pass configs linked to a league. */
export type GetDiscoverableLeaguePassConfigsByLeagueInput = {
  /** The ID of the league associated with the pass configs. */
  leagueEntityId: Scalars['ID'];
  /** The type of the league associated with the pass configs. */
  leagueEntityType: Scalars['String'];
};

/** Input to fetch paginated broadcasts. */
export type GetFanBroadcastsPaginatedInput = {
  /** Value used to start a page of broadcasts. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of broadcasts. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Used to further filter broadcasts by status. */
  broadcastStatusFilter: FanBroadcastFilter;
  /** Filters broadcasts by GraphQL encoded competitionIds. */
  competitionIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded competitionPeriodIds. */
  competitionPeriodIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Return broadcasts that occur before specified date. Can be used in tandem with StartDate to create a date range. */
  endDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of broadcasts requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Filters broadcasts by GraphQL encoded governingBodyIds. */
  governingBodyIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will include hidden broadcasts. */
  includeHiddenBroadcasts?: InputMaybe<Scalars['Boolean']>;
  /** If true, will include broadcast's from hidden teams. */
  includeHiddenTeams?: InputMaybe<Scalars['Boolean']>;
  /** If true, will include unavailable broadcasts. */
  includeUnavailableBroadcasts?: InputMaybe<Scalars['Boolean']>;
  /** Number of broadcasts requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** Filters broadcasts by GraphQL encoded municipalityIds. */
  municipalityIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded opponentOrganizationIds. */
  opponentOrganizationIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded regionalAlignmentIds. */
  regionalAlignmentIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded scheduleEntryIds. */
  scheduleEntryIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded schoolIds. */
  schoolIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by vCloud siteIds. */
  siteIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting broadcasts. */
  sortType: FanBroadcastSortType;
  /** Return broadcasts that occur after specified date. Can be used in tandem with EndDate to create a date range. */
  startDate?: InputMaybe<Scalars['DateTime']>;
  /** Filters broadcasts by GraphQL encoded subdivisionIds. */
  subdivisionIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters broadcasts by GraphQL encoded teamIds. */
  teamIds?: InputMaybe<Array<Scalars['ID']>>;
};

/** Input to fetch paginated highlight reels. */
export type GetFanHighlightReelsPaginatedInput = {
  /** Value used to start a page of highlight reels. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Filters highlight reels by GraphQL encoded athleteUserIds mapped to OwnerId with OwnerType = User. */
  athleteUserIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Value used to end a page of highlight reels. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Filters highlight reels by GraphQL encoded competitionIds. */
  competitionIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters highlight reels by GraphQL encoded competitionPeriodIds. */
  competitionPeriodIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Return highlight reels that occur before specified date. Can be used in tandem with StartDate to create a date range. */
  endDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of highlight reels requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Filters highlight reels by GraphQL encoded governingBodyIds. */
  governingBodyIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will include highlight reels associated with any teams that have their profiles hidden. */
  includeHiddenTeams: Scalars['Boolean'];
  /** Number of highlight reels requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** Filters highlight reels by GraphQL encoded municipalityIds. */
  municipalityIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters highlight reels by GraphQL encoded regionalAlignmentIds. */
  regionalAlignmentIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters highlight reels by GraphQL encoded scheduleEntryIds. */
  scheduleEntryIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting highlight reels. */
  sortType: FanHighlightSortType;
  /** Return highlight reels that occur after specified date. Can be used in tandem with EndDate to create a date range. */
  startDate?: InputMaybe<Scalars['DateTime']>;
  /** Filters highlight reels by GraphQL encoded subdivisionIds. */
  subdivisionIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters highlight reels by GraphQL encoded teamIds mapped to OwnerId with OwnerType = Team. */
  teamIds?: InputMaybe<Array<Scalars['ID']>>;
};

/** Input to fetch paginated schedule entries. */
export type GetFanScheduleEntriesPaginatedInput = {
  /** Value used to start a page of schedule entries. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of schedule entries. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Filters schedule entries by GraphQL encoded competitionIds. */
  competitionIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entries by GraphQL encoded competitionPeriodIds. */
  competitionPeriodIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Return schedule entries that occur before specified date. Can be used in tandem with FilterStartDate to create a date range. */
  filterEndDate?: InputMaybe<Scalars['DateTime']>;
  /** Return schedule entries that occur after specified date. Can be used in tandem with FilterEndDate to create a date range. */
  filterStartDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of schedule entries requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entries by GenderId. */
  genderId?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entries by GraphQL encoded governingBodyIds. */
  governingBodyIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will include schedule entries belonging to hidden teams. */
  includeHiddenTeams: Scalars['Boolean'];
  /** If true, will include in progress schedule entries. */
  includeInProgressScheduleEntries: Scalars['Boolean'];
  /** If true, will include schedule entries without reported scores. */
  includeUnreportedScores?: InputMaybe<Scalars['Boolean']>;
  /** Number of schedule entries requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entries by ScheduleEntryLocation. */
  location?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entries by GraphQL encoded municipalityIds. */
  municipalityIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entries by ScheduleEntryOutcome. */
  outcome?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entries by GraphQL encoded regionalAlignmentIds. */
  regionalAlignmentIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entries by GraphQL encoded scheduleEntryIds. */
  scheduleEntryIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entries by GraphQL encoded schoolIds. */
  schoolIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entries by GraphQL encoded seasonIds. */
  seasonIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting schedule entries. */
  sortType: FanScheduleEntrySortType;
  /** Filters schedule entries by SportId. */
  sportId?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entries by GraphQL encoded subdivisionIds. */
  subdivisionIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entries by GraphQL encoded teamIds. */
  teamIds?: InputMaybe<Array<Scalars['ID']>>;
};

/** Representation of an input to fetch highlight reel summaries. */
export type GetHighlightReelSummariesInput = {
  /** Cursor used to start a page of summaries. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Filters highlights by athleteUserIds. */
  athleteUserIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Cursor used to end a page of summaries. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** If true, will exclude highlights that are private and owned by a team. */
  excludePrivateTeamHighlights?: InputMaybe<Scalars['Boolean']>;
  /** Number of summaries requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Filters highlights by deletion status. */
  includeDeleted: Scalars['Boolean'];
  /** Filters highlights by publish status. */
  includeDrafts: Scalars['Boolean'];
  /** If true, will include highlight's from hidden teams */
  includeHiddenTeams?: InputMaybe<Scalars['Boolean']>;
  /** Number of summaries requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** If set, this will bypass all privacy checks. */
  overridePrivacy: Scalars['Boolean'];
  /** Filters highlights by scheduleEntryIds. Maps to gameId property. */
  scheduleEntryIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters highlights by schoolIds. */
  schoolIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting highlights. */
  sortType: HighlightSortType;
  /** Filters highlights by teamIds. */
  teamIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Controls use of the primary database node. This should only be set on operations that critically require the most up to date data. */
  usePrimary: Scalars['Boolean'];
};

/** Representation of an input to fetch highlight reel summaries. */
export type GetHighlightReelSummaryInput = {
  /** Filters highlights by deletion status. */
  includeDeleted: Scalars['Boolean'];
  /** Filters highlights by publish status. */
  includeDrafts: Scalars['Boolean'];
  /** The legacy ReelId */
  legacyReelId?: InputMaybe<Scalars['String']>;
  /** If set, this will bypass all privacy checks. */
  overridePrivacy: Scalars['Boolean'];
  /** Filters highlights by the owner's internal id. Should be used in tandem with OwnerType. */
  ownerId?: InputMaybe<Scalars['String']>;
  /** Filters highlights by the type of owner, usually team or athlete. */
  ownerType: HighlightOwnerType;
  /** The GraphQL encoded Id ReelId */
  reelId: Scalars['ID'];
  /** Controls use of the primary database node. This should only be set on operations that critically require the most up to date data. */
  usePrimaryReadPreference: Scalars['Boolean'];
};

/** Representation of an input to fetch league pass configs linked to a league. */
export type GetLeaguePassConfigsByLeagueInput = {
  /** Value used to start a page of league pass configs. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of league pass configs. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Filters for pass configs with a EndDate property before this date. */
  endDateMax?: InputMaybe<Scalars['DateTime']>;
  /** Filters for pass configs with a EndDate property after this date. */
  endDateMin?: InputMaybe<Scalars['DateTime']>;
  /** Number of league pass configs requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Number of league pass configs requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** The ID of the league associated with the pass configs. */
  leagueEntityId: Scalars['ID'];
  /** The type of the league associated with the pass configs. */
  leagueEntityType: Scalars['String'];
  /** Pass config statuses to filter by. */
  leaguePassConfigStatuses?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting summaries. */
  sortType: PassConfigSortType;
  /** Filters for pass configs with a StartDate property before this date. */
  startDateMax?: InputMaybe<Scalars['DateTime']>;
  /** Filters for pass configs with a StartDate property after this date. */
  startDateMin?: InputMaybe<Scalars['DateTime']>;
  /** Pass config visibilities to filter by, such as public or private passes. */
  visibilities?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type GetMembersForLeagueInput = {
  /** Value used to start a page of members. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of members. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** The internal id of the entity to get members for. */
  entityId: Scalars['String'];
  /** The type of entity to get members for. */
  entityType: FanLeagueEntityType;
  /** Number of members requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Number of members requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** The type of member to get. E.g. Organization */
  memberType: FanLeagueMemberType;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting members. */
  sortType: FanLeagueMemberSortType;
};

/** Representation of an input to fetch pass configs linked to an organization ID. */
export type GetPassConfigsByOrganizationIdInput = {
  /** Value used to start a page of pass configs. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of pass configs. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Filters for pass configs with a EndDate property before this date. */
  endDateMax?: InputMaybe<Scalars['DateTime']>;
  /** Filters for pass configs with a EndDate property after this date. */
  endDateMin?: InputMaybe<Scalars['DateTime']>;
  /** Pass config exclusions to filter by, such as filtering out passes associated with a league pass. */
  exclusions?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Number of pass configs requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Number of pass configs requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** The ID of the organization associated with the pass configs. */
  organizationId: Scalars['ID'];
  /** Pass config statuses to filter by. */
  passConfigStatuses?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting summaries. */
  sortType: PassConfigSortType;
  /** Filters for pass configs with a StartDate property before this date. */
  startDateMax?: InputMaybe<Scalars['DateTime']>;
  /** Filters for pass configs with a StartDate property after this date. */
  startDateMin?: InputMaybe<Scalars['DateTime']>;
  /** Pass config visibilities to filter by, such as public or private passes. */
  visibilities?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

export type GetRosterForTeamInput = {
  /** Filters Roster by GraphQL encoded seasonId. */
  seasonId: Scalars['ID'];
  /** Filters Roster by GraphQL encoded teamId. */
  teamId: Scalars['ID'];
};

/** Representation of an input to fetch schedule entry summaries. */
export type GetScheduleEntryPublicSummariesInput = {
  /** Value used to start a page of summaries. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of summaries. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Date used to end a filter time span. */
  filterEndDate?: InputMaybe<Scalars['DateTime']>;
  /** Date used to start a filter time span. */
  filterStartDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of summaries requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entry summaries by gender. */
  genderId?: InputMaybe<Scalars['Int']>;
  /** If true, will include schedule entry summaries of hidden teams */
  includeHiddenTeams?: InputMaybe<Scalars['Boolean']>;
  /** If true, will include in progress schedule entry summaries */
  includeInProgressScheduleEntrySummaries?: InputMaybe<Scalars['Boolean']>;
  /** Filters schedule entry summaries by internal teamIds. This will take precedence over TeamIds. */
  internalTeamIds?: InputMaybe<Array<Scalars['String']>>;
  /** Number of summaries requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entry summaries by location. */
  location?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entry summaries by outcome. */
  outcome?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entry summaries by GraphQL encoded schedule entry Id. */
  scheduleEntryIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entry summaries by GraphQL encoded schoolId. */
  schoolIds?: InputMaybe<Array<Scalars['ID']>>;
  /** Filters schedule entry summaries by GraphQL encoded seasonIds. */
  seasonIds?: InputMaybe<Array<Scalars['ID']>>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting summaries. */
  sortType: ScheduleEntrySortType;
  /** Filters schedule entry summaries by sport. */
  sportId?: InputMaybe<Scalars['Int']>;
  /** Filters schedule entry summaries by GraphQL encoded teamIds. */
  teamIds?: InputMaybe<Array<Scalars['ID']>>;
};

/** Representation of an input to fetch schools for a competition period. */
export type GetSchoolsForCompetitionPeriodByTeamIdsPaginatedInput = {
  /** Team Id value used to start a page of schools. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** GraphQL encoded competition period Id to filter schools by. */
  competitionPeriodId: Scalars['ID'];
  /** Number of teams to fetch schools for. Note that the number of schools returned may be less than this value if there are duplicates. Maximum is 100 */
  first: Scalars['Int'];
};

/** Representation of an input to fetch schools for a municipality. */
export type GetSchoolsForMunicipalityPaginatedInput = {
  /** Value used to start a page of schools. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Number of schools requested in a single page. Maximum is 100 */
  first: Scalars['Int'];
  /** GraphQL encoded municipalityId to filter schools by. */
  municipalityId: Scalars['ID'];
};

/** Representation of an input to fetch schools for a Regional Alignment. */
export type GetSchoolsForRegionalAlignmentByTeamIdsPaginatedInput = {
  /** Team Id value used to start a page of schools. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Number of teams to fetch schools for. Note that the number of schools returned may be less than this value if there are duplicates. Maximum is 100 */
  first: Scalars['Int'];
  /** GraphQL encoded regional alignment Id to filter schools by. */
  regionalAlignmentId: Scalars['ID'];
};

/** Representation of an input to fetch team headers. */
export type GetTeamHeadersByIdsInput = {
  /** Filters teams by teamIds */
  teamIds: Array<InputMaybe<Scalars['String']>>;
};

/** Representation of an input to fetch teams for a competition period. */
export type GetTeamHeadersForCompetitionPeriodPaginatedInput = {
  /** Value used to start a page of schools. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** GraphQL encoded competition period Id to filter teams by. */
  competitionPeriodId: Scalars['ID'];
  /** Number of teams requested in a single page. Maximum is 150. */
  first: Scalars['Int'];
};

/** Representation of an input to fetch team headers. */
export type GetTeamHeadersForSchoolInput = {
  /** If true, will include hidden teams */
  includeHiddenTeams?: InputMaybe<Scalars['Boolean']>;
  /** Filters teams by schoolId. */
  schoolId: Scalars['ID'];
  /** Filters teams by sport. */
  sports?: InputMaybe<Array<Sport>>;
  /** Filters teams by team level. */
  teamLevels?: InputMaybe<Array<TeamLevel>>;
};

/** Representation of an input to fetch an access code linked to an organization. */
export type GetTicketedEventAccessCodeInput = {
  /** Access Code associated with an organization */
  accessCode?: InputMaybe<Scalars['String']>;
};

/** Representation of an input to fetch ticketed events linked to an organization ID. */
export type GetTicketedEventsByOrganizationIdInput = {
  /** Value used to start a page of ticketed events. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of ticketed events. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Filters for Ticketed Events with a Date property before this date. */
  endDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of ticketed events requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** If true, will return results that have been deleted. */
  includeDeleted?: InputMaybe<Scalars['Boolean']>;
  /** Number of ticketed events requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** The ID of the organization associated with the ticketed event. */
  organizationId: Scalars['ID'];
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting summaries. */
  sortType: TicketedEventSortType;
  /** Filters for Ticketed Events with a Date property after this date. */
  startDate?: InputMaybe<Scalars['DateTime']>;
  /** Event statuses to filter by. */
  ticketedEventStatuses?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Event visibilities to filter by, such as public, private, or not for sale events. */
  visibilities?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

/** Representation of an input to fetch ticketed events linked to a Pass Config ID. */
export type GetTicketedEventsByPassConfigIdInput = {
  /** Value used to start a page of ticketed events. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of ticketed events. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Number of ticketed events requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** If true, will return results that have been deleted. */
  includeDeleted?: InputMaybe<Scalars['Boolean']>;
  /** Number of ticketed events requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** The ID of the pass config associated with the ticketed event. */
  passConfigId: Scalars['ID'];
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting summaries. */
  sortType: TicketedEventSortType;
};

/** Representation of an input to fetch upcoming ticketed events. */
export type GetTicketedEventsForTeamInput = {
  /** Value used to start a page of ticketedEvents. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to start a page of ticketedEvents. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Number of ticketed events requested in a single page. */
  first?: InputMaybe<Scalars['Int']>;
  /** Number of ticketed events requested in a single page. */
  last?: InputMaybe<Scalars['Int']>;
  /** Id of the team */
  teamId: Scalars['String'];
};

export type GoverningBodyHeader = {
  __typename?: 'GoverningBodyHeader';
  /** The abbreviation of the Governing Body */
  abbreviation?: Maybe<Scalars['String']>;
  /** True if the user can edit the governing body. */
  canUserEditLeagueEntity: Scalars['Boolean'];
  /** All competitions for a Governing Body, including those that aren't connected to subdivisions. */
  competitions?: Maybe<Array<Maybe<CompetitionHeader>>>;
  /** The graphQL Id of the Governing Body */
  id: Scalars['ID'];
  /** The internal Id of the Governing Body */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the Governing Body */
  name?: Maybe<Scalars['String']>;
  /** The organization branding for the public governing body. */
  organizationBranding?: Maybe<OrganizationBranding>;
  /** The list of a regions for the Governing Body */
  regions?: Maybe<Array<Maybe<Region>>>;
};

export enum Grade {
  Eighth = 'EIGHTH',
  Eleventh = 'ELEVENTH',
  Fifth = 'FIFTH',
  First = 'FIRST',
  Fourth = 'FOURTH',
  Kindergarten = 'KINDERGARTEN',
  Ninth = 'NINTH',
  PreK = 'PRE_K',
  Second = 'SECOND',
  Seventh = 'SEVENTH',
  Sixth = 'SIXTH',
  Tenth = 'TENTH',
  Third = 'THIRD',
  Twelfth = 'TWELFTH'
}

/** Representation of a relationship between two entities. */
export type GuardianMetadata = {
  __typename?: 'GuardianMetadata';
  /** The id of the user that updated the status of this metadata. */
  approverUserId?: Maybe<Scalars['String']>;
  /** The id of the organization reference. */
  organizationId: Scalars['String'];
  /** The status the approving user set this metadata to. */
  status: GuardianStatusType;
  /** The list of teams Ids that the guardian has access to via their athlete. */
  teamIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The date and time this metadata was last updated. */
  updatedAt?: Maybe<Scalars['DateTime']>;
};

/** Representation of a guardian relationship between two users. */
export type GuardianRelationship = {
  __typename?: 'GuardianRelationship';
  /** The date and time this relationship was created. */
  createdAt?: Maybe<Scalars['DateTime']>;
  /** The date and time this relationship was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** Indicates the level of guardian access to the athlete and their teams. */
  guardianshipLevel?: Maybe<GuardianshipLevel>;
  /** The GraphQL ID of the relationship. */
  id: Scalars['ID'];
  /** The list of metadata informing which organizations the guardian has access to and the status of that access. */
  metadata?: Maybe<Array<Maybe<GuardianMetadata>>>;
  /** The primary entity of the relationship's Id. */
  primaryEntityId: Scalars['ID'];
  /** The type of entity that PrimaryEntityId refers to. */
  primaryEntityType: EntityType;
  /** The related entity of the relationship's Id. */
  relatedEntityId: Scalars['ID'];
  /** The type of entity that RelatedEntityId refers to. */
  relatedEntityType: EntityType;
  /** The type of relationship. */
  relationshipType: RelationshipType;
  /** The date and time this relationship was last updated. */
  updatedAt?: Maybe<Scalars['DateTime']>;
};

export enum GuardianStatusType {
  Approved = 'APPROVED',
  Denied = 'DENIED',
  Pending = 'PENDING'
}

export enum GuardianshipLevel {
  AthleteGuardianAccess = 'ATHLETE_GUARDIAN_ACCESS'
}

export enum HighlightOwnerType {
  HudlAccount = 'HUDL_ACCOUNT',
  Team = 'TEAM',
  Unknown = 'UNKNOWN',
  User = 'USER'
}

export type HighlightReelGameInfo = {
  __typename?: 'HighlightReelGameInfo';
  /** The gameId (scheduleEntryId) of the game referenced by the highlight reel. */
  gameId?: Maybe<Scalars['String']>;
  /** The GraphQL encoded scheduleEntryId of the game referenced by the highlight reel. */
  scheduleEntryId?: Maybe<Scalars['ID']>;
  /** The seasonId of the game referenced by the highlight reel. */
  seasonId?: Maybe<Scalars['String']>;
  /** The season label of the game referenced by the highlight reel. E.g. 2020-2021 where the first year is the SeasonYear property. */
  seasonLabel?: Maybe<Scalars['String']>;
  /** The season year of the game referenced by the highlight reel. */
  seasonYear: Scalars['Short'];
};

export enum HighlightReelOwnerType {
  HudlAccount = 'HUDL_ACCOUNT',
  Team = 'TEAM',
  Unknown = 'UNKNOWN',
  User = 'USER'
}

export type HighlightReelSummary = {
  __typename?: 'HighlightReelSummary';
  /** Time the highlight reel was created. */
  createdAt: Scalars['DateTime'];
  /** The description for the highlight reel. */
  description?: Maybe<Scalars['String']>;
  /** Duration of the highlight. */
  duration?: Maybe<Scalars['TimeSpan']>;
  /** Convenience property to get Duration expressed as a number instead of a TimeSpan string */
  durationInMs: Scalars['Float'];
  /** The GraphQL id of the highlight reel summary. */
  id: Scalars['ID'];
  /** The internal id of the highlight reel summary. */
  internalId?: Maybe<Scalars['String']>;
  /** Whether or not the highlight was auto generated */
  isAutoGen: Scalars['Boolean'];
  /** The internal primary key of the owner of the highlight. */
  ownerId?: Maybe<Scalars['String']>;
  /** The type of owner of the highlight. */
  ownerType: HighlightOwnerType;
  /** List of the teams related the highlight reel. */
  teamInfo?: Maybe<Array<Maybe<HighlightReelTeamInfo>>>;
  /** The thumbnail for the highlight. */
  thumbnail?: Maybe<Scalars['String']>;
  /** The title of the highlight. */
  title?: Maybe<Scalars['String']>;
  /** User tags for highlight. */
  userTags?: Maybe<Array<Maybe<TaggedFeedUser>>>;
  /** The video url for the highlight. */
  videoUrl?: Maybe<Scalars['String']>;
  /** The number of views on the highlight. */
  views: Scalars['Long'];
};

/** A connection to a list of items. */
export type HighlightReelSummaryConnection = {
  __typename?: 'HighlightReelSummaryConnection';
  /** A list of edges. */
  edges?: Maybe<Array<HighlightReelSummaryEdge>>;
  items?: Maybe<Array<HighlightReelSummary>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<HighlightReelSummary>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type HighlightReelSummaryEdge = {
  __typename?: 'HighlightReelSummaryEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<HighlightReelSummary>;
};

export type HighlightReelTeamInfo = {
  __typename?: 'HighlightReelTeamInfo';
  /** The list of games for the team referenced by the highlight. */
  games?: Maybe<Array<Maybe<HighlightReelGameInfo>>>;
  /** The internal teamId of the team referenced by the highlight. */
  internalTeamId?: Maybe<Scalars['String']>;
  /** The sportId of the team referenced by the highlight. */
  sportId: Scalars['Int'];
  /** The GraphQL encoded teamId of the team referenced by the highlight. */
  teamId: Scalars['ID'];
};

export enum HighlightSortType {
  HighlightCreationDate = 'HIGHLIGHT_CREATION_DATE'
}

/** A connection to a list of items. */
export type IdConnection = {
  __typename?: 'IDConnection';
  /** A list of edges. */
  edges?: Maybe<Array<IdEdge>>;
  items?: Maybe<Array<Scalars['ID']>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type IdEdge = {
  __typename?: 'IDEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<Scalars['ID']>;
};

export enum ImageSize {
  Full = 'FULL',
  Normal = 'NORMAL',
  Retina = 'RETINA',
  Thumbnail = 'THUMBNAIL'
}

/** Representation of an input to log an impression of community content. */
export type ImpressionCommunityContentInput = {
  /** The Turn ID or mobile device ID. */
  adTrackingId?: InputMaybe<Scalars['String']>;
  /** Additional properties to log with the impression. Ex: pageVisibility, sessionId, usePostRollCta */
  additionalProperties?: InputMaybe<Array<InputMaybe<AdditionalPropertiesKeyValuePairInput>>>;
  /** The authorization ID of the user who is viewing the content. */
  authUserId?: InputMaybe<Scalars['String']>;
  /** The packages the user has access to. */
  authUserPackages: Array<Package>;
  /** The authorization ID of the team the user is viewing the content for. */
  authUserTeamId?: InputMaybe<Scalars['String']>;
  /** Uniquely identifies a piece of community content. */
  communityContentId?: InputMaybe<CommunityContentIdInput>;
  /** Describes the location of the content container. */
  container: CommunityContentContainerType;
  /** A way to segment deeper into the container. Ex: for the explore hub, each timeline would be a different container section. */
  containerSection?: InputMaybe<Scalars['String']>;
  /** A way to segment deeper into the continer. Ex: for profiles, this could be User, Team, Author, Region, or Conference. */
  containerType?: InputMaybe<Scalars['String']>;
  /** The IP address of the client making the request. */
  ipAddress?: InputMaybe<Scalars['String']>;
  /** Whether the content is a recommendation. */
  isRecommendation: Scalars['Boolean'];
  /** The locale of the user making the request. */
  locale?: InputMaybe<Scalars['String']>;
  /** Describes the type of player rendering the community content video. */
  player: CommunityContentContainerPlayer;
  /** If the content is displayed in a list or timeline, this should be the index in the list. */
  position?: InputMaybe<Scalars['Int']>;
  /** The URL of the page that referred the user to the content. */
  referrerUrl?: InputMaybe<Scalars['String']>;
  /** The URL of the request. */
  requestUrl?: InputMaybe<Scalars['String']>;
  /** The application type that is requesting the content. */
  requestingApp: CommunityContentRequestingApp;
  /** The referrer url for when the session on hudl.com began. */
  sessionReferrerUrl?: InputMaybe<Scalars['String']>;
  /** The reason the content is being suggested. Ex: UserBasedHighlights */
  suggestionReason?: InputMaybe<Scalars['String']>;
  /** The user agent of the client making the request. */
  userAgent?: InputMaybe<Scalars['String']>;
};

/** Representation of a payable installment for a program registration. */
export type Installment = {
  __typename?: 'Installment';
  /** The amount of the installment. */
  amountInBaseDenomination: Scalars['Int'];
  /** The date the installment was created. */
  createdAt: Scalars['DateTime'];
  /** The ID of the user who created the installment. */
  createdBy: Scalars['ID'];
  /** The date the installment was deleted, if applicable. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The ID of the user who deleted the installment, if applicable. */
  deletedBy?: Maybe<Scalars['ID']>;
  /** The due date of the installment. */
  dueDate: Scalars['DateTime'];
  /** The ID of the installment. */
  id: Scalars['ID'];
  /** The number of the installment. */
  installmentNumber: Scalars['Int'];
  /** The ID of the installment plan. */
  installmentPlanId?: Maybe<Scalars['ID']>;
  /** The ID of the registrant. */
  registrantId: Scalars['ID'];
  /** The status of the installment. */
  status: InstallmentStatus;
  /** Gets transactions for an installment. */
  transactions: Array<Transaction>;
  /** The date the installment was last updated. */
  updatedAt: Scalars['DateTime'];
  /** The ID of the user who last updated the installment. */
  updatedBy: Scalars['ID'];
};

export type InstallmentAllocationDto = {
  __typename?: 'InstallmentAllocationDto';
  amountInBaseDenomination: Scalars['Int'];
  currencyCode?: Maybe<Scalars['String']>;
  installmentId?: Maybe<Scalars['String']>;
};

export enum InstallmentStatus {
  None = 'NONE',
  Overdue = 'OVERDUE',
  Paid = 'PAID',
  Pending = 'PENDING'
}

export type InviteCode = {
  __typename?: 'InviteCode';
  /** Cell carriers */
  cellCarriers?: Maybe<Array<Maybe<Carrier>>>;
  /** Date created */
  dateCreated: Scalars['DateTime'];
  /** Invite code Id */
  id?: Maybe<Scalars['String']>;
  /** Invite requests */
  inviteRequests?: Maybe<Array<Maybe<InviteRequest>>>;
  /** Is Family Members enabled for team */
  isFamilyMembersEnabled: Scalars['Boolean'];
  /** Date expired */
  lastModifiedDate: Scalars['DateTime'];
  /** Team */
  team?: Maybe<TeamHeader>;
  /** Team Id */
  teamId?: Maybe<Scalars['String']>;
};

export type InviteRequest = {
  __typename?: 'InviteRequest';
  /** Cell carrier */
  cellCarrier?: Maybe<Scalars['String']>;
  /** Cell number. */
  cellPhone?: Maybe<Scalars['String']>;
  /** Date created */
  dateCreated: Scalars['DateTime'];
  /** Email */
  email?: Maybe<Scalars['String']>;
  /** First Name */
  firstName?: Maybe<Scalars['String']>;
  /** Graduation Year */
  graduationYear?: Maybe<Scalars['String']>;
  /** Jersey number */
  jersey?: Maybe<Scalars['String']>;
  /** Last Name */
  lastName?: Maybe<Scalars['String']>;
  /** Position */
  position?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Role */
  role: Role;
  /** User Id */
  userId?: Maybe<Scalars['String']>;
};

export type LeagueAggregationInfo = {
  __typename?: 'LeagueAggregationInfo';
  /** The list of league entities in an aggregation */
  leagueEntities: Array<LeagueEntityInfo>;
};

export type LeagueEntityInfo = {
  __typename?: 'LeagueEntityInfo';
  /** The abbreviation of the league entity */
  abbreviation?: Maybe<Scalars['String']>;
  /** The internal id of the league entity */
  id: Scalars['String'];
  /** The name of the league entity */
  name?: Maybe<Scalars['String']>;
  /** The type of the league entity */
  type: Scalars['String'];
};

export enum LeagueEntityType {
  Competition = 'COMPETITION',
  CompetitionPeriod = 'COMPETITION_PERIOD',
  GoverningBody = 'GOVERNING_BODY',
  Municipality = 'MUNICIPALITY',
  RegionalAlignment = 'REGIONAL_ALIGNMENT',
  Subdivision = 'SUBDIVISION',
  Unknown = 'UNKNOWN'
}

export type LeagueMember = {
  __typename?: 'LeagueMember';
  /** When organization members are requested, this field will be populated with the organization data. */
  organization?: Maybe<FanSearchOrganization>;
};

/** A connection to a list of items. */
export type LeagueMemberConnection = {
  __typename?: 'LeagueMemberConnection';
  /** A list of edges. */
  edges?: Maybe<Array<LeagueMemberEdge>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<LeagueMember>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type LeagueMemberEdge = {
  __typename?: 'LeagueMemberEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<LeagueMember>;
};

/** Representation of a league pass config. */
export type LeaguePassConfig = {
  __typename?: 'LeaguePassConfig';
  /** The pass configs associated with this leage pass config */
  associatedPassConfigIds?: Maybe<Array<LeaguePassConfigAssociatedPassConfig>>;
  /** Associated schools that have child pass configs */
  associatedSchools?: Maybe<Array<Maybe<School>>>;
  /** Date the league pass config was created. */
  createdAt: Scalars['DateTime'];
  /** The currency associated with the priceInCents of the league pass config. */
  currency: Scalars['String'];
  /** Date the league pass config was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The description of the league pass config. */
  description?: Maybe<Scalars['String']>;
  /** The end date the league pass config is valid for. (inclusive) */
  endDate: Scalars['DateTime'];
  /** The game types that are excluded from the league pass config. */
  excludedGameTypes?: Maybe<Array<Scalars['String']>>;
  /** The fee strategy to apply to the league pass config. */
  feeStrategy: Scalars['String'];
  /** The GraphQL id of the pass config. */
  id: Scalars['ID'];
  /** The league associated with the league pass config. */
  leagueEntityId: Scalars['ID'];
  /** The status of the league pass config. */
  leagueEntityType: Scalars['String'];
  /** The status of the league pass config. */
  leaguePassConfigStatus: Scalars['String'];
  /** The name of the league pass config. */
  name: Scalars['String'];
  /** Price of the league pass. */
  priceInCents: Scalars['Int'];
  /** The start date the league pass config is valid for. (inclusive) */
  startDate: Scalars['DateTime'];
  /** The event filters used to determine which events are valid for a pass config. */
  teamFilters: Array<LeaguePassConfigTeamFilter>;
  /** Date the league pass config was updated. */
  updatedAt: Scalars['DateTime'];
  /** The visibility of the league pass config. */
  visibility: Scalars['String'];
};

export type LeaguePassConfigAssociatedPassConfig = {
  __typename?: 'LeaguePassConfigAssociatedPassConfig';
  organizationId: Scalars['ID'];
  passConfigId: Scalars['ID'];
};

/** A connection to a list of items. */
export type LeaguePassConfigConnection = {
  __typename?: 'LeaguePassConfigConnection';
  /** A list of edges. */
  edges?: Maybe<Array<LeaguePassConfigEdge>>;
  items?: Maybe<Array<LeaguePassConfig>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<LeaguePassConfig>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type LeaguePassConfigEdge = {
  __typename?: 'LeaguePassConfigEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<LeaguePassConfig>;
};

/** The filter used to determine which teams to include in the leage pass child configs. */
export type LeaguePassConfigTeamFilter = {
  __typename?: 'LeaguePassConfigTeamFilter';
  /** The team id of the event filter */
  excludedTeamIds: Array<Scalars['String']>;
  /** The team filter gender */
  gender: Scalars['String'];
  /** The team filter sport */
  sport: Scalars['String'];
  /** The team filter level */
  teamLevel: Scalars['String'];
};

export type LeagueScaffoldingHierarchy = {
  __typename?: 'LeagueScaffoldingHierarchy';
  /** The Competition within this league's hierarchical structure. */
  competition?: Maybe<CompetitionHeader>;
  /** The Competition Period within this league's hierarchical structure. */
  competitionPeriod?: Maybe<CompetitionPeriodHeader>;
  /** The Governing Body within this league's hierarchical structure. */
  governingBody?: Maybe<GoverningBodyHeader>;
  /** The Municipality within this league's hierarchical structure. */
  municipality?: Maybe<MunicipalityHeader>;
  /** The Regional Alignment within this league's hierarchical structure. */
  regionalAlignment?: Maybe<RegionalAlignmentHeader>;
  /** The Subdivision within this league's hierarchical structure. */
  subdivision?: Maybe<SubdivisionHeader>;
};

/** The result of a registration operation. */
export type LicenseRegistrationPayload = {
  __typename?: 'LicenseRegistrationPayload';
  /** The response authentication string. */
  authentication?: Maybe<AuthenticationPayload>;
  /** The client mutation identifier. */
  clientMutationId?: Maybe<Scalars['String']>;
};

export enum LicenseType {
  Elite = 'ELITE',
  EliteReview = 'ELITE_REVIEW',
  GamebreakerPlus = 'GAMEBREAKER_PLUS',
  GslApiToken = 'GSL_API_TOKEN',
  Player = 'PLAYER',
  Pro = 'PRO',
  ProReview = 'PRO_REVIEW',
  Studiocode = 'STUDIOCODE',
  WimuSpro = 'WIMU_SPRO',
  WimuSvivo = 'WIMU_SVIVO'
}

/** Representation of an entry linked to a ticketed event. */
export type LinkedEntry = {
  __typename?: 'LinkedEntry';
  /** The ID of the linked entry. */
  id: Scalars['ID'];
  /** The type of the linked entry. */
  type?: Maybe<Scalars['String']>;
};

export enum MediaQuality {
  HdH264_3000Kb = 'HD_H264_3000_KB',
  LdH264_722Kb = 'LD_H264_722_KB',
  SdH264_1100Kb = 'SD_H264_1100_KB',
  ThdH264_6500Kb = 'THD_H264_6500_KB',
  UhdH264_16000Kb = 'UHD_H264_16000_KB',
  Unknown = 'UNKNOWN'
}

export type MessagingParticipantsMetadata = {
  __typename?: 'MessagingParticipantsMetadata';
  /** If the user is an org admin. */
  isOrgAdmin: Scalars['Boolean'];
  /** Maximum role of participant across the entire organization. */
  maxRole?: Maybe<Role>;
  /** The internal ID of the participant. */
  userId?: Maybe<Scalars['String']>;
};

/** A connection to a list of items. */
export type MessagingParticipantsMetadataConnection = {
  __typename?: 'MessagingParticipantsMetadataConnection';
  /** A list of edges. */
  edges?: Maybe<Array<MessagingParticipantsMetadataEdge>>;
  items?: Maybe<Array<MessagingParticipantsMetadata>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<MessagingParticipantsMetadata>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type MessagingParticipantsMetadataEdge = {
  __typename?: 'MessagingParticipantsMetadataEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<MessagingParticipantsMetadata>;
};

export type MunicipalityHeader = {
  __typename?: 'MunicipalityHeader';
  /** The abbreviation of the Municipality */
  abbreviation?: Maybe<Scalars['String']>;
  /** True if the user can edit the municipality. */
  canUserEditLeagueEntity: Scalars['Boolean'];
  /** The city of the Municipality */
  city?: Maybe<Scalars['String']>;
  /** The ISO 3166-1 alpha-3 code representing the country of the Municipality */
  countryIso?: Maybe<Scalars['String']>;
  /** The Id of the Municipality */
  id: Scalars['ID'];
  /** The internal Id of the Municipality */
  internalId?: Maybe<Scalars['String']>;
  /** Flag to indicate if the Municipality is hidden from search */
  isHiddenFromSearch: Scalars['Boolean'];
  /** Flag to indicate if the Municipality profile is hidden */
  isProfileHidden: Scalars['Boolean'];
  /** The name of the Municipality */
  name?: Maybe<Scalars['String']>;
  /** The organization branding for the public municipality. */
  organizationBranding?: Maybe<OrganizationBranding>;
  /** The list of school Ids associated with the Municipality */
  schoolIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The ISO 3166-2 code representing the state or province of the Municipality */
  subdivisionIso?: Maybe<Scalars['String']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  cashCheckoutWithTicketingLineItems?: Maybe<TicketingCheckoutResult>;
  checkoutWithTicketingLineItems?: Maybe<TicketingCheckoutResult>;
  /** Logs an impression of community content. */
  communityContentImpression: Scalars['Boolean'];
  /** Logs a play of community content. */
  communityContentPlay: Scalars['Boolean'];
  /** Logs a completed view of community content. */
  communityContentViewComplete: Scalars['Boolean'];
  /** Logs a view for the first quartile of community content. */
  communityContentViewFirstQuartile: Scalars['Boolean'];
  /** Logs a view for the midpoint quartile of community content. */
  communityContentViewMidpointQuartile: Scalars['Boolean'];
  /** Logs a view for the third quartile of community content. */
  communityContentViewThirdQuartile: Scalars['Boolean'];
  createProvisionalAthleteAndGuardianRelationship: AthleteAndGuardianRelationship;
  /** Creates a terminal connection token */
  createTerminalConnectionToken?: Maybe<TerminalConnectionToken>;
  createTicketingPaymentToken?: Maybe<Scalars['String']>;
  /** Deregister a machine. */
  deregister: DeregistrationPayload;
  id?: Maybe<Scalars['String']>;
  /** Queues scanned Tickets and Passes for redemption */
  queueBatchRedemption: Scalars['Boolean'];
  /** Redeems a ticketing entity via the specified redemption method */
  redeemTicketingEntity: Array<RedemptionEntity>;
  /**
   * Register a machine for the first time.
   * @deprecated This is for old HSC clients. Use RegisterLicense which includes the signature
   */
  register: RegistrationPayload;
  /** Register a machine for the first time. */
  registerLicense: LicenseRegistrationPayload;
  /** Shares ticketing entities by associating them with a new ticket group. */
  shareTicketingEntities: ShareHistory;
  transferTicketingEntities: ShareHistory;
  /** Update the registration for a machine. */
  updateLicenseRegistration: LicenseRegistrationPayload;
  /**
   * Update the registration for a machine.
   * @deprecated This is for old HSC clients. Use UpdateLicenseRegistration which includes the signature
   */
  updateRegistration: RegistrationPayload;
  /** Upserts registrants for a program registration. */
  upsertRegistrants: Array<Registrant>;
};


export type MutationCashCheckoutWithTicketingLineItemsArgs = {
  input?: InputMaybe<TicketingCashCheckoutInput>;
};


export type MutationCheckoutWithTicketingLineItemsArgs = {
  input?: InputMaybe<TicketingCheckoutInput>;
};


export type MutationCommunityContentImpressionArgs = {
  input: ImpressionCommunityContentInput;
};


export type MutationCommunityContentPlayArgs = {
  input: PlayCommunityContentInput;
};


export type MutationCommunityContentViewCompleteArgs = {
  input: ViewQuartileCommunityContentInput;
};


export type MutationCommunityContentViewFirstQuartileArgs = {
  input: ViewQuartileCommunityContentInput;
};


export type MutationCommunityContentViewMidpointQuartileArgs = {
  input: ViewQuartileCommunityContentInput;
};


export type MutationCommunityContentViewThirdQuartileArgs = {
  input: ViewQuartileCommunityContentInput;
};


export type MutationCreateProvisionalAthleteAndGuardianRelationshipArgs = {
  input: CreateProvisionalAthleteAndGuardianRelationshipInput;
};


export type MutationCreateTerminalConnectionTokenArgs = {
  input: CreateTerminalConnectionTokenInput;
};


export type MutationCreateTicketingPaymentTokenArgs = {
  input?: InputMaybe<CreateTicketingPaymentTokenInput>;
};


export type MutationDeregisterArgs = {
  input: DeregistrationInput;
};


export type MutationQueueBatchRedemptionArgs = {
  input: ScanningRedemptionInput;
};


export type MutationRedeemTicketingEntityArgs = {
  input: RedemptionInput;
};


export type MutationRegisterArgs = {
  input: RegistrationInput;
};


export type MutationRegisterLicenseArgs = {
  input: RegistrationInput;
};


export type MutationShareTicketingEntitiesArgs = {
  shareTicketingEntitiesInput: ShareTicketingEntitiesInput;
};


export type MutationTransferTicketingEntitiesArgs = {
  transferTicketingEntitiesInput: TransferTicketingEntitiesInput;
};


export type MutationUpdateLicenseRegistrationArgs = {
  input: UpdateRegistrationInput;
};


export type MutationUpdateRegistrationArgs = {
  input: UpdateRegistrationInput;
};


export type MutationUpsertRegistrantsArgs = {
  input: Array<UpsertRegistrantInput>;
};

/** Opponent details for a schedule entry. Primarily used for fan and public facing pages. */
export type OpponentDetails = {
  __typename?: 'OpponentDetails';
  /** The abbreviation of the opponent for the schedule entry. */
  abbreviation?: Maybe<Scalars['String']>;
  /** The internal schoolId of the opponent for the schedule entry. */
  internalSchoolId?: Maybe<Scalars['String']>;
  /** The internal teamId of the opponent for the schedule entry. */
  internalTeamId?: Maybe<Scalars['String']>;
  /** The mascot name of the opponent for the schedule entry. */
  mascot?: Maybe<Scalars['String']>;
  /** The name of the opponent for the schedule entry. */
  name?: Maybe<Scalars['String']>;
  /** The primary color of the opponent for the schedule entry. */
  primaryColor?: Maybe<Scalars['String']>;
  /** The profile image uri of the opponent for the schedule entry. */
  profileImageUri?: Maybe<Scalars['String']>;
  /** The GraphQL encoded schoolId of the opponent for the schedule entry. Can be null for non-Hudl opponents. */
  schoolId?: Maybe<Scalars['ID']>;
  /** The seasonId for the opponent for the schedule entry. */
  seasonId?: Maybe<Scalars['String']>;
  /** The season year for the opponent for the schedule entry. */
  seasonYear?: Maybe<Scalars['Short']>;
  /** The secondary color of the opponent for the schedule entry. */
  secondaryColor?: Maybe<Scalars['String']>;
  /** The short name of the opponent for the schedule entry. */
  shortName?: Maybe<Scalars['String']>;
  /** The GraphQL encoded teamId of the opponent for the schedule entry. Can be null for non-Hudl opponents. */
  teamId?: Maybe<Scalars['ID']>;
};

export type OrganizationBranding = {
  __typename?: 'OrganizationBranding';
  /** The banner image uri of the organization  */
  bannerImageUri?: Maybe<Scalars['String']>;
  /** The Id of the organization */
  id: Scalars['ID'];
  /** The internal Id of the league entity associated with the branding */
  internalLeagueId?: Maybe<Scalars['String']>;
  /** The internal Id of the school */
  internalSchoolId?: Maybe<Scalars['String']>;
  /** The type of the league entity associated with the branding. Will be Unknown for schools. */
  leagueEntityType: LeagueEntityType;
  /** The mascot of the organization */
  mascot?: Maybe<Scalars['String']>;
  /** School's primary color as RGB hex */
  primaryColor?: Maybe<Scalars['String']>;
  /** The profile image uri of the organization */
  profileImageUri?: Maybe<Scalars['String']>;
  /** The graphql Id of the school */
  schoolId?: Maybe<Scalars['ID']>;
  /** School's secondary color as RBG hex */
  secondaryColor?: Maybe<Scalars['String']>;
};

/** A summary of Organization information that is public. */
export type OrganizationHeader = {
  __typename?: 'OrganizationHeader';
  /** GraphQL Encoded Organization Id */
  id: Scalars['ID'];
  /** Organization abbreviation */
  orgAbbreviation?: Maybe<Scalars['String']>;
  /** Organization classification type */
  orgClassificationId: Scalars['Int'];
  /** Organization name */
  organizationName?: Maybe<Scalars['String']>;
};

/** Representation of an organization program. */
export type OrganizationProgram = {
  __typename?: 'OrganizationProgram';
  /** When the program was created. */
  createdAt: Scalars['DateTime'];
  /** Who created the program. */
  createdBy: Scalars['ID'];
  /** When the program was deleted, if applicable. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** Who deleted the program, if applicable. */
  deletedBy?: Maybe<Scalars['ID']>;
  /** The description of the program. */
  description?: Maybe<Scalars['String']>;
  /** The end date of the program. */
  endDate?: Maybe<Scalars['DateTime']>;
  /** The fee responsibility setting of the program. */
  feeResponsibility: FeeResponsibility;
  /** List of form references associated with the program. */
  formRefs: Array<FormRef>;
  /** The Id of the Organization Program. */
  id: Scalars['ID'];
  /** The id of the organization reference. */
  orgId: Scalars['ID'];
  /** Gets registrations for a program. */
  registrationsForProgram: Array<ProgramRegistration>;
  /** The start date of the program. */
  startDate?: Maybe<Scalars['DateTime']>;
  /** The current state of the program. */
  state: ProgramState;
  /** The timezone identifier for the program. */
  timeZoneIdentifier?: Maybe<Scalars['String']>;
  /** The title of the program. */
  title?: Maybe<Scalars['String']>;
  /** The type of the program. */
  type?: Maybe<EmbeddedOrganizationProgramType>;
  /** When the program was last updated. */
  updatedAt: Scalars['DateTime'];
  /** Who last updated the program. */
  updatedBy: Scalars['ID'];
  /** The visibility setting of the program. */
  visibility: ProgramVisibility;
};

/** Settings for an organization */
export type OrganizationSettings = {
  __typename?: 'OrganizationSettings';
  /** Indicates if the organization profile is hidden */
  isOrgProfileHidden?: Maybe<Scalars['Boolean']>;
  /** Indicates if student data privacy is enabled for the organization */
  isStudentDataPrivacyOn?: Maybe<Scalars['Boolean']>;
};

export enum Package {
  Basic = 'BASIC',
  Elite = 'ELITE',
  ExchangeOnly = 'EXCHANGE_ONLY',
  FreeRecruiting = 'FREE_RECRUITING',
  Gold = 'GOLD',
  Invalid = 'INVALID',
  Lightning = 'LIGHTNING',
  Limited = 'LIMITED',
  NoPackage = 'NO_PACKAGE',
  Platinum = 'PLATINUM',
  Recruiting = 'RECRUITING',
  RegionalRecruiting = 'REGIONAL_RECRUITING',
  Silver = 'SILVER',
  TeamBasedBronze = 'TEAM_BASED_BRONZE',
  TeamBasedElite = 'TEAM_BASED_ELITE',
  TeamBasedGold = 'TEAM_BASED_GOLD',
  TeamBasedLimited = 'TEAM_BASED_LIMITED',
  TeamBasedPlatinum = 'TEAM_BASED_PLATINUM',
  TeamBasedSilver = 'TEAM_BASED_SILVER',
  TeamBasedStarter = 'TEAM_BASED_STARTER',
  TrialBase = 'TRIAL_BASE',
  Varsity = 'VARSITY',
  Youth = 'YOUTH',
  YouthMonthly = 'YOUTH_MONTHLY'
}

/** Information about pagination in a connection. */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  endCursor?: Maybe<Scalars['Cursor']>;
  /** Indicates whether more edges exist following the set defined by the clients arguments. */
  hasNextPage: Scalars['Boolean'];
  /** Indicates whether more edges exist prior the set defined by the clients arguments. */
  hasPreviousPage: Scalars['Boolean'];
  /** When paginating backwards, the cursor to continue. */
  startCursor?: Maybe<Scalars['Cursor']>;
};

/** Representation of a pass. */
export type Pass = {
  __typename?: 'Pass';
  /** Date the pass was created. */
  createdAt: Scalars['DateTime'];
  /** Date the pass was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The email of the person associated with this pass. */
  email?: Maybe<Scalars['String']>;
  /** The first name of the person associated with this pass. */
  firstName?: Maybe<Scalars['String']>;
  /** The GraphQL id of the pass. */
  id: Scalars['ID'];
  /** The last name of the person associated with this pass. */
  lastName?: Maybe<Scalars['String']>;
  /** The identifier for the Pass Config this pass is for. */
  passConfigId: Scalars['ID'];
  /** The QR Code data for this pass */
  qrCodeData?: Maybe<Scalars['String']>;
  /**
   * The QR code linking to this pass.
   * @deprecated This property will no longer return a value. Use the QRCodeData property instead.
   */
  qrCodeUrl?: Maybe<Scalars['String']>;
  /** The reserved seats associated with the pass. */
  reservedSeats?: Maybe<Array<ReservedSeat>>;
  /** Date the pass was shared. */
  sharedAt?: Maybe<Scalars['DateTime']>;
  /** The source where the pass was purchased. */
  source?: Maybe<Scalars['String']>;
  /** The tickets created for this pass */
  tickets?: Maybe<Array<Maybe<Ticket>>>;
  /** Date the pass was updated. */
  updatedAt: Scalars['DateTime'];
  /** The Hudl user id of the person associated with this pass, if one exists. */
  userId?: Maybe<Scalars['String']>;
};

/** Representation of a pass config. */
export type PassConfig = {
  __typename?: 'PassConfig';
  /** Date the ticketed event was created. */
  createdAt: Scalars['DateTime'];
  /** The currency associated with the priceInCents of the pass config. */
  currency?: Maybe<Scalars['String']>;
  /** Date the ticketed event was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The description of the pass config. */
  description?: Maybe<Scalars['String']>;
  /** The end date the pass config is valid for. (inclusive) */
  endDate?: Maybe<Scalars['DateTime']>;
  /** The event filters used to determine which events are valid for a pass config. */
  eventFilters?: Maybe<Array<PassConfigEventFilter>>;
  /** The game types that are excluded from the pass config. */
  excludedGameTypes?: Maybe<Array<Scalars['String']>>;
  /** Events that are excluded from the pass config. */
  excludedTicketedEventIds?: Maybe<Array<Scalars['ID']>>;
  /** The fee strategy to apply to the pass config. */
  feeStrategy?: Maybe<Scalars['String']>;
  /** The IDs of the form fields associated with the pass config. */
  formFieldIds?: Maybe<Array<Scalars['ID']>>;
  /** The form fields associated with the pass config */
  formFields?: Maybe<Array<Maybe<FormField>>>;
  /** The GraphQL id of the pass config. */
  id: Scalars['ID'];
  /** Events that are included in the pass config outside of the events applied via filters. */
  includedTicketedEventIds?: Maybe<Array<Scalars['ID']>>;
  /** The ID of the League Pass config associated with this pass config. */
  leaguePassConfigId?: Maybe<Scalars['ID']>;
  /** The name of the pass config. */
  name?: Maybe<Scalars['String']>;
  /** The organization associated with the pass config. */
  organizationId: Scalars['ID'];
  /** The pricing summary associated with the pass config */
  passConfigPricingSummary?: Maybe<PassConfigPricingSummary>;
  /** The status of the pass config. */
  passConfigStatus?: Maybe<Scalars['String']>;
  /** The number of passes sold associated with a pass config */
  passCount: Scalars['Int'];
  /** Price of the pass. */
  priceInCents: Scalars['Int'];
  /** The date the pass config is no longer available for purchase. (inclusive) */
  purchaseExpirationDate?: Maybe<Scalars['DateTime']>;
  /** Number of passes available for purchase. */
  quantityAvailable?: Maybe<Scalars['Int']>;
  /** The quantity of passes remaining for the pass config */
  quantityRemaining?: Maybe<QuantityRemainingOutput>;
  /** The renewal campaign associated with this pass config */
  renewalCampaign?: Maybe<PassConfigRenewalCampaign>;
  /** The start date the pass config is valid for. (inclusive) */
  startDate?: Maybe<Scalars['DateTime']>;
  /** List of the team ids valid for this pass config. */
  teamIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** Selected teams for the pass config */
  teams?: Maybe<Array<Maybe<TeamHeader>>>;
  /** List of the ticketed event ids valid for this pass config. */
  ticketedEventIds?: Maybe<Array<Scalars['ID']>>;
  /** The status of the pass config's TicketedEventIds list. */
  ticketedEventIdsCalculationStatus?: Maybe<Scalars['String']>;
  /** The ticketed events associated with a pass config */
  ticketedEvents?: Maybe<Array<Maybe<TicketedEvent>>>;
  /** Date the ticketed event was updated. */
  updatedAt: Scalars['DateTime'];
  /** The visibility of the pass config. */
  visibility?: Maybe<Scalars['String']>;
};

/** A connection to a list of items. */
export type PassConfigConnection = {
  __typename?: 'PassConfigConnection';
  /** A list of edges. */
  edges?: Maybe<Array<PassConfigEdge>>;
  items?: Maybe<Array<PassConfig>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<PassConfig>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type PassConfigEdge = {
  __typename?: 'PassConfigEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<PassConfig>;
};

/** The event filter used to determine which events are valid for a pass config. */
export type PassConfigEventFilter = {
  __typename?: 'PassConfigEventFilter';
  /** List of category ids that should be enabled in context of the venue config. */
  seatsDotIoEnabledCategoryIds?: Maybe<Array<Scalars['String']>>;
  /** The Seats.io partial season id associated with the events calculated by the event filter. */
  seatsDotIoPartialSeasonId?: Maybe<Scalars['String']>;
  /** The team id of the event filter */
  teamId: Scalars['ID'];
  /** The venue configuration id of the event filter */
  venueConfigurationId?: Maybe<Scalars['ID']>;
};

/** Representation of a pass config pricing summary including information about the Hudl fees applied. */
export type PassConfigPricingSummary = {
  __typename?: 'PassConfigPricingSummary';
  /** The currency associated with the priceInCents of the pass config. */
  currency?: Maybe<Scalars['String']>;
  /** The Hudl Fee applied to the pass purchase in cents */
  hudlFeeInCents: Scalars['Int'];
  /** The ID of the pass config. */
  passConfigId: Scalars['ID'];
  /** The price of the pass config in cents */
  priceInCents: Scalars['Int'];
  /** The price of the pass config in cents including the Hudl Fee */
  priceInCentsWithHudlFee: Scalars['Int'];
  /** Whether the Hudl fee should be displayed to the user. */
  shouldShowFee: Scalars['Boolean'];
};

/** Representation of a Renewal Campaign for a Pass Config. */
export type PassConfigRenewalCampaign = {
  __typename?: 'PassConfigRenewalCampaign';
  /** The date and time when the campaign was created */
  createdAt: Scalars['DateTime'];
  /** The date and time when the campaign was deleted */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The ID of the Pass Config Renewal Campaign */
  id: Scalars['ID'];
  /** The ID of the organization associated with this campaign */
  organizationId: Scalars['ID'];
  /** The pass config associated with this renewal campaign */
  passConfig?: Maybe<PassConfig>;
  /** The ID of the Pass Config associated with this campaign */
  passConfigId: Scalars['ID'];
  /** The visibility of the Pass Config after the renewal campaign ends */
  postRenewalVisibility: Scalars['String'];
  /** The end date of the renewal campaign */
  renewalEndDate: Scalars['DateTime'];
  /** The start date of the renewal campaign */
  renewalStartDate: Scalars['DateTime'];
  /** The time zone associated with the renewal campaign */
  timeZoneIdentifier: Scalars['String'];
  /** The date and time when the campaign was last updated */
  updatedAt: Scalars['DateTime'];
};

/** Representation of a Renewer that is a part of a Pass Config Renewal Campaign. */
export type PassConfigRenewer = {
  __typename?: 'PassConfigRenewer';
  /** The date and time when the renewer was created. */
  createdAt: Scalars['DateTime'];
  /** The date and time when the renewer was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The email address of the Renewer. */
  email: Scalars['String'];
  /** The first name of the Renewer. */
  firstName: Scalars['String'];
  /** The ID of the Renewer record. */
  id: Scalars['ID'];
  /** The last name of the Renewer. */
  lastName: Scalars['String'];
  /** The renewal campaign associated with this renewer */
  renewalCampaign?: Maybe<PassConfigRenewalCampaign>;
  /** The ID of the Pass Config Renewal Campaign this Renewer is associated with. */
  renewalCampaignId: Scalars['ID'];
  /** The ID of the SeatsDotIo channel that is associated with this Renewer. */
  seatsDotIoChannelId?: Maybe<Scalars['String']>;
  /** The list of seat identifiers that the Renewer can select. */
  selectableSeatIdentifiers?: Maybe<Array<Scalars['String']>>;
  /** The date and time when the renewer was last updated. */
  updatedAt: Scalars['DateTime'];
  /** The ID of the user associated with this Renewer. */
  userId?: Maybe<Scalars['String']>;
};

export enum PassConfigSortType {
  StartDate = 'START_DATE',
  Unknown = 'UNKNOWN'
}

export type PayloadAuthentication = {
  __typename?: 'PayloadAuthentication';
  endDate?: Maybe<Scalars['DateTime']>;
  licenseType?: Maybe<LicenseType>;
  machine?: Maybe<Scalars['String']>;
  publicHash?: Maybe<Scalars['String']>;
  registration?: Maybe<Scalars['String']>;
  schoolName?: Maybe<Scalars['String']>;
  special?: Maybe<Scalars['String']>;
  startDate?: Maybe<Scalars['DateTime']>;
  subscriptionEndDate?: Maybe<Scalars['DateTime']>;
  subscriptionStartDate?: Maybe<Scalars['DateTime']>;
  token?: Maybe<Scalars['String']>;
};

export enum PaymentMethodType {
  Ach = 'ACH',
  Cash = 'CASH',
  Check = 'CHECK',
  CreditCard = 'CREDIT_CARD',
  None = 'NONE'
}

/** Representation of an input to log a play of community content. */
export type PlayCommunityContentInput = {
  /** The Turn ID or mobile device ID. */
  adTrackingId?: InputMaybe<Scalars['String']>;
  /** Additional properties to log with the impression. Ex: pageVisibility, sessionId, usePostRollCta */
  additionalProperties?: InputMaybe<Array<InputMaybe<AdditionalPropertiesKeyValuePairInput>>>;
  /** The authorization ID of the user who is viewing the content. */
  authUserId?: InputMaybe<Scalars['String']>;
  /** The packages the user has access to. */
  authUserPackages?: InputMaybe<Array<Package>>;
  /** The authorization ID of the team the user is viewing the content for. */
  authUserTeamId?: InputMaybe<Scalars['String']>;
  /** Uniquely identifies a piece of community content. */
  communityContentId?: InputMaybe<CommunityContentIdInput>;
  /** Describes the location of the content container. */
  container: CommunityContentContainerType;
  /** A way to segment deeper into the container. Ex: for the explore hub, each timeline would be a different container section. */
  containerSection?: InputMaybe<Scalars['String']>;
  /** A way to segment deeper into the continer. Ex: for profiles, this could be User, Team, Author, Region, or Conference. */
  containerType?: InputMaybe<Scalars['String']>;
  /** The IP address of the client making the request. */
  ipAddress?: InputMaybe<Scalars['String']>;
  /** True if Hudl automatically advanced the user to this video and played it as the top suggestion. */
  isAutoAdvance: Scalars['Boolean'];
  /** Specifies if the video was played automatically. */
  isAutoPlay: Scalars['Boolean'];
  /** Specifies if the video is being played inline. */
  isInlinePlay: Scalars['Boolean'];
  /** Specifies if the video is muted. */
  isMuted: Scalars['Boolean'];
  /** The locale of the user making the request. */
  locale?: InputMaybe<Scalars['String']>;
  /** Describes the type of player rendering the community content video. */
  player: CommunityContentContainerPlayer;
  /** The height of the video player in pixels. */
  playerHeight?: InputMaybe<Scalars['Int']>;
  /** The width of the video player in pixels. */
  playerWidth?: InputMaybe<Scalars['Int']>;
  /** The URL of the page that referred the user to the content. */
  referrerUrl?: InputMaybe<Scalars['String']>;
  /** The URL of the request. */
  requestUrl?: InputMaybe<Scalars['String']>;
  /** The application type that is requesting the content. */
  requestingApp: CommunityContentRequestingApp;
  /** The referrer url for when the session on hudl.com began. */
  sessionReferrerUrl?: InputMaybe<Scalars['String']>;
  /** The user agent of the client making the request. */
  userAgent?: InputMaybe<Scalars['String']>;
  /** The number of times the content has been viewed. */
  views: Scalars['Int'];
};

/** Representation of an organization program registration. */
export type ProgramRegistration = {
  __typename?: 'ProgramRegistration';
  /** When the program was created. */
  createdAt: Scalars['DateTime'];
  /** Who created the program. */
  createdBy: Scalars['ID'];
  /** The currency of the registration. This is a 3-letter ISO 4217 currency code that currently defaults to USD. */
  currencyCode?: Maybe<Scalars['String']>;
  /** When the program was deleted, if applicable. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** Who deleted the program, if applicable. */
  deletedBy?: Maybe<Scalars['ID']>;
  /** The description of the registration. */
  description?: Maybe<Scalars['String']>;
  /** The eligibility settings for the registration. */
  eligibility?: Maybe<RegistrationEligibilityOutput>;
  /** The end date of the program. */
  endDate?: Maybe<Scalars['DateTime']>;
  /** The Id of the Registration. */
  id: Scalars['ID'];
  /** Is the waitlist enabled for the registration. */
  isWaitlistEnabled?: Maybe<Scalars['Boolean']>;
  /** The max capacity of the registration. */
  maxCapacity?: Maybe<Scalars['Int']>;
  /** The id of the organization reference. */
  orgId: Scalars['ID'];
  /** The price of the registration. */
  priceInBaseDenomination: Scalars['Int'];
  /** The id of the program reference. */
  programId: Scalars['ID'];
  /** The start date of the program. */
  startDate: Scalars['DateTime'];
  /** The status of the registration. */
  status: ProgramRegistrationStatus;
  /** The timezone identifier for the program. */
  timeZoneIdentifier?: Maybe<Scalars['String']>;
  /** The title of the registration. */
  title?: Maybe<Scalars['String']>;
  /** When the program was last updated. */
  updatedAt: Scalars['DateTime'];
  /** Who last updated the program. */
  updatedBy: Scalars['ID'];
};

export enum ProgramRegistrationStatus {
  Closed = 'CLOSED',
  Open = 'OPEN'
}

export enum ProgramState {
  Archived = 'ARCHIVED',
  Draft = 'DRAFT',
  Published = 'PUBLISHED'
}

export enum ProgramVisibility {
  Private = 'PRIVATE',
  Public = 'PUBLIC',
  Unset = 'UNSET'
}

export type PublicMember = {
  __typename?: 'PublicMember';
  /** Member's first name. */
  firstName?: Maybe<Scalars['String']>;
  /** Member's full name. */
  fullName?: Maybe<Scalars['String']>;
  /** Member Graduation Year */
  graduationYear?: Maybe<Scalars['Short']>;
  /** Member groups */
  groups?: Maybe<IdConnection>;
  /** Height */
  height?: Maybe<Scalars['String']>;
  /** The graphql Id of the member. */
  id: Scalars['ID'];
  /** The hudl Id of the member. */
  internalId?: Maybe<Scalars['String']>;
  /** IsDisabled */
  isDisabled: Scalars['Boolean'];
  /** Jersey */
  jersey?: Maybe<Scalars['String']>;
  /** Last Login Date */
  lastLoginDate?: Maybe<Scalars['DateTime']>;
  /** Member's last name. */
  lastName?: Maybe<Scalars['String']>;
  /** Participant Id */
  participantId?: Maybe<Scalars['String']>;
  /** PictureUrl */
  pictureUrl?: Maybe<Scalars['String']>;
  /** Position */
  position?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Season Ids */
  seasonIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** The teamId that the member belongs to */
  teamId: Scalars['ID'];
  /** Weight */
  weight?: Maybe<Scalars['Long']>;
};


export type PublicMemberGroupsArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  includeFamilyMembers?: InputMaybe<Scalars['Boolean']>;
  last?: InputMaybe<Scalars['Int']>;
};

export type PublishEventStreamSummary = {
  __typename?: 'PublishEventStreamSummary';
  /** The camera id of the publish event stream summary. */
  cameraId?: Maybe<Scalars['String']>;
  /** The external microphone volume for the publish event stream summary. */
  externalMicVolume?: Maybe<Scalars['Float']>;
  /** If the scoreboard is included in the publish event stream summary. */
  includeScoreboard: Scalars['Boolean'];
  /** The Installation Id for the publish event stream summary. */
  installationId?: Maybe<Scalars['String']>;
  /** The internal microphone volume for the publish event stream summary. */
  internalMicVolume?: Maybe<Scalars['Float']>;
  /**
   * The quality of the publish event stream summary.
   * @deprecated Use QualityId instead.
   */
  quality?: Maybe<MediaQuality>;
  /** The qualityId of the publish event stream summary. */
  qualityId: Scalars['Int'];
  /** The stream type of the publish event stream */
  streamType: StreamType;
};

export type PublishEventSummary = {
  __typename?: 'PublishEventSummary';
  /** The GraphQL Id of the publish event summary */
  id: Scalars['ID'];
  /** The InternalId of the publish event. */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the publish event. */
  name?: Maybe<Scalars['String']>;
  /** The RestreamSummaries for the publish event summary. */
  restreamSummaries?: Maybe<Array<Maybe<RestreamSummary>>>;
  /** The start time for the publish event. */
  startTime: Scalars['DateTime'];
  /** The stop time for the publish event. */
  stopTime: Scalars['DateTime'];
  /** The Publish event stream summaries for this publish event. */
  streams?: Maybe<Array<Maybe<PublishEventStreamSummary>>>;
};

export enum PublishSessionUploadSource {
  Admin = 'ADMIN',
  AutomaticCapture = 'AUTOMATIC_CAPTURE',
  CutupConversion = 'CUTUP_CONVERSION',
  Exchange = 'EXCHANGE',
  ExternalIngest = 'EXTERNAL_INGEST',
  Ios = 'IOS',
  KrossoverImport = 'KROSSOVER_IMPORT',
  LeroyApi = 'LEROY_API',
  Mercury = 'MERCURY',
  Sportscode = 'SPORTSCODE',
  Unknown = 'UNKNOWN',
  Volleymetrics = 'VOLLEYMETRICS',
  WebUploader = 'WEB_UPLOADER'
}

/** Representation of quantity remaining for a ticket type or pass config. */
export type QuantityRemainingOutput = {
  __typename?: 'QuantityRemainingOutput';
  /** The number of tickets or passes remaining for the ticket type or pass config. */
  quantityRemaining: Scalars['Int'];
  /** The GraphQL Id of the ticket type or pass config. */
  referenceId: Scalars['ID'];
};

export type Query = {
  __typename?: 'Query';
  /** Gets information about an ad context related to a user. */
  adTargeting: AdTargeting;
  /** Fetch all valid ticket groups containing tickets or passes for a provided event. Must provide an access code. */
  associatedTicketGroupsForTicketedEventId?: Maybe<TicketGroupConnection>;
  athleteAndGuardianRelationships: Array<AthleteAndGuardianRelationship>;
  /** Fetch Athlete by MemberId */
  athleteProfile?: Maybe<AthleteProfile>;
  /** Fetch broadcast by GraphQL encoded or internal id. Internal id takes precedence. */
  broadcast?: Maybe<Broadcast>;
  /** Fetch paginated broadcasts by using filters. */
  broadcasts?: Maybe<BroadcastConnection>;
  /** Fetch the canonical url a vanity url should redirect to. Returns null if the vanity url does not exist. */
  canonicalUrlForSchoolVanityUrl?: Maybe<Scalars['String']>;
  /** Fetch the public Competition data by Id */
  competitionHeader?: Maybe<CompetitionHeader>;
  /** Fetch the public Competitions associated with a Governing Body Id */
  competitionHeadersForGoverningBody?: Maybe<Array<Maybe<CompetitionHeader>>>;
  /** Fetch the public Competitions associated with a Subdivision Id */
  competitionHeadersForSubdivision?: Maybe<Array<Maybe<CompetitionHeader>>>;
  /** Fetch the public Competition Period data by Id */
  competitionPeriodHeader?: Maybe<CompetitionPeriodHeader>;
  /** Fetch the public Competition Periods associated with a Competition Id */
  competitionPeriodHeadersForCompetition?: Maybe<Array<Maybe<CompetitionPeriodHeader>>>;
  /** Fetch the Current public competition period associated with a Competition Id */
  currentCompetitionPeriodHeaderForCompetition?: Maybe<CompetitionPeriodHeader>;
  /** Fetch the current terms of service for ticketing and payouts. */
  currentTermsOfService?: Maybe<TermsOfService>;
  /** Fetch discoverable league pass configs for a given league. */
  discoverableLeaguePassConfigsByLeague?: Maybe<LeaguePassConfigConnection>;
  /** Fetch discoverable ticketed events for a team. */
  discoverableTicketedEventsForTeam?: Maybe<TicketedEventConnection>;
  /** Fetch paginated broadcasts using filters. */
  fanBroadcasts?: Maybe<FanBroadcastConnection>;
  /** Fetch paginated highlight reels using filters. */
  fanHighlightReels?: Maybe<FanHighlightReelConnection>;
  /** Fetch paginated schedule entries using filters. */
  fanScheduleEntries?: Maybe<FanScheduleEntryConnection>;
  /** Fetch a collection of results by search term, entity type, and organization classification ids. */
  fanSearch?: Maybe<Array<Maybe<FanSearchResult>>>;
  /** Fetch form field by GraphQL ID. */
  formFieldById?: Maybe<FormField>;
  /** Fetch broadcast by media stream id. */
  getBroadcastByMediaStreamId?: Maybe<Broadcast>;
  /** Fetch broadcast by schedule entry id. */
  getBroadcastByScheduleEntryId?: Maybe<Broadcast>;
  /** Fetch the public Governing Body data by Id */
  governingBodyHeader?: Maybe<GoverningBodyHeader>;
  /** Fetch highlight reel summaries with pagination */
  highlightReelSummaries?: Maybe<HighlightReelSummaryConnection>;
  /** Fetch highlight reel summaries with pagination */
  highlightReelSummary?: Maybe<HighlightReelSummary>;
  /** Get invite code by id */
  inviteCode?: Maybe<InviteCode>;
  /** Fetches league aggregation information by an organization id or teamId. */
  leagueAggregation?: Maybe<LeagueAggregationInfo>;
  /** Fetches a given league's hierarchy by ID and type. */
  leagueHierarchy?: Maybe<LeagueScaffoldingHierarchy>;
  /** Fetches members of leagues using filters. */
  leagueMembers?: Maybe<LeagueMemberConnection>;
  /** Fetch league pass config by GraphQL ID. */
  leaguePassConfigById?: Maybe<LeaguePassConfig>;
  /** Fetch league pass configs for a given league. */
  leaguePassConfigsByLeague?: Maybe<LeaguePassConfigConnection>;
  /** Fetch the public municipality data by Id */
  municipalityHeader?: Maybe<MunicipalityHeader>;
  /** Fetch the organization branding data for a school or league. If the league entity id is provided, the school id is not required, and vice versa. */
  organizationBranding?: Maybe<OrganizationBranding>;
  /** Checks if an organization has ticketing enabled via the config value in hudl-ticketing */
  organizationHasTicketingEnabled: Scalars['Boolean'];
  /** Fetch pass by GraphQL ID. */
  passById?: Maybe<Pass>;
  /** Fetch pass config by GraphQL ID. */
  passConfigById?: Maybe<PassConfig>;
  /** Fetch pass config renewer by GraphQL ID. */
  passConfigRenewerById?: Maybe<PassConfigRenewer>;
  /** Fetch pass configs by a list of GraphQL IDs. */
  passConfigsByIds?: Maybe<Array<Maybe<PassConfig>>>;
  /** Fetch pass configs for a given Organization ID. */
  passConfigsByOrganizationId?: Maybe<PassConfigConnection>;
  /** Fetch the public Regional Alignment data by Id */
  regionalAlignmentHeader?: Maybe<RegionalAlignmentHeader>;
  /** Fetch active roster members for the current season, and all members for previous seasons */
  roster?: Maybe<Array<Maybe<PublicMember>>>;
  /** Fetch schedule entry summaries by using filters. Summaries include only public information. */
  scheduleEntryPublicSummaries?: Maybe<ScheduleEntryPublicSummaryConnection>;
  /** Fetch a schedule entry summary by using a schedule entry ID. */
  scheduleEntryPublicSummary?: Maybe<ScheduleEntryPublicSummary>;
  /** Fetch the school data by Id */
  school?: Maybe<School>;
  /** Fetch the schools data for a list of internal school Ids */
  schools?: Maybe<Array<Maybe<School>>>;
  /** Fetch schools by their corresponding teams on a Competition Period given a CompetitionPeriodId, with schools returned according to lexicographically paginated TeamIds. */
  schoolsForCompetitionPeriod?: Maybe<SchoolConnection>;
  /** Fetch paginated schools by municipality Id. */
  schoolsForMunicipality?: Maybe<SchoolConnection>;
  /** Fetch schools by their corresponding teams on a Regional Alignment given a CompetitionPeriodId, with schools returned according to lexicographically paginated TeamIds. */
  schoolsForRegionalAlignment?: Maybe<SchoolConnection>;
  /** Fetch season by ID */
  season?: Maybe<Season>;
  /** Fetch season record by ID */
  seasonRecord?: Maybe<SeasonRecord>;
  /** Fetch seasons for team. */
  seasons?: Maybe<Array<Maybe<Season>>>;
  /** Fetch the public Subdivision data by Id */
  subdivisionHeader?: Maybe<SubdivisionHeader>;
  /** Fetch the public Subdivisions associated with a Governing Body Id */
  subdivisionHeadersForGoverningBody?: Maybe<Array<Maybe<SubdivisionHeader>>>;
  /** Fetch the team header data by id */
  teamHeader?: Maybe<TeamHeader>;
  /** Fetch team headers based on a list of Ids. Maximum is 150 */
  teamHeaders?: Maybe<Array<Maybe<TeamHeader>>>;
  /** Fetch paginated team headers by competition period Id. */
  teamHeadersForCompetitionPeriod?: Maybe<TeamHeaderConnection>;
  /** Fetch team headers using filters. */
  teamHeadersForSchool?: Maybe<Array<Maybe<TeamHeader>>>;
  /** Fetch ticket group by GraphQL ID. */
  ticketGroupById?: Maybe<TicketGroup>;
  /** Fetch ticket groups by user id */
  ticketGroupsForCurrentUser?: Maybe<TicketGroupConnection>;
  /** Fetch ticket types by GraphQL ID. */
  ticketTypeById?: Maybe<TicketType>;
  /** Fetch ticketable schedule entry summaries by using filters. These summaries do not have linked ticketed events. */
  ticketableScheduleEntrySummaries?: Maybe<ScheduleEntryPublicSummaryConnection>;
  /** Fetch Access Code */
  ticketedEventAccessCode?: Maybe<TicketedEventAccessCode>;
  /** Fetch ticketed event by GraphQL ID. */
  ticketedEventById?: Maybe<TicketedEvent>;
  /** Fetch ticketed events by GraphQL IDs. */
  ticketedEventsByIds?: Maybe<Array<Maybe<TicketedEvent>>>;
  /** Fetch ticketed events for a given Organization ID. */
  ticketedEventsByOrganizationId?: Maybe<TicketedEventConnection>;
  /** Fetch ticketed events for a given Pass Config ID. */
  ticketedEventsByPassConfigId?: Maybe<TicketedEventConnection>;
  /** Fetch a ticketing experiment by name. */
  ticketingExperimentByName?: Maybe<TicketingExperiment>;
  /** Query for a pricing summarization for a cart of ticketing items including fees. */
  ticketingPricingSummary?: Maybe<TicketingPricingSummary>;
  /** Fetch the vanity urls for a destination. */
  vanityUrlForDestination?: Maybe<Scalars['String']>;
};


export type QueryAdTargetingArgs = {
  adTargetingInput?: InputMaybe<GetAdTargetingInput>;
};


export type QueryAssociatedTicketGroupsForTicketedEventIdArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetAssociatedTicketGroupsWithAccessCodeInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryAthleteAndGuardianRelationshipsArgs = {
  input: GetAthleteAndGuardianRelationshipsInput;
};


export type QueryAthleteProfileArgs = {
  memberId: Scalars['ID'];
};


export type QueryBroadcastArgs = {
  broadcastId?: InputMaybe<Scalars['ID']>;
  internalBroadcastId?: InputMaybe<Scalars['String']>;
};


export type QueryBroadcastsArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetBroadcastsPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryCanonicalUrlForSchoolVanityUrlArgs = {
  input: GetCanonicalUrlForVanityUrlInput;
};


export type QueryCompetitionHeaderArgs = {
  competitionId?: InputMaybe<Scalars['ID']>;
  internalCompetitionId?: InputMaybe<Scalars['String']>;
};


export type QueryCompetitionHeadersForGoverningBodyArgs = {
  internalGoverningBodyId?: InputMaybe<Scalars['String']>;
};


export type QueryCompetitionHeadersForSubdivisionArgs = {
  internalSubdivisionId?: InputMaybe<Scalars['String']>;
};


export type QueryCompetitionPeriodHeaderArgs = {
  competitionPeriodId?: InputMaybe<Scalars['ID']>;
  internalCompetitionPeriodId?: InputMaybe<Scalars['String']>;
};


export type QueryCompetitionPeriodHeadersForCompetitionArgs = {
  internalCompetitionId?: InputMaybe<Scalars['String']>;
};


export type QueryCurrentCompetitionPeriodHeaderForCompetitionArgs = {
  internalCompetitionId?: InputMaybe<Scalars['String']>;
};


export type QueryDiscoverableLeaguePassConfigsByLeagueArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetDiscoverableLeaguePassConfigsByLeagueInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryDiscoverableTicketedEventsForTeamArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetTicketedEventsForTeamInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryFanBroadcastsArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetFanBroadcastsPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryFanHighlightReelsArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetFanHighlightReelsPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryFanScheduleEntriesArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetFanScheduleEntriesPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryFanSearchArgs = {
  input: FanSearchRequestInput;
};


export type QueryFormFieldByIdArgs = {
  formFieldId: Scalars['ID'];
};


export type QueryGetBroadcastByMediaStreamIdArgs = {
  mediaStreamId: Scalars['ID'];
};


export type QueryGetBroadcastByScheduleEntryIdArgs = {
  scheduleEntryId: Scalars['ID'];
};


export type QueryGoverningBodyHeaderArgs = {
  governingBodyId?: InputMaybe<Scalars['ID']>;
  internalGoverningBodyId?: InputMaybe<Scalars['String']>;
};


export type QueryHighlightReelSummariesArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetHighlightReelSummariesInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryHighlightReelSummaryArgs = {
  input: GetHighlightReelSummaryInput;
};


export type QueryInviteCodeArgs = {
  inviteCodeId: Scalars['String'];
};


export type QueryLeagueAggregationArgs = {
  includeAllTeamsForSchool?: InputMaybe<Scalars['Boolean']>;
  schoolId?: InputMaybe<Scalars['String']>;
  teamId?: InputMaybe<Scalars['String']>;
  timeUtc?: InputMaybe<Scalars['DateTime']>;
};


export type QueryLeagueHierarchyArgs = {
  leagueEntityId: Scalars['ID'];
  leagueEntityType: LeagueEntityType;
};


export type QueryLeagueMembersArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetMembersForLeagueInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryLeaguePassConfigByIdArgs = {
  leaguePassConfigId: Scalars['ID'];
};


export type QueryLeaguePassConfigsByLeagueArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetLeaguePassConfigsByLeagueInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryMunicipalityHeaderArgs = {
  internalMunicipalityId?: InputMaybe<Scalars['String']>;
  municipalityId?: InputMaybe<Scalars['ID']>;
};


export type QueryOrganizationBrandingArgs = {
  internalLeagueId?: InputMaybe<Scalars['String']>;
  internalSchoolId?: InputMaybe<Scalars['String']>;
  leagueEntityType?: LeagueEntityType;
};


export type QueryOrganizationHasTicketingEnabledArgs = {
  organizationId: Scalars['String'];
};


export type QueryPassByIdArgs = {
  passId: Scalars['ID'];
};


export type QueryPassConfigByIdArgs = {
  passConfigId: Scalars['ID'];
};


export type QueryPassConfigRenewerByIdArgs = {
  renewerId: Scalars['ID'];
};


export type QueryPassConfigsByIdsArgs = {
  passConfigIds?: InputMaybe<Array<Scalars['ID']>>;
};


export type QueryPassConfigsByOrganizationIdArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input?: InputMaybe<GetPassConfigsByOrganizationIdInput>;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryRegionalAlignmentHeaderArgs = {
  internalRegionalAlignmentId?: InputMaybe<Scalars['String']>;
  regionalAlignmentId?: InputMaybe<Scalars['ID']>;
};


export type QueryRosterArgs = {
  input: GetRosterForTeamInput;
};


export type QueryScheduleEntryPublicSummariesArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetScheduleEntryPublicSummariesInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryScheduleEntryPublicSummaryArgs = {
  scheduleEntryId: Scalars['ID'];
};


export type QuerySchoolArgs = {
  internalSchoolId?: InputMaybe<Scalars['String']>;
  schoolId?: InputMaybe<Scalars['ID']>;
};


export type QuerySchoolsArgs = {
  graphQLSchoolIds?: InputMaybe<Array<Scalars['ID']>>;
  schoolIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QuerySchoolsForCompetitionPeriodArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetSchoolsForCompetitionPeriodByTeamIdsPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QuerySchoolsForMunicipalityArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetSchoolsForMunicipalityPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QuerySchoolsForRegionalAlignmentArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetSchoolsForRegionalAlignmentByTeamIdsPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QuerySeasonArgs = {
  internalSeasonId?: InputMaybe<Scalars['String']>;
  internalTeamId: Scalars['String'];
  seasonId?: InputMaybe<Scalars['ID']>;
};


export type QuerySeasonRecordArgs = {
  seasonRecordId?: InputMaybe<Scalars['String']>;
};


export type QuerySeasonsArgs = {
  teamId: Scalars['ID'];
};


export type QuerySubdivisionHeaderArgs = {
  internalSubdivisionId?: InputMaybe<Scalars['String']>;
  subdivisionId?: InputMaybe<Scalars['ID']>;
};


export type QuerySubdivisionHeadersForGoverningBodyArgs = {
  internalGoverningBodyId?: InputMaybe<Scalars['String']>;
};


export type QueryTeamHeaderArgs = {
  internalTeamId?: InputMaybe<Scalars['String']>;
  teamId?: InputMaybe<Scalars['ID']>;
};


export type QueryTeamHeadersArgs = {
  input: GetTeamHeadersByIdsInput;
};


export type QueryTeamHeadersForCompetitionPeriodArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetTeamHeadersForCompetitionPeriodPaginatedInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryTeamHeadersForSchoolArgs = {
  input: GetTeamHeadersForSchoolInput;
};


export type QueryTicketGroupByIdArgs = {
  ticketGroupId: Scalars['ID'];
};


export type QueryTicketGroupsForCurrentUserArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input?: InputMaybe<TicketGroupsByUserIdInput>;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryTicketTypeByIdArgs = {
  ticketTypeId: Scalars['ID'];
};


export type QueryTicketableScheduleEntrySummariesArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input: GetScheduleEntryPublicSummariesInput;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryTicketedEventAccessCodeArgs = {
  input?: InputMaybe<GetTicketedEventAccessCodeInput>;
};


export type QueryTicketedEventByIdArgs = {
  ticketedEventId: Scalars['ID'];
};


export type QueryTicketedEventsByIdsArgs = {
  ticketedEventIds?: InputMaybe<Array<Scalars['ID']>>;
};


export type QueryTicketedEventsByOrganizationIdArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input?: InputMaybe<GetTicketedEventsByOrganizationIdInput>;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryTicketedEventsByPassConfigIdArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  input?: InputMaybe<GetTicketedEventsByPassConfigIdInput>;
  last?: InputMaybe<Scalars['Int']>;
};


export type QueryTicketingExperimentByNameArgs = {
  experimentName?: InputMaybe<Scalars['String']>;
};


export type QueryTicketingPricingSummaryArgs = {
  lineItems?: InputMaybe<Array<InputMaybe<TicketingPricingLineItemInput>>>;
  source?: InputMaybe<Scalars['String']>;
};


export type QueryVanityUrlForDestinationArgs = {
  destinationId?: InputMaybe<Scalars['String']>;
  destinationType: VanityDestinationType;
};

/** Ticketing entity that has been redeemed */
export type RedemptionEntity = {
  __typename?: 'RedemptionEntity';
  /** The ID of the ticket that was created as a result of redeeming the entity */
  associatedTicketId?: Maybe<Scalars['ID']>;
  /** The GraphQL encoded Id for the redeemed entity */
  entityId: Scalars['ID'];
  /** The type of the redeemed entity */
  ticketingEntityType: Scalars['String'];
};

/** Entity to be redeemed */
export type RedemptionEntityInput = {
  /** The GraphQL encoded Id for the redeemed entity */
  entityId: Scalars['ID'];
  /** The type of the redeemed entity */
  ticketingEntityType: Scalars['String'];
};

/** Input to redeem Tickets and Passes */
export type RedemptionInput = {
  /** The entities being redeemed */
  redemptionEntities: Array<RedemptionEntityInput>;
  /** The method of the redemption */
  redemptionMethod: Scalars['String'];
  /** GraphQL encoded Id of the event the entity is redeemed for */
  ticketedEventId: Scalars['ID'];
};

export type Region = {
  __typename?: 'Region';
  /** The ISO 3166-1 alpha-3 code representing a country */
  countryIso?: Maybe<Scalars['String']>;
  /** The ISO 3166-2 code representing a state or province */
  subdivisionIso?: Maybe<Scalars['String']>;
};

export type RegionalAlignmentHeader = {
  __typename?: 'RegionalAlignmentHeader';
  /** The abbreviation of the Regional Alignment */
  abbreviation?: Maybe<Scalars['String']>;
  /** True if the user can edit the regional alignment. */
  canUserEditLeagueEntity: Scalars['Boolean'];
  /** The ISO 3166-1 alpha-3 code representing the country of the Regional Alignment */
  countryIso?: Maybe<Scalars['String']>;
  /** The Id of the Regional Alignment */
  id: Scalars['ID'];
  /** The internal Id of the Regional Alignment */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the Regional Alignment */
  name?: Maybe<Scalars['String']>;
  /** The organization branding for the public regional alignment. */
  organizationBranding?: Maybe<OrganizationBranding>;
  /** The ISO 3166-2 code representing the state or province of the Regional Alignment */
  subdivisionIso?: Maybe<Scalars['String']>;
  /** The list of team Ids associated with the Regional Alignment */
  teamIds?: Maybe<Array<Maybe<Scalars['String']>>>;
};

/** Representation of an organization program registration. */
export type Registrant = {
  __typename?: 'Registrant';
  /** The date the registrant was created. */
  createdAt: Scalars['DateTime'];
  /** The id of the user who created the registrant. */
  createdBy: Scalars['ID'];
  /** The date the registrant was deleted, if applicable. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The id of the user who deleted the registrant, if applicable. */
  deletedBy?: Maybe<Scalars['ID']>;
  /** The email of the registrant. */
  email: Scalars['String'];
  /** The first name of the registrant. */
  firstName: Scalars['String'];
  /** The id of the guardian reference. */
  guardianIds: Array<Scalars['ID']>;
  /** The Id of the Registrant. */
  id: Scalars['ID'];
  /** Gets installments for a registrant. */
  installments: Array<Installment>;
  /** The last name of the registrant. */
  lastName: Scalars['String'];
  /** The id of the registration reference. */
  registrationId: Scalars['ID'];
  /** The date the registrant was last updated. */
  updatedAt: Scalars['DateTime'];
  /** The id of the user who last updated the registrant. */
  updatedBy: Scalars['ID'];
};

/** Output the eligibility of a registration. */
export type RegistrationEligibilityOutput = {
  __typename?: 'RegistrationEligibilityOutput';
  /** The birth date from which the registration is eligible. */
  birthDateFrom?: Maybe<Scalars['DateTime']>;
  /** The birth date to which the registration is eligible. */
  birthDateTo?: Maybe<Scalars['DateTime']>;
  /** The gender to which the registration is eligible. */
  gender?: Maybe<RegistrationGender>;
  /** The grades of which the registration is eligible. */
  grades?: Maybe<Array<Grade>>;
};

export enum RegistrationGender {
  Any = 'ANY',
  Female = 'FEMALE',
  Male = 'MALE'
}

/** Input parameters for a registration operation. */
export type RegistrationInput = {
  /** The client mutation identifier. */
  clientMutationId?: InputMaybe<Scalars['String']>;
  /** The machine identifier. */
  machineIdentifier: Scalars['String'];
  /** The machine name. */
  machineName?: InputMaybe<Scalars['String']>;
  /** The os version. */
  osVersion?: InputMaybe<Scalars['String']>;
  /** The registration code. */
  registrationCode: Scalars['String'];
  /** The installed version of Sportscode. */
  userVersion: Scalars['String'];
};

/** The result of a registration operation. */
export type RegistrationPayload = {
  __typename?: 'RegistrationPayload';
  /** The response authentication string. */
  authentication?: Maybe<PayloadAuthentication>;
  /** The client mutation identifier. */
  clientMutationId?: Maybe<Scalars['String']>;
};

export enum RelationshipType {
  Favorite = 'FAVORITE',
  Guardian = 'GUARDIAN'
}

/** Representation of a Reserved Seat. */
export type ReservedSeat = {
  __typename?: 'ReservedSeat';
  /** The identifier of the general admission area associated with the reservation */
  generalAdmissionArea?: Maybe<Scalars['String']>;
  /** The row of the reserved seat */
  row?: Maybe<Scalars['String']>;
  /** The identifier of the reserved seat itself */
  seat?: Maybe<Scalars['String']>;
  /** The full identifier of the reserved seat */
  seatIdentifier?: Maybe<Scalars['String']>;
  /** The section of the reserved seat */
  section?: Maybe<Scalars['String']>;
  /** The identifier of the table associated with the reservation */
  table?: Maybe<Scalars['String']>;
};

export enum RestreamStatus {
  Initialized = 'INITIALIZED',
  Started = 'STARTED',
  Stopped = 'STOPPED',
  Uninitialized = 'UNINITIALIZED',
  Unknown = 'UNKNOWN'
}

export type RestreamSummary = {
  __typename?: 'RestreamSummary';
  /** The GraphQL Restream Id. */
  id: Scalars['ID'];
  /** The internal Id of the restream. */
  internalId?: Maybe<Scalars['String']>;
  /** The last known status of the restream. */
  lastKnownStatus: RestreamStatus;
  /** The destination platform of the restream. */
  platformId: Scalars['Int'];
  /** The hudl-livestreaming restream Id of the restream. */
  restreamId?: Maybe<Scalars['String']>;
  /** The stream type of the restream. */
  streamType: StreamType;
};

export enum Role {
  Administrator = 'ADMINISTRATOR',
  Coach = 'COACH',
  /** @deprecated Try to avoid using this. We don't store 'Fan' roles for user records, so prefer to use a nullable Role if 'no role' is a possibility. */
  Fan = 'FAN',
  /** @deprecated caleb - This is going away for now. There are some Insider accounts still, but no more should be getting created. */
  Insider = 'INSIDER',
  /** @deprecated Try to avoid using this. We don't store 'None' roles for user records, so prefer to use a nullable Role if 'no role' is a possibility. */
  None = 'NONE',
  Participant = 'PARTICIPANT',
  /** @deprecated jond - This should be going away...at some point.  However, it needs to continue to be accounted for in code/security checks */
  Recruiter = 'RECRUITER',
  Technique = 'TECHNIQUE'
}

/** Input to redeem Tickets and Passes */
export type ScannedQrCodeResultInput = {
  /** The GQL encoded ID of scanned object */
  id: Scalars['String'];
  /** The version of the QR code when scanned */
  qrCodeVersion: Scalars['String'];
  /** The time the QR code was scanned */
  scannedAt: Scalars['DateTime'];
  /** Status of the scanned QR code */
  status?: InputMaybe<Scalars['String']>;
  /** The type of scanned object */
  type?: InputMaybe<Scalars['String']>;
};

/** Input to redeem Tickets and Passes via scanning */
export type ScanningRedemptionInput = {
  /** Access code that was used for scanning */
  accessCode?: InputMaybe<Scalars['String']>;
  /** Version of the app used for redemption */
  appVersion?: InputMaybe<Scalars['String']>;
  /** Email of the user scanning QR Codes */
  email?: InputMaybe<Scalars['String']>;
  /** First name of the user scanning QR codes */
  firstName?: InputMaybe<Scalars['String']>;
  /** Last name of the user scanning QR codes */
  lastName?: InputMaybe<Scalars['String']>;
  /** QR Codes that were scanned */
  results?: InputMaybe<Array<InputMaybe<ScannedQrCodeResultInput>>>;
  /** GraphQL encoded Id of the event being scanned for */
  ticketedEventId?: InputMaybe<Scalars['String']>;
};

/** A summary for a schedule entry. Primarily used for fan and public facing pages. */
export type ScheduleEntryPublicSummary = {
  __typename?: 'ScheduleEntryPublicSummary';
  /** The broadcast status for the public schedule entry summary */
  broadcastStatus?: Maybe<Scalars['String']>;
  /** The numeric enum value of the game type of the schedule entry. */
  gameType: Scalars['Int'];
  /** The numeric enum value of the gender of the owning team of the schedule entry. */
  genderId: Scalars['Int'];
  /** The Id of the schedule entry summary. */
  id: Scalars['ID'];
  /** The internal Id of the schedule entry summary. */
  internalId?: Maybe<Scalars['String']>;
  /** The internal seasonId for the schedule entry. */
  internalSeasonId?: Maybe<Scalars['String']>;
  /** The internal teamId of the owning team for the schedule entry. */
  internalTeamId: Scalars['String'];
  /** The details of the opponent for the schedule entry. */
  opponentDetails?: Maybe<OpponentDetails>;
  /** The UTC time that the schedule entry is projected to end. */
  projectedEndTime?: Maybe<Scalars['DateTime']>;
  /** The GraphQL encoded Id of the schedule entry that this summary describes. */
  scheduleEntryId: Scalars['ID'];
  /** The numeric enum value of the location of the schedule entry. */
  scheduleEntryLocation: Scalars['Int'];
  /** The numeric enum value of the outcome of the schedule entry */
  scheduleEntryOutcome: Scalars['Int'];
  /** The GraphQL encoded schoolId of the owning team for the schedule entry. */
  schoolId: Scalars['ID'];
  /** One of the team scores for a schedule entry. */
  score1?: Maybe<Scalars['Short']>;
  /** One of the team scores for a schedule entry. */
  score2?: Maybe<Scalars['Short']>;
  /** The GraphQL encoded seasonId for the schedule entry. */
  seasonId: Scalars['ID'];
  /** The sport of owning team of the schedule entry. */
  sport: Sport;
  /** The numeric enum value of the sport of owning team of the schedule entry. */
  sportId: Scalars['Int'];
  /** The GraphQL encoded teamId of the owning team for the schedule entry. */
  teamId: Scalars['ID'];
  /** The UTC time that the schedule entry will start. */
  timeUtc: Scalars['DateTime'];
  /** Data about the tournament this schedule entry belongs to (if applicable). */
  tournamentMetadata?: Maybe<TournamentMetadata>;
};

/** A connection to a list of items. */
export type ScheduleEntryPublicSummaryConnection = {
  __typename?: 'ScheduleEntryPublicSummaryConnection';
  /** A list of edges. */
  edges?: Maybe<Array<ScheduleEntryPublicSummaryEdge>>;
  items?: Maybe<Array<ScheduleEntryPublicSummary>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<ScheduleEntryPublicSummary>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type ScheduleEntryPublicSummaryEdge = {
  __typename?: 'ScheduleEntryPublicSummaryEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<ScheduleEntryPublicSummary>;
};

export enum ScheduleEntrySortType {
  ScheduleEntryDate = 'SCHEDULE_ENTRY_DATE'
}

export type School = {
  __typename?: 'School';
  /** Abbreviation of school name */
  abbreviation?: Maybe<Scalars['String']>;
  /** School's first address line */
  addressLine1?: Maybe<Scalars['String']>;
  /** School's second address line */
  addressLine2?: Maybe<Scalars['String']>;
  /** The banner image uri of the organization */
  bannerImageUri?: Maybe<Scalars['String']>;
  /** A boolean representing if the current user can edit the current school. This will always be false for unregistered users. */
  canUserEditSchool: Scalars['Boolean'];
  /** City where school is located */
  city?: Maybe<Scalars['String']>;
  /** Country where school is located */
  country?: Maybe<Scalars['String']>;
  /** The two-character ISO 3166-1 alpha-2 country code corresponding to this organization's country */
  countryIsoAlpha2?: Maybe<Scalars['String']>;
  /** The three-character ISO 3166-1 alpha-3 country code corresponding to this organization's country */
  countryIsoAlpha3?: Maybe<Scalars['String']>;
  /** Full name of school */
  fullName?: Maybe<Scalars['String']>;
  /** The Id of the school */
  id: Scalars['ID'];
  /** The internal Id of the school */
  internalId?: Maybe<Scalars['String']>;
  /** If school is active or not */
  isActive: Scalars['Boolean'];
  /** If school is enabled or not */
  isEnabled: Scalars['Boolean'];
  /** Allows league name to be seen and edited from edit profile page in hudl-profiles */
  leagueName?: Maybe<Scalars['String']>;
  /** The mascot of the organization */
  mascot?: Maybe<Scalars['String']>;
  /** The messaging participants' metadata for a school. */
  messagingParticipantsMetadata?: Maybe<MessagingParticipantsMetadataConnection>;
  /** Maps to how an organization is classified */
  orgClassificationId: Scalars['Int'];
  /** The url for the school's public organization profile. */
  organizationProfileUrl?: Maybe<Scalars['String']>;
  /** Program for an organization by program Id. */
  organizationProgram?: Maybe<OrganizationProgram>;
  /** Programs for an organization. */
  organizationPrograms: Array<Maybe<OrganizationProgram>>;
  /** The settings of the organization */
  organizationSettings?: Maybe<OrganizationSettings>;
  /** School's primary color */
  primaryColor?: Maybe<Scalars['String']>;
  /** The profile image uri of the organization */
  profileImageUri?: Maybe<Scalars['String']>;
  /** School's secondary color */
  secondaryColor?: Maybe<Scalars['String']>;
  /** First 20 characters of school name */
  shortName?: Maybe<Scalars['String']>;
  /** State where school is located */
  state?: Maybe<Scalars['String']>;
  /** The teams for a school. */
  teamHeaders?: Maybe<Array<Maybe<TeamHeader>>>;
  /** The time zone of the organization */
  timeZone?: Maybe<Scalars['String']>;
  /** The vanity url for the school's public organization profile. */
  vanityUrl?: Maybe<Scalars['String']>;
  /** Zip code where school is located */
  zipCode?: Maybe<Scalars['String']>;
};


export type SchoolMessagingParticipantsMetadataArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  last?: InputMaybe<Scalars['Int']>;
};


export type SchoolOrganizationProgramArgs = {
  organizationProgramId: Scalars['String'];
};

/** A connection to a list of items. */
export type SchoolConnection = {
  __typename?: 'SchoolConnection';
  /** A list of edges. */
  edges?: Maybe<Array<SchoolEdge>>;
  items?: Maybe<Array<School>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<School>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type SchoolEdge = {
  __typename?: 'SchoolEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<School>;
};

/** Representation of a Scoreboard Entry. */
export type ScoreboardEntry = GenericScoreboardEntry | VolleyballScoreboardEntry;

/** Representation of a Scoreboard Session. */
export type ScoreboardSession = {
  __typename?: 'ScoreboardSession';
  /** The timestamp that this Scoreboard Session was created at. */
  createdAt: Scalars['DateTime'];
  /** The timestamp that this Scoreboard Session was deleted at. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The timestamp that this Scoreboard Session was finalized at. */
  finalizedAt?: Maybe<Scalars['DateTime']>;
  /** The GraphQL id of the Scoreboard Session. */
  id?: Maybe<Scalars['String']>;
  /** Most recent Scoreboard Entry for this Scoreboard Session. */
  latestEntry?: Maybe<ScoreboardEntry>;
  /** The id of the associated Media Stream. */
  mediaStreamId?: Maybe<Scalars['String']>;
  /** The id of the associated Schedule Entry Id */
  scheduleEntryId?: Maybe<Scalars['String']>;
  /** List of all settings updates throughout the Scoreboard Session. */
  settings?: Maybe<Array<Maybe<ScoreboardSettings>>>;
  /** The sport being played for the Scoreboard Session. */
  sport: Sport;
  /** The id of the team that owns Scoreboard Session. */
  teamId?: Maybe<Scalars['String']>;
  /** The timestamp that this Scoreboard Session was last updated at. */
  updatedAt?: Maybe<Scalars['DateTime']>;
};

/** Representation of a Scoreboard Settings object. */
export type ScoreboardSettings = GenericScoreboardSettings | VolleyballScoreboardSettings;

export enum SearchItemResultType {
  Athlete = 'ATHLETE',
  Club = 'CLUB',
  GoverningBody = 'GOVERNING_BODY',
  Municipality = 'MUNICIPALITY',
  Organization = 'ORGANIZATION',
  RegionalAlignment = 'REGIONAL_ALIGNMENT',
  Unknown = 'UNKNOWN'
}

export enum SearchItemType {
  Athlete = 'ATHLETE',
  Club = 'CLUB',
  GoverningBody = 'GOVERNING_BODY',
  Municipality = 'MUNICIPALITY',
  Organization = 'ORGANIZATION',
  RegionalAlignment = 'REGIONAL_ALIGNMENT'
}

export type Season = {
  __typename?: 'Season';
  /** Season description {SeasonYear} - {SeasonYear + 1} */
  description?: Maybe<Scalars['String']>;
  /** The internal id of the season. */
  internalId?: Maybe<Scalars['String']>;
  /** The internal team Id of the team who owns this season */
  internalTeamId?: Maybe<Scalars['String']>;
  /** Flag to verify if max preps was imported */
  isMaxPrepsImported?: Maybe<Scalars['Boolean']>;
  roster?: Maybe<Array<Maybe<PublicMember>>>;
  /** The GraphQL id of the season. */
  seasonId: Scalars['ID'];
  seasonRecord?: Maybe<SeasonRecord>;
  /** Season value maps to internal season id */
  value?: Maybe<Scalars['String']>;
  /** The year that the season was started. */
  year: Scalars['Int'];
};

export type SeasonRecord = {
  __typename?: 'SeasonRecord';
  /** Date of most recent modification */
  dateModified: Scalars['DateTime'];
  /** Number of draws for the season */
  draws: Scalars['Int'];
  /** Number of losses for the season */
  losses: Scalars['Int'];
  /** The InternalId for the season */
  seasonId?: Maybe<Scalars['String']>;
  /** Number of wins for the season */
  wins: Scalars['Int'];
};

/** Representation of a share history. */
export type ShareHistory = {
  __typename?: 'ShareHistory';
  /** Date the share history was created. */
  createdAt: Scalars['DateTime'];
  /** Date the share history was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The GraphQL id of the share history. */
  id: Scalars['ID'];
  /** The unique identifier for the new ticket group. */
  newTicketGroupId: Scalars['ID'];
  /** The unique identifier for the original ticket group. */
  originalTicketGroupId: Scalars['ID'];
  /** The list of entities shared */
  sharedEntities: Array<SharedEntity>;
  /** Date the share history was updated. */
  updatedAt: Scalars['DateTime'];
};

/** Representation of an input to share ticketing entities. */
export type ShareTicketingEntitiesInput = {
  /** The id associated with the original ticket group. */
  originalTicketGroupId: Scalars['ID'];
  /** List of entities shared. */
  sharedEntities: Array<SharedEntityInput>;
};

/** Representation of a shared ticketing entity. */
export type SharedEntity = {
  __typename?: 'SharedEntity';
  /** The ID of the shared entity. */
  entityId: Scalars['ID'];
  /** The type of the shared entity. */
  entityType: Scalars['String'];
};

/** Representation of the shared entities in a ticket group. */
export type SharedEntityInput = {
  /** The ID of the shared entity. */
  entityId: Scalars['ID'];
  /** The type of the shared entity. */
  entityType: Scalars['String'];
};

export enum Sport {
  AustralianRulesFootball = 'AUSTRALIAN_RULES_FOOTBALL',
  AustralianRulesFootballRecruiting = 'AUSTRALIAN_RULES_FOOTBALL_RECRUITING',
  Badminton = 'BADMINTON',
  BadmintonRecruiting = 'BADMINTON_RECRUITING',
  Baseball = 'BASEBALL',
  BaseballRecruiting = 'BASEBALL_RECRUITING',
  Basketball = 'BASKETBALL',
  BasketballRecruiting = 'BASKETBALL_RECRUITING',
  CheerAndSpirit = 'CHEER_AND_SPIRIT',
  CheerAndSpiritRecruiting = 'CHEER_AND_SPIRIT_RECRUITING',
  Cricket = 'CRICKET',
  CricketRecruiting = 'CRICKET_RECRUITING',
  CrossCountry = 'CROSS_COUNTRY',
  CrossCountryRecruiting = 'CROSS_COUNTRY_RECRUITING',
  Cycling = 'CYCLING',
  CyclingRecruiting = 'CYCLING_RECRUITING',
  DanceAndDrill = 'DANCE_AND_DRILL',
  DanceAndDrillRecruiting = 'DANCE_AND_DRILL_RECRUITING',
  Fencing = 'FENCING',
  FencingRecruiting = 'FENCING_RECRUITING',
  FieldHockey = 'FIELD_HOCKEY',
  FieldHockeyRecruiting = 'FIELD_HOCKEY_RECRUITING',
  Football = 'FOOTBALL',
  FootballRecruiting = 'FOOTBALL_RECRUITING',
  Golf = 'GOLF',
  GolfRecruiting = 'GOLF_RECRUITING',
  Gymnastics = 'GYMNASTICS',
  GymnasticsRecruiting = 'GYMNASTICS_RECRUITING',
  Handball = 'HANDBALL',
  HandballRecruiting = 'HANDBALL_RECRUITING',
  IceHockey = 'ICE_HOCKEY',
  IceHockeyRecruiting = 'ICE_HOCKEY_RECRUITING',
  Lacrosse = 'LACROSSE',
  LacrosseRecruiting = 'LACROSSE_RECRUITING',
  Netball = 'NETBALL',
  NetballRecruiting = 'NETBALL_RECRUITING',
  NoSport = 'NO_SPORT',
  Other = 'OTHER',
  PerformingArts = 'PERFORMING_ARTS',
  PerformingArtsRecruiting = 'PERFORMING_ARTS_RECRUITING',
  Rugby = 'RUGBY',
  RugbyLeague = 'RUGBY_LEAGUE',
  RugbyLeagueRecruiting = 'RUGBY_LEAGUE_RECRUITING',
  RugbyRecruiting = 'RUGBY_RECRUITING',
  RugbyUnion = 'RUGBY_UNION',
  RugbyUnionRecruiting = 'RUGBY_UNION_RECRUITING',
  SailingAndYachting = 'SAILING_AND_YACHTING',
  SailingAndYachtingRecruiting = 'SAILING_AND_YACHTING_RECRUITING',
  Soccer = 'SOCCER',
  SoccerRecruiting = 'SOCCER_RECRUITING',
  Softball = 'SOFTBALL',
  SoftballRecruiting = 'SOFTBALL_RECRUITING',
  Squash = 'SQUASH',
  SquashRecruiting = 'SQUASH_RECRUITING',
  Surfing = 'SURFING',
  SurfingRecruiting = 'SURFING_RECRUITING',
  SwimmingAndDiving = 'SWIMMING_AND_DIVING',
  SwimmingAndDivingRecruiting = 'SWIMMING_AND_DIVING_RECRUITING',
  Tennis = 'TENNIS',
  TennisRecruiting = 'TENNIS_RECRUITING',
  TenpinBowling = 'TENPIN_BOWLING',
  TenpinBowlingRecruiting = 'TENPIN_BOWLING_RECRUITING',
  Track = 'TRACK',
  TrackRecruiting = 'TRACK_RECRUITING',
  Volleyball = 'VOLLEYBALL',
  VolleyballRecruiting = 'VOLLEYBALL_RECRUITING',
  WaterPolo = 'WATER_POLO',
  WaterPoloRecruiting = 'WATER_POLO_RECRUITING',
  Wrestling = 'WRESTLING',
  WrestlingRecruiting = 'WRESTLING_RECRUITING'
}

export enum StreamType {
  EdgeData = 'EDGE_DATA',
  ExtraWideTactical = 'EXTRA_WIDE_TACTICAL',
  GameData = 'GAME_DATA',
  MosaicPlayerFeed = 'MOSAIC_PLAYER_FEED',
  Panoramic = 'PANORAMIC',
  PanoramicDebug = 'PANORAMIC_DEBUG',
  RawBallData = 'RAW_BALL_DATA',
  RawHistogramData = 'RAW_HISTOGRAM_DATA',
  RawVideo = 'RAW_VIDEO',
  ScoreboardVideo = 'SCOREBOARD_VIDEO',
  StaticCamera = 'STATIC_CAMERA',
  Tactical = 'TACTICAL',
  TacticalDebug = 'TACTICAL_DEBUG',
  Unknown = 'UNKNOWN'
}

export type SubdivisionHeader = {
  __typename?: 'SubdivisionHeader';
  /** The Id of the Governing Body the Subdivision belongs to */
  governingBodyId?: Maybe<Scalars['String']>;
  /** The graphQL Id of the Subdivision */
  id: Scalars['ID'];
  /** The internal Id of the Subdivision */
  internalId?: Maybe<Scalars['String']>;
  /** The name of the Subdivision */
  name?: Maybe<Scalars['String']>;
};

export type Subscription = {
  __typename?: 'Subscription';
  id?: Maybe<Scalars['String']>;
};

export type TaggedFeedUser = {
  __typename?: 'TaggedFeedUser';
  feedUserId?: Maybe<TaggedFeedUserId>;
  followersCount: Scalars['Long'];
  friendsCount: Scalars['Long'];
  lowestResImageUrl?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
};

export type TaggedFeedUserId = {
  __typename?: 'TaggedFeedUserId';
  relatedId?: Maybe<Scalars['String']>;
  type: FeedUserType;
};

/** A summary of team information that is public. */
export type TeamHeader = {
  __typename?: 'TeamHeader';
  /** Team additional Fields */
  additionalFields?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Background images for team. */
  backgroundImages?: Maybe<Array<Maybe<BackgroundImage>>>;
  /** Current Season for a team */
  currentSeason?: Maybe<Season>;
  /** Current season year */
  currentSeasonYear?: Maybe<Scalars['Int']>;
  /** Discoverable Pass Configs */
  discoverablePassConfigs?: Maybe<DiscoverablePassConfigsConnection>;
  /** Discoverable Ticketed Events */
  discoverableTicketedEvents?: Maybe<DiscoverableTicketedEventsConnection>;
  /** The gender of the team. */
  gender: Gender;
  /** GraphQL Encoded Team Id */
  id: Scalars['ID'];
  /** Internal teamId */
  internalId?: Maybe<Scalars['String']>;
  /** Is the user a coach or admin for the team. */
  isCoachOrAdmin: Scalars['Boolean'];
  /** Indicates whether the team's profile is hidden. */
  isTeamProfileHidden: Scalars['Boolean'];
  /** Indicates whether the team is a test team. */
  isTestTeam: Scalars['Boolean'];
  /** Team logo */
  logo?: Maybe<Scalars['String']>;
  /** Team name */
  name?: Maybe<Scalars['String']>;
  /** The summary of the team's organization. */
  organizationHeader?: Maybe<OrganizationHeader>;
  /** @deprecated Use OrganizationHeader.OrganizationName instead */
  organizationName?: Maybe<Scalars['String']>;
  /** Positions */
  positions?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The primary color of the team. */
  primaryColor?: Maybe<Scalars['String']>;
  /** Seasons for the team. */
  seasons?: Maybe<Array<Maybe<Season>>>;
  /** The secondary color of the team. */
  secondaryColor?: Maybe<Scalars['String']>;
  /** The sport of the team. */
  sport: Sport;
  /** Team Level */
  teamLevel: TeamLevel;
  /** The url for the teams's public team profile. */
  teamProfileUrl?: Maybe<Scalars['String']>;
};


/** A summary of team information that is public. */
export type TeamHeaderDiscoverablePassConfigsArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  last?: InputMaybe<Scalars['Int']>;
};


/** A summary of team information that is public. */
export type TeamHeaderDiscoverableTicketedEventsArgs = {
  after?: InputMaybe<Scalars['Cursor']>;
  before?: InputMaybe<Scalars['Cursor']>;
  first?: InputMaybe<Scalars['Int']>;
  last?: InputMaybe<Scalars['Int']>;
};

/** A connection to a list of items. */
export type TeamHeaderConnection = {
  __typename?: 'TeamHeaderConnection';
  /** A list of edges. */
  edges?: Maybe<Array<TeamHeaderEdge>>;
  items?: Maybe<Array<TeamHeader>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<TeamHeader>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type TeamHeaderEdge = {
  __typename?: 'TeamHeaderEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<TeamHeader>;
};

export enum TeamLevel {
  Freshman = 'FRESHMAN',
  JuniorVarsity = 'JUNIOR_VARSITY',
  Other = 'OTHER',
  OtherNonHs = 'OTHER_NON_HS',
  Sophomore = 'SOPHOMORE',
  Varsity = 'VARSITY'
}

/** Representation of a terminal connection token. */
export type TerminalConnectionToken = {
  __typename?: 'TerminalConnectionToken';
  /** The client secret of the connection token. */
  clientSecret?: Maybe<Scalars['String']>;
};

/** Representation of a terms of service for ticketing. */
export type TermsOfService = {
  __typename?: 'TermsOfService';
  /** The effective date of the terms of service. */
  effectiveDate: Scalars['DateTime'];
  /** The legal text of the terms of service. */
  text?: Maybe<Scalars['String']>;
  /** The title of the terms of service. */
  title?: Maybe<Scalars['String']>;
  /** The semantic version (major.minor) of the terms of service. */
  version?: Maybe<Scalars['String']>;
};

/** Representation of a ticket. */
export type Ticket = {
  __typename?: 'Ticket';
  /** Date the ticket was created. */
  createdAt: Scalars['DateTime'];
  /** Date the ticket was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The email of the person associated with this ticket. */
  email?: Maybe<Scalars['String']>;
  /** The first name of the person associated with this ticket. */
  firstName?: Maybe<Scalars['String']>;
  /** The GraphQL id of the ticket. */
  id: Scalars['ID'];
  /** The last name of the person associated with this ticket. */
  lastName?: Maybe<Scalars['String']>;
  /** The unique identifier for the Pass associated with this ticket. */
  passId?: Maybe<Scalars['ID']>;
  /** The payment type used to purchase this ticket. */
  paymentType?: Maybe<Scalars['String']>;
  /** The QR Code data for this ticket */
  qrCodeData?: Maybe<Scalars['String']>;
  /**
   * The url of the QR code associated with this ticket.
   * @deprecated This property will no longer return a value. Use the QRCodeData property instead.
   */
  qrCodeUrl?: Maybe<Scalars['String']>;
  /** The timestamp when this ticket was redeemed. Will be null if the ticket has not yet been redeemed. */
  redeemedAt?: Maybe<Scalars['DateTime']>;
  /** Whether or not the ticket is refundable. Taken from the related Ticketed Event. */
  refundable: Scalars['Boolean'];
  /** The reserved seat associated with this ticket. */
  reservedSeat?: Maybe<ReservedSeat>;
  /** Date the ticket was shared. */
  sharedAt?: Maybe<Scalars['DateTime']>;
  /** The source where the ticket was purchased. */
  source?: Maybe<Scalars['String']>;
  /** The status of the ticket. */
  ticketStatus?: Maybe<Scalars['String']>;
  /** The unique identifier for the Ticket Type associated with this ticket. */
  ticketTypeId?: Maybe<Scalars['ID']>;
  /** The unique identifier for the Ticketed Event this ticket is for. */
  ticketedEventId: Scalars['ID'];
  /** Date the ticket was updated. */
  updatedAt: Scalars['DateTime'];
  /** The Hudl user id of the person associated with this ticket, if one exists. */
  userId?: Maybe<Scalars['String']>;
};

/** Representation of a ticket group. */
export type TicketGroup = {
  __typename?: 'TicketGroup';
  /** The timestamp when this ticket group was created. */
  createdAt: Scalars['DateTime'];
  /** The currency associated with the OrderTotalInCents of this ticket group. */
  currency?: Maybe<Scalars['String']>;
  /** The timestamp when this ticket group was deleted. Will be null if the ticket is not deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The email of the person associated with this ticket group. */
  email?: Maybe<Scalars['String']>;
  /** The first name of the person associated with this ticket group */
  firstName?: Maybe<Scalars['String']>;
  /** The responses to the form fields associated with this ticket group. */
  formFieldResponses?: Maybe<Array<Maybe<FormFieldResponse>>>;
  /** The GraphQL id of the ticket group. */
  id: Scalars['ID'];
  /** The item categories associated with the ticket group. */
  itemCategories?: Maybe<Array<Scalars['String']>>;
  /** The last name of the person associated with this ticket group */
  lastName?: Maybe<Scalars['String']>;
  /** The total cost of the ticket group in cents. */
  orderTotalInCents?: Maybe<Scalars['Int']>;
  /** The list of pass ids associated with this ticket group. */
  passIds?: Maybe<Array<Scalars['ID']>>;
  /** The passes associated with this ticket group */
  passes?: Maybe<Array<Maybe<Pass>>>;
  /** The payment type used for this ticket group. */
  paymentType?: Maybe<Scalars['String']>;
  /** The source where the ticket group was purchased. */
  source?: Maybe<Scalars['String']>;
  /** The more readable but not necessarily unique reference/identifier for this ticket group. */
  ticketGroupReference?: Maybe<Scalars['String']>;
  /** The list of ticket ids associated with this ticket group. */
  ticketIds?: Maybe<Array<Scalars['ID']>>;
  /** The tickets associated with this ticket group */
  tickets?: Maybe<Array<Maybe<Ticket>>>;
  /** The timestamp when this ticket group was last updated. */
  updatedAt: Scalars['DateTime'];
  /** The Hudl user id of the person associated with this ticket group, if one exists */
  userId?: Maybe<Scalars['String']>;
};

/** A connection to a list of items. */
export type TicketGroupConnection = {
  __typename?: 'TicketGroupConnection';
  /** A list of edges. */
  edges?: Maybe<Array<TicketGroupEdge>>;
  items?: Maybe<Array<TicketGroup>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<TicketGroup>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type TicketGroupEdge = {
  __typename?: 'TicketGroupEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node: TicketGroup;
};

export enum TicketGroupItemFilterType {
  Passes = 'PASSES',
  Tickets = 'TICKETS'
}

export enum TicketGroupSortType {
  TicketGroupPurchaserLastName = 'TICKET_GROUP_PURCHASER_LAST_NAME',
  TicketGroupPurchaseDate = 'TICKET_GROUP_PURCHASE_DATE'
}

/** Representation of an input to fetch ticket groups associated with a user. */
export type TicketGroupsByUserIdInput = {
  /** Value used to start a page of ticket groups. */
  after?: InputMaybe<Scalars['Cursor']>;
  /** Value used to end a page of ticket groups. */
  before?: InputMaybe<Scalars['Cursor']>;
  /** Filters for Ticket Groups created before this date. */
  endDate?: InputMaybe<Scalars['DateTime']>;
  /** Number of ticket groups requested, taken from the start of the total list. Also used for paging. */
  first?: InputMaybe<Scalars['Int']>;
  /** Filters for ticket groups with certain items in the ticket group. */
  itemFilterType?: InputMaybe<TicketGroupItemFilterType>;
  /** Number of ticket groups requested, taken from the end of the total list. Also used for paging. */
  last?: InputMaybe<Scalars['Int']>;
  /** If true, will sort results ascending instead of descending. */
  sortByAscending: Scalars['Boolean'];
  /** Used to determine how to sort the resulting summaries. */
  sortType: TicketGroupSortType;
  /** Filters for Ticket Groups created after this date. */
  startDate?: InputMaybe<Scalars['DateTime']>;
};

/** Representation of a ticket type. */
export type TicketType = {
  __typename?: 'TicketType';
  /** Date the ticket type was created. */
  createdAt: Scalars['DateTime'];
  /** The currency associated with the priceInCents of the ticket type. */
  currency?: Maybe<Scalars['String']>;
  /** Date the ticket type was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The GraphQL id of the ticket type. */
  id: Scalars['ID'];
  /** The name of the ticket type. */
  name?: Maybe<Scalars['String']>;
  /** The organization associated with the ticket type. */
  organizationId?: Maybe<Scalars['String']>;
  /** The price in cents of the ticket type. */
  priceInCents: Scalars['Int'];
  /** The quantity of the ticket type. */
  quantity?: Maybe<Scalars['Int']>;
  /** Date the ticket type was updated. */
  updatedAt: Scalars['DateTime'];
};

/** Representation of a ticket type pricing summary including information about the Hudl fees applied. */
export type TicketTypePricingSummary = {
  __typename?: 'TicketTypePricingSummary';
  /** The currency associated with the priceInCents of the ticket type. */
  currency?: Maybe<Scalars['String']>;
  /** The Hudl Fee applied to the ticket purchase in cents */
  hudlFeeInCents: Scalars['Int'];
  /** The price of the ticket type in cents, factoring in price overrides set on the event */
  priceInCents: Scalars['Int'];
  /** The price of the ticket type in cents including the Hudl Fee */
  priceInCentsWithHudlFee: Scalars['Int'];
  /** Whether the Hudl fee should be displayed to the user. */
  shouldShowFee: Scalars['Boolean'];
  /** The ID of the ticket type. */
  ticketTypeId: Scalars['ID'];
  /** The ID of the ticketed event. */
  ticketedEventId: Scalars['ID'];
};

/** Representation of a ticket type referenced by a ticketed event. */
export type TicketTypeReference = {
  __typename?: 'TicketTypeReference';
  /** The price override to the referenced ticket type. */
  priceOverride?: Maybe<Scalars['Int']>;
  /** The quantity override to the referenced ticket type. */
  quantityOverride?: Maybe<Scalars['Int']>;
  /** The seating chart category IDs for the referenced ticket type. */
  seatingChartCategoryIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The GraphQL id of the referenced ticket type. */
  ticketTypeId?: Maybe<Scalars['ID']>;
};

/** Representation of a ticketable event */
export type TicketedEvent = {
  __typename?: 'TicketedEvent';
  /** The pass configurations that are associated with a given ticketed event */
  associatedPassConfigs?: Maybe<Array<Maybe<PassConfig>>>;
  /** Date the ticketed event was created. */
  createdAt: Scalars['DateTime'];
  /** The date/time the ticketed event takes place. */
  date: Scalars['DateTime'];
  /** Date the ticketed event was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The description of the ticketed event. */
  description?: Maybe<Scalars['String']>;
  /** The status of the ticketed event. */
  eventStatus?: Maybe<Scalars['String']>;
  /** The fee strategy applied to the ticketed event. */
  feeStrategy?: Maybe<Scalars['String']>;
  /** The IDs of the form fields associated with the ticketed event. */
  formFieldIds?: Maybe<Array<Scalars['ID']>>;
  /** The form fields associated with the ticketed event */
  formFields?: Maybe<Array<Maybe<FormField>>>;
  /** The gender of the teams participating in the ticketed event. */
  gender?: Maybe<Scalars['String']>;
  /** The GraphQL id of the ticketed event. */
  id: Scalars['ID'];
  /** List of event entries linked to the ticketed event. */
  linkedEntries?: Maybe<Array<Maybe<LinkedEntry>>>;
  /** The name of the ticketed event. */
  name?: Maybe<Scalars['String']>;
  /** The organization associated with the ticketed event. */
  organizationId: Scalars['ID'];
  /** List of the team ids of teams participating in the ticketed event. */
  participatingTeamIds?: Maybe<Array<Maybe<Scalars['ID']>>>;
  /** The quantity remaining for each ticket type associated with a ticketed event */
  quantityRemainingForTicketTypes?: Maybe<Array<Maybe<QuantityRemainingOutput>>>;
  /** Is the ticketed event refundable. */
  refundable?: Maybe<Scalars['Boolean']>;
  /** The ID of the seating chart event associated with this ticketed event. */
  seatingChartEventId?: Maybe<Scalars['String']>;
  /** The sport being played at the ticketed event. */
  sport?: Maybe<Scalars['String']>;
  /** The pricing summaries of the ticket types associated with the event */
  ticketTypePricingSummaries?: Maybe<Array<Maybe<TicketTypePricingSummary>>>;
  /** Referenced ticket types for the event */
  ticketTypeReferences?: Maybe<Array<Maybe<TicketTypeReference>>>;
  /** The hydrated ticket types associated with this ticketed event */
  ticketTypes?: Maybe<Array<Maybe<TicketType>>>;
  /** The time zone identifier for when the ticketed event takes place. Must be a valid IANA time zone identifier. */
  timeZoneIdentifier?: Maybe<Scalars['String']>;
  /** Date the ticketed event was updated. */
  updatedAt: Scalars['DateTime'];
  /** The ID of the venue configuration the ticketed event is being held at. */
  venueConfigurationId?: Maybe<Scalars['ID']>;
  /**
   * The ID of the venue the ticketed event is being held at.
   * @deprecated Use VenueConfigurationId instead.
   */
  venueId?: Maybe<Scalars['String']>;
  /** The visibility of the ticketed event. */
  visibility?: Maybe<Scalars['String']>;
};

/** Representation of an Access Code */
export type TicketedEventAccessCode = {
  __typename?: 'TicketedEventAccessCode';
  /** The Access Code associated with the organization */
  accessCode?: Maybe<Scalars['String']>;
  /** Date the Access Code was created. */
  createdAt: Scalars['DateTime'];
  /** Date the Access Code was deleted. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The GraphQL id of the access code. */
  id: Scalars['ID'];
  organization?: Maybe<School>;
  /** The organization associated with the Access Code. */
  organizationId: Scalars['ID'];
  /** Date the Access Code was updated. */
  updatedAt: Scalars['DateTime'];
};

/** A connection to a list of items. */
export type TicketedEventConnection = {
  __typename?: 'TicketedEventConnection';
  /** A list of edges. */
  edges?: Maybe<Array<TicketedEventEdge>>;
  items?: Maybe<Array<TicketedEvent>>;
  /** A flattened list of the nodes. */
  nodes?: Maybe<Array<Maybe<TicketedEvent>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** Identifies the total count of items in the connection. */
  totalCount: Scalars['Int'];
};

/** An edge in a connection. */
export type TicketedEventEdge = {
  __typename?: 'TicketedEventEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['String'];
  /** The item at the end of the edge. */
  node?: Maybe<TicketedEvent>;
};

export enum TicketedEventSortType {
  TicketedEventDate = 'TICKETED_EVENT_DATE'
}

/** Input to create a cash checkout for a cart of ticketing items. */
export type TicketingCashCheckoutInput = {
  /** The access code used to log in to the app. */
  accessCode: Scalars['String'];
  /** If true, will redeem the ticket at the time of creation */
  autoRedeemTickets?: InputMaybe<Scalars['Boolean']>;
  /** A unique identifier for the checkout session that is used to prevent duplicate checkouts. */
  checkoutSessionId: Scalars['String'];
  /** The checksum for the cash checkout request. */
  checksum: Scalars['String'];
  /** The email of the checking out person */
  email?: InputMaybe<Scalars['String']>;
  /** If true, will email the order to purchaser email address */
  emailOrder?: InputMaybe<Scalars['Boolean']>;
  /** The first name of the checking out person */
  firstName?: InputMaybe<Scalars['String']>;
  /** The form field responses for the checkout */
  formFieldResponses?: InputMaybe<Array<InputMaybe<FormFieldResponseInput>>>;
  /** The last name of the checking out person */
  lastName?: InputMaybe<Scalars['String']>;
  /** A list of ticketing items to include in the checkout. */
  lineItems: Array<InputMaybe<TicketingPricingLineItemInput>>;
  /** The seats.io channel ids of the channels that the hold is associated with. Only set for pass renewals. */
  seatsDotIoChannelIds?: InputMaybe<Array<Scalars['String']>>;
  /** The token identifying the hold in the seats.io system on the seats selected for this checkout */
  seatsDotIoHoldToken?: InputMaybe<Scalars['String']>;
  /** The source where the checkout is happening */
  source?: InputMaybe<Scalars['String']>;
  /** Indicates terms of service agreement, if true will save agreement with purchaser details */
  tosAgreement?: InputMaybe<Scalars['Boolean']>;
  /** The total amount in cents to charge for the checkout. */
  totalInCents: Scalars['Int'];
  /** The Hudl user id of the checking out person, if one exists */
  userId?: InputMaybe<Scalars['String']>;
};

/** Input to create a checkout for a cart of ticketing items. */
export type TicketingCheckoutInput = {
  /** If true, will redeem the ticket at the time of creation */
  autoRedeemTickets?: InputMaybe<Scalars['Boolean']>;
  /** The email of the checking out person */
  email?: InputMaybe<Scalars['String']>;
  /** If true, will email the order to purchaser email address */
  emailOrder?: InputMaybe<Scalars['Boolean']>;
  /** The first name of the checking out person */
  firstName?: InputMaybe<Scalars['String']>;
  /** The form field responses for the checkout */
  formFieldResponses?: InputMaybe<Array<InputMaybe<FormFieldResponseInput>>>;
  /** The Hudl payment token id to use for the checkout. */
  hudlPaymentTokenId?: InputMaybe<Scalars['String']>;
  /** The last name of the checking out person */
  lastName?: InputMaybe<Scalars['String']>;
  /** A list of ticketing items to include in the checkout. */
  lineItems: Array<InputMaybe<TicketingPricingLineItemInput>>;
  /** The seats.io channel ids of the channels that the hold is associated with. Only set for pass renewals. */
  seatsDotIoChannelIds?: InputMaybe<Array<Scalars['String']>>;
  /** The token identifying the hold in the seats.io system on the seats selected for this checkout */
  seatsDotIoHoldToken?: InputMaybe<Scalars['String']>;
  /** The source where the checkout is happening */
  source?: InputMaybe<Scalars['String']>;
  /** Indicates terms of service agreement, if true will save agreement with purchaser details */
  tosAgreement?: InputMaybe<Scalars['Boolean']>;
  /** The total amount in cents to charge for the checkout. */
  totalInCents: Scalars['Int'];
  /** The Hudl user id of the checking out person, if one exists */
  userId?: InputMaybe<Scalars['String']>;
};

/** A checkout result for a ticketing checkout. */
export type TicketingCheckoutResult = {
  __typename?: 'TicketingCheckoutResult';
  /** A summary of pricing information for the checkout. */
  pricingSummary?: Maybe<TicketingPricingSummary>;
  /** The ticket group created as a result of the checkout. */
  ticketGroup?: Maybe<TicketGroup>;
};

/** Representation of an experiment conducted in hudl-ticketing. */
export type TicketingExperiment = {
  __typename?: 'TicketingExperiment';
  /** The name of the experiment. */
  experimentName?: Maybe<Scalars['String']>;
  /** The ID of the experiment. */
  id: Scalars['ID'];
  /** The organization ids associated with the experiment. */
  organizationIds?: Maybe<Array<Maybe<Scalars['String']>>>;
};

/** A ticketing line item with pricing and quantity information. All amounts in cents. */
export type TicketingLineItem = {
  __typename?: 'TicketingLineItem';
  /** The ID of the entity with pricing information for the line item. */
  lineItemId?: Maybe<Scalars['ID']>;
  /** The type of the entity referenced by the line item ID. */
  lineItemType?: Maybe<Scalars['String']>;
  /** The quantity of the line item. */
  quantity: Scalars['Int'];
  /** A supplementary ID for the line item. e.g. for ticket types, this may be the ID of the relevant ticketed event. */
  referenceId?: Maybe<Scalars['ID']>;
  /** The total price of the line item (quantity * unit price). */
  totalPriceInCents: Scalars['Int'];
  /** The unit price of the line item. */
  unitPriceInCents: Scalars['Int'];
};

export type TicketingPricingLineItemInput = {
  /** The custom price in cents for this line item. */
  customPriceInCents?: InputMaybe<Scalars['Int']>;
  /** The category for the line item. */
  lineItemCategory?: InputMaybe<Scalars['String']>;
  /** The ID of the entity containing pricing information. */
  lineItemId?: InputMaybe<Scalars['ID']>;
  /** The type of the item to be priced. */
  lineItemType?: InputMaybe<Scalars['String']>;
  /** The organization ID for the line item. */
  organizationId?: InputMaybe<Scalars['ID']>;
  /** The quantity of the item to be priced. */
  quantity: Scalars['Int'];
  /** A supplementary ID for the line item to be priced. e.g. for ticket types, include the ticketed event ID. */
  referenceId?: InputMaybe<Scalars['ID']>;
  /** The selected seats for this line item. */
  selectedSeats?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};

/** Representation of a pricing summary for a list of ticketing line items. All amounts listed in cents. */
export type TicketingPricingSummary = {
  __typename?: 'TicketingPricingSummary';
  /** The currency for the pricing summary. */
  currency?: Maybe<Scalars['String']>;
  /** The total amount of fees for the pricing summary. This field should no longer be used in the UI. Due to transparent pricing, the fee components should be broken down into their own fields. */
  feesInCents: Scalars['Int'];
  /** The service fee Hudl applies to the order. */
  hudlFeeInCents: Scalars['Int'];
  /** The list of line items for the pricing summary, each including detailed price information. */
  lineItems?: Maybe<Array<Maybe<TicketingLineItem>>>;
  /** The processing fee our payment provider applies to the order. */
  paymentProcessingFeeInCents: Scalars['Int'];
  /** Determines whether the fee should be displayed in the UI. */
  shouldShowFee?: Maybe<Scalars['Boolean']>;
  /** The subtotal of the pricing summary. This is the sum of all line items * their quantity (exclusive of fees). This field should no longer be used in the UI. Due to transparent pricing, the subtotal should include the Hudl fee, so opt to use SubtotalWithHudlFeeInCents. */
  subtotalInCents: Scalars['Int'];
  /** The subtotal of all line items in the pricing summary, including the Hudl fee. */
  subtotalWithHudlFeeInCents: Scalars['Int'];
  /** The total amount for the pricing summary (inclusive of fees). */
  totalInCents: Scalars['Int'];
};

export type TournamentMetadata = {
  __typename?: 'TournamentMetadata';
  /** The OpponentDetailsDto for the Away Team. */
  awayTeam?: Maybe<OpponentDetails>;
  /** The Name of the Away Tournament Team */
  awayTeamName: Scalars['String'];
  /** The OpponentDetailsDto for the Home Team. */
  homeTeam?: Maybe<OpponentDetails>;
  /** The Name of the Home Tournament Team */
  homeTeamName: Scalars['String'];
  /** Tournament ID from hudl-tournaments. */
  tournamentId?: Maybe<Scalars['ID']>;
};

/** Representation of a transaction for an org program registration. */
export type Transaction = {
  __typename?: 'Transaction';
  /** The allocations of the transaction. */
  allocations: Array<InstallmentAllocationDto>;
  /** The amount of the transaction. */
  amountInBaseDenomination: Scalars['Int'];
  /** The date the transaction was created. */
  createdAt: Scalars['DateTime'];
  /** The id of the user who created the transaction. */
  createdBy: Scalars['ID'];
  /** The date the transaction was deleted, if applicable. */
  deletedAt?: Maybe<Scalars['DateTime']>;
  /** The id of the user who deleted the transaction, if applicable. */
  deletedBy?: Maybe<Scalars['ID']>;
  /** The id of the transaction. */
  id: Scalars['ID'];
  /** The payment method of the transaction. */
  paymentMethodType: PaymentMethodType;
  /** The id of the reference. */
  referenceId: Scalars['String'];
  /** The ids of the registrants. */
  registrantIds: Array<Scalars['ID']>;
  /** The status of the transaction. */
  status: TransactionStatus;
  /** The date the transaction was last updated. */
  updatedAt: Scalars['DateTime'];
  /** The id of the user who last updated the transaction. */
  updatedBy: Scalars['ID'];
};

export enum TransactionStatus {
  Completed = 'COMPLETED',
  Failed = 'FAILED',
  None = 'NONE',
  Pending = 'PENDING'
}

/** Representation of an input to transfer ticketing entities. */
export type TransferTicketingEntitiesInput = {
  /** List of entities to transfer. */
  entitiesToTransfer: Array<SharedEntityInput>;
  /** The id associated with the original ticket group. */
  originalTicketGroupId: Scalars['ID'];
  /** The email of the user to transfer the entities to */
  recipientEmail: Scalars['String'];
  /** The first name of the user to transfer the entities to. */
  recipientFirstName: Scalars['String'];
  /** The last name of the user to transfer the entities to */
  recipientLastName: Scalars['String'];
};

/** Input parameters for a registration update operation. */
export type UpdateRegistrationInput = {
  /** The client mutation identifier. */
  clientMutationId?: InputMaybe<Scalars['String']>;
  /** The machine identifier. */
  machineIdentifier: Scalars['String'];
  /** The machine name. */
  machineName?: InputMaybe<Scalars['String']>;
  /** The os version. */
  osVersion?: InputMaybe<Scalars['String']>;
  /** The registration code. */
  registrationCode: Scalars['String'];
  /** The License security token. */
  token: Scalars['String'];
  /** The installed version of Sportscode. */
  userVersion: Scalars['String'];
};

/** Input type for upserting a registrant for a program registration. */
export type UpsertRegistrantInput = {
  /** The email of the registrant. */
  email: Scalars['String'];
  /** The first name of the registrant. */
  firstName: Scalars['String'];
  /** The IDs of the guardians of the registrant. */
  guardianIds?: InputMaybe<Array<Scalars['ID']>>;
  /** The ID of the registrant. Required for updates, null for creates. */
  id?: InputMaybe<Scalars['ID']>;
  /** The ID of the installment plan for the registrant. */
  installmentPlanId?: InputMaybe<Scalars['ID']>;
  /** The last name of the registrant. */
  lastName: Scalars['String'];
  /** The ID of the program registration this registrant belongs to. */
  registrationId: Scalars['ID'];
};

export enum VanityDestinationType {
  Athlete = 'ATHLETE',
  GoverningBody = 'GOVERNING_BODY',
  Municipality = 'MUNICIPALITY',
  RegionalAlignment = 'REGIONAL_ALIGNMENT',
  School = 'SCHOOL',
  Team = 'TEAM',
  Unknown = 'UNKNOWN'
}

/** Representation of an input to log a viewed quartile of community content. */
export type ViewQuartileCommunityContentInput = {
  /** The Turn ID or mobile device ID. */
  adTrackingId?: InputMaybe<Scalars['String']>;
  /** Additional properties to log with the impression. Ex: pageVisibility, sessionId, usePostRollCta */
  additionalProperties?: InputMaybe<Array<InputMaybe<AdditionalPropertiesKeyValuePairInput>>>;
  /** The authorization ID of the user who is viewing the content. */
  authUserId?: InputMaybe<Scalars['String']>;
  /** The packages the user has access to. */
  authUserPackages?: InputMaybe<Array<Package>>;
  /** The authorization ID of the team the user is viewing the content for. */
  authUserTeamId?: InputMaybe<Scalars['String']>;
  /** Uniquely identifies a piece of community content. */
  communityContentId?: InputMaybe<CommunityContentIdInput>;
  /** Describes the location of the content container. */
  container: CommunityContentContainerType;
  /** A way to segment deeper into the container. Ex: for the explore hub, each timeline would be a different container section. */
  containerSection?: InputMaybe<Scalars['String']>;
  /** A way to segment deeper into the continer. Ex: for profiles, this could be User, Team, Author, Region, or Conference. */
  containerType?: InputMaybe<Scalars['String']>;
  /** The IP address of the client making the request. */
  ipAddress?: InputMaybe<Scalars['String']>;
  /** True if Hudl automatically advanced the user to this video and played it as the top suggestion. */
  isAutoAdvance: Scalars['Boolean'];
  /** Specifies if the video was played automatically. */
  isAutoPlay: Scalars['Boolean'];
  /** Specifies if the video is being played inline. */
  isInlinePlay: Scalars['Boolean'];
  /** Specifies if the video is muted. */
  isMuted: Scalars['Boolean'];
  /** The locale of the user making the request. */
  locale?: InputMaybe<Scalars['String']>;
  /** Describes the type of player rendering the community content video. */
  player: CommunityContentContainerPlayer;
  /** The height of the video player in pixels. */
  playerHeight?: InputMaybe<Scalars['Int']>;
  /** The width of the video player in pixels. */
  playerWidth?: InputMaybe<Scalars['Int']>;
  /** The URL of the page that referred the user to the content. */
  referrerUrl?: InputMaybe<Scalars['String']>;
  /** The URL of the request. */
  requestUrl?: InputMaybe<Scalars['String']>;
  /** The application type that is requesting the content. */
  requestingApp: CommunityContentRequestingApp;
  /** The referrer url for when the session on hudl.com began. */
  sessionReferrerUrl?: InputMaybe<Scalars['String']>;
  /** The user agent of the client making the request. */
  userAgent?: InputMaybe<Scalars['String']>;
};

/** Representation of a volleyball Scoreboard Entry. */
export type VolleyballScoreboardEntry = {
  __typename?: 'VolleyballScoreboardEntry';
  /** The GraphQL id of the volleyball scoreboard entry. */
  id: Scalars['ID'];
  /** The id of the scoreboard session the volleyball scoreboard entry belongs to. */
  scoreboardSessionId: Scalars['String'];
  /** The current set in the volleyball scoreboard entry. */
  set: Scalars['Int'];
  /** Team 1's current score in the volleyball scoreboard entry. */
  team1Score: Scalars['Int'];
  /** Team 1's current set score in the volleyball scoreboard entry. */
  team1SetScore: Scalars['Int'];
  /** Team 2's current score in the volleyball scoreboard entry. */
  team2Score: Scalars['Int'];
  /** Team 2's current set score in the volleyball scoreboard entry. */
  team2SetScore: Scalars['Int'];
  /** The UTC timestamp of the volleyball scoreboard entry. */
  timeUtc: Scalars['DateTime'];
};

/** Representation of a volleyball Scoreboard Settings object. */
export type VolleyballScoreboardSettings = {
  __typename?: 'VolleyballScoreboardSettings';
  /** Whether or not the team names are displayed on the scoreboard overlay. */
  namesOn: Scalars['Boolean'];
  /** Whether or not the scoreboard overlay should be displayed on the video. */
  overlayOn: Scalars['Boolean'];
  /** Whether or not the scores are displayed on the scoreboard overlay. */
  scoresOn: Scalars['Boolean'];
  /** Whether or not the set is displayed on the scoreboard overlay. */
  setOn: Scalars['Boolean'];
  /** Whether or not the set scores are displayed on the scoreboard overlay. */
  setScoresOn: Scalars['Boolean'];
  /** Team 1's name that is displayed on the scoreboard overlay. */
  team1Name: Scalars['String'];
  /** Team 1's primary color that is displayed on the scoreboard overlay. */
  team1PrimaryColor: Scalars['String'];
  /** Team 2's name that is displayed on the scoreboard overlay. */
  team2Name: Scalars['String'];
  /** Team 2's primary color that is displayed on the scoreboard overlay. */
  team2PrimaryColor: Scalars['String'];
  /** The UTC timestamp of the scoreboard settings. */
  timeUtc: Scalars['DateTime'];
};

export type Rn_Pos_CashCheckoutWithTicketingLineItems_R1MutationVariables = Exact<{
  input: TicketingCashCheckoutInput;
}>;


export type Rn_Pos_CashCheckoutWithTicketingLineItems_R1Mutation = { __typename?: 'Mutation', cashCheckoutWithTicketingLineItems?: { __typename?: 'TicketingCheckoutResult', ticketGroup?: { __typename?: 'TicketGroup', createdAt: any, deletedAt?: any | null, email?: string | null, id: string, ticketGroupReference?: string | null, updatedAt: any } | null } | null };

export type Rn_Pos_CheckoutWithTicketingLineItems_R1MutationVariables = Exact<{
  input: TicketingCheckoutInput;
}>;


export type Rn_Pos_CheckoutWithTicketingLineItems_R1Mutation = { __typename?: 'Mutation', checkoutWithTicketingLineItems?: { __typename?: 'TicketingCheckoutResult', pricingSummary?: { __typename?: 'TicketingPricingSummary', currency?: string | null, feesInCents: number, subtotalInCents: number, totalInCents: number, lineItems?: Array<{ __typename?: 'TicketingLineItem', lineItemId?: string | null, lineItemType?: string | null, quantity: number, referenceId?: string | null, totalPriceInCents: number, unitPriceInCents: number } | null> | null } | null, ticketGroup?: { __typename?: 'TicketGroup', createdAt: any, deletedAt?: any | null, email?: string | null, id: string, ticketGroupReference?: string | null, updatedAt: any, tickets?: Array<{ __typename?: 'Ticket', createdAt: any, deletedAt?: any | null, id: string, updatedAt: any, email?: string | null, passId?: string | null, redeemedAt?: any | null, refundable: boolean, ticketedEventId: string, ticketStatus?: string | null, ticketTypeId?: string | null, qrCodeUrl?: string | null } | null> | null, passes?: Array<{ __typename?: 'Pass', id: string, createdAt: any, deletedAt?: any | null, email?: string | null, passConfigId: string, qrCodeUrl?: string | null, updatedAt: any } | null> | null } | null } | null };

export type Rn_Pos_CreateTerminalConnectionToken_R1MutationVariables = Exact<{
  input: CreateTerminalConnectionTokenInput;
}>;


export type Rn_Pos_CreateTerminalConnectionToken_R1Mutation = { __typename?: 'Mutation', createTerminalConnectionToken?: { __typename?: 'TerminalConnectionToken', clientSecret?: string | null } | null };

export type Rn_Pos_CreateTicketingPaymentToken_R1MutationVariables = Exact<{
  input: CreateTicketingPaymentTokenInput;
}>;


export type Rn_Pos_CreateTicketingPaymentToken_R1Mutation = { __typename?: 'Mutation', createTicketingPaymentToken?: string | null };

export type Rn_Scanner_QueueBatchRedemption_R1MutationVariables = Exact<{
  input: ScanningRedemptionInput;
}>;


export type Rn_Scanner_QueueBatchRedemption_R1Mutation = { __typename?: 'Mutation', queueBatchRedemption: boolean };

export type Rn_Scanner_GetTicketedEventAccessCode_R1QueryVariables = Exact<{
  input: GetTicketedEventAccessCodeInput;
}>;


export type Rn_Scanner_GetTicketedEventAccessCode_R1Query = { __typename?: 'Query', ticketedEventAccessCode?: { __typename?: 'TicketedEventAccessCode', organization?: { __typename?: 'School', id: string, internalId?: string | null, abbreviation?: string | null, fullName?: string | null, shortName?: string | null, mascot?: string | null, primaryColor?: string | null, secondaryColor?: string | null, countryIsoAlpha2?: string | null } | null } | null };

export type Rn_Scanner_GetAssociatedTicketGroupsForTicketedEventId_R1QueryVariables = Exact<{
  input: GetAssociatedTicketGroupsWithAccessCodeInput;
}>;


export type Rn_Scanner_GetAssociatedTicketGroupsForTicketedEventId_R1Query = { __typename?: 'Query', associatedTicketGroupsForTicketedEventId?: { __typename?: 'TicketGroupConnection', pageInfo: { __typename?: 'PageInfo', startCursor?: any | null, endCursor?: any | null, hasNextPage: boolean, hasPreviousPage: boolean }, items?: Array<{ __typename?: 'TicketGroup', id: string, ticketGroupReference?: string | null, userId?: string | null, firstName?: string | null, lastName?: string | null, email?: string | null, tickets?: Array<{ __typename?: 'Ticket', id: string } | null> | null, passes?: Array<{ __typename?: 'Pass', id: string } | null> | null }> | null } | null };

export type Web_Fan_GetSchool_R1QueryVariables = Exact<{
  schoolId?: InputMaybe<Scalars['ID']>;
  internalSchoolId?: InputMaybe<Scalars['String']>;
}>;


export type Web_Fan_GetSchool_R1Query = { __typename?: 'Query', school?: { __typename?: 'School', id: string, countryIsoAlpha2?: string | null } | null };

export type Rn_Scanner_GetTicketedEventsByOrganizationId_R1QueryVariables = Exact<{
  input: GetTicketedEventsByOrganizationIdInput;
}>;


export type Rn_Scanner_GetTicketedEventsByOrganizationId_R1Query = { __typename?: 'Query', ticketedEventsByOrganizationId?: { __typename?: 'TicketedEventConnection', totalCount: number, edges?: Array<{ __typename?: 'TicketedEventEdge', node?: { __typename?: 'TicketedEvent', id: string } | null }> | null, pageInfo: { __typename?: 'PageInfo', startCursor?: any | null, endCursor?: any | null, hasNextPage: boolean, hasPreviousPage: boolean }, items?: Array<{ __typename?: 'TicketedEvent', id: string, date: any, eventStatus?: string | null, gender?: string | null, name?: string | null, organizationId: string, participatingTeamIds?: Array<string | null> | null, sport?: string | null, timeZoneIdentifier?: string | null, venueConfigurationId?: string | null, associatedPassConfigs?: Array<{ __typename?: 'PassConfig', id: string } | null> | null, ticketTypePricingSummaries?: Array<{ __typename?: 'TicketTypePricingSummary', hudlFeeInCents: number, priceInCents: number, priceInCentsWithHudlFee: number, ticketedEventId: string, ticketTypeId: string, shouldShowFee: boolean, currency?: string | null } | null> | null }> | null } | null };

export type Rn_TicketedEventByIdWithAnalytics_R1QueryVariables = Exact<{
  ticketedEventId: Scalars['ID'];
}>;


export type Rn_TicketedEventByIdWithAnalytics_R1Query = { __typename?: 'Query', ticketedEventById?: { __typename?: 'TicketedEvent', id: string, name?: string | null, description?: string | null, date: any, timeZoneIdentifier?: string | null, organizationId: string, participatingTeamIds?: Array<string | null> | null, venueConfigurationId?: string | null, associatedPassConfigs?: Array<{ __typename?: 'PassConfig', id: string } | null> | null, ticketTypeReferences?: Array<{ __typename?: 'TicketTypeReference', priceOverride?: number | null, quantityOverride?: number | null, ticketTypeId?: string | null } | null> | null, ticketTypes?: Array<{ __typename?: 'TicketType', createdAt: any, deletedAt?: any | null, id: string, name?: string | null, organizationId?: string | null, priceInCents: number, quantity?: number | null, updatedAt: any } | null> | null, quantityRemainingForTicketTypes?: Array<{ __typename?: 'QuantityRemainingOutput', quantityRemaining: number, referenceId: string } | null> | null, ticketTypePricingSummaries?: Array<{ __typename?: 'TicketTypePricingSummary', hudlFeeInCents: number, priceInCents: number, priceInCentsWithHudlFee: number, ticketedEventId: string, ticketTypeId: string, shouldShowFee: boolean, currency?: string | null } | null> | null } | null };

export type Rn_GetTicketingPricingSummary_R1QueryVariables = Exact<{
  lineItems: Array<InputMaybe<TicketingPricingLineItemInput>> | InputMaybe<TicketingPricingLineItemInput>;
  source?: InputMaybe<Scalars['String']>;
}>;


export type Rn_GetTicketingPricingSummary_R1Query = { __typename?: 'Query', ticketingPricingSummary?: { __typename?: 'TicketingPricingSummary', currency?: string | null, feesInCents: number, shouldShowFee?: boolean | null, subtotalInCents: number, totalInCents: number, hudlFeeInCents: number, paymentProcessingFeeInCents: number, subtotalWithHudlFeeInCents: number, lineItems?: Array<{ __typename?: 'TicketingLineItem', lineItemId?: string | null, lineItemType?: string | null, quantity: number, referenceId?: string | null, totalPriceInCents: number, unitPriceInCents: number } | null> | null } | null };


export const Rn_Pos_CashCheckoutWithTicketingLineItems_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RN_POS_CashCheckoutWithTicketingLineItems_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"TicketingCashCheckoutInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"cashCheckoutWithTicketingLineItems"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ticketGroup"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"deletedAt"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticketGroupReference"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}}]}}]} as unknown as DocumentNode<Rn_Pos_CashCheckoutWithTicketingLineItems_R1Mutation, Rn_Pos_CashCheckoutWithTicketingLineItems_R1MutationVariables>;
export const Rn_Pos_CheckoutWithTicketingLineItems_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RN_POS_CheckoutWithTicketingLineItems_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"TicketingCheckoutInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"checkoutWithTicketingLineItems"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pricingSummary"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currency"}},{"kind":"Field","name":{"kind":"Name","value":"feesInCents"}},{"kind":"Field","name":{"kind":"Name","value":"lineItems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lineItemId"}},{"kind":"Field","name":{"kind":"Name","value":"lineItemType"}},{"kind":"Field","name":{"kind":"Name","value":"quantity"}},{"kind":"Field","name":{"kind":"Name","value":"referenceId"}},{"kind":"Field","name":{"kind":"Name","value":"totalPriceInCents"}},{"kind":"Field","name":{"kind":"Name","value":"unitPriceInCents"}}]}},{"kind":"Field","name":{"kind":"Name","value":"subtotalInCents"}},{"kind":"Field","name":{"kind":"Name","value":"totalInCents"}}]}},{"kind":"Field","name":{"kind":"Name","value":"ticketGroup"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"deletedAt"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticketGroupReference"}},{"kind":"Field","name":{"kind":"Name","value":"tickets"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"deletedAt"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"passId"}},{"kind":"Field","name":{"kind":"Name","value":"redeemedAt"}},{"kind":"Field","name":{"kind":"Name","value":"refundable"}},{"kind":"Field","name":{"kind":"Name","value":"ticketedEventId"}},{"kind":"Field","name":{"kind":"Name","value":"ticketStatus"}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypeId"}},{"kind":"Field","name":{"kind":"Name","value":"qrCodeUrl"}}]}},{"kind":"Field","name":{"kind":"Name","value":"passes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"deletedAt"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"passConfigId"}},{"kind":"Field","name":{"kind":"Name","value":"qrCodeUrl"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}}]}}]}}]} as unknown as DocumentNode<Rn_Pos_CheckoutWithTicketingLineItems_R1Mutation, Rn_Pos_CheckoutWithTicketingLineItems_R1MutationVariables>;
export const Rn_Pos_CreateTerminalConnectionToken_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RN_POS_CreateTerminalConnectionToken_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateTerminalConnectionTokenInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createTerminalConnectionToken"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"clientSecret"}}]}}]}}]} as unknown as DocumentNode<Rn_Pos_CreateTerminalConnectionToken_R1Mutation, Rn_Pos_CreateTerminalConnectionToken_R1MutationVariables>;
export const Rn_Pos_CreateTicketingPaymentToken_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RN_POS_CreateTicketingPaymentToken_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"CreateTicketingPaymentTokenInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createTicketingPaymentToken"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}]}]}}]} as unknown as DocumentNode<Rn_Pos_CreateTicketingPaymentToken_R1Mutation, Rn_Pos_CreateTicketingPaymentToken_R1MutationVariables>;
export const Rn_Scanner_QueueBatchRedemption_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"RN_Scanner_QueueBatchRedemption_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ScanningRedemptionInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"queueBatchRedemption"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}]}]}}]} as unknown as DocumentNode<Rn_Scanner_QueueBatchRedemption_R1Mutation, Rn_Scanner_QueueBatchRedemption_R1MutationVariables>;
export const Rn_Scanner_GetTicketedEventAccessCode_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"RN_Scanner_GetTicketedEventAccessCode_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"GetTicketedEventAccessCodeInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ticketedEventAccessCode"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"organization"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"internalId"}},{"kind":"Field","name":{"kind":"Name","value":"abbreviation"}},{"kind":"Field","name":{"kind":"Name","value":"fullName"}},{"kind":"Field","name":{"kind":"Name","value":"shortName"}},{"kind":"Field","name":{"kind":"Name","value":"mascot"}},{"kind":"Field","name":{"kind":"Name","value":"primaryColor"}},{"kind":"Field","name":{"kind":"Name","value":"secondaryColor"}},{"kind":"Field","name":{"kind":"Name","value":"countryIsoAlpha2"}}]}}]}}]}}]} as unknown as DocumentNode<Rn_Scanner_GetTicketedEventAccessCode_R1Query, Rn_Scanner_GetTicketedEventAccessCode_R1QueryVariables>;
export const Rn_Scanner_GetAssociatedTicketGroupsForTicketedEventId_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"RN_Scanner_GetAssociatedTicketGroupsForTicketedEventId_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"GetAssociatedTicketGroupsWithAccessCodeInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"associatedTicketGroupsForTicketedEventId"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"startCursor"}},{"kind":"Field","name":{"kind":"Name","value":"endCursor"}},{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasPreviousPage"}}]}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"ticketGroupReference"}},{"kind":"Field","name":{"kind":"Name","value":"userId"}},{"kind":"Field","name":{"kind":"Name","value":"firstName"}},{"kind":"Field","name":{"kind":"Name","value":"lastName"}},{"kind":"Field","name":{"kind":"Name","value":"email"}},{"kind":"Field","name":{"kind":"Name","value":"tickets"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"passes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}}]}}]}}]} as unknown as DocumentNode<Rn_Scanner_GetAssociatedTicketGroupsForTicketedEventId_R1Query, Rn_Scanner_GetAssociatedTicketGroupsForTicketedEventId_R1QueryVariables>;
export const Web_Fan_GetSchool_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"Web_Fan_GetSchool_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"schoolId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"internalSchoolId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"school"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"schoolId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"schoolId"}}},{"kind":"Argument","name":{"kind":"Name","value":"internalSchoolId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"internalSchoolId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"countryIsoAlpha2"}}]}}]}}]} as unknown as DocumentNode<Web_Fan_GetSchool_R1Query, Web_Fan_GetSchool_R1QueryVariables>;
export const Rn_Scanner_GetTicketedEventsByOrganizationId_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"RN_Scanner_GetTicketedEventsByOrganizationId_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"input"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"GetTicketedEventsByOrganizationIdInput"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ticketedEventsByOrganizationId"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"input"},"value":{"kind":"Variable","name":{"kind":"Name","value":"input"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"totalCount"}},{"kind":"Field","name":{"kind":"Name","value":"edges"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"node"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}}]}},{"kind":"Field","name":{"kind":"Name","value":"pageInfo"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"startCursor"}},{"kind":"Field","name":{"kind":"Name","value":"endCursor"}},{"kind":"Field","name":{"kind":"Name","value":"hasNextPage"}},{"kind":"Field","name":{"kind":"Name","value":"hasPreviousPage"}}]}},{"kind":"Field","name":{"kind":"Name","value":"items"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"eventStatus"}},{"kind":"Field","name":{"kind":"Name","value":"gender"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"organizationId"}},{"kind":"Field","name":{"kind":"Name","value":"participatingTeamIds"}},{"kind":"Field","name":{"kind":"Name","value":"sport"}},{"kind":"Field","name":{"kind":"Name","value":"timeZoneIdentifier"}},{"kind":"Field","name":{"kind":"Name","value":"venueConfigurationId"}},{"kind":"Field","name":{"kind":"Name","value":"associatedPassConfigs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypePricingSummaries"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hudlFeeInCents"}},{"kind":"Field","name":{"kind":"Name","value":"priceInCents"}},{"kind":"Field","name":{"kind":"Name","value":"priceInCentsWithHudlFee"}},{"kind":"Field","name":{"kind":"Name","value":"ticketedEventId"}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypeId"}},{"kind":"Field","name":{"kind":"Name","value":"shouldShowFee"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}}]}}]}}]}}]}}]} as unknown as DocumentNode<Rn_Scanner_GetTicketedEventsByOrganizationId_R1Query, Rn_Scanner_GetTicketedEventsByOrganizationId_R1QueryVariables>;
export const Rn_TicketedEventByIdWithAnalytics_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"RN_TicketedEventByIdWithAnalytics_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"ticketedEventId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"ID"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ticketedEventById"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"ticketedEventId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"ticketedEventId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"description"}},{"kind":"Field","name":{"kind":"Name","value":"date"}},{"kind":"Field","name":{"kind":"Name","value":"timeZoneIdentifier"}},{"kind":"Field","name":{"kind":"Name","value":"organizationId"}},{"kind":"Field","name":{"kind":"Name","value":"participatingTeamIds"}},{"kind":"Field","name":{"kind":"Name","value":"venueConfigurationId"}},{"kind":"Field","name":{"kind":"Name","value":"associatedPassConfigs"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}}]}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypeReferences"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"priceOverride"}},{"kind":"Field","name":{"kind":"Name","value":"quantityOverride"}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypeId"}}]}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"deletedAt"}},{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"name"}},{"kind":"Field","name":{"kind":"Name","value":"organizationId"}},{"kind":"Field","name":{"kind":"Name","value":"priceInCents"}},{"kind":"Field","name":{"kind":"Name","value":"quantity"}},{"kind":"Field","name":{"kind":"Name","value":"updatedAt"}}]}},{"kind":"Field","name":{"kind":"Name","value":"quantityRemainingForTicketTypes"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"quantityRemaining"}},{"kind":"Field","name":{"kind":"Name","value":"referenceId"}}]}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypePricingSummaries"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"hudlFeeInCents"}},{"kind":"Field","name":{"kind":"Name","value":"priceInCents"}},{"kind":"Field","name":{"kind":"Name","value":"priceInCentsWithHudlFee"}},{"kind":"Field","name":{"kind":"Name","value":"ticketedEventId"}},{"kind":"Field","name":{"kind":"Name","value":"ticketTypeId"}},{"kind":"Field","name":{"kind":"Name","value":"shouldShowFee"}},{"kind":"Field","name":{"kind":"Name","value":"currency"}}]}}]}}]}}]} as unknown as DocumentNode<Rn_TicketedEventByIdWithAnalytics_R1Query, Rn_TicketedEventByIdWithAnalytics_R1QueryVariables>;
export const Rn_GetTicketingPricingSummary_R1Document = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"RN_GetTicketingPricingSummary_r1"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"lineItems"}},"type":{"kind":"NonNullType","type":{"kind":"ListType","type":{"kind":"NamedType","name":{"kind":"Name","value":"TicketingPricingLineItemInput"}}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"source"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"ticketingPricingSummary"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"lineItems"},"value":{"kind":"Variable","name":{"kind":"Name","value":"lineItems"}}},{"kind":"Argument","name":{"kind":"Name","value":"source"},"value":{"kind":"Variable","name":{"kind":"Name","value":"source"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"currency"}},{"kind":"Field","name":{"kind":"Name","value":"feesInCents"}},{"kind":"Field","name":{"kind":"Name","value":"lineItems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"lineItemId"}},{"kind":"Field","name":{"kind":"Name","value":"lineItemType"}},{"kind":"Field","name":{"kind":"Name","value":"quantity"}},{"kind":"Field","name":{"kind":"Name","value":"referenceId"}},{"kind":"Field","name":{"kind":"Name","value":"totalPriceInCents"}},{"kind":"Field","name":{"kind":"Name","value":"unitPriceInCents"}}]}},{"kind":"Field","name":{"kind":"Name","value":"shouldShowFee"}},{"kind":"Field","name":{"kind":"Name","value":"subtotalInCents"}},{"kind":"Field","name":{"kind":"Name","value":"totalInCents"}},{"kind":"Field","name":{"kind":"Name","value":"hudlFeeInCents"}},{"kind":"Field","name":{"kind":"Name","value":"paymentProcessingFeeInCents"}},{"kind":"Field","name":{"kind":"Name","value":"subtotalWithHudlFeeInCents"}}]}}]}}]} as unknown as DocumentNode<Rn_GetTicketingPricingSummary_R1Query, Rn_GetTicketingPricingSummary_R1QueryVariables>;