import React, { ReactElement } from 'react';

import { NavigationContainer } from '@react-navigation/native';
import { render } from '@testing-library/react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { AccessProvider } from '../context/AccessContext';
import { AppSessionProvider } from '../session/AppSession';

interface Options {
  withNavigationContainer?: boolean;
  withAppSessionProvider?: boolean;
  withAccessContext?: boolean;
  withSafeAreaProvider?: boolean;
}

const renderWithOptions = (children: ReactElement, options?: Options): void => {
  const withSafeAreaProvider = options?.withSafeAreaProvider ? (
    <SafeAreaProvider>{children}</SafeAreaProvider>
  ) : (
    children
  );
  const withNavigationContainer = options?.withNavigationContainer ? (
    <NavigationContainer>{children}</NavigationContainer>
  ) : (
    withSafeAreaProvider
  );

  const withAccessContext = options?.withAccessContext ? (
    <AccessProvider>{withNavigationContainer}</AccessProvider>
  ) : (
    withNavigationContainer
  );

  const withAppSessionProvider = options?.withAppSessionProvider ? (
    <AppSessionProvider>{withAccessContext}</AppSessionProvider>
  ) : (
    withAccessContext
  );

  render(withAppSessionProvider);
};

export { renderWithOptions };
