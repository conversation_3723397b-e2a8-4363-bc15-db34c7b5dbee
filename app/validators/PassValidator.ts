import moment from 'moment-timezone';

import { logError, logInfo, logWarn } from '../common/Logging';
import { EventInfo } from '../models/EventInfo';
import { Pass } from '../models/ScannedQRCode';
import { PassQRCodeVersion, QRCodeScanStatus } from '../utils/Enums';

export function validatePassForEvent(pass: Pass, currentEvent: EventInfo): QRCodeScanStatus {
  switch (pass.QRCodeVersion as PassQRCodeVersion) {
    case PassQRCodeVersion.V2:
      return checkPassStatusForEventUsingPassConfigId(pass, currentEvent);
    case PassQRCodeVersion.V1:
      return checkPassStatusForEventUsingDates(pass, currentEvent);
    default:
      return checkPassStatusForEventUsingDates(pass, currentEvent);
  }
}

/**
 * Ensures the pass is valid for the currently selected event by comparing the dates
 */
export function checkPassStatusForEventUsingDates(
  pass: Pass,
  currentEvent: EventInfo
): QRCodeScanStatus {
  const isForSameOrg = pass.OrganizationId === currentEvent.organizationId;

  const ticketIncludesAtLeastOnePlayingTeam =
    pass.Teams?.some((p) => currentEvent.participatingTeamIds.includes(p)) ?? true;

  if (!isForSameOrg || !ticketIncludesAtLeastOnePlayingTeam) {
    logInfo(
      'Pass is not for the same org or does not include a playing team',
      `checkPassStatusForEventUsingDates`,
      {
        passOrgId: pass.OrganizationId,
        eventOrgId: currentEvent.organizationId,
        passTeams: pass.Teams,
        eventTeams: currentEvent.participatingTeamIds,
      }
    );
    return QRCodeScanStatus.IncorrectParticipants;
  }

  if (pass.StartDate && pass.EndDate) {
    if (!isEventDateInRange(currentEvent, pass.StartDate, pass.EndDate)) {
      logInfo('Pass is outside of date range', `checkPassStatusForEventUsingDates`, {
        passStartDate: pass.StartDate,
        passEndDate: pass.EndDate,
        eventDate: currentEvent.date,
      });
      return QRCodeScanStatus.OutsideOfDateRange;
    }
  }

  return QRCodeScanStatus.CorrectEvent;
}

/**
 * Ensures the pass is valid for the currently selected event by doing a lookup
 * against the valid pass config ids for the event
 */
export function checkPassStatusForEventUsingPassConfigId(
  pass: Pass,
  currentEvent: EventInfo
): QRCodeScanStatus {
  if (!pass.PassConfigurationId) {
    logWarn('Pass does not have a pass config id', `checkPassStatusForEventUsingPassConfigId`, {
      pass: JSON.stringify(pass),
    });
    return QRCodeScanStatus.CorrectEvent;
  }

  const validPassConfigIds = currentEvent.associatedPassConfigIds;
  const isPassValidForEvent = validPassConfigIds.includes(pass.PassConfigurationId);

  if (!isPassValidForEvent) {
    logError('Pass is not valid for event.', `checkPassStatusForEventUsingPassConfigId`, {
      validPassConfigIds,
      passConfigId: pass.PassConfigurationId,
      ticketedEventId: currentEvent.id,
    });
  }

  return isPassValidForEvent ? QRCodeScanStatus.CorrectEvent : QRCodeScanStatus.IncorrectEvent;
}

/**
 * Ensures the event date is in the range of the passed in start and end date
 */
export function isEventDateInRange(
  currentEvent: EventInfo,
  startDate: string,
  endDate: string
): boolean {
  const eventDate = moment
    .tz(currentEvent.date, currentEvent.timezoneIdentifier)
    .utcOffset(+0, true);
  const startOfStartDate = moment.utc(startDate).startOf('day');
  const endOfEndDate = moment.utc(endDate).endOf('day');

  return !eventDate.isBefore(startOfStartDate) && !eventDate.isAfter(endOfEndDate);
}
