import { logInfo } from '../common/Logging';
import { EventInfo } from '../models/EventInfo';
import { Ticket } from '../models/ScannedQRCode';
import { QRCodeScanStatus, TicketQRCodeVersion } from '../utils/Enums';

export function validateTicketForEvent(ticket: Ticket, currentEvent: EventInfo): QRCodeScanStatus {
  switch (ticket.QRCodeVersion as TicketQRCodeVersion) {
    case TicketQRCodeVersion.V3:
    case TicketQRCodeVersion.V2:
    case TicketQRCodeVersion.V1:
    default:
      return checkTicketStatusForEvent(ticket, currentEvent);
  }
}

/**
 * Ensures the ticket is valid for the currently selected event
 */
export function checkTicketStatusForEvent(
  ticket: Ticket,
  currentEvent: EventInfo
): QRCodeScanStatus {
  const isTicketForSameEvent = ticket.TicketedEventId === currentEvent.id;
  if (!isTicketForSameEvent) {
    logInfo('Ticket is not for the same event', `checkTicketStatusForEvent`, {
      ticketEventId: ticket.TicketedEventId,
      eventEventId: currentEvent.id,
    });
  }

  return isTicketForSameEvent ? QRCodeScanStatus.CorrectEvent : QRCodeScanStatus.IncorrectEvent;
}
