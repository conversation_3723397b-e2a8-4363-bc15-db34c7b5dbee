import { EventInfo } from '../../models/EventInfo';
import { Ticket } from '../../models/ScannedQRCode';
import { QRCodeScanStatus } from '../../utils/Enums';
import { checkTicketStatusForEvent, validateTicketForEvent } from '../TicketValidator';

jest.mock('@hudl/jarvis/logging', () => ({
  HudlLogger: {
    logError: jest.fn(),
    logWarn: jest.fn(),
    logInfo: jest.fn(),
  },
}));

describe('checkTicketStatusForEvent', () => {
  it('returns CorrectEvent when ticket is for the same event', () => {
    const ticket: Ticket = {
      TicketedEventId: 'event1',
      QRCodeVersion: '1',
      TicketedEventName: 'event1',
      TicketId: 'ticket1',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkTicketStatusForEvent(ticket, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });

  it('returns IncorrectEvent when ticket is for the same event', () => {
    const ticket: Ticket = {
      TicketedEventId: 'event1123',
      QRCodeVersion: '1',
      TicketedEventName: 'event1',
      TicketId: 'ticket1',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkTicketStatusForEvent(ticket, currentEvent)).toBe(QRCodeScanStatus.IncorrectEvent);
  });
});

describe('validateTicketForEvent', () => {
  it('returns CorrectEvent when ticket is for the same event: V1', () => {
    const ticket: Ticket = {
      TicketedEventId: 'event1',
      QRCodeVersion: '1',
      TicketedEventName: 'event1',
      TicketId: 'ticket1',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validateTicketForEvent(ticket, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });

  it('returns CorrectEvent when ticket is for the same event: V2', () => {
    const ticket: Ticket = {
      TicketedEventId: 'event1',
      QRCodeVersion: '2',
      TicketedEventName: 'event1',
      TicketId: 'ticket1',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validateTicketForEvent(ticket, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });

  it('returns CorrectEvent when ticket is for the same event: V3', () => {
    const ticket: Ticket = {
      TicketedEventId: 'event1',
      QRCodeVersion: '3',
      TicketedEventName: 'event1',
      TicketId: 'ticket1',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validateTicketForEvent(ticket, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });

  it('returns default behavior when version is not handled', () => {
    const ticket: Ticket = {
      TicketedEventId: 'event1',
      QRCodeVersion: '100',
      TicketedEventName: 'event1',
      TicketId: 'ticket1',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validateTicketForEvent(ticket, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });
});
