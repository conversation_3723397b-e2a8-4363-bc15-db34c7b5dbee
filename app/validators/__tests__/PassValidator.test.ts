import { EventInfo } from '../../models/EventInfo';
import { Pass } from '../../models/ScannedQRCode';
import { QRCodeScanStatus } from '../../utils/Enums';
import {
  checkPassStatusForEventUsingDates,
  checkPassStatusForEventUsingPassConfigId,
  isEventDateInRange,
  validatePassForEvent,
} from '../PassValidator';

beforeAll(() => {
  jest.useFakeTimers();
  jest.setSystemTime(new Date(2023, 1, 15, 13, 12, 0));
});

jest.mock('@hudl/jarvis/logging', () => ({
  HudlLogger: {
    logError: jest.fn(),
    logWarn: jest.fn(),
    logInfo: jest.fn(),
  },
}));

describe('checkPassStatusForEventUsingDates', () => {
  it('should return IncorrectParticipants if the pass is not for the same org', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
    };
    const currentEvent: EventInfo = {
      organizationId: 'org2',
      participatingTeamIds: ['team1'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.IncorrectParticipants
    );
  });

  it('should return IncorrectParticipants if the participating teams does not include team on pass', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team2'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
    };
    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.IncorrectParticipants
    );
  });

  it('should return CorrectEvent if the teams on the pass happen to be undefined', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
      StartDate: '2022-05-05',
      EndDate: '2024-05-06',
    };
    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.CorrectEvent
    );
  });

  it('should return CorrectEvent if there happens to be not start and end date', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
    };
    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.CorrectEvent
    );
  });

  it('should return OutsideOfDateRange if the event date is outside the range of the pass: past', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
      StartDate: '2000-05-05',
      EndDate: '2000-05-06',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.OutsideOfDateRange
    );
  });

  it('should return OutsideOfDateRange if the event date is outside the range of the pass: future', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
      StartDate: '2024-05-05',
      EndDate: '2024-05-06',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.OutsideOfDateRange
    );
  });

  it('should return CorrectEvent if all checks pass', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
      StartDate: '2022-05-05',
      EndDate: '2024-05-06',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingDates(pass, currentEvent)).toBe(
      QRCodeScanStatus.CorrectEvent
    );
  });
});

describe('isEventDateInRange', () => {
  it('should return true if the event date is in the range of the pass', () => {
    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(isEventDateInRange(currentEvent, '2022-05-05', '2024-05-06')).toBe(true);
  });

  it('should return false if the event date is not in the range of the pass', () => {
    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: [],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(isEventDateInRange(currentEvent, '2022-05-05', '2022-05-06')).toBe(false);
  });
});

describe('checkPassStatusForEventUsingPassConfigId', () => {
  it('should return CorrectEvent if pass config is in list of associated pass configs', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
      PassConfigurationId: 'passConfigId',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: ['passConfigId'],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingPassConfigId(pass, currentEvent)).toBe(
      QRCodeScanStatus.CorrectEvent
    );
  });

  it('should return IncorrectEvent if pass config is not in list of associated pass configs', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '2',
      Checksum: 'checksum',
      PassConfigurationId: 'passConfigId1',
      StartDate: '2022-05-05',
      EndDate: '2022-05-06',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: ['passConfigId'],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingPassConfigId(pass, currentEvent)).toBe(
      QRCodeScanStatus.IncorrectEvent
    );
  });

  it('should return CorrectEvent if pass config id is undefined', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '2',
      Checksum: 'checksum',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: ['passConfigId'],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(checkPassStatusForEventUsingPassConfigId(pass, currentEvent)).toBe(
      QRCodeScanStatus.CorrectEvent
    );
  });
});

describe('validatePassForEvent', () => {
  it('should return CorrectEvent for V1 pass', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '1',
      Checksum: 'checksum',
      StartDate: '2022-05-05',
      EndDate: '2024-05-06',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: ['passConfigId'],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validatePassForEvent(pass, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });

  it('should return CorrectEvent for V2 pass', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '2',
      Checksum: 'checksum',
      PassConfigurationId: 'passConfigId',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: ['passConfigId'],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validatePassForEvent(pass, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });

  it('Unhandled QR code version defaults to date logic', () => {
    const pass: Pass = {
      OrganizationId: 'org1',
      Teams: ['team1', 'team5'],
      PassId: 'pass1',
      QRCodeVersion: '100',
      Checksum: 'checksum',
      StartDate: '2022-05-05',
      EndDate: '2024-05-06',
    };

    const currentEvent: EventInfo = {
      organizationId: 'org1',
      participatingTeamIds: ['team5'],
      id: 'event1',
      name: 'event1',
      associatedPassConfigIds: ['passConfigId'],
      description: 'event1',
      date: new Date().toISOString(),
      timezoneIdentifier: 'Etc/UTC',
      ticketTypePricingSummaries: [],
    };

    expect(validatePassForEvent(pass, currentEvent)).toBe(QRCodeScanStatus.CorrectEvent);
  });
});
