import AsyncStorage from '@react-native-async-storage/async-storage';
import { act, renderHook, waitFor } from '@testing-library/react-native';

import { HudlApolloClient } from '@hudl/gql-toolkit';

import { getResultGroupsFor, saveResultGroup } from '../../local_storage/ResultGroupStorage';
import {
  mockScannedQRCodeResult,
  mockStoredResultGroup,
} from '../../local_storage/__mocks__/ResultGroupStorage.mocks';
import { useUploadResults, useUploadResultsInIntervals } from '../TicketUploadingHooks';

jest.mock('../QueueBatchRedemptionRequest', () => {
  return {
    queueBatchRedemption: () => Promise.resolve({ success: true }),
  };
});

describe('TicketUploadingHooks', (): void => {
  const group = mockStoredResultGroup({
    results: [mockScannedQRCodeResult(), mockScannedQRCodeResult(), mockScannedQRCodeResult()],
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const client: any = jest.mocked(HudlApolloClient, { shallow: true });

  beforeEach(async (): Promise<void> => {
    AsyncStorage.clear();
  });

  it('useUploadResults', async (): Promise<void> => {
    await saveResultGroup(group);

    const { result } = renderHook(() => useUploadResults(client));

    act(() => {
      result.current.startUpload(group.ticketedEventId, /*uploadDelay*/ 0);
    });

    await waitFor(() => expect(result.current.processComplete).toBe(true));

    const remainingGroups = await getResultGroupsFor(group.ticketedEventId);

    expect(result.current.errorGroups).toBeDefined();
    expect(result.current.errorGroups.groupsFailedToUpload.length).toBe(0);

    expect(remainingGroups).toBeDefined();
    expect(remainingGroups.groups.length).toBe(1);
    expect(remainingGroups.groups[0].ticketedEventId).toBe(group.ticketedEventId);
    expect(remainingGroups.groups[0].results.length).toBe(0);
    expect(remainingGroups.groups[0].uploadedResults.length).toBe(group.results.length);

    expect(remainingGroups.errors.length).toBe(0);
  });

  it('useUploadResults - upload remaining results', async (): Promise<void> => {
    await saveResultGroup(group);

    const { result } = renderHook(() => useUploadResults(client));

    act(() => {
      result.current.startUpload(undefined, /*uploadDelay*/ 0);
    });

    await waitFor(() => expect(result.current.processComplete).toBe(true));

    const remainingGroups = await getResultGroupsFor(group.ticketedEventId);

    expect(result.current.errorGroups).toBeDefined();
    expect(result.current.errorGroups.groupsFailedToUpload.length).toBe(0);

    expect(remainingGroups).toBeDefined();
    expect(remainingGroups.groups.length).toBe(1);
    expect(remainingGroups.groups[0].ticketedEventId).toBe(group.ticketedEventId);
    expect(remainingGroups.groups[0].results.length).toBe(0);
    expect(remainingGroups.groups[0].uploadedResults.length).toBe(group.results.length);

    expect(remainingGroups.errors.length).toBe(0);
  });

  it('useUploadResultsInIntervals', async (): Promise<void> => {
    await saveResultGroup(group);

    const { result } = renderHook(() =>
      useUploadResultsInIntervals(client, group.ticketedEventId, 100, 0)
    );

    await waitFor(() => expect(result.current.processComplete).toBe(true));

    const remainingGroups = await getResultGroupsFor(group.ticketedEventId);

    expect(remainingGroups).toBeDefined();
    expect(remainingGroups.groups.length).toBe(1);
    expect(remainingGroups.groups[0].ticketedEventId).toBe(group.ticketedEventId);
    expect(remainingGroups.groups[0].results.length).toBe(0);
    expect(remainingGroups.groups[0].uploadedResults.length).toBe(group.results.length);

    expect(remainingGroups.errors.length).toBe(0);
  });

  it('useUploadResultsInIntervals - upload remaining results', async (): Promise<void> => {
    await saveResultGroup(group);

    const { result } = renderHook(() => useUploadResultsInIntervals(client, undefined, 100, 0));

    await waitFor(() => expect(result.current.processComplete).toBe(true));

    const remainingGroups = await getResultGroupsFor(group.ticketedEventId);

    expect(remainingGroups).toBeDefined();
    expect(remainingGroups.groups.length).toBe(1);
    expect(remainingGroups.groups[0].ticketedEventId).toBe(group.ticketedEventId);
    expect(remainingGroups.groups[0].results.length).toBe(0);
    expect(remainingGroups.groups[0].uploadedResults.length).toBe(group.results.length);

    expect(remainingGroups.errors.length).toBe(0);
  });
});
