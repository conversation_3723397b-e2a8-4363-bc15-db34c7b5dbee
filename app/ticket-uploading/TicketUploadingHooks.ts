import { useCallback, useEffect, useState } from 'react';

import { HudlApolloClient } from '@hudl/gql-toolkit';

import { queueBatchRedemption } from './QueueBatchRedemptionRequest';
import { byGroupsForUpload } from '../local_storage/LocalStorageHooks';
import {
  StoredResultGroup,
  byId,
  getAllRemainingScanResultGroups,
  getResultGroupsFor,
  saveResultGroup,
} from '../local_storage/ResultGroupStorage';

const UPLOAD_RESULTS_INTERVAL_IN_MS = 30_000; // 30 seconds

/**
 * Attempts to upload results for a given event Id
 * @param ticketedEventId
 * @returns
 */
export function useUploadResultsInIntervals(
  client: HudlApolloClient,
  ticketedEventId?: string,
  intervalInMs?: number,
  uploadDelay?: number
): {
  processingUpload: boolean;
  processComplete: boolean;
  errorGroups: ErrorGroups;
} {
  const { processingUpload, processComplete, startUpload, errorGroups } = useUploadResults(client);

  useInterval(() => {
    startUpload(ticketedEventId, uploadDelay);
  }, intervalInMs ?? UPLOAD_RESULTS_INTERVAL_IN_MS);

  return { processingUpload, processComplete, errorGroups };
}

/**
 * Attempts to upload results for a given event Id
 * @param ticketedEventId
 * @returns
 */
export function useUploadResults(client: HudlApolloClient): {
  processingUpload: boolean;
  processComplete: boolean;
  errorGroups: ErrorGroups;
  startUpload: (ticketedEventId?: string, uploadDelay?: number) => void;
} {
  const [processingUpload, setProcessing] = useState(true);
  const [processComplete, setProcessComplete] = useState(false);
  const [errorGroups, setErrorGroups] = useState<ErrorGroups>({
    groupsFailedToUpload: [],
    success: false,
  });

  const startUpload = useCallback(
    (ticketedEventId?: string, uploadDelay?: number) => {
      async function uploadResults(): Promise<void> {
        setProcessComplete(false);
        setProcessing(true);

        setTimeout(async () => {
          if (ticketedEventId) {
            const results = await uploadResultsFor(ticketedEventId, client);
            setErrorGroups(results);
          } else {
            const results = await uploadRemainingResults(client);
            setErrorGroups(results);
          }

          setProcessComplete(true);
        }, uploadDelay ?? 1_000);
      }
      uploadResults();
    },
    [client]
  );

  return { processingUpload, processComplete, errorGroups, startUpload };
}

export async function uploadRemainingResults(client: HudlApolloClient): Promise<ErrorGroups> {
  const groups = await getAllRemainingScanResultGroups();

  return uploadResultGroups(groups.groups, client);
}

export async function uploadResultsFor(
  ticketedEventById: string,
  client: HudlApolloClient
): Promise<ErrorGroups> {
  const storedGroups = await getResultGroupsFor(ticketedEventById);
  const groupsForUpload = storedGroups.groups.filter(byGroupsForUpload);

  return uploadResultGroups(groupsForUpload, client);
}

export async function uploadResultGroups(
  groups: StoredResultGroup[],
  client: HudlApolloClient
): Promise<ErrorGroups> {
  let failedToUploadGroups: StoredResultGroup[] = [];

  let success = true;
  for (const group of groups) {
    if (group.results.length === 0) {
      continue;
    }

    const redemptionResult = await queueBatchRedemption(client, {
      firstName: group.firstName,
      lastName: group.lastName,
      email: group.email,
      accessCode: group.accessCode,
      ticketedEventId: group.ticketedEventId,
      appVersion: group.appVersion,
      results: group.results,
    });

    if (redemptionResult.success) {
      const uploadedResults = [...group.uploadedResults, ...group.results].sort(byId);

      await saveResultGroup({ ...group, results: [], uploadedResults });
    } else {
      failedToUploadGroups = failedToUploadGroups.concat(group);
      success = false;
    }
  }

  return {
    success: success,
    groupsFailedToUpload: failedToUploadGroups as StoredResultGroup[],
  };
}

interface ErrorGroups {
  success: boolean;
  groupsFailedToUpload: StoredResultGroup[];
}

function useInterval(callBack: () => void, delayMs: number): void {
  useEffect(() => {
    function tick(): void {
      callBack();
    }

    const id = setInterval(tick, delayMs);
    return () => {
      clearInterval(id);
    };
  }, [callBack, delayMs]);
}
