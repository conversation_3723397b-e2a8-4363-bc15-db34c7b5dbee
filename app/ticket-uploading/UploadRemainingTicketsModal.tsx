import React, { useCallback, useEffect, useState } from 'react';
import { StyleSheet, View } from 'react-native';

import { TouchableOpacity } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ViewProps } from 'react-native-svg/lib/typescript/fabric/utils';

import { useI18n } from '@hudl/jarvis/i18n';
import {
  Button,
  ButtonStatus,
  ColorsDark,
  IconUIDismissLarge,
  Text,
  UniformSpace,
} from '@hudl/rn-uniform';

import { useUploadResults } from './TicketUploadingHooks';
import { AppNav } from '../Nav';
import { AppTestIDs } from '../components/AppTestIDs';
import { getAllRemainingScanResultGroups } from '../local_storage/ResultGroupStorage';
import { useAppSession } from '../session/AppSession';

export const styles = StyleSheet.create({
  container: {
    flexGrow: 1,
    flexDirection: 'column-reverse',
  },
  safeAreaView: {
    backgroundColor: ColorsDark.bgLevel0,
    padding: UniformSpace.one,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  bodyText: {
    marginVertical: UniformSpace.one,
  },
});

enum UploadStatus {
  NeedsUpload = 'needsUpload',
  Uploading = 'uploading',
  Complete = 'complete',
  Error = 'error',
}

type ButtonState = {
  text: string;
  status: ButtonStatus | undefined;
  testID: string;
};

export function UploadRemainingTicketsModal(): React.ReactElement {
  const nav = AppNav.root.useNavigation();

  const onDismiss = useCallback(() => {
    nav.goBack();
  }, [nav]);

  return (
    <View style={styles.container}>
      <TicketsFound onDismiss={onDismiss} />
    </View>
  );
}

function TicketsFound({ onDismiss: onDismiss }: { onDismiss: () => void }): React.ReactElement {
  const [ticketsToUpload, setTicketsToUpload] = useState(0);
  const fetchUploadsNeeded = useCallback(async () => {
    const resultGroups = await getAllRemainingScanResultGroups();
    setTicketsToUpload(resultGroups.numberOfResults);
  }, []);

  useEffect(() => {
    fetchUploadsNeeded();
  }, [fetchUploadsNeeded]);

  const strings = useI18n(
    {
      finishedScanningHeader: 'upload-tickets.header.finished-scanning',
      finishedScanningMessageStart: 'upload-tickets.message.finished-scanning-start',
      finishedScanningMessageEnd: 'finished-scanning-end',
      ticketsFoundForUploadHeader: 'upload-tickets.header.tickets-found-to-upload',
      ticketsFoundForUploadMessage: [
        'upload-tickets.message.tickets-found-to-upload',
        { count: ticketsToUpload },
      ],
    },
    [ticketsToUpload]
  );

  return (
    <SafeAreaView style={styles.safeAreaView}>
      <View style={styles.headerContainer}>
        <Text>{strings.ticketsFoundForUploadHeader}</Text>
        <TouchableOpacity onPress={onDismiss}>
          <IconUIDismissLarge />
        </TouchableOpacity>
      </View>
      <Text style={{ marginVertical: UniformSpace.one }}>
        {strings.ticketsFoundForUploadMessage}
      </Text>
      <UploadTicketsButton onFinished={onDismiss} />
    </SafeAreaView>
  );
}

export function UploadTicketsButton({
  style,
  mainButtonText,
  onFinished,
}: {
  mainButtonText?: string;
  onFinished?: () => void;
} & ViewProps): React.ReactElement {
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>(UploadStatus.NeedsUpload);

  const session = useAppSession();
  const { processComplete, startUpload, errorGroups } = useUploadResults(session.gqlClients.public);

  const onNeedsUpload = useCallback(() => {
    if (uploadStatus !== UploadStatus.NeedsUpload) {
      return;
    }

    setUploadStatus(UploadStatus.Uploading);
    startUpload();
  }, [startUpload, uploadStatus]);

  const onUploadingComplete = useCallback(() => {
    if (onFinished) {
      onFinished();
    }
  }, [onFinished]);

  useEffect(() => {
    if (processComplete) {
      if (errorGroups.groupsFailedToUpload.length > 0) {
        setUploadStatus(UploadStatus.Error);
        setTimeout(() => {
          setUploadStatus(UploadStatus.NeedsUpload);
        }, 2500);
      } else {
        setUploadStatus(UploadStatus.Complete);
        setTimeout(() => {
          onUploadingComplete();
          setUploadStatus(UploadStatus.NeedsUpload);
        }, 1000);
      }
    }
  }, [errorGroups.groupsFailedToUpload.length, onUploadingComplete, processComplete]);

  const strings = useI18n(
    {
      needsUploading: 'upload-tickets.button.needs-uploading',
      error: 'upload-tickets.button.error-uploading',
      completed: 'upload-tickets.button.completed-uploading',
      uploading: 'upload-tickets.button.uploading',
    },
    []
  );

  const uploadButtonStates: Record<UploadStatus, ButtonState> = {
    [UploadStatus.NeedsUpload]: {
      text: mainButtonText ?? strings.needsUploading,
      status: undefined,
      testID: AppTestIDs.uploadRemainingTicketsButton,
    },
    [UploadStatus.Uploading]: {
      text: strings.uploading,
      status: 'spinning',
      testID: AppTestIDs.uploadRemainingTicketsButton,
    },
    [UploadStatus.Complete]: {
      text: strings.completed,
      status: 'success',
      testID: AppTestIDs.uploadRemainingTicketsButton,
    },
    [UploadStatus.Error]: {
      text: strings.error,
      status: 'failure',
      testID: AppTestIDs.uploadRemainingTicketsButton,
    },
  };

  const buttonState = uploadButtonStates[uploadStatus];

  return (
    <Button
      testID={buttonState.testID}
      text={buttonState.text}
      style={style}
      size={'small'}
      status={buttonState.status}
      buttonType="secondary"
      onPress={onNeedsUpload}
    />
  );
}
