import { HudlApolloClient } from '@hudl/gql-toolkit';

import { logError } from '../common/Logging';
import { ScanningRedemptionInput } from '../gql/public/__generated__/graphql';
import QueueBatchRedemption from '../gql/public/mutations/QueueBatchRedemption';

export async function queueBatchRedemption(
  client: HudlApolloClient,
  input: ScanningRedemptionInput
): Promise<{ success: boolean }> {
  try {
    const result = await client.mutate({
      mutation: QueueBatchRedemption,
      variables: {
        input,
      },
    });

    return {
      success: result.data?.queueBatchRedemption ?? false,
    };
  } catch (error) {
    logError(`QueueBatchRedemption failed: ${error}`, 'useQueueBatchRedemption');

    return {
      success: false,
    };
  }
}
