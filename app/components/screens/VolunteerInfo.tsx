import React, { useCallback, useMemo, useState } from 'react';
import { Text, View } from 'react-native';

import { ScrollView, TextInput } from 'react-native-gesture-handler';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useI18n } from '@hudl/jarvis/i18n';
import {
  Button,
  UniformSpace,
  UniformStyleSheet,
  useUniformStyles,
  useUniformTheme,
} from '@hudl/rn-uniform';

import { AppNav } from '../../Nav';
import { useAccessContext } from '../../context/AccessContext';
import { removeCurrentVolunteer, saveCurrentVolunteer } from '../../local_storage/VolunteerStorage';
import { useAppSession } from '../../session/AppSession';
import { DeveloperModeAccessCode } from '../../utils/Constants';
import { Navigation } from '../../utils/Enums';
import { isNullOrEmpty, isValidEmail } from '../../utils/StringUtils';
import { AppTestIDs } from '../AppTestIDs';
import { generalUniformStyles } from '../Styles';

const uniStyles = UniformStyleSheet.create((colors) => ({
  container: {
    flexDirection: 'column',
    height: '100%',
    paddingHorizontal: UniformSpace.one,
    justifyContent: 'space-between',
    backgroundColor: colors.bgLevel1,
    paddingTop: UniformSpace.four + UniformSpace.half,
  },
  inputContainer: {
    paddingBottom: UniformSpace.one,
    backgroundColor: colors.bgLevel1,
  },
  inputTextHeaderContainer: {
    flexDirection: 'row',
    backgroundColor: colors.bgLevel1,
  },
}));

export function VolunteerInfo(): React.ReactElement {
  const styles = useUniformStyles(uniStyles);
  const generalStyles = useUniformStyles(generalUniformStyles);
  const nav = AppNav.root.useNavigation();
  const { toggleDevelopmentEnvironment } = useAppSession();
  const { colors } = useUniformTheme();
  const accessContext = useAccessContext();

  const {
    params: { currentUser },
  } = AppNav.root.useRoute(Navigation.VolunteerInfo);

  const [firstName, setFirstName] = useState<string | undefined>(currentUser?.firstName);
  const [lastName, setLastName] = useState<string | undefined>(currentUser?.lastName);
  const [email, setEmail] = useState<string | undefined>(currentUser?.email);
  const [accessCode, setAccessCode] = useState<string | undefined>(currentUser?.accessCode);

  const isContinueDisabled = useMemo(() => {
    return (
      isNullOrEmpty(firstName) ||
      isNullOrEmpty(lastName) ||
      isNullOrEmpty(accessCode) ||
      isNullOrEmpty(email) ||
      !isValidEmail(email)
    );
  }, [accessCode, email, firstName, lastName]);

  const onSubmitPressed = useCallback(() => {
    if (!accessCode || !firstName || !lastName || !email) {
      return;
    }
    accessContext.setVolunteerInfo({
      accessCode,
      firstName,
      lastName,
      email,
    });
    accessContext.setEventInfo(undefined);
    saveCurrentVolunteer({
      firstName,
      lastName,
      email,
      accessCode: accessCode.toLocaleLowerCase('en-US'),
    });

    // Enabled developer options
    if (accessCode === DeveloperModeAccessCode) {
      console.log('Developer mode enabled');
      toggleDevelopmentEnvironment();
      nav.goBack();
      return;
    }

    nav.navigate(Navigation.LoadingOrg, {
      volunteerInfo: { accessCode, firstName, lastName, email },
    });
  }, [accessCode, firstName, lastName, email, accessContext, nav, toggleDevelopmentEnvironment]);

  const onNoPressed = useCallback(() => {
    nav.goBack();

    removeCurrentVolunteer();
  }, [nav]);

  const insets = useSafeAreaInsets();

  const strings = useI18n(
    {
      header: 'volunteer-info.header',
      subHeader: 'volunteer-info.sub-header',
      continueAsHeader: 'volunteer-info.continue-as-header',
      continueAsSubHeader: 'volunteer-info.continue-as-sub-header',
      firstName: 'volunteer-info.first-name',
      firstNamePlaceholder: 'volunteer-info.first-name-placeholder',
      lastName: 'volunteer-info.last-name',
      lastNamePlaceholder: 'volunteer-info.last-name-placeholder',
      accessCode: 'volunteer-info.access-code',
      accessCodePlaceholder: 'volunteer-info.access-code-placeholder',
      email: 'volunteer-info.email',
      emailPlaceholder: 'volunteer-info.email-placeholder',
    },
    []
  );
  const header = useMemo(() => {
    return currentUser === undefined ? strings.header : strings.continueAsHeader;
  }, [currentUser, strings.continueAsHeader, strings.header]);

  const subHeader = useMemo(() => {
    return currentUser === undefined ? strings.subHeader : strings.continueAsSubHeader;
  }, [currentUser, strings.continueAsSubHeader, strings.subHeader]);

  const canEdit = useMemo(() => {
    return currentUser === undefined;
  }, [currentUser]);

  return (
    <View style={[generalStyles.container]}>
      <View style={styles.container}>
        <ScrollView
          style={[generalStyles.container]}
          testID={AppTestIDs.volunteerInfoRoot}
          automaticallyAdjustKeyboardInsets={true}>
          <Text style={generalStyles.headerText}>{header}</Text>
          <Text style={generalStyles.contentText}>{subHeader}</Text>

          <View style={styles.inputContainer}>
            <View style={styles.inputTextHeaderContainer}>
              <Text style={generalStyles.textInputTitle}>{`${strings.firstName}`}</Text>
              <Text style={generalStyles.textReadDot}>{` *`}</Text>
            </View>
            <TextInput
              style={generalStyles.textInput}
              value={firstName}
              onChangeText={setFirstName}
              autoComplete={'given-name'}
              placeholder={strings.firstNamePlaceholder}
              placeholderTextColor={colors.contentSubtle}
              editable={canEdit}
              enabled={canEdit}
              testID={AppTestIDs.volunteerInfoFirstNameInput}
            />
          </View>

          <View style={styles.inputContainer}>
            <View style={styles.inputTextHeaderContainer}>
              <Text style={generalStyles.textInputTitle}>{`${strings.lastName}`}</Text>
              <Text style={generalStyles.textReadDot}>{` *`}</Text>
            </View>

            <TextInput
              style={generalStyles.textInput}
              value={lastName}
              onChangeText={setLastName}
              placeholder={strings.lastNamePlaceholder}
              placeholderTextColor={colors.contentSubtle}
              autoComplete={'family-name'}
              editable={canEdit}
              enabled={canEdit}
              testID={AppTestIDs.volunteerInfoLastNameInput}
            />
          </View>

          <View style={styles.inputContainer}>
            <View style={styles.inputTextHeaderContainer}>
              <Text style={generalStyles.textInputTitle}>{`${strings.email}`}</Text>
              <Text style={generalStyles.textReadDot}>{` *`}</Text>
            </View>
            <TextInput
              style={generalStyles.textInput}
              value={email}
              onChangeText={setEmail}
              placeholder={strings.emailPlaceholder}
              placeholderTextColor={colors.contentSubtle}
              editable={canEdit}
              enabled={canEdit}
              autoComplete={'email'}
              inputMode={'email'}
              testID={AppTestIDs.volunteerInfoEmailInput}
              keyboardType="email-address"
            />
          </View>

          <View style={styles.inputContainer}>
            <View style={styles.inputTextHeaderContainer}>
              <Text style={generalStyles.textInputTitle}>{`${strings.accessCode}`}</Text>
              <Text style={generalStyles.textReadDot}>{` *`}</Text>
            </View>

            <TextInput
              style={generalStyles.textInput}
              value={accessCode}
              onChangeText={setAccessCode}
              placeholder={strings.accessCodePlaceholder}
              placeholderTextColor={colors.contentSubtle}
              autoComplete={'off'}
              autoCapitalize={'none'}
              autoCorrect={false}
              editable={canEdit}
              enabled={canEdit}
              testID={AppTestIDs.volunteerInfoAccessCodeInput}
            />
          </View>
        </ScrollView>
        <View style={{ paddingBottom: insets.bottom + UniformSpace.one }}>
          {currentUser ? (
            <ConfirmInformation
              onYesPressed={onSubmitPressed}
              onNoPressed={onNoPressed}
              isContinueDisabled={isContinueDisabled}
            />
          ) : (
            <Button
              text="Continue"
              onPress={onSubmitPressed}
              isDisabled={isContinueDisabled}
              testID={AppTestIDs.volunteerInfoContinue}
            />
          )}
        </View>
      </View>
    </View>
  );
}

function ConfirmInformation({
  isContinueDisabled,
  onYesPressed,
  onNoPressed,
}: {
  isContinueDisabled: boolean;
  onYesPressed: () => void;
  onNoPressed: () => void;
}): React.ReactElement {
  return (
    <View>
      <Button
        style={{ marginBottom: UniformSpace.one }}
        text="Yes"
        onPress={onYesPressed}
        isDisabled={isContinueDisabled}
        buttonType={'secondary'}
        size="large"
        testID={AppTestIDs.volunteerInfoYes}
      />
      <Button
        text="No"
        onPress={onNoPressed}
        isDisabled={isContinueDisabled}
        buttonType={'subtle'}
        size="large"
        testID={AppTestIDs.volunteerInfoNo}
      />
    </View>
  );
}
