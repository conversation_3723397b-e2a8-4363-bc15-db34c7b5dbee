import React from 'react';

import { <PERSON> } from '@stripe/stripe-terminal-react-native';
import { act, fireEvent, screen, waitFor } from '@testing-library/react-native';

import { ConnectReader } from './ConnectReader';
import { mockReader } from '../../../../gql/mockData/TestData';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { DiscoveryMethod } from '../../../../utils/Enums';
import { setupAccessContextMocks } from '../../../../utils/TestUtils';
import { AppTestIDs } from '../../../AppTestIDs';

const mockConnectReader = jest.fn();
const mockDisconnectReader = jest.fn();

let mockConnectedReader = null as unknown as Reader.Type;

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: jest.fn().mockImplementation(() => ({
    connectedReader: mockConnectedReader,
    connectReader: mockConnectReader,
    disconnectReader: mockDisconnectReader,
  })),
  CommonError: {
    Canceled: 'canceled',
  },
}));

jest.mock('@sentry/react-native', () => ({
  captureMessage: jest.fn(),
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    addListener: jest.fn(),
  }),
}));

jest.mock('../../../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../../../components/queries/useAccessCodeHook');
setupAccessContextMocks();

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

describe('ConnectReader render tests- No Connected Reader', () => {
  it('Readers display correctly', async () => {
    const reader = mockReader;
    renderWithOptions(
      <ConnectReader
        discoveredReaders={[reader]}
        discoveryMethod={DiscoveryMethod.BluetoothScan}
        setReaderWithConnectionError={jest.fn()}
        onConnected={jest.fn()}
        onDisconnect={jest.fn()}
      />,
      {
        withNavigationContainer: true,
        withAppSessionProvider: true,
        withAccessContext: true,
      }
    );

    const testId = AppTestIDs.connectReaderDiscoveredReader(reader.serialNumber, false);
    await waitFor(() => expect(screen.getByTestId(testId)).toBeTruthy());
    expect(screen.getByTestId(testId)).not.toHaveProperty('disabled');
  });

  it('On press reader calls handler', async () => {
    mockConnectReader.mockImplementation(() => {
      return {
        reader: mockReader,
      };
    });
    const reader = mockReader;
    renderWithOptions(
      <ConnectReader
        discoveredReaders={[reader]}
        discoveryMethod={DiscoveryMethod.BluetoothScan}
        setReaderWithConnectionError={jest.fn()}
        onConnected={jest.fn()}
        onDisconnect={jest.fn()}
      />,
      {
        withNavigationContainer: true,
        withAppSessionProvider: true,
        withAccessContext: true,
      }
    );

    const testId = AppTestIDs.connectReaderDiscoveredReader(reader.serialNumber, false);
    await waitFor(() => expect(screen.getByTestId(testId)).toBeTruthy());
    // eslint-disable-next-line @typescript-eslint/await-thenable -- test fails without the await
    await act(() => fireEvent.press(screen.getByTestId(testId)));
    expect(mockConnectReader).toHaveBeenCalled();
  });
});

describe('ConnectReader render tests- Connected Reader', () => {
  it('Connected reader with no discovered readers', async () => {
    mockConnectedReader = mockReader;
    const reader = mockReader;
    renderWithOptions(
      <ConnectReader
        discoveredReaders={[reader]}
        discoveryMethod={DiscoveryMethod.BluetoothScan}
        setReaderWithConnectionError={jest.fn()}
        onConnected={jest.fn()}
        onDisconnect={jest.fn()}
      />,
      {
        withNavigationContainer: true,
        withAppSessionProvider: true,
        withAccessContext: true,
      }
    );

    await waitFor(() =>
      expect(
        screen.getByTestId(AppTestIDs.connectReaderConnectedReader(reader.serialNumber))
      ).toBeTruthy()
    );
  });

  it('Connected reader with discovered readers', async () => {
    const reader = mockReader;
    const discoveredReader = { ...reader, serialNumber: '1234' };
    renderWithOptions(
      <ConnectReader
        discoveredReaders={[discoveredReader, reader]}
        discoveryMethod={DiscoveryMethod.BluetoothScan}
        setReaderWithConnectionError={jest.fn()}
        onConnected={jest.fn()}
        onDisconnect={jest.fn()}
      />,
      {
        withNavigationContainer: true,
        withAppSessionProvider: true,
        withAccessContext: true,
      }
    );

    await waitFor(() =>
      expect(
        screen.getByTestId(
          AppTestIDs.connectReaderDiscoveredReader(discoveredReader.serialNumber, true)
        )
      ).toBeTruthy()
    );
    expect(
      screen.getByTestId(AppTestIDs.connectReaderConnectedReader(reader.serialNumber))
    ).toBeTruthy();
  });

  it('Pressing disconnect calls handler', async () => {
    const reader = mockReader;
    const onDisconnect = jest.fn();

    renderWithOptions(
      <ConnectReader
        discoveredReaders={[reader]}
        discoveryMethod={DiscoveryMethod.BluetoothScan}
        setReaderWithConnectionError={jest.fn()}
        onConnected={jest.fn()}
        onDisconnect={onDisconnect}
      />,
      {
        withNavigationContainer: true,
        withAppSessionProvider: true,
        withAccessContext: true,
      }
    );

    await waitFor(() =>
      fireEvent.press(screen.getByTestId(AppTestIDs.connectReaderDisconnectButton))
    );
    await waitFor(() => expect(mockDisconnectReader).toHaveBeenCalled());

    await waitFor(() => expect(onDisconnect).toHaveBeenCalled());
  });
});
