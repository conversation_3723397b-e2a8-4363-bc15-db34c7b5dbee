import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const stylesheet = UniformStyleSheet.create((colors) => ({
  container: {
    flexGrow: 1,
  },
  scrollableContainer: {
    flexGrow: 1,
  },
  discoveredReaders: {
    gap: UniformSpace.threeQuarter,
  },
  touchableContainer: {
    backgroundColor: colors.bgLevel0,
    padding: UniformSpace.one,
    borderRadius: 4,
  },
  discoveredReader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  connectedReader: {
    backgroundColor: colors.bgLevel0,
    padding: UniformSpace.one,
    borderRadius: 4,
  },
  discoveredReaderText: {
    flexGrow: 1,
    flexShrink: 1,
    minWidth: 0,
    fontWeight: '700',
    paddingBottom: UniformSpace.quarter,
  },
  connectedText: {
    flexShrink: 0,
    textAlign: 'right',
  },
  discoveredReaderContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: UniformSpace.two,
  },
  readerNameContainer: {
    display: 'flex',
    width: '80%',
  },
  disconnectButton: {
    paddingTop: UniformSpace.one,
  },
  batteryLevelContainer: {
    display: 'flex',
    flexDirection: 'row',
    gap: UniformSpace.quarter,
    alignItems: 'center',
    paddingTop: UniformSpace.one,
  },
  reactNativeText: {
    color: colors.contentDefault,
    fontSize: 16,
    lineHeight: 24,
  },
  linkText: {
    color: colors.linkArticleText,
    fontSize: 16,
    lineHeight: 24,
  },
  deviceType: {
    paddingTop: UniformSpace.quarter,
  },
  spinnerContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignContent: 'center',
  },
  softwareUpdateInstalling: {
    paddingTop: UniformSpace.half,
  },
  loadingContainer: {
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
}));
