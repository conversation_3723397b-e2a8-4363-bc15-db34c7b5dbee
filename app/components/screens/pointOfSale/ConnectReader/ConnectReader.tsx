import React, { useCallback, useState } from 'react';
import { View } from 'react-native';

import {
  CommonError,
  Reader,
  StripeError,
  useStripeTerminal,
} from '@stripe/stripe-terminal-react-native';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, ItemTitle, Spinner, Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './ConnectReader.style';
import { logError } from '../../../../common/Logging';
import { useAccessContext } from '../../../../context/AccessContext';
import { useAppSession } from '../../../../session/AppSession';
import { getBatteryLevelIcon } from '../../../../utils/BatteryLevelUtils';
import { DiscoveryMethod, StripeCommonErrorCodes } from '../../../../utils/Enums';
import { getLocationIdForSchool } from '../../../../utils/LocationUtils';
import { getFormattedBatteryLevel } from '../../../../utils/NumberFormatUtils';
import {
  formatSoftwareUpdateProgress,
  getEstimatedUpdateDuration,
  getReaderDisplayName,
} from '../../../../utils/ReaderUtils';
import { AppTestIDs } from '../../../AppTestIDs';

type Props = {
  discoveredReaders: Reader.Type[];
  discoveryMethod: string;
  setReaderWithConnectionError: (errorCode: string | null) => void;
  onDisconnect: () => void;
  onConnected: () => void;
};

export function ConnectReader(props: Props): React.ReactElement {
  const {
    discoveredReaders,
    discoveryMethod,
    setReaderWithConnectionError,
    onDisconnect,
    onConnected,
  } = props;
  const styles = useUniformStyles(stylesheet);
  const [connectingReader, setConnectingReader] = useState<{ [serialNumber: string]: boolean }>({});
  const [installingUpdate, setInstallingUpdate] = useState<{
    [serialNumber: string]: string | null;
  }>({});
  const [installProgress, setInstallProgress] = useState<string>('');
  const { isDevelopmentEnvironment } = useAppSession();
  const accessContext = useAccessContext();
  const { organization } = accessContext;

  const { connectedReader, connectReader, disconnectReader } = useStripeTerminal({
    onDidStartInstallingUpdate: (update: Reader.SoftwareUpdate) => {
      const connectingReaderId = Object.entries(connectingReader).find(([, value]) => value)?.[0];
      connectingReaderId &&
        setInstallingUpdate({ [connectingReaderId]: getEstimatedUpdateDuration(update) });
    },
    onDidReportReaderSoftwareUpdateProgress(progress) {
      setInstallProgress(progress);
    },
    onDidFinishInstallingUpdate: (result) => {
      if (result.error) {
        logError(`Error installing update: ${result.error?.code}`, 'ConnectReader');
        if (
          (result.error.code as string) ===
          StripeCommonErrorCodes.ReaderSoftwareUpdateFailedBatteryLow
        ) {
          setReaderWithConnectionError(StripeCommonErrorCodes.ReaderSoftwareUpdateFailedBatteryLow);
        }
      }
      const connectingReaderId = Object.entries(connectingReader).find(([, value]) => value)?.[0];
      connectingReaderId && setInstallingUpdate({ [connectingReaderId]: null });
    },
  });

  const i18nStrings = useI18n(
    {
      connected: 'connect-reader.connected',
      disconnect: 'connect-reader.disconnect',
      softwareUpdateInstalling: 'connect-reader.software-update-installing',
    },
    []
  );

  const handleConnectBluetoothReader = useCallback(
    async (
      reader: Reader.Type
    ): Promise<{
      error: StripeError<CommonError> | undefined;
    }> => {
      setConnectingReader({ [reader.serialNumber]: true });
      setReaderWithConnectionError(null);

      const { reader: connectedBluetoothReader, error } = await connectReader(
        {
          reader,
          locationId:
            organization && getLocationIdForSchool(organization, isDevelopmentEnvironment),
          autoReconnectOnUnexpectedDisconnect: true,
        },
        'bluetoothScan'
      );

      if (!error && connectedBluetoothReader) {
        setConnectingReader({ [reader.serialNumber]: false });
      }

      return { error };
    },
    [connectReader, isDevelopmentEnvironment, organization, setReaderWithConnectionError]
  );

  const handleConnectReader = useCallback(
    (reader: Reader.Type): (() => void) => {
      return async (): Promise<void> => {
        let error: StripeError | undefined;
        if (discoveryMethod === DiscoveryMethod.BluetoothScan) {
          const result = await handleConnectBluetoothReader(reader);
          error = result?.error;
        }

        if (error) {
          logError(
            `Connection error: ${error.code}. Message: ${error.message}.`,
            'handleConnectReader'
          );

          setConnectingReader({ [reader.serialNumber]: false });
          setReaderWithConnectionError(error.code);
        } else {
          onConnected();
        }
      };
    },
    [discoveryMethod, handleConnectBluetoothReader, onConnected, setReaderWithConnectionError]
  );

  const onDisconnectPress = useCallback(async () => {
    await disconnectReader();
    onDisconnect?.();
  }, [disconnectReader, onDisconnect]);

  const renderDiscoveredReader = (reader: Reader.Type): React.ReactElement => {
    const isDisabled =
      (!!connectedReader && connectedReader?.serialNumber !== reader.serialNumber) ||
      Object.values(connectingReader).some((v) => v);
    return (
      <TouchableOpacity
        key={reader.serialNumber}
        style={styles.touchableContainer}
        testID={AppTestIDs.connectReaderDiscoveredReader(reader.serialNumber, isDisabled)}
        disabled={isDisabled}
        onPress={handleConnectReader(reader)}>
        <View style={styles.discoveredReader}>
          <View style={styles.readerNameContainer}>
            <Text style={styles.discoveredReaderText}>{reader.serialNumber}</Text>
            <View style={styles.deviceType}>
              <ItemTitle>{getReaderDisplayName(reader)}</ItemTitle>
            </View>
          </View>
          <View style={styles.spinnerContainer}>
            {connectingReader[reader.serialNumber] && <Spinner size="small" />}
          </View>
        </View>
        {installingUpdate[reader.serialNumber] && (
          <View style={styles.softwareUpdateInstalling}>
            <Text color="subtle">{`${i18nStrings.softwareUpdateInstalling} ${
              installingUpdate[reader.serialNumber]
            }`}</Text>
            <Text>{formatSoftwareUpdateProgress(installProgress)}</Text>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const renderConnectedReader = (reader: Reader.Type): React.ReactElement => (
    <View
      key={reader.serialNumber}
      style={styles.connectedReader}
      testID={AppTestIDs.connectReaderConnectedReader(reader.serialNumber)}>
      <View style={styles.discoveredReaderContainer}>
        <Text style={styles.discoveredReaderText}>{reader.serialNumber}</Text>
        <Text color="subtle" style={styles.connectedText}>
          {i18nStrings.connected}
        </Text>
      </View>
      <View style={styles.deviceType}>
        <ItemTitle>{getReaderDisplayName(reader)}</ItemTitle>
      </View>
      <View style={styles.discoveredReaderContainer}>
        <View style={styles.disconnectButton}>
          <Button
            onPress={onDisconnectPress}
            text={i18nStrings.disconnect}
            size="small"
            buttonType="secondary"
            testID={AppTestIDs.connectReaderDisconnectButton}
          />
        </View>
        <View style={styles.batteryLevelContainer}>
          {connectedReader &&
            getBatteryLevelIcon(
              connectedReader.batteryLevel ?? 0,
              Boolean(connectedReader.isCharging),
              'large',
              'subtle'
            )}
          {
            <Text color={(connectedReader?.batteryLevel ?? 0) < 0.25 ? 'critical' : 'subtle'}>
              {getFormattedBatteryLevel(connectedReader?.batteryLevel)}
            </Text>
          }
        </View>
      </View>
    </View>
  );

  return (
    <View testID={AppTestIDs.connectReaderLandingRoot} style={styles.container}>
      <ScrollView style={styles.scrollableContainer}>
        <View style={styles.discoveredReaders}>
          {discoveredReaders?.map((reader) => {
            if (connectedReader && connectedReader.serialNumber === reader.serialNumber) {
              return renderConnectedReader(reader);
            }
            return renderDiscoveredReader(reader);
          })}
        </View>
      </ScrollView>
    </View>
  );
}
