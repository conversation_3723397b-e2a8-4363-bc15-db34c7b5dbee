import React from 'react';
import { Text } from 'react-native';

import { Reader } from '@stripe/stripe-terminal-react-native';
import { screen, act } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import DiscoverReadersContainer from './DiscoverReadersContainer';
import enUS from '../../../../../strings/en-US.json';
import { renderWithOptions } from '../../../../../test/renderHelpers';
import { DiscoverReadersError } from '../../../../../utils/Enums';

jest.useFakeTimers();

const mockDiscoverReaders = jest.fn();
const mockCancelDiscovering = jest.fn();
const mockSimulateReaderUpdate = jest.fn();
const mockConnectedReader: Reader.Type | null = null;

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: jest.fn().mockImplementation(({}) => ({
    discoverReaders: mockDiscoverReaders,
    cancelDiscovering: mockCancelDiscovering,
    simulateReaderUpdate: mockSimulateReaderUpdate,
    connectedReader: mockConnectedReader,
  })),
  CommonError: { Canceled: 'canceled' },
}));

const mockWrapper = jest.fn();
function MockDiscoverReadersWrapper({
  headerText,
  buttonText,
}: {
  headerText: string;
  buttonText: string;
}): JSX.Element {
  mockWrapper({ headerText, buttonText });
  return (
    <>
      <Text testID="header">{headerText}</Text>
      <Text testID="button">{buttonText}</Text>
    </>
  );
}

jest.mock('../DiscoverReadersWrapper/DiscoverReadersWrapper', () => ({
  __esModule: true,
  default: MockDiscoverReadersWrapper,
}));

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

describe('DiscoverReadersContainer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows initializing state by default', () => {
    renderWithOptions(<DiscoverReadersContainer hide={jest.fn()} />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId('header').props.children.toLowerCase()).toContain('finding');
    expect(screen.getByTestId('button').props.children.toLowerCase()).toContain('cancel');
  });

  it('shows stillLooking state after 15 seconds', () => {
    renderWithOptions(<DiscoverReadersContainer hide={jest.fn()} />, {
      withNavigationContainer: true,
    });

    act(() => {
      jest.advanceTimersByTime(15000);
    });

    expect(mockWrapper).toHaveBeenLastCalledWith(
      expect.objectContaining({
        headerText: expect.stringMatching("We're Still Looking..."),
      })
    );
  });

  it('renders Bluetooth error state', () => {
    jest
      .spyOn(React, 'useState')
      .mockImplementationOnce(() => ['finding', jest.fn()])
      .mockImplementationOnce(() => [DiscoverReadersError.BluetoothError, jest.fn()])
      .mockImplementation(() => [false, jest.fn()]);

    renderWithOptions(<DiscoverReadersContainer hide={jest.fn()} />, {
      withNavigationContainer: true,
    });

    expect(mockWrapper).toHaveBeenLastCalledWith(
      expect.objectContaining({
        headerText: expect.stringMatching('Finding Readers Nearby'),
        buttonText: expect.stringMatching('Cancel'),
      })
    );
  });
});
