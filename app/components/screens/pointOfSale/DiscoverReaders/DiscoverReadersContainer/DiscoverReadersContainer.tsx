import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Text, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconBattery0, IconCritical, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './DiscoverReadersContainer.style';
import IconBluetoothDisabled from '../../../../../icons/IconBluetoothDisabled';
import IconFindReadersError from '../../../../../icons/IconFindReadersError';
import { DiscoverReadersError, DiscoverReadersStage } from '../../../../../utils/Enums';
import { openSupportLink } from '../../../../../utils/UrlUtils';
import DiscoverReadersInit from '../DiscoverReadersInit/DiscoverReadersInit';
import DiscoverReadersList from '../DiscoverReadersList/DiscoverReadersList';
import DiscoverReadersWrapper, {
  DiscoverReadersWrapperProps,
} from '../DiscoverReadersWrapper/DiscoverReadersWrapper';

interface Props {
  hide: () => void;
}

export default function DiscoverReadersContainer({ hide }: Props): React.ReactElement {
  const styles = useUniformStyles(stylesheet);

  const initRef = useRef<{ retry: () => void }>(null);
  const listRef = useRef<{ retry: () => void }>(null);

  const [stage, setStage] = useState<DiscoverReadersStage>(DiscoverReadersStage.Initializing);
  const [error, setError] = useState<DiscoverReadersError | undefined>();
  const [stillLooking, setStillLooking] = useState(false);

  const onInitialized = useCallback(() => setStage(DiscoverReadersStage.Finding), []);

  const retryInit = useCallback(() => {
    setError(undefined);
    setStage(DiscoverReadersStage.Initializing);
    initRef.current?.retry();
  }, []);

  const retryList = useCallback(() => {
    setError(undefined);
    setStage(DiscoverReadersStage.Finding);
    listRef.current?.retry();
  }, []);

  const onConnectionError = useCallback(
    (hasError: boolean) => {
      if (hasError) {
        setError(DiscoverReadersError.SystemConnection);
      } else if (error === DiscoverReadersError.SystemConnection) {
        setError(undefined);
      }
    },
    [error]
  );

  useEffect(() => {
    if (stage !== DiscoverReadersStage.Finding && stage !== DiscoverReadersStage.Initializing) {
      setStillLooking(false);
    }

    const stillLookingTimeout = setTimeout(async () => {
      if (stage === DiscoverReadersStage.Finding || stage === DiscoverReadersStage.Initializing) {
        setStillLooking(true);
      }
    }, 15000);

    return () => {
      clearTimeout(stillLookingTimeout);
    };
  }, [stage]);

  useEffect(() => {
    if (error === DiscoverReadersError.NoReadersFound) {
      setStillLooking(false);
    }
  }, [error]);

  const strings = useI18n(
    {
      findingTitle: 'reader-connection-wrapper.finding-title',
      connectTitle: 'reader-connection-wrapper.connect-title',
      findingDetails: 'reader-connection-wrapper.finding-details',
      connectDetails: 'reader-connection-wrapper.connect-details',
      continueButton: 'reader-connection-wrapper.continue-button',
      cancelButton: 'reader-connection-wrapper.cancel-button',
      tryAgain: 'reader-connection-wrapper.try-again',
      stillLookingTitle: 'reader-connection-wrapper.still-looking-title',
      stillLookingDetails: 'reader-connection-wrapper.still-looking-details',
      systemConnectionErrorHeaderText: 'discover-readers-errors.system-connection-error-title',
      systemConnectionErrorSubheaderText:
        'discover-readers-errors.system-connection-error-subheader',
      errorContactSupport: 'discover-readers-errors.error-contact-support',
      genericError: 'discover-readers-errors.generic-error',
      unableToDiscover: 'discover-readers-errors.unable-to-discover',
      unableToConnect: 'discover-readers-errors.unable-to-connect',
      noReadersFoundTitle: 'discover-readers-errors.no-readers-found-title',
      noReadersFoundSubtitle: 'discover-readers-errors.no-readers-found-subtitle',
      lowBatteryTitle: 'discover-readers-errors.low-battery-title',
      lowBatterySubtitle: 'discover-readers-errors.low-battery-details',
    },
    []
  );

  const systemConnectionErrorSubheader = useMemo(() => {
    return (
      <>
        <Text style={styles.subheaderText}>{strings.systemConnectionErrorSubheaderText}</Text>
        <Text style={styles.subheaderText}>
          {strings.errorContactSupport[0]}
          <Text style={styles.linkText} onPress={openSupportLink}>
            {strings.errorContactSupport[1]}
          </Text>
          {strings.errorContactSupport[2]}
        </Text>
      </>
    );
  }, [
    strings.errorContactSupport,
    strings.systemConnectionErrorSubheaderText,
    styles.subheaderText,
    styles.linkText,
  ]);

  const genericErrorSubheader = useMemo(() => {
    return (
      <>
        <Text style={styles.subheaderText}>
          {strings.genericError[0]}
          <Text style={styles.linkText} onPress={openSupportLink}>
            {strings.genericError[1]}
          </Text>
        </Text>
      </>
    );
  }, [strings.genericError, styles.subheaderText, styles.linkText]);

  const noReadersFoundSubheader = useMemo(() => {
    return (
      <>
        <Text style={styles.subheaderText}>
          {strings.noReadersFoundSubtitle[0]}
          <Text style={styles.linkText} onPress={openSupportLink}>
            {strings.noReadersFoundSubtitle[1]}
          </Text>
        </Text>
      </>
    );
  }, [strings.noReadersFoundSubtitle, styles.subheaderText, styles.linkText]);

  const connectDetailsSubheader = useMemo(() => {
    return <Text style={styles.subheaderText}>{strings.connectDetails}</Text>;
  }, [strings.connectDetails, styles.subheaderText]);

  const findingSubheader = useMemo(() => {
    return (
      <Text style={styles.subheaderText}>
        {stillLooking ? strings.stillLookingDetails : strings.findingDetails}
      </Text>
    );
  }, [stillLooking, strings.findingDetails, strings.stillLookingDetails, styles.subheaderText]);

  const lowBatterySubheader = useMemo(() => {
    return (
      <>
        <Text style={styles.subheaderText}>
          {strings.lowBatterySubtitle[0]}
          <Text style={styles.linkText} onPress={openSupportLink}>
            {strings.lowBatterySubtitle[1]}
          </Text>
        </Text>
      </>
    );
  }, [strings.lowBatterySubtitle, styles.linkText, styles.subheaderText]);

  const errorContent: DiscoverReadersWrapperProps | undefined = useMemo(() => {
    switch (error) {
      case DiscoverReadersError.BluetoothError:
        return {
          headerText: strings.unableToConnect,
          subHeader: genericErrorSubheader,
          onButtonPress: () => retryList(),
          buttonText: strings.tryAgain,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: () => hide(),
          children: (
            <View style={styles.iconWrapper}>
              <IconBluetoothDisabled height={110} width={105} />
            </View>
          ),
        };
      case DiscoverReadersError.ConnectionError:
        return {
          headerText: strings.unableToDiscover,
          subHeader: genericErrorSubheader,
          onButtonPress: () => retryList(),
          buttonText: strings.tryAgain,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: () => hide(),
          children: (
            <View style={styles.iconWrapper}>
              <IconCritical height={110} width={105} />
            </View>
          ),
        };
      case DiscoverReadersError.NoReadersFound:
        return {
          headerText: strings.noReadersFoundTitle,
          subHeader: noReadersFoundSubheader,
          onButtonPress: () => retryList(),
          buttonText: strings.tryAgain,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: () => hide(),
          children: (
            <View style={styles.iconWrapper}>
              <IconFindReadersError height={110} width={105} />
            </View>
          ),
        };
      case DiscoverReadersError.LowBattery:
        return {
          headerText: strings.lowBatteryTitle,
          subHeader: lowBatterySubheader,
          onButtonPress: () => retryList(),
          buttonText: strings.tryAgain,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: () => hide(),
          children: (
            <View style={styles.iconWrapper}>
              <IconBattery0 height={110} width={105} />
            </View>
          ),
        };
      default:
        return {
          headerText: strings.systemConnectionErrorHeaderText,
          subHeader: systemConnectionErrorSubheader,
          onButtonPress: () => retryInit(),
          buttonText: strings.tryAgain,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: () => hide(),
          children: (
            <View style={styles.iconWrapper}>
              <IconCritical height={110} width={105} />
            </View>
          ),
        };
    }
  }, [
    error,
    genericErrorSubheader,
    hide,
    lowBatterySubheader,
    noReadersFoundSubheader,
    retryInit,
    retryList,
    strings,
    styles.iconWrapper,
    systemConnectionErrorSubheader,
  ]);

  const discoverReadersList = useMemo(
    () => <DiscoverReadersList ref={listRef} setError={setError} setStage={setStage} />,
    []
  );

  const listContent: DiscoverReadersWrapperProps | undefined = useMemo(() => {
    switch (stage) {
      case DiscoverReadersStage.Finding:
        return {
          headerText: stillLooking ? strings.stillLookingTitle : strings.findingTitle,
          subHeader: findingSubheader,
          onButtonPress: hide,
          buttonText: strings.cancelButton,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: hide,
          children: discoverReadersList,
        };
      case DiscoverReadersStage.Selecting:
        return {
          headerText: strings.connectTitle,
          subHeader: connectDetailsSubheader,
          onButtonPress: hide,
          buttonText: strings.continueButton,
          buttonType: 'primary',
          isDisabled: true,
          onClose: hide,
          children: discoverReadersList,
        };
      case DiscoverReadersStage.Selected:
        return {
          headerText: strings.connectTitle,
          subHeader: connectDetailsSubheader,
          onButtonPress: hide,
          buttonText: strings.continueButton,
          buttonType: 'primary',
          isDisabled: false,
          onClose: hide,
          children: discoverReadersList,
        };
      default:
        return {
          headerText: stillLooking ? strings.stillLookingTitle : strings.findingTitle,
          subHeader: findingSubheader,
          onButtonPress: hide,
          buttonText: strings.cancelButton,
          buttonType: 'subtle',
          isDisabled: false,
          onClose: hide,
          children: (
            <DiscoverReadersInit
              ref={initRef}
              onInitialized={onInitialized}
              onSetError={onConnectionError}
            />
          ),
        };
    }
  }, [
    connectDetailsSubheader,
    discoverReadersList,
    findingSubheader,
    hide,
    onConnectionError,
    onInitialized,
    stage,
    stillLooking,
    strings,
  ]);

  const wrapperContent: DiscoverReadersWrapperProps | undefined = useMemo(() => {
    if (error) {
      return errorContent;
    } else {
      return listContent;
    }
  }, [error, errorContent, listContent]);

  return <DiscoverReadersWrapper {...wrapperContent} />;
}
