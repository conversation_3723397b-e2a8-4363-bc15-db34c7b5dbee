import React from 'react';
import { View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './DiscoverReadersLoading.style';
import IconFindReaders from '../../../../../icons/IconFindReaders';
import { AppTestIDs } from '../../../../AppTestIDs';

export default function DiscoverReadersLoading(): React.ReactElement {
  const styles = useUniformStyles(stylesheet);

  const strings = useI18n(
    {
      findingReaders: 'discover-readers-content.finding-readers',
    },
    []
  );

  return (
    <View style={styles.container} testID={AppTestIDs.readerConnectionLoadingContainer}>
      <IconFindReaders width={192} height={96} />
      <Text style={styles.findingText}>{strings.findingReaders}</Text>
    </View>
  );
}
