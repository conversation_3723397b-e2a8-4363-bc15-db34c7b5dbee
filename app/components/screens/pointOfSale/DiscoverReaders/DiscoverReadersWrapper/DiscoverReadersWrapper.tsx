import React, { ReactElement } from 'react';
import { SafeAreaView, TouchableOpacity, View } from 'react-native';

import { Button, IconUIDismissMedium, Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './DiscoverReadersWrapper.style';
import { AppTestIDs } from '../../../../AppTestIDs';

export interface DiscoverReadersWrapperProps {
  headerText: string;
  subHeader?: ReactElement;
  buttonText: string;
  buttonType: 'subtle' | 'primary';
  isDisabled: boolean;
  children: ReactElement;
  onButtonPress: () => void;
  onClose: () => void;
}

export default function DiscoverReadersWrapper({
  headerText,
  subHeader,
  buttonText,
  buttonType,
  isDisabled,
  children,
  onButtonPress,
  onClose,
}: DiscoverReadersWrapperProps): React.ReactElement {
  const styles = useUniformStyles(stylesheet);

  return (
    <SafeAreaView>
      <View style={styles.container}>
        <View style={styles.header}>
          <View style={styles.headerTop}>
            <TouchableOpacity onPress={onClose} testID={AppTestIDs.readerConnectionWrapperClose}>
              <IconUIDismissMedium color={'subtle'} space={['quarterBottom']} />
            </TouchableOpacity>
            <Text style={styles.headerText}>{headerText}</Text>
          </View>
          {subHeader}
        </View>
        {children}
        <View style={styles.buttonWrapper}>
          <Button
            onPress={onButtonPress}
            text={buttonText}
            isDisabled={isDisabled}
            buttonType={buttonType}
            testID={AppTestIDs.readerConnectionWrapperButton}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
