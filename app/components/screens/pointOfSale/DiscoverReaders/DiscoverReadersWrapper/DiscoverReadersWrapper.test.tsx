import React from 'react';
import { Text } from 'react-native';

import { fireEvent, screen } from '@testing-library/react-native';

import DiscoverReadersWrapper, { DiscoverReadersWrapperProps } from './DiscoverReadersWrapper';
import { renderWithOptions } from '../../../../../test/renderHelpers';
import { AppTestIDs } from '../../../../AppTestIDs';

describe('DiscoverReadersWrapper render tests', () => {
  const defaultProps: DiscoverReadersWrapperProps = {
    headerText: 'Test Header',
    subHeader: <Text testID="sub-header">Sub header content</Text>,
    buttonText: 'Continue',
    buttonType: 'primary',
    isDisabled: false,
    children: <Text testID="child">Child content</Text>,
    onButtonPress: jest.fn(),
    onClose: jest.fn(),
  };

  it('renders the wrapper with header, subheader, and children', () => {
    renderWithOptions(<DiscoverReadersWrapper {...defaultProps} />, {
      withNavigationContainer: true,
    });

    expect(screen.getByText('Test Header')).toBeTruthy();
    expect(screen.getByTestId('sub-header')).toBeTruthy();
    expect(screen.getByTestId('child')).toBeTruthy();
    expect(screen.getByText('Continue')).toBeTruthy();
  });

  it('calls onClose when the dismiss icon is pressed', () => {
    renderWithOptions(<DiscoverReadersWrapper {...defaultProps} />, {
      withNavigationContainer: true,
    });

    const closeButton = screen.getByTestId(AppTestIDs.readerConnectionWrapperClose);
    fireEvent.press(closeButton);

    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('calls onButtonPress when the CTA button is pressed', () => {
    renderWithOptions(<DiscoverReadersWrapper {...defaultProps} />, {
      withNavigationContainer: true,
    });

    fireEvent.press(screen.getByText('Continue'));

    expect(defaultProps.onButtonPress).toHaveBeenCalled();
  });

  it('disables the CTA button when isDisabled is true', () => {
    const onButtonPress = jest.fn();

    renderWithOptions(
      <DiscoverReadersWrapper {...defaultProps} isDisabled={true} onButtonPress={onButtonPress} />,
      {
        withNavigationContainer: true,
      }
    );

    const button = screen.getByTestId(AppTestIDs.readerConnectionWrapperButton);
    fireEvent.press(button);

    expect(onButtonPress).not.toHaveBeenCalled();
  });
});
