import { UniformStyleSheet, UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../../utils/ColorUtils';

export const stylesheet = UniformStyleSheet.create(() => ({
  container: {
    height: '100%',
    paddingTop: UniformSpace.oneAndQuarter,
  },
  header: {
    paddingHorizontal: UniformSpace.one,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: UniformSpace.half,
  },
  headerText: {
    color: nonUniformColors.contentStrongBG,
    fontSize: 24,
    fontWeight: '700',
  },
  buttonWrapper: {
    paddingHorizontal: UniformSpace.one,
    backgroundColor: nonUniformColors.containerBG,
    marginTop: 'auto',
    paddingTop: UniformSpace.one,
  },
}));
