import React, {
  ForwardedRef,
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
} from 'react';
import { PermissionsAndroid, Platform } from 'react-native';

import { useStripeTerminal } from '@stripe/stripe-terminal-react-native';

import { useI18n } from '@hudl/jarvis/i18n';

import { logError } from '../../../../../common/Logging';
import { PlatformOS } from '../../../../../utils/Enums';
import DiscoverReadersLoading from '../DiscoverReadersLoading/DiscoverReadersLoading';

interface Props {
  onInitialized: () => void;
  onSetError: (connectionError: boolean) => void;
}

export type DiscoverReadersInitHandle = {
  retry: () => void;
};

function DiscoverReadersInit(
  { onInitialized, onSetError }: Props,
  ref: ForwardedRef<DiscoverReadersInitHandle | undefined>
): React.ReactElement {
  const { initialize } = useStripeTerminal();

  const strings = useI18n(
    {
      androidPermissionTitle: 'reader-connection-init.android-permission-title',
      androidPermissionMessage: 'reader-connection-init.android-permission-message',
      androidPermissionButton: 'reader-connection-init.android-permission-button',
    },
    []
  );

  const initializeStripe = useCallback(async (): Promise<void> => {
    onSetError(false);
    const { error } = await initialize();

    if (error) {
      logError(
        `Stripe failed to initialize. Code: ${error.code}. Message: ${error.message}.`,
        'initializeStripe'
      );
      onSetError(true);
    } else {
      onInitialized();
    }
  }, [initialize, onInitialized, onSetError]);

  const requestAndroidPermissions = useCallback(async (): Promise<void> => {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      {
        title: strings.androidPermissionTitle,
        message: strings.androidPermissionMessage,
        buttonPositive: strings.androidPermissionButton,
      }
    );

    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      initializeStripe();
    } else {
      onSetError(true);
    }
  }, [initializeStripe, onSetError, strings]);

  const requestPermissionsAndInitialize = useCallback(async (): Promise<void> => {
    if (Platform.OS === PlatformOS.Android) {
      await requestAndroidPermissions();
    } else {
      await initializeStripe();
    }
  }, [initializeStripe, requestAndroidPermissions]);

  useEffect(() => {
    requestPermissionsAndInitialize();
  }, [requestPermissionsAndInitialize]);

  useImperativeHandle(ref, () => ({
    retry() {
      requestPermissionsAndInitialize();
    },
  }));

  return <DiscoverReadersLoading />;
}

export default forwardRef(DiscoverReadersInit);
