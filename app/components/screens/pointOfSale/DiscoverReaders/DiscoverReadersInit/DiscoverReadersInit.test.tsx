import React, { createRef } from 'react';
import { Platform, PermissionsAndroid } from 'react-native';

import { waitFor } from '@testing-library/react-native';

import DiscoverReadersInit, { DiscoverReadersInitHandle } from './DiscoverReadersInit';
import { renderWithOptions } from '../../../../../test/renderHelpers';
import { PlatformOS } from '../../../../../utils/Enums';

const mockInitialize = jest.fn();

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: jest.fn().mockImplementation(() => ({
    initialize: mockInitialize,
  })),
}));

jest.mock('@hudl/jarvis/logging', () => ({
  HudlLogger: {
    logError: jest.fn(),
  },
}));

describe('DiscoverReadersInit', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('calls initialize on iOS mount', async () => {
    Platform.OS = PlatformOS.IOS;
    mockInitialize.mockImplementation(() => {
      return {
        clientSecret: 'clientSecret',
      };
    });

    const onInitialized = jest.fn();
    const onSetError = jest.fn();

    renderWithOptions(
      <DiscoverReadersInit onInitialized={onInitialized} onSetError={onSetError} />,
      {
        withNavigationContainer: true,
      }
    );

    await waitFor(() => expect(mockInitialize).toHaveBeenCalled());
    expect(onSetError).toHaveBeenCalledWith(false);
    expect(onInitialized).toHaveBeenCalled();
  });

  it('requests permissions and initializes on Android', async () => {
    Platform.OS = PlatformOS.Android;
    mockInitialize.mockImplementation(() => {
      return {
        clientSecret: 'clientSecret',
      };
    });

    const requestSpy = jest
      .spyOn(PermissionsAndroid, 'request')
      .mockResolvedValue(PermissionsAndroid.RESULTS.GRANTED);

    const onInitialized = jest.fn();
    const onSetError = jest.fn();

    renderWithOptions(
      <DiscoverReadersInit onInitialized={onInitialized} onSetError={onSetError} />,
      {
        withNavigationContainer: true,
      }
    );

    await waitFor(() => {
      expect(requestSpy).toHaveBeenCalled();
      expect(mockInitialize).toHaveBeenCalled();
      expect(onInitialized).toHaveBeenCalled();
    });
  });

  it('sets error if Android permissions denied', async () => {
    Platform.OS = PlatformOS.Android;

    jest.spyOn(PermissionsAndroid, 'request').mockResolvedValue(PermissionsAndroid.RESULTS.DENIED);

    const onInitialized = jest.fn();
    const onSetError = jest.fn();

    renderWithOptions(
      <DiscoverReadersInit onInitialized={onInitialized} onSetError={onSetError} />,
      {
        withNavigationContainer: true,
      }
    );

    await waitFor(() => {
      expect(onSetError).toHaveBeenCalledWith(true);
      expect(mockInitialize).not.toHaveBeenCalled();
    });
  });

  it('exposes retry method through ref', async () => {
    Platform.OS = PlatformOS.IOS;
    mockInitialize.mockResolvedValue({});

    const onInitialized = jest.fn();
    const onSetError = jest.fn();
    const ref = createRef<DiscoverReadersInitHandle>();

    renderWithOptions(
      <DiscoverReadersInit ref={ref} onInitialized={onInitialized} onSetError={onSetError} />,
      { withNavigationContainer: true }
    );

    await waitFor(() => expect(mockInitialize).toHaveBeenCalledTimes(1));

    ref.current?.retry();

    await waitFor(() => {
      expect(mockInitialize).toHaveBeenCalledTimes(2);
    });
  });

  it('calls onSetError when initialize returns error', async () => {
    Platform.OS = PlatformOS.IOS;
    mockInitialize.mockResolvedValue({
      error: { code: '123', message: 'fail' },
    });

    const onInitialized = jest.fn();
    const onSetError = jest.fn();

    renderWithOptions(
      <DiscoverReadersInit onInitialized={onInitialized} onSetError={onSetError} />,
      { withNavigationContainer: true }
    );

    await waitFor(() => expect(mockInitialize).toHaveBeenCalled());
    expect(onSetError).toHaveBeenCalledWith(true);
    expect(onInitialized).not.toHaveBeenCalled();
  });
});
