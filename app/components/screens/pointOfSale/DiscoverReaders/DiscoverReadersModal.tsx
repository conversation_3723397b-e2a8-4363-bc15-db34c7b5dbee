import React, { useCallback } from 'react';

import DiscoverReadersContainer from './DiscoverReadersContainer/DiscoverReadersContainer';
import { AppNav } from '../../../../Nav';
import { Navigation } from '../../../../utils/Enums';
import { ModalSwipeGestureHandler } from '../../../Shared/ModalUtils/ModalSwipe';

export function DiscoverReadersModal(): React.ReactElement {
  const nav = AppNav.root.useNavigation();

  const hide = useCallback(() => {
    if (nav.canGoBack()) {
      nav.goBack();
    }
  }, [nav]);

  return (
    <ModalSwipeGestureHandler hide={hide} iosFling={true} androidFling={true}>
      <DiscoverReadersContainer hide={hide} />
    </ModalSwipeGestureHandler>
  );
}

export function useShowDiscoverReaders(): {
  showDiscoverReaders: () => void;
} {
  const nav = AppNav.root.useNavigation();
  const showDiscoverReaders = useCallback(() => {
    return nav.navigate(Navigation.DiscoverReaders);
  }, [nav]);
  return { showDiscoverReaders };
}
