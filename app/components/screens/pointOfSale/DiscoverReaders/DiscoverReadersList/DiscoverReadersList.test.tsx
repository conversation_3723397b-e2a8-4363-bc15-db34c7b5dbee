import React, { createRef } from 'react';
import { PermissionsAndroid, Platform } from 'react-native';

import { Reader } from '@stripe/stripe-terminal-react-native';
import { waitFor, screen, act } from '@testing-library/react-native';

import DiscoverReadersList, { DiscoverReadersListHandle } from './DiscoverReadersList';
import { renderWithOptions } from '../../../../../test/renderHelpers';
import { DiscoverReadersError, PlatformOS } from '../../../../../utils/Enums';
import { setupAccessContextMocks } from '../../../../../utils/TestUtils';

const mockDiscoverReaders = jest.fn();
const mockCancelDiscovering = jest.fn();
const mockSimulateReaderUpdate = jest.fn();
let mockConnectedReader: Reader.Type | null = null;

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: jest.fn().mockImplementation(({}) => ({
    discoverReaders: mockDiscoverReaders,
    cancelDiscovering: mockCancelDiscovering,
    simulateReaderUpdate: mockSimulateReaderUpdate,
    connectedReader: mockConnectedReader,
  })),
  CommonError: { Canceled: 'canceled' },
}));

jest.mock('@hudl/jarvis/logging', () => ({
  HudlLogger: {
    logError: jest.fn(),
  },
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    addListener: jest.fn(),
  }),
}));

jest.useFakeTimers();
jest.spyOn(global, 'setTimeout');

jest.mock('../../../../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../../../../components/queries/useAccessCodeHook');
setupAccessContextMocks();

beforeEach(() => {
  jest.clearAllMocks();
  mockConnectedReader = null;
  Platform.OS = PlatformOS.IOS;
});

describe('DiscoverReadersList', () => {
  it('starts discovery and shows loading state', async () => {
    mockDiscoverReaders.mockResolvedValue({});

    renderWithOptions(<DiscoverReadersList setError={jest.fn()} setStage={jest.fn()} />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });

    await waitFor(() => expect(mockDiscoverReaders).toHaveBeenCalled());
    expect(screen.getByText(/finding/i)).toBeTruthy();
  });

  it('cancels discovery after 75 seconds when no readers found', async () => {
    mockDiscoverReaders.mockResolvedValue({});

    renderWithOptions(<DiscoverReadersList setError={jest.fn()} setStage={jest.fn()} />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });

    await waitFor(() => expect(mockDiscoverReaders).toHaveBeenCalled());

    await act(async () => {
      jest.advanceTimersByTime(75000);
    });

    expect(mockCancelDiscovering).toHaveBeenCalled();
  });

  it('handles discovery error and sets error', async () => {
    const setError = jest.fn();
    mockDiscoverReaders.mockResolvedValue({
      error: { code: 'BluetoothAccessDenied', message: 'error' },
    });

    renderWithOptions(<DiscoverReadersList setError={setError} setStage={jest.fn()} />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });

    await waitFor(() => expect(mockDiscoverReaders).toHaveBeenCalled());
    await waitFor(() => expect(setError).toHaveBeenCalledWith(DiscoverReadersError.BluetoothError));
  });

  it('exposes retry method through ref', async () => {
    const setError = jest.fn();
    const setStage = jest.fn();
    mockDiscoverReaders.mockResolvedValue({});

    const ref = createRef<DiscoverReadersListHandle>();

    renderWithOptions(<DiscoverReadersList ref={ref} setError={setError} setStage={setStage} />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });

    await waitFor(() => expect(mockDiscoverReaders).toHaveBeenCalledTimes(1));

    await act(async () => {
      ref.current?.retry();
    });

    await waitFor(() => expect(mockDiscoverReaders).toHaveBeenCalledTimes(2));
  });

  it('requests Android permissions when Platform is Android', async () => {
    Platform.OS = PlatformOS.Android;

    const requestSpy = jest
      .spyOn(PermissionsAndroid, 'request')
      .mockResolvedValue(PermissionsAndroid.RESULTS.GRANTED);

    mockDiscoverReaders.mockResolvedValue({});

    renderWithOptions(<DiscoverReadersList setError={jest.fn()} setStage={jest.fn()} />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });

    await waitFor(() => {
      expect(requestSpy).toHaveBeenCalled();
      expect(mockDiscoverReaders).toHaveBeenCalled();
    });
  });
});
