import React, {
  ForwardedRef,
  forwardRef,
  ReactElement,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { PermissionsAndroid, Platform, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';
import { useStripeTerminal, Reader, CommonError } from '@stripe/stripe-terminal-react-native';
import { DetoxContext } from 'react-native-detox-context';

import { useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './DiscoverReadersList.style';
import { AppNav } from '../../../../../Nav';
import { logError } from '../../../../../common/Logging';
import {
  DiscoverReadersError,
  DiscoverReadersStage,
  DiscoveryMethod,
  PlatformOS,
  StripeCommonErrorCodes,
} from '../../../../../utils/Enums';
import { developerOptionFlags } from '../../../../../utils/stateVars';
import { useAndroidBackHandler } from '../../../../../utils/useAndroidBackHandler';
import { AppTestIDs } from '../../../../AppTestIDs';
import { ConnectReader } from '../../ConnectReader/ConnectReader';
import DiscoverReadersLoading from '../DiscoverReadersLoading/DiscoverReadersLoading';

interface Props {
  setError: (error?: DiscoverReadersError) => void;
  setStage: (stage: DiscoverReadersStage) => void;
}

export type DiscoverReadersListHandle = {
  retry: () => void;
};

function DiscoverReadersList(
  { setError, setStage }: Props,
  ref: ForwardedRef<DiscoverReadersListHandle | undefined>
): React.ReactElement {
  const styles = useUniformStyles(stylesheet);
  const nav = AppNav.root.useNavigation();

  const [discoveringLoading, setDiscoveringLoading] = useState(false);
  const [discoveredReaders, setDiscoveredReaders] = useState<Reader.Type[]>([]);
  const discoveredReadersRef = useRef<Reader.Type[]>(undefined);
  discoveredReadersRef.current = discoveredReaders;

  const developerFlags = useReactiveVar(developerOptionFlags);

  const { cancelDiscovering, discoverReaders, connectedReader, simulateReaderUpdate } =
    useStripeTerminal({
      onUpdateDiscoveredReaders: (readers) => {
        setDiscoveredReaders(readers);
      },
    });

  useAndroidBackHandler();

  const handleDiscoveryError = useCallback(
    async (code: string): Promise<void> => {
      await cancelDiscovering();
      setDiscoveringLoading(false);
      logError(`Reader Discovery error: ${code}.`, 'handleDiscoveryError');

      if (
        code === StripeCommonErrorCodes.BluetoothAccessDenied ||
        code === StripeCommonErrorCodes.BluetoothDisabled ||
        code === StripeCommonErrorCodes.AndroidBluetoothError
      ) {
        setError(DiscoverReadersError.BluetoothError);
        return;
      } else {
        setError(DiscoverReadersError.ConnectionError);
      }
    },
    [cancelDiscovering, setError]
  );

  const discoveryErrorNotCanceled = (code: string): boolean => {
    // accounts for differing error codes thrown by IOS and Android
    return code !== StripeCommonErrorCodes.UserErrorCanceled && code !== CommonError.Canceled;
  };

  const handleDiscoverReaders = useCallback(async () => {
    if (
      developerFlags.simulateReaderUpdate.enabled &&
      developerFlags.simulateReaderUpdate.value !== undefined
    ) {
      await simulateReaderUpdate(
        developerFlags.simulateReaderUpdate.value as Reader.SimulateUpdateType
      );
    }
    const { error: discoveryError } = await discoverReaders({
      discoveryMethod: DiscoveryMethod.BluetoothScan,
      simulated: developerFlags.simulateReader.enabled || DetoxContext.isAutomatedTest,
    });

    if (discoveryError && discoveryErrorNotCanceled(discoveryError.code)) {
      handleDiscoveryError(discoveryError.code);
    }
  }, [
    developerFlags.simulateReader.enabled,
    developerFlags.simulateReaderUpdate.enabled,
    developerFlags.simulateReaderUpdate.value,
    discoverReaders,
    handleDiscoveryError,
    simulateReaderUpdate,
  ]);

  useEffect(() => {
    if (discoveredReaders.length > 0) {
      setStage(DiscoverReadersStage.Selecting);
    }
  }, [discoveredReaders.length, setStage]);

  const requestAndroidPermissions = async (): Promise<boolean> => {
    const connectGranted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT
    );
    const scanGranted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN
    );

    return (
      connectGranted === PermissionsAndroid.RESULTS.GRANTED &&
      scanGranted === PermissionsAndroid.RESULTS.GRANTED
    );
  };

  const cancelDiscovery = useCallback(async () => {
    await cancelDiscovering();
    setDiscoveringLoading(false);
  }, [cancelDiscovering]);

  const initiateDiscovery = useCallback(async () => {
    if (!connectedReader) {
      setError(undefined);
      setDiscoveringLoading(true);
      if (Platform.OS === PlatformOS.Android) {
        const granted = await requestAndroidPermissions();
        if (!granted) {
          setDiscoveringLoading(false);
          setStage(DiscoverReadersStage.Selecting);
          setError(DiscoverReadersError.BluetoothError);
          return;
        }
      }
      handleDiscoverReaders();
    }
  }, [connectedReader, handleDiscoverReaders, setError, setStage]);

  useEffect(() => {
    initiateDiscovery();
    // We only want to run this effect once with the initial values
    // so the dependency array is intentionally left empty.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useImperativeHandle(ref, () => ({
    retry() {
      initiateDiscovery();
    },
  }));

  useEffect(() => {
    const noReadersTimeout = setTimeout(async () => {
      if (discoveredReadersRef.current?.length === 0) {
        setError(DiscoverReadersError.NoReadersFound);
        cancelDiscovery();
      }
    }, 75000);

    return () => {
      clearTimeout(noReadersTimeout);
    };
  }, [cancelDiscovery, discoveringLoading, discoveredReaders, setError]);

  useEffect(() => {
    nav.addListener('beforeRemove', async () => {
      await cancelDiscovering();
    });
  }, [cancelDiscovering, nav]);

  const setReaderWithConnectionError = useCallback(
    (errorCode: string | null) => {
      if (errorCode) {
        if (
          errorCode === StripeCommonErrorCodes.BluetoothConnectionFailedBatteryCriticallyLow ||
          errorCode === StripeCommonErrorCodes.ReaderSoftwareUpdateFailedBatteryLow
        ) {
          setError(DiscoverReadersError.LowBattery);
        } else {
          setError(DiscoverReadersError.ConnectionError);
        }
      }
    },
    [setError]
  );

  const onConnected = useCallback(() => {
    setStage(DiscoverReadersStage.Selected);
  }, [setStage]);

  const onDisconnect = useCallback(() => {
    setStage(DiscoverReadersStage.Selecting);
    initiateDiscovery();
  }, [initiateDiscovery, setStage]);

  const renderDiscoveredReaders = (readers: Reader.Type[]): ReactElement => {
    const sortedReaders = readers.sort((a, b) => a.serialNumber.localeCompare(b.serialNumber));
    return (
      <ConnectReader
        discoveredReaders={sortedReaders}
        discoveryMethod={DiscoveryMethod.BluetoothScan}
        setReaderWithConnectionError={setReaderWithConnectionError}
        onDisconnect={onDisconnect}
        onConnected={onConnected}
      />
    );
  };

  if (discoveredReaders.length === 0) {
    return <DiscoverReadersLoading />;
  }

  return (
    <View style={styles.readersContainer} testID={AppTestIDs.readerConnectionListContainer}>
      {discoveredReaders.length === 0 && connectedReader
        ? renderDiscoveredReaders([connectedReader])
        : renderDiscoveredReaders(discoveredReaders)}
    </View>
  );
}

export default forwardRef(DiscoverReadersList);
