import React, { ReactNode } from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import { Button, IconUIDismissMedium } from '@hudl/rn-uniform';

import { styles } from './GenericError.style';
import { AppTestIDs } from '../../../AppTestIDs';

type Props = {
  header: string;
  subHeaderContent: ReactNode;
  callToActionText: string;
  onPress: () => void;
  icon: ReactNode;
  xPress?: () => void;
};

export function GenericError(props: Props): React.ReactElement {
  const { onPress, header, subHeaderContent, callToActionText, icon, xPress } = props;

  return (
    <View style={styles.container} testID={AppTestIDs.genericErrorRoot}>
      <View style={styles.contentContainer}>
        <View style={styles.header}>
          {xPress && (
            <TouchableOpacity onPress={xPress} testID={AppTestIDs.readerConnectionWrapperClose}>
              <IconUIDismissMedium color={'subtle'} />
            </TouchableOpacity>
          )}
          <Text style={styles.headerText} testID={AppTestIDs.genericErrorHeader}>
            {header}
          </Text>
        </View>

        <View style={styles.helpTextContainer}>{subHeaderContent}</View>
        <View style={styles.iconContainer} testID={AppTestIDs.genericErrorIcon}>
          {icon}
        </View>
      </View>
      <View style={styles.buttonWrapper}>
        <Button
          text={callToActionText}
          onPress={onPress}
          testID={AppTestIDs.genericErrorCTAButton}
          style={styles.button}
        />
      </View>
    </View>
  );
}
