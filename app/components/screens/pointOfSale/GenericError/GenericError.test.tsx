import React from 'react';

import { fireEvent, screen } from '@testing-library/react-native';

import { IconCritical, Text } from '@hudl/rn-uniform';

import { GenericError } from './GenericError';
import { renderWithOptions } from '../../../../test/renderHelpers';

const icon = <IconCritical height={'128'} width={'128'} />;
describe('ConnectionError render tests', () => {
  it('Screen renders correctly', async () => {
    const onTryAgain = jest.fn();

    renderWithOptions(
      <GenericError
        onPress={onTryAgain}
        header={''}
        subHeaderContent={<Text>{'This is sub header test'}</Text>}
        callToActionText="Try Again"
        icon={icon}
      />,
      {
        withNavigationContainer: true,
      }
    );

    expect(screen.getByTestId('generic-error-root')).toBeTruthy();
  });

  it('On try again triggered on click', async () => {
    const onTryAgain = jest.fn();

    renderWithOptions(
      <GenericError
        onPress={onTryAgain}
        header={''}
        subHeaderContent={<Text>{'This is sub header test'}</Text>}
        callToActionText="Try Again"
        icon={icon}
      />,
      {
        withNavigationContainer: true,
      }
    );

    fireEvent.press(screen.getByTestId('generic-error-cta-button'));

    expect(onTryAgain).toHaveBeenCalled();
  });
});
