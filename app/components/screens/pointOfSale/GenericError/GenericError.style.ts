import { StyleSheet } from 'react-native';

import { UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    height: '100%',
    width: '100%',
  },
  contentContainer: {
    gap: UniformSpace.one,
    flexGrow: 1,
    padding: UniformSpace.oneAndHalf,
  },
  helpTextContainer: {
    gap: UniformSpace.one,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    marginTop: UniformSpace.four,
  },
  buttonWrapper: {
    padding: UniformSpace.one,
    backgroundColor: nonUniformColors.containerBlackBg,
  },
  button: {
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: UniformSpace.half,
  },
  headerText: {
    color: nonUniformColors.contentStrongBG,
    fontSize: 24,
    fontWeight: '700',
  },
});
