import React, { useCallback, useEffect, useState } from 'react';
import { FlatList, ListRenderItem, View, Text, SafeAreaView, ScrollView } from 'react-native';

import { useReactiveVar } from '@apollo/client';
import { useStripeTerminal } from '@stripe/stripe-terminal-react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './CustomSaleCalculator.style';
import { AppNav } from '../../../Nav';
import { LineItemType } from '../../../enums/shared';
import { CalculatorKey, LineItemSelection } from '../../../types/shared';
import { Navigation } from '../../../utils/Enums';
import { checkoutItemSelections, customSaleType } from '../../../utils/stateVars';
import { AppTestIDs } from '../../AppTestIDs';
import { useShowDiscoverReaders } from '../pointOfSale/DiscoverReaders/DiscoverReadersModal';

export function CustomSaleCalculator(): React.ReactElement {
  const styles = useUniformStyles(styleSheet);
  const customSalesType = useReactiveVar(customSaleType);
  const nav = AppNav.sell.useNavigation();
  const { connectedReader } = useStripeTerminal();
  const { showDiscoverReaders } = useShowDiscoverReaders();

  const i18nStrings = useI18n(
    {
      customPriceHeader: 'custom-sales.calculator-input-price-header',
      minimumPrice: 'custom-sales.minimum-sales-price',
      maximumPrice: 'custom-sales.maximum-sales-price',
      checkout: 'custom-sales.checkout',
      cancelOrder: 'custom-sales.cancel-order',
      cashButton: 'pos.item-selection.cash-button',
      cardButton: 'pos.item-selection.card-button',
      feeMessage: 'pos.order-total.additional-fees',
    },
    []
  );
  const DeleteValue = 'delete';
  const DeleteLabel = '⌫';
  const One = '1';
  const Two = '2';
  const Three = '3';
  const Four = '4';
  const Five = '5';
  const Six = '6';
  const Seven = '7';
  const Eight = '8';
  const Nine = '9';
  const Zero = '0';
  const Clear = 'C';
  const minimumCustomSalePriceInCents = 50;
  const maximumCustomSalePriceInCents = 25000;

  const calculatorKeys: CalculatorKey[] = [
    { label: Seven, value: Seven },
    { label: Eight, value: Eight },
    { label: Nine, value: Nine },
    { label: Four, value: Four },
    { label: Five, value: Five },
    { label: Six, value: Six },
    { label: One, value: One },
    { label: Two, value: Two },
    { label: Three, value: Three },
    { label: Zero, value: Zero },
    { label: Clear, value: Clear },
    { label: DeleteLabel, value: DeleteValue },
  ];
  const [calculatorValue, setCalculatorValue] = useState(0);
  const [minimumPriceError, setMinimumPriceError] = useState(false);
  const [maximumPriceError, setMaximumPriceError] = useState(false);
  // Check against the minimum price rather than the error since the error is delayed by 500ms
  const checkoutButtonDisabled =
    calculatorValue === 0 || calculatorValue < minimumCustomSalePriceInCents;

  const formatCurrency = (value: number): string => {
    return `$${(value / 100).toFixed(2)}`;
  };

  const onCalculatorButtonPress = useCallback(
    (value: string): (() => void) => {
      return () => {
        if (value === Clear) {
          setCalculatorValue(0);
        } else if (value === DeleteValue) {
          setCalculatorValue(Math.floor(calculatorValue / 10));
        } else {
          const newValue = parseInt(`${calculatorValue}${value}`, 10);
          if (newValue > maximumCustomSalePriceInCents) {
            setMaximumPriceError(true);
            return;
          }
          setCalculatorValue(newValue);
        }
      };
    },
    [calculatorValue]
  );

  useEffect(() => {
    setMaximumPriceError(calculatorValue > maximumCustomSalePriceInCents);
    const hasError = calculatorValue > 0 && calculatorValue < minimumCustomSalePriceInCents;
    if (!hasError) {
      setMinimumPriceError(hasError);
      return;
    }
    const timer = setTimeout(() => {
      setMinimumPriceError(hasError);
    }, 500);

    return () => clearTimeout(timer);
  }, [calculatorValue]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setMaximumPriceError(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, [maximumPriceError]);

  const renderCalculatorButton = useCallback<ListRenderItem<CalculatorKey>>(
    ({ item }) => {
      return (
        <TouchableOpacity
          onPress={onCalculatorButtonPress(item.value)}
          style={styles.key}
          testID={AppTestIDs.customSaleCalculatorButton(item.value)}>
          <Text style={styles.keyText}>{item.label}</Text>
        </TouchableOpacity>
      );
    },
    [onCalculatorButtonPress, styles.key, styles.keyText]
  );

  const getErrorText = (): string => {
    if (minimumPriceError) {
      return i18nStrings.minimumPrice;
    }
    if (maximumPriceError) {
      return i18nStrings.maximumPrice;
    }
    return '';
  };

  const getErrorTestId = (): string => {
    if (minimumPriceError) {
      return AppTestIDs.customSaleCalculatorErrorMin;
    }
    if (maximumPriceError) {
      return AppTestIDs.customSaleCalculatorErrorMax;
    }
    return '';
  };

  const goToReviewOrder = useCallback(
    (isCashSale: boolean) => {
      if (connectedReader || isCashSale) {
        const lineItemSelection: LineItemSelection = {
          lineItemType: LineItemType.Custom,
          name: customSalesType,
          unitPriceInCents: calculatorValue,
          lineItemCategory: customSalesType,
          quantitySelected: 1,
        };
        checkoutItemSelections([lineItemSelection]);

        nav.navigate(Navigation.ReviewOrder, {
          isCustomSale: true,
          isCashSale: isCashSale,
        });
        setCalculatorValue(0);
      } else {
        showDiscoverReaders();
      }
    },
    [calculatorValue, connectedReader, customSalesType, nav, showDiscoverReaders]
  );

  const onCardReviewOrderPress = useCallback((): void => goToReviewOrder(false), [goToReviewOrder]);
  const onCashReviewOrderPress = useCallback((): void => goToReviewOrder(true), [goToReviewOrder]);

  const onCancelOrder = useCallback(() => {
    setCalculatorValue(0);
    nav.navigate(Navigation.Sell, {});
  }, [nav]);

  return (
    <SafeAreaView style={styles.container} testID={AppTestIDs.customSaleCalculator}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        testID={AppTestIDs.customSaleCalculatorScroll}>
        <View style={styles.calculatorContainer}>
          <View style={styles.valueContainer}>
            <Text style={styles.customPriceHeader} testID={AppTestIDs.customSaleCalculatorHeader}>
              {i18nStrings.customPriceHeader}
            </Text>
            <Text style={styles.priceInputValue} testID={AppTestIDs.customSaleCalculatorTotal}>
              {formatCurrency(calculatorValue)}
            </Text>
          </View>
          <Text style={styles.errorText} testID={getErrorTestId()}>
            {getErrorText()}
          </Text>
          <FlatList
            data={calculatorKeys}
            numColumns={3}
            renderItem={renderCalculatorButton}
            contentContainerStyle={styles.flatListContainer}
            scrollEnabled={false}
          />
        </View>
        <View style={styles.footer}>
          <View style={styles.feeContainer}>
            <Text style={styles.feeText}>{i18nStrings.feeMessage}</Text>
          </View>
          <View style={styles.reviewOrderButtonContainer}>
            <Button
              onPress={onCashReviewOrderPress}
              text={i18nStrings.cashButton}
              isDisabled={checkoutButtonDisabled}
              testID={AppTestIDs.customSaleCalculatorCheckoutButtonCash(checkoutButtonDisabled)}
              style={styles.button}
            />
            <Button
              onPress={onCardReviewOrderPress}
              text={i18nStrings.cardButton}
              isDisabled={checkoutButtonDisabled}
              testID={AppTestIDs.customSaleCalculatorCheckoutButtonCard(checkoutButtonDisabled)}
              style={styles.button}
            />
          </View>
          <TouchableOpacity
            onPress={onCancelOrder}
            testID={AppTestIDs.customSaleCalculatorCancelOrderButton}>
            <Text style={styles.cancelOrder}>{i18nStrings.cancelOrder}</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
