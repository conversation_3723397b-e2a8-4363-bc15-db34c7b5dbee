import React from 'react';

import { waitFor } from '@testing-library/react-native';
import { fireEvent, screen } from '@testing-library/react-native';

import { CustomSaleCalculator } from './CustomSaleCalculator';
import { LineItemType } from '../../../enums/shared';
import { renderWithOptions } from '../../../test/renderHelpers';
import { CustomSalesType, Navigation } from '../../../utils/Enums';
import { checkoutItemSelections } from '../../../utils/stateVars';
import { AppTestIDs } from '../../AppTestIDs';

const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    goBack: mockGoBack,
    canGoBack: () => true,
  }),
  useRoute: () => ({}),
}));

const mockUseStripeTerminal = jest.fn();

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: (...args: unknown[]) => mockUseStripeTerminal(...args),
  CommonError: {
    Canceled: 'canceled',
  },
}));

beforeEach(() => {
  checkoutItemSelections([]);
  mockUseStripeTerminal.mockReturnValue({ connectedReader: null });
});

describe('CustomSaleCalculator', () => {
  it('should render correctly', () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    expect(screen.getByTestId('custom-sale-calculator')).toBeDefined();
    expect(screen.getByText('$0.00')).toBeDefined();
  });

  it('Pressing buttons updates input', () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-1'));
    expect(screen.getByText('$0.01')).toBeDefined();
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-2'));
    expect(screen.getByText('$0.12')).toBeDefined();
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-delete'));
    expect(screen.getByText('$0.01')).toBeDefined();
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-C'));
    expect(screen.getByText('$0.00')).toBeDefined();
  });

  it('Less than $0.50 triggers error', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    expect(screen.getByText('$0.05')).toBeDefined();
    await waitFor(() =>
      expect(screen.getByTestId(AppTestIDs.customSaleCalculatorErrorMin)).toBeDefined()
    );
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-0'));
    expect(screen.queryByTestId(AppTestIDs.customSaleCalculatorErrorMin)).toBeFalsy();
  });

  it('More than $250 triggers error', () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-2'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-0'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-0'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    expect(screen.getByText('$25.00')).toBeDefined();
    expect(screen.getByTestId(AppTestIDs.customSaleCalculatorErrorMax)).toBeDefined();
  });

  it('Less than $0.50 triggers error, cannot checkout', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    expect(screen.getByText('$0.05')).toBeDefined();
    await waitFor(() =>
      expect(screen.getByTestId(AppTestIDs.customSaleCalculatorErrorMin)).toBeDefined()
    );
    expect(
      screen.getByTestId(AppTestIDs.customSaleCalculatorCheckoutButtonCard(true))
    ).toBeTruthy();
  });

  it('Checkout button active with valid price', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    expect(
      screen.getByTestId(AppTestIDs.customSaleCalculatorCheckoutButtonCard(false))
    ).toBeTruthy();
  });

  it('Clicking card checkout button takes you to review order screen', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    mockUseStripeTerminal.mockReturnValue({
      connectedReader: { id: 'rdr_123' } as unknown,
    });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId(AppTestIDs.customSaleCalculatorCheckoutButtonCard(false)));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.ReviewOrder, {
      isCustomSale: true,
      isCashSale: false,
    });
    expect(checkoutItemSelections()).toEqual([
      {
        lineItemType: LineItemType.Custom,
        name: CustomSalesType.Concessions,
        unitPriceInCents: 55,
        lineItemCategory: CustomSalesType.Concessions,
        quantitySelected: 1,
      },
    ]);
  });

  it('Clicking checkout button opens discover readers if none connected', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId(AppTestIDs.customSaleCalculatorCheckoutButtonCard(false)));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.DiscoverReaders);
  });

  it('Clicking cash checkout button takes you to review order screen', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    mockUseStripeTerminal.mockReturnValue({
      connectedReader: { id: 'rdr_123' } as unknown,
    });

    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId(AppTestIDs.customSaleCalculatorCheckoutButtonCash(false)));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.ReviewOrder, {
      isCustomSale: true,
      isCashSale: true,
    });
    expect(checkoutItemSelections()).toEqual([
      {
        lineItemType: LineItemType.Custom,
        name: CustomSalesType.Concessions,
        unitPriceInCents: 55,
        lineItemCategory: CustomSalesType.Concessions,
        quantitySelected: 1,
      },
    ]);
  });

  it('Clicking cancel order takes you back to sell tab', async () => {
    renderWithOptions(<CustomSaleCalculator />, { withNavigationContainer: true });

    fireEvent.press(screen.getByTestId(AppTestIDs.customSaleCalculatorCancelOrderButton));
    fireEvent.press(screen.getByTestId('custom-sale-calculator-button-5'));
    fireEvent.press(screen.getByTestId(AppTestIDs.customSaleCalculatorCheckoutButtonCard(true)));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.Sell, {});
  });
});
