import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    height: '100%',
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
  },
  calculatorContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 'auto',
    gap: UniformSpace.quarter,
  },
  valueContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: UniformSpace.one,
    marginTop: UniformSpace.one,
  },
  flatListContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  key: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 90,
    height: 70,
    marginTop: 5,
    marginBottom: 0,
    marginHorizontal: 15,
  },
  keyText: {
    color: colors.baseWhite,
    fontSize: 30,
  },
  customPriceHeader: {
    fontWeight: '700',
    fontSize: 16,
    color: colors.baseWhite,
  },
  priceInputValue: {
    fontWeight: '700',
    fontSize: 40,
    color: colors.baseWhite,
  },
  errorText: {
    fontWeight: '400',
    fontSize: 14,
    color: colors.utilityCritical,
    height: UniformSpace.one,
  },
  feeText: {
    color: colors.baseWhite,
    fontStyle: 'italic',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'center',
  },
  feeContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  buttonGroup: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.half,
  },
  footer: {
    backgroundColor: colors.bgLevel0,
    padding: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.one,
  },
  reviewOrderButtonContainer: {
    paddingTop: UniformSpace.half,
    flexDirection: 'row',
    gap: UniformSpace.half,
  },
  button: {
    flex: 1,
    backgroundColor: nonUniformColors.contentEmphasesBgContrast,
  },
  cancelOrder: {
    color: nonUniformColors.contentForegroundSubtle,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    paddingTop: UniformSpace.one,
  },
  scrollContent: {
    flexGrow: 1,
  },
}));
