import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import { SafeAreaView, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';
import {
  CommonError,
  ConfirmPaymentMethodParams,
  PaymentIntent,
  Reader,
  useStripeTerminal,
} from '@stripe/stripe-terminal-react-native';
import { DetoxContext } from 'react-native-detox-context';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, Headline, Text } from '@hudl/rn-uniform';

import { styles } from './AcceptPayment.style';
import DeclinedByStripeError from './Errors/DeclinedByStripeError';
import LocationError from './Errors/LocationError';
import PaymentError from './Errors/PaymentError';
import { AppNav } from '../../../../Nav';
import { logError } from '../../../../common/Logging';
import { useAccessContext } from '../../../../context/AccessContext';
import useCheckoutWithTicketingLineItems from '../../../../gql/hooks/useCheckoutWithTicketingLineItems';
import useCreatePaymentIntent from '../../../../gql/hooks/useCreatePaymentIntent';
import useCreateTicketingPaymentToken from '../../../../gql/hooks/useCreateTicketingPaymentToken';
import IconFindReaders from '../../../../icons/IconFindReaders';
import { Navigation, PaymentIntentStatus, StripeCommonErrorCodes } from '../../../../utils/Enums';
import { getReaderInputOptions } from '../../../../utils/ReaderDisplayUtils';
import { constructPOSDescriptionString } from '../../../../utils/StringUtils';
import { checkoutItemSelections, developerOptionFlags } from '../../../../utils/stateVars';
import { useAndroidBackHandler } from '../../../../utils/useAndroidBackHandler';
import { AppTestIDs } from '../../../AppTestIDs';
import ReaderDisplayMessage from '../../../ReaderDisplay/ReaderDisplayMessage';

export function AcceptPayment(): ReactElement {
  const nav = AppNav.root.useNavigation();
  const {
    params: { totalPrice, purchaserEmail },
  } = AppNav.root.useRoute(Navigation.AcceptPayment);
  const { eventInfo: event, organization } = useAccessContext();

  const lineItemSelections = useReactiveVar(checkoutItemSelections);
  const [paymentTokenId, setPaymentTokenId] = useState<string>('');
  const [readerDisplayMessage, setReaderDisplayMessage] = useState<ReactElement | null>(null);
  const [declinedByStripe, setDeclinedByStripeError] = useState(false);
  const [paymentError, setPaymentError] = useState(false);
  const [locationError, setLocationError] = useState(false);
  const [createdPaymentIntent, setCreatedPaymentIntent] = useState<
    PaymentIntent.Type | undefined
  >();
  const [cancelPaymentIntentDisabled, setCancelPaymentIntentDisabled] = useState(false);

  const developerFlags = useReactiveVar(developerOptionFlags);
  const inputTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const i18nStrings = useI18n(
    {
      loadingHeader: 'accept-payments.loading-header',
      loadingHelpText: 'accept-payments.loading-help-text',
      acceptingPayment: 'accept-payments.accepting-payment',
      cancelPayment: 'accept-payments.cancel-payment',
      tryAgain: 'accept-payments.try-again',
      paymentErrorHeader: 'accept-payments.payment-error-header',
      paymentErrorHelpText: 'accept-payments.payment-error-help-text',
    },
    []
  );

  const renderReaderDisplayMessage = useCallback((message: Reader.DisplayMessage) => {
    return <ReaderDisplayMessage displayType={message} />;
  }, []);

  const onDidRequestReaderDisplayMessage = useCallback((message: Reader.DisplayMessage) => {
    const readerDisplay = renderReaderDisplayMessage(message);
    setReaderDisplayMessage(readerDisplay);
  }, [renderReaderDisplayMessage]);

  const onDidRequestReaderInput = useCallback((input: Reader.InputOptions) => {
    inputTimeoutRef.current = setTimeout(() => {
      const readerDisplay = getReaderInputOptions(input);
      setReaderDisplayMessage(readerDisplay);
    }, 1500);
  }, []);

  const {
    collectPaymentMethod,
    confirmPaymentIntent,
    retrievePaymentIntent,
    cancelCollectPaymentMethod,
    setSimulatedCard,
    cancelPaymentIntent,
  } = useStripeTerminal({
    onDidRequestReaderDisplayMessage,
    onDidRequestReaderInput,
  });

  useEffect(() => {
    const unsubscribe = nav.addListener('beforeRemove', async (e) => {
      e.preventDefault();
      await cancelCollectPaymentMethod();
      nav.dispatch(e.data.action);
    });

    return unsubscribe;
  }, [cancelCollectPaymentMethod, nav]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (inputTimeoutRef.current) {
        clearTimeout(inputTimeoutRef.current);
      }
    };
  }, []);

  useAndroidBackHandler();

  const { createTicketingPaymentToken } = useCreateTicketingPaymentToken(
    lineItemSelections,
    totalPrice,
    event?.id ?? ''
  );

  const { createPaymentIntent } = useCreatePaymentIntent(
    paymentTokenId,
    constructPOSDescriptionString(organization?.id ?? '')
  );

  const { checkoutWithTicketingLineItems } = useCheckoutWithTicketingLineItems(
    paymentTokenId,
    lineItemSelections,
    totalPrice,
    event?.id ?? '',
    purchaserEmail
  );

  const handlePaymentError = useCallback(
    (code: string | undefined) => {
      if (
        code === StripeCommonErrorCodes.DeclinedByStripeAPI ||
        code === StripeCommonErrorCodes.AndroidDeclinedByStripeAPI
      ) {
        setReaderDisplayMessage(null);
        setDeclinedByStripeError(true);
        return;
      }
      if (code === StripeCommonErrorCodes.LocationServicesDisabled) {
        setLocationError(true);
        return;
      }
      setPaymentError(true);
    },
    [setPaymentError]
  );

  const navigateToOrderComplete = useCallback(
    (ticketGroupReference?: string) => {
      const ticketCount = lineItemSelections.reduce((sum, li) => sum + li.quantitySelected, 0);

      nav.navigate(Navigation.OrderComplete, {
        lineItemType: lineItemSelections.map((li) => li.lineItemType)[0],
        ticketGroupReference: ticketGroupReference,
        ticketCount: ticketCount,
        totalAmount: totalPrice,
        usedCard: true,
      });
    },
    [lineItemSelections, nav, totalPrice]
  );

  const confirmPayment = useCallback(
    async (collectedPaymentIntent: ConfirmPaymentMethodParams) => {
      setCancelPaymentIntentDisabled(true);
      const { paymentIntent, error } = await confirmPaymentIntent(collectedPaymentIntent);

      if (error) {
        logError(
          `Confirming payment error. Code: ${error.code} Message: ${error.message}`,
          'confirmPayment'
        );

        handlePaymentError(error.code);
        return;
      }

      if (paymentIntent?.status === PaymentIntentStatus.Succeeded) {
        setReaderDisplayMessage(null);
        const checkoutRes = await checkoutWithTicketingLineItems();
        setCancelPaymentIntentDisabled(false);
        navigateToOrderComplete(
          checkoutRes.data?.checkoutWithTicketingLineItems?.ticketGroup?.ticketGroupReference ?? ''
        );
      }
    },
    [
      checkoutWithTicketingLineItems,
      confirmPaymentIntent,
      handlePaymentError,
      navigateToOrderComplete,
    ]
  );

  const collectPayment = useCallback(
    async (paymentIntentResult: PaymentIntent.Type): Promise<void> => {
      if (developerFlags.simulatedCardNumber.enabled && developerFlags.simulatedCardNumber.value) {
        setSimulatedCard(developerFlags.simulatedCardNumber.value);
      }
      if (DetoxContext.isAutomatedTest) {
        setSimulatedCard('****************');
      }

      const { paymentIntent, error } = await collectPaymentMethod({
        paymentIntent: paymentIntentResult,
      });

      if (error && error.code !== CommonError.Canceled) {
        handlePaymentError(error.code);
        logError(
          `Collecting payment error. Code: ${error.code} Message: ${error.message}`,
          'collectPayment'
        );

        return;
      }

      if (paymentIntent) {
        inputTimeoutRef.current && clearTimeout(inputTimeoutRef.current);
        setReaderDisplayMessage(null);
        confirmPayment({ paymentIntent });
      } else {
        handlePaymentError(undefined);
      }
    },
    [
      collectPaymentMethod,
      confirmPayment,
      developerFlags.simulatedCardNumber.enabled,
      developerFlags.simulatedCardNumber.value,
      handlePaymentError,
      setSimulatedCard,
    ]
  );

  const initiatePayment = useCallback(async (): Promise<void> => {
    let clientSecret;
    try {
      const paymentToken = await createTicketingPaymentToken();
      setPaymentTokenId(paymentToken?.data?.createTicketingPaymentToken ?? undefined);

      const ticketingPaymentIntent = await createPaymentIntent();
      clientSecret = ticketingPaymentIntent.data.createPaymentIntent.clientSecret;
    } catch (catchError) {
      handlePaymentError(undefined);
      return;
    }

    const { paymentIntent, error } = await retrievePaymentIntent(clientSecret);

    setCreatedPaymentIntent(paymentIntent);

    if (!paymentIntent || error) {
      handlePaymentError(error.code);
      logError(
        `Payment intent error. Code: ${error.code} Message: ${error.message}`,
        `initiatePayment`
      );
      return;
    }
    await collectPayment(paymentIntent);
  }, [
    collectPayment,
    createPaymentIntent,
    createTicketingPaymentToken,
    handlePaymentError,
    retrievePaymentIntent,
  ]);

  const handleFreeTicketingOrder = useCallback(async () => {
    const checkoutRes = await checkoutWithTicketingLineItems();
    navigateToOrderComplete(
      checkoutRes.data?.checkoutWithTicketingLineItems?.ticketGroup?.ticketGroupReference ?? ''
    );
  }, [checkoutWithTicketingLineItems, navigateToOrderComplete]);

  const ticketingCheckout = useCallback(() => {
    setDeclinedByStripeError(false);
    setPaymentError(false);
    setLocationError(false);
    setReaderDisplayMessage(null);

    if (totalPrice > 0) {
      initiatePayment();
      return;
    }
    handleFreeTicketingOrder();
  }, [handleFreeTicketingOrder, initiatePayment, totalPrice]);

  const goBack = useCallback(() => {
    if (nav.canGoBack()) {
      nav.goBack();
    }
  }, [nav]);

  const cancel = useCallback(async () => {
    if (createdPaymentIntent) {
      cancelPaymentIntent({ paymentIntent: createdPaymentIntent });
    }
    goBack();
  }, [cancelPaymentIntent, createdPaymentIntent, goBack]);

  useEffect(() => {
    ticketingCheckout();
    // We only want to run this effect once with the initial values
    // so the dependency array is intentionally left empty.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (paymentError) {
    return (
      <PaymentError
        retryPayment={ticketingCheckout}
        goBack={goBack}
        strings={{
          tryAgain: i18nStrings.tryAgain,
          paymentErrorHeader: i18nStrings.paymentErrorHeader,
          paymentErrorHelpText: i18nStrings.paymentErrorHelpText,
        }}
      />
    );
  }

  if (locationError) {
    return <LocationError retryPayment={ticketingCheckout} goBack={goBack} />;
  }

  return (
    <SafeAreaView style={styles.container} testID={AppTestIDs.acceptPaymentsRoot}>
      <View style={styles.headerContainer}>
        <Headline testID={AppTestIDs.acceptPaymentsLoadingHeader}>
          {i18nStrings.loadingHeader}
        </Headline>
        <Text testID={AppTestIDs.acceptPaymentsLoadingHelpText}>{i18nStrings.loadingHelpText}</Text>
      </View>
      <View style={styles.loadingContainer}>
        {!readerDisplayMessage && !declinedByStripe && (
          <View style={styles.loadingSection}>
            <IconFindReaders
              width={192}
              height={96}
              testID={AppTestIDs.discoverNearbyReadersLoadingIcon}
            />
            <Text style={styles.loadingText}>{i18nStrings.acceptingPayment}</Text>
          </View>
        )}
      </View>
      {readerDisplayMessage && readerDisplayMessage}
      {declinedByStripe && <DeclinedByStripeError retryPayment={ticketingCheckout} />}
      <View style={styles.buttonWrapper}>
        <Button
          text={i18nStrings.cancelPayment}
          onPress={cancel}
          testID={AppTestIDs.genericErrorCTAButton}
          style={styles.button}
          isDisabled={cancelPaymentIntentDisabled}
        />
      </View>
    </SafeAreaView>
  );
}
