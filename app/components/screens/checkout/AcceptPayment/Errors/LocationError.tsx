import React, { ReactElement, useCallback } from 'react';
import { SafeAreaView, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconCritical, Text, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './Errors.style';
import { AppTestIDs } from '../../../../AppTestIDs';
import { generalUniformStyles } from '../../../../Styles';
import { GenericError } from '../../../pointOfSale/GenericError/GenericError';

type Props = {
  retryPayment: () => void;
  goBack: () => void;
};

export function LocationError(props: Props): ReactElement {
  const { retryPayment, goBack } = props;
  const generalStyles = useUniformStyles(generalUniformStyles);
  const styles = useUniformStyles(styleSheet);

  const i18nStrings = useI18n(
    {
      tryAgain: 'accept-payments.try-again',
      locationServicesDisabled: 'accept-payments.location-services-disabled',
      locationServicesDisabledHelpText: 'accept-payments.location-services-disabled-help-text',
    },
    []
  );

  const onTryAgainPress = useCallback(() => {
    retryPayment();
  }, [retryPayment]);

  return (
    <SafeAreaView>
      <View
        style={[generalStyles.container, styles.errorContainer]}
        testID={AppTestIDs.acceptPaymentsLocationDisabled}>
        <GenericError
          onPress={onTryAgainPress}
          header={i18nStrings.locationServicesDisabled}
          subHeaderContent={<Text>{i18nStrings.locationServicesDisabledHelpText}</Text>}
          callToActionText={i18nStrings.tryAgain}
          icon={<IconCritical height={'128'} width={'128'} />}
          xPress={goBack}
        />
      </View>
    </SafeAreaView>
  );
}

export default LocationError;
