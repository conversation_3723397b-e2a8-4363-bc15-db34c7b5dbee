import React, { ReactElement, useCallback } from 'react';
import { SafeAreaView, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, IconCritical, Text, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './Errors.style';
import { boldSubstring } from '../../../../../utils/TextUtils';
import { AppTestIDs } from '../../../../AppTestIDs';

type Props = {
  retryPayment: () => void;
};

export function DeclinedByStripeError(props: Props): ReactElement {
  const { retryPayment } = props;
  const styles = useUniformStyles(styleSheet);

  const i18nStrings = useI18n(
    {
      tryAgain: 'accept-payments.try-again',
    },
    []
  );

  const onTryAgainPress = useCallback(() => {
    retryPayment();
  }, [retryPayment]);

  return (
    <SafeAreaView
      style={styles.declinedErrorContainer}
      testID={AppTestIDs.acceptPaymentsDeclinedByStripe}>
      <View style={styles.declinedErrorText}>
        <IconCritical height={128} width={128} color="critical" />
        <Text alignment="center">{boldSubstring('accept-payments.card-declined', false)}</Text>
      </View>
      <View style={styles.buttonContainer}>
        <Button
          text={i18nStrings.tryAgain}
          onPress={onTryAgainPress}
          testID={AppTestIDs.acceptPaymentsDeclinedByStripeTryAgain}
          style={styles.button}
        />
      </View>
    </SafeAreaView>
  );
}

export default DeclinedByStripeError;
