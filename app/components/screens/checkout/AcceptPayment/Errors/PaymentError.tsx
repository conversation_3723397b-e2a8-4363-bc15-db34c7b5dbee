import React, { ReactElement, useCallback } from 'react';
import { SafeAreaView, Text, View } from 'react-native';

import { IconCritical, Text as UniformText, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './Errors.style';
import { openSupportLink } from '../../../../../utils/UrlUtils';
import { AppTestIDs } from '../../../../AppTestIDs';
import { generalUniformStyles } from '../../../../Styles';
import { GenericError } from '../../../pointOfSale/GenericError/GenericError';

type Props = {
  retryPayment: () => void;
  goBack: () => void;
  strings: {
    tryAgain: string;
    paymentErrorHeader: string;
    paymentErrorHelpText: string;
  };
};

export function PaymentError(props: Props): ReactElement {
  const { retryPayment, goBack, strings } = props;
  const generalStyles = useUniformStyles(generalUniformStyles);
  const styles = useUniformStyles(styleSheet);

  const onTryAgainPress = useCallback(() => {
    retryPayment();
  }, [retryPayment]);

  const getSupportText = (): ReactElement => {
    return (
      <>
        <UniformText>{strings.paymentErrorHelpText[0]}</UniformText>
        <Text style={styles.reactNativeText}>
          {strings.paymentErrorHelpText[1]}
          <Text style={styles.linkText} onPress={openSupportLink}>
            {strings.paymentErrorHelpText[2]}
          </Text>
          {strings.paymentErrorHelpText[3]}
        </Text>
      </>
    );
  };

  return (
    <SafeAreaView>
      <View
        style={[generalStyles.container, styles.errorContainer]}
        testID={AppTestIDs.acceptPaymentsPaymentsError}>
        <GenericError
          onPress={onTryAgainPress}
          header={strings.paymentErrorHeader}
          subHeaderContent={getSupportText()}
          callToActionText={strings.tryAgain}
          icon={<IconCritical height={'128'} width={'128'} />}
          xPress={goBack}
        />
      </View>
    </SafeAreaView>
  );
}

export default PaymentError;
