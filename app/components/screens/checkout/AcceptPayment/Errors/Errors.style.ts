import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  errorContainer: {
    height: '100%',
    width: '100%',
    alignItems: 'center',
    gap: UniformSpace.four,
    justifyContent: 'space-between',
  },
  reactNativeText: {
    color: colors.contentDefault,
    fontSize: 16,
    lineHeight: 24,
  },
  linkText: {
    color: colors.linkArticleText,
    fontSize: 16,
    lineHeight: 24,
  },
  declinedErrorContainer: {
    alignItems: 'center',
    alignContent: 'center',
    justifyContent: 'space-between',
    flexGrow: 1,
  },
  declinedErrorText: {
    alignItems: 'center',
    gap: UniformSpace.two,
    paddingTop: UniformSpace.four,
    padding: UniformSpace.two,
  },
  buttonContainer: {
    padding: UniformSpace.one,
    paddingBottom: 0,
    backgroundColor: nonUniformColors.containerBlackBg,
    width: '100%',
  },
  button: {
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
  },
}));
