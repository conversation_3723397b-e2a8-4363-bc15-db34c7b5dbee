import React from 'react';

import { fireEvent, screen, waitFor } from '@testing-library/react-native';

import { AcceptPayment } from './AcceptPayment';
import useCheckoutWithTicketingLineItems from '../../../../gql/hooks/useCheckoutWithTicketingLineItems';
import useCreatePaymentIntent from '../../../../gql/hooks/useCreatePaymentIntent';
import useCreateTicketingPaymentToken from '../../../../gql/hooks/useCreateTicketingPaymentToken';
import {
  mockEventInfo,
  mockLineItemSelection,
  mockOrganization,
  mockPaymentIntentGQLResult,
  mockStripePaymentIntent,
  mockTicketingPaymentTokenGQLResult,
  mockVolunteerInfo,
} from '../../../../gql/mockData/TestData';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { LineItemSelection } from '../../../../types/shared';
import { Navigation } from '../../../../utils/Enums';
import { setupAccessContextMocks } from '../../../../utils/TestUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';

const mockCollectPaymentMethod = jest.fn();
const mockConfirmPaymentIntent = jest.fn();
const mockRetrievePaymentIntent = jest.fn();
const mockCancelPaymentIntent = jest.fn().mockResolvedValue(undefined);

const mockParams = {
  volunteerInfo: mockVolunteerInfo,
  event: mockEventInfo,
  organization: mockOrganization,
  totalPrice: 1000,
  purchaserEmail: '',
};

const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    addListener: jest.fn(),
    canGoBack: () => true,
    goBack: mockGoBack,
  }),
  useRoute: () => ({
    params: mockParams,
  }),
}));

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: jest.fn().mockImplementation(() => ({
    collectPaymentMethod: mockCollectPaymentMethod,
    confirmPaymentIntent: mockConfirmPaymentIntent,
    retrievePaymentIntent: mockRetrievePaymentIntent,
    cancelPaymentIntent: mockCancelPaymentIntent,
  })),
  CommonError: {
    Canceled: 'canceled',
  },
}));

jest.mock('@hudl/jarvis/logging', () => ({
  HudlLogger: {
    logError: jest.fn(),
  },
}));

jest.mock('../../../../gql/hooks/useCreatePaymentIntent');

const mockUseCreatePaymentIntent = useCreatePaymentIntent as jest.MockedFunction<
  typeof useCreatePaymentIntent
>;

const mockPaymentIntentGqlResult = {
  createPaymentIntent: jest.fn().mockImplementation(() => mockPaymentIntentGQLResult),
};

jest.mock('../../../../gql/hooks/useCreateTicketingPaymentToken');

const mockUseCreateTicketingPaymentToken = useCreateTicketingPaymentToken as jest.MockedFunction<
  typeof useCreateTicketingPaymentToken
>;

const mockPaymentTokenGqlResult = {
  createTicketingPaymentToken: jest
    .fn()
    .mockImplementation(() => mockTicketingPaymentTokenGQLResult),
};

jest.mock('../../../../gql/hooks/useCheckoutWithTicketingLineItems');

const mockUseCheckoutWithTicketingLineItems =
  useCheckoutWithTicketingLineItems as jest.MockedFunction<
    typeof useCheckoutWithTicketingLineItems
  >;

const mockCheckoutGqlResult = {
  checkoutWithTicketingLineItems: jest.fn().mockResolvedValue({
    data: {
      checkoutWithTicketingLineItems: {
        ticketGroup: { ticketGroupReference: 'TG-123' },
        pricingSummary: { totalInCents: 1000 },
      },
    },
  }),
};

jest.mock('../../../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../../../components/queries/useAccessCodeHook');

setupAccessContextMocks();

describe('AcceptPayment Tests', () => {
  it('Payment is successfully completed', async () => {
    mockUseCreatePaymentIntent.mockImplementation(() => ({
      ...mockPaymentIntentGqlResult,
    }));
    mockUseCreateTicketingPaymentToken.mockImplementation(() => ({
      ...mockPaymentTokenGqlResult,
    }));
    mockUseCheckoutWithTicketingLineItems.mockImplementation(() => ({
      ...mockCheckoutGqlResult,
    }));
    const lineItemSelections: LineItemSelection[] = [
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId1',
        quantitySelected: 1,
        unitPriceInCents: 100,
        name: 'Ticket Type 1',
        hudlFeeInCents: 100,
      },
    ];
    checkoutItemSelections(lineItemSelections);
    mockRetrievePaymentIntent.mockImplementation(() => {
      return {
        paymentIntent: mockStripePaymentIntent,
        error: undefined,
      };
    });
    mockCollectPaymentMethod.mockImplementation(() => {
      return {
        paymentIntent: { ...mockStripePaymentIntent, status: 'requiresConfirmation' },
        error: undefined,
      };
    });
    mockConfirmPaymentIntent.mockImplementation(() => {
      return {
        paymentIntent: { ...mockStripePaymentIntent, status: 'succeeded' },
        error: undefined,
      };
    });
    renderWithOptions(<AcceptPayment />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });
    await waitFor(() =>
      expect(mockNavigate).toHaveBeenCalledWith(Navigation.OrderComplete, {
        lineItemType: 'TicketType',
        ticketGroupReference: 'TG-123',
        ticketCount: 1,
        totalAmount: 1000,
        usedCard: true,
      })
    );
  });

  it('App waits for payment to be collected', async () => {
    mockUseCreatePaymentIntent.mockImplementation(() => ({
      ...mockPaymentIntentGqlResult,
    }));
    mockUseCreateTicketingPaymentToken.mockImplementation(() => ({
      ...mockPaymentTokenGqlResult,
    }));
    mockUseCheckoutWithTicketingLineItems.mockImplementation(() => ({
      ...mockCheckoutGqlResult,
    }));
    mockCollectPaymentMethod.mockImplementation(() => new Promise(() => {}));
    mockRetrievePaymentIntent.mockImplementation(() => {
      return {
        paymentIntent: mockStripePaymentIntent,
        error: undefined,
      };
    });
    renderWithOptions(<AcceptPayment />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });
    await waitFor(() => expect(AppTestIDs.acceptPaymentsRoot).toBeTruthy());
    await waitFor(() => expect(AppTestIDs.acceptPaymentsLoadingHelpText).toBeTruthy());
  });

  it('Declined by Stripe Error', async () => {
    mockUseCreatePaymentIntent.mockImplementation(() => ({ ...mockPaymentIntentGqlResult }));
    mockUseCreateTicketingPaymentToken.mockImplementation(() => ({ ...mockPaymentTokenGqlResult }));
    mockUseCheckoutWithTicketingLineItems.mockImplementation(() => ({ ...mockCheckoutGqlResult }));

    mockRetrievePaymentIntent.mockResolvedValue({
      paymentIntent: mockStripePaymentIntent,
      error: undefined,
    });
    mockCollectPaymentMethod.mockResolvedValue({
      paymentIntent: { ...mockStripePaymentIntent, status: 'requiresConfirmation' },
      error: undefined,
    });
    mockConfirmPaymentIntent.mockResolvedValue({
      paymentIntent: undefined,
      error: { code: 'DeclinedByStripeAPI', message: 'Stripe Error' },
    });

    renderWithOptions(<AcceptPayment />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    await screen.findByTestId(AppTestIDs.acceptPaymentsDeclinedByStripe);
  });

  it('Location Services Disabled Error', async () => {
    mockUseCreatePaymentIntent.mockImplementation(() => ({
      ...mockPaymentIntentGqlResult,
    }));
    mockUseCreateTicketingPaymentToken.mockImplementation(() => ({
      ...mockPaymentTokenGqlResult,
    }));
    mockUseCheckoutWithTicketingLineItems.mockImplementation(() => ({
      ...mockCheckoutGqlResult,
    }));
    mockRetrievePaymentIntent.mockImplementation(() => {
      return {
        paymentIntent: mockStripePaymentIntent,
        error: undefined,
      };
    });
    mockCollectPaymentMethod.mockImplementation(() => {
      return {
        error: {
          code: 'LocationServicesDisabled',
          message: 'Stripe Error',
        },
      };
    });
    renderWithOptions(<AcceptPayment />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });
    await waitFor(() => expect(mockCollectPaymentMethod).toHaveBeenCalled());
    expect(AppTestIDs.acceptPaymentsLocationDisabled).toBeTruthy();
  });

  it('Generic payments error', async () => {
    mockUseCreatePaymentIntent.mockImplementation(() => ({
      ...mockPaymentIntentGqlResult,
    }));
    mockUseCreateTicketingPaymentToken.mockImplementation(() => ({
      ...mockPaymentTokenGqlResult,
    }));
    mockUseCheckoutWithTicketingLineItems.mockImplementation(() => ({
      ...mockCheckoutGqlResult,
    }));
    mockRetrievePaymentIntent.mockImplementation(() => {
      return {
        paymentIntent: mockStripePaymentIntent,
        error: undefined,
      };
    });
    mockCollectPaymentMethod.mockImplementation(() => {
      return {
        paymentIntent: { ...mockStripePaymentIntent, status: 'requiresConfirmation' },
        error: {
          code: 'Stripe error',
          message: 'Stripe Error',
        },
      };
    });
    renderWithOptions(<AcceptPayment />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });
    await waitFor(() => expect(AppTestIDs.acceptPaymentsRoot).toBeTruthy());
    await waitFor(() => expect(AppTestIDs.acceptPaymentsPaymentsError).toBeTruthy());
  });

  it('calls cancelPaymentIntent then goBack when createdPaymentIntent exists', async () => {
    mockUseCreateTicketingPaymentToken.mockImplementation(() => ({
      ...mockPaymentTokenGqlResult,
    }));
    mockUseCreatePaymentIntent.mockImplementation(() => ({
      ...mockPaymentIntentGqlResult,
    }));
    mockUseCheckoutWithTicketingLineItems.mockImplementation(() => ({
      ...mockCheckoutGqlResult,
    }));
    mockRetrievePaymentIntent.mockResolvedValue({
      paymentIntent: {
        status: 'requiresConfirmation',
      },
      error: undefined,
    });

    mockCollectPaymentMethod.mockImplementation(() => new Promise(() => {}));

    renderWithOptions(<AcceptPayment />, {
      withNavigationContainer: true,
      withAccessContext: true,
      withAppSessionProvider: true,
    });

    await waitFor(async () => {
      await screen.findByTestId(AppTestIDs.genericErrorCTAButton);
      expect(mockRetrievePaymentIntent).toHaveBeenCalled();
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.genericErrorCTAButton));

    await waitFor(() => {
      expect(mockCancelPaymentIntent).toHaveBeenCalledTimes(1);
      expect(mockCancelPaymentIntent).toHaveBeenCalledWith({
        paymentIntent: {
          status: 'requiresConfirmation',
        },
      });
      expect(mockGoBack).toHaveBeenCalledTimes(1);
    });
  });
});
