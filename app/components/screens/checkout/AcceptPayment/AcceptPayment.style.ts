import { StyleSheet } from 'react-native';

import { UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styles = StyleSheet.create({
  container: {
    display: 'flex',
    height: '100%',
  },
  headerContainer: {
    gap: UniformSpace.two,
    padding: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.four,
  },
  loadingContainer: {
    display: 'flex',
    alignItems: 'center',
  },
  loadingSection: {
    alignItems: 'center',
    gap: UniformSpace.one,
  },
  loadingText: {
    color: 'white',
  },
  buttonWrapper: {
    padding: UniformSpace.one,
    backgroundColor: nonUniformColors.containerBlackBg,
    marginTop: 'auto',
  },
  button: {
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
  },
});
