import React from 'react';

import { act, fireEvent, render, screen } from '@testing-library/react-native';

import { SelectableItemList } from './SelectableItemList';
import { mockLineItemSelection } from '../../../../gql/mockData/TestData';
import { LineItemSelection } from '../../../../types/shared';
import { checkoutItemSelections } from '../../../../utils/stateVars';

const onRefresh = jest.fn();

describe('Selectable Item List tests', () => {
  it('Item List displays correctly', async () => {
    const mockLineItems: LineItemSelection[] = [
      { ...mockLineItemSelection, quantitySelected: 3, lineItemId: 'lineItemId' },
      { ...mockLineItemSelection, quantitySelected: 0, lineItemId: 'lineItemId2' },
    ];
    checkoutItemSelections(mockLineItems);

    render(
      <SelectableItemList
        items={mockLineItems}
        totalItemSelectionLimit={10}
        onRefresh={onRefresh}
        refreshing={false}
      />
    );

    expect(screen.getByTestId('selectable-item-list-root')).toBeTruthy();
  });

  it('Increment disabled when total item selection limit met', async () => {
    const mockLineItems: LineItemSelection[] = [
      { ...mockLineItemSelection, quantitySelected: 9, lineItemId: 'lineItemId' },
      { ...mockLineItemSelection, quantitySelected: 1, lineItemId: 'lineItemId2' },
    ];
    checkoutItemSelections(mockLineItems);

    render(
      <SelectableItemList
        items={mockLineItems}
        totalItemSelectionLimit={10}
        onRefresh={onRefresh}
        refreshing={false}
      />
    );

    expect(screen.getByTestId('counter-increment-lineItemId-disabled')).toBeTruthy();
    expect(screen.getByTestId('counter-increment-lineItemId2-disabled')).toBeTruthy();
  });

  it('Calls onRefresh when refresh triggered', async () => {
    const mockLineItems: LineItemSelection[] = [
      { ...mockLineItemSelection, quantitySelected: 2, lineItemId: 'lineItemId' },
      { ...mockLineItemSelection, quantitySelected: 0, lineItemId: 'lineItemId2' },
    ];
    checkoutItemSelections(mockLineItems);

    render(
      <SelectableItemList
        items={mockLineItems}
        totalItemSelectionLimit={10}
        onRefresh={onRefresh}
        refreshing={true}
      />
    );

    const scrollView = screen.getByTestId('selectable-item-list-list');
    expect(scrollView).toBeDefined();

    const { refreshControl } = scrollView.props;
    await act(async () => {
      refreshControl.props.onRefresh();
    });

    expect(onRefresh).toHaveBeenCalled();
  });

  it('ItemSelections properly updated', async () => {
    const mockLineItems = [
      { ...mockLineItemSelection, quantitySelected: 2, lineItemId: 'lineItemId' },
      { ...mockLineItemSelection, quantitySelected: 0, lineItemId: 'lineItemId2' },
    ];
    checkoutItemSelections(mockLineItems);

    render(
      <SelectableItemList
        items={mockLineItems}
        totalItemSelectionLimit={10}
        onRefresh={onRefresh}
        refreshing={false}
      />
    );

    fireEvent.press(screen.getByTestId('counter-increment-lineItemId'));
    await act(async () => {
      expect(screen.getByTestId('counter-input-lineItemId').props.value).toBe('3');
    });
  });
});
