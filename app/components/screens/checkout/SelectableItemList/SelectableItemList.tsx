import React, { ReactElement, useCallback, useRef, useState } from 'react';
import { FlatList, ListRenderItem, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';
import { RefreshControl } from 'react-native-gesture-handler';

import { useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './SelectableItemList.style';
import { LineItemSelection } from '../../../../types/shared';
import { sumLineItemQuantities } from '../../../../utils/LineItemUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';
import ListItemSeparator from '../../../Shared/ListItemSeparator/ListItemSeparator';
import { SelectableItem } from '../SelectableItem/SelectableItem';

interface SelectableItemListProps {
  items: LineItemSelection[];
  totalItemSelectionLimit: number;
  onRefresh: () => void;
  refreshing: boolean;
}

export function SelectableItemList(props: SelectableItemListProps): ReactElement {
  const {
    items,
    totalItemSelectionLimit: overallItemSelectionLimit,
    onRefresh,
    refreshing,
  } = props;

  const styles = useUniformStyles(styleSheet);
  const [lastItemChanged, setLastItemChanged] = useState<string | null>(null);
  const flatListRef = useRef<FlatList<LineItemSelection>>(null);
  const itemSelections = useReactiveVar(checkoutItemSelections);

  const remainingSelectableItemCount =
    overallItemSelectionLimit - sumLineItemQuantities(itemSelections);

  const getItemCount = useCallback(
    (itemId: string): number => {
      return (
        itemSelections.find((lineItem) => lineItem.lineItemId === itemId)?.quantitySelected ?? 0
      );
    },
    [itemSelections]
  );

  const onCountChange = useCallback(
    (itemId: string): ((newCount: number) => void) => {
      return (newCount: number): void => {
        setLastItemChanged(itemId);
        const newSelections = itemSelections.map((item) =>
          item.lineItemId === itemId ? { ...item, quantitySelected: newCount } : item
        );

        checkoutItemSelections(newSelections);
      };
    },
    [itemSelections]
  );

  const renderSelectableItem = useCallback<ListRenderItem<LineItemSelection>>(
    ({ item }) => {
      const itemCount = item.lineItemId ? getItemCount(item.lineItemId) : 0;
      return (
        <SelectableItem
          item={item}
          count={itemCount}
          onCountChange={onCountChange(item.lineItemId ?? '')}
          remainingOrderLimit={remainingSelectableItemCount}
        />
      );
    },
    [getItemCount, onCountChange, remainingSelectableItemCount]
  );

  const handleOnLayout = useCallback(() => {
    if (lastItemChanged) {
      const index = items.findIndex((item) => item.lineItemId === lastItemChanged);
      if (index >= 0) {
        flatListRef.current?.scrollToIndex({ index });
      }
      setLastItemChanged(null);
    }
  }, [items, lastItemChanged]);

  return (
    <View testID={AppTestIDs.selectableItemListRoot}>
      <FlatList
        ref={flatListRef}
        style={styles.flatList}
        data={items}
        renderItem={renderSelectableItem}
        ItemSeparatorComponent={ListItemSeparator}
        showsVerticalScrollIndicator={false}
        testID={AppTestIDs.selectableItemListList}
        onLayout={handleOnLayout}
        contentContainerStyle={styles.contentContainerStyle}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#fff" />
        }
      />
    </View>
  );
}
