import React from 'react';

import { fireEvent, screen } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import { ReceiptSelection } from './ReceiptSelection';
import enUS from '../../../../strings/en-US.json';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { Navigation } from '../../../../utils/Enums';
import { AppTestIDs } from '../../../AppTestIDs';

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

let mockParams = {
  totalPrice: 0,
};
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
  }),
  useRoute: () => ({
    params: mockParams,
  }),
  useFocusEffect: jest.fn(),
}));

describe('ReceiptSelection', () => {
  it('renders the component', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    expect(screen.getByTestId(AppTestIDs.receiptSelectionRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionHeader)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionSubHeader)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false))).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionOptionNo(false))).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(true, false))).toBeTruthy();
  });

  it('renders the component with paid button', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    expect(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(true, false))).toBeTruthy();
  });

  it('renders the component with free button', () => {
    mockParams = { ...mockParams, totalPrice: 100 };
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    expect(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(true, true))).toBeTruthy();
  });

  it('enables continue button when no option is selected', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionNo(false)));
    expect(screen.getByTestId(AppTestIDs.receiptSelectionOptionNo(true))).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(false, true))).toBeTruthy();
  });

  it('enables continue button when yes option is selected and valid email entered', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    expect(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(true))).toBeTruthy();
    fireEvent.changeText(
      screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput),
      '<EMAIL>'
    );
    expect(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(false, true))).toBeTruthy();
  });

  it('disables continue button when yes option is selected and invalid email entered', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    expect(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(true))).toBeTruthy();
    fireEvent.changeText(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'bob');
    expect(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(true, true))).toBeTruthy();
  });
});

describe('EmailInput Tests', () => {
  it('renders the email input', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    expect(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputHeader)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput)).toBeTruthy();
    expect(screen.queryByTestId(AppTestIDs.receiptSelectionEmailInputError)).toBeFalsy();
  });

  it('renders the email input error on blur', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    fireEvent.changeText(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'bob');
    fireEvent(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'onBlur');
    expect(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputError)).toBeTruthy();
  });

  it('does not render the email input error on blur if email is valid', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    fireEvent.changeText(
      screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput),
      '<EMAIL>'
    );
    fireEvent(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'onBlur');
    expect(screen.queryByTestId(AppTestIDs.receiptSelectionEmailInputError)).toBeFalsy();
  });

  it('does not render the email input error on blur if email is empty', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    fireEvent(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'onBlur');
    expect(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputError)).toBeTruthy();
  });

  it('removes error state on typing', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    fireEvent.changeText(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'bob');
    fireEvent(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput), 'onBlur');
    expect(screen.getByTestId(AppTestIDs.receiptSelectionEmailInputError)).toBeTruthy();
    fireEvent.changeText(
      screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput),
      '<EMAIL>'
    );
    expect(screen.queryByTestId(AppTestIDs.receiptSelectionEmailInputError)).toBeFalsy();
  });
});

describe('Navigation Tests', () => {
  it('navigates to the next screen with email when continue button is pressed', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionYes(false)));
    fireEvent.changeText(
      screen.getByTestId(AppTestIDs.receiptSelectionEmailInputInput),
      '<EMAIL>'
    );
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(false, true)));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.AcceptPayment, {
      totalPrice: 100,
      purchaserEmail: '<EMAIL>',
    });
  });

  it('navigates to the next screen without email when continue button is pressed', () => {
    renderWithOptions(<ReceiptSelection />, { withNavigationContainer: true });
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionOptionNo(false)));
    fireEvent.press(screen.getByTestId(AppTestIDs.receiptSelectionContinueButton(false, true)));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.AcceptPayment, {
      totalPrice: 100,
      purchaserEmail: '',
    });
  });
});
