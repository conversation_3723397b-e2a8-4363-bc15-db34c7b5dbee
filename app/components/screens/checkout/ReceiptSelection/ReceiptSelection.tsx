import React, { ReactElement, useCallback, useState } from 'react';
import {
  Keyboard,
  SafeAreaView,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, useUniformStyles, useUniformTheme } from '@hudl/rn-uniform';

import { styleSheet } from './ReceiptSelection.style';
import { AppNav } from '../../../../Nav';
import { Navigation } from '../../../../utils/Enums';
import { useEmailValidation } from '../../../../utils/emailValidationHook';
import { useKeyboard } from '../../../../utils/useKeyboardHook';
import { AppTestIDs } from '../../../AppTestIDs';
import { generalUniformStyles } from '../../../Styles';

export function ReceiptSelection(): ReactElement {
  const generalStyles = useUniformStyles(generalUniformStyles);
  const styles = useUniformStyles(styleSheet);
  const {
    params: { totalPrice, isCashSale },
  } = AppNav.root.useRoute(Navigation.ReceiptSelection);

  const nav = AppNav.root.useNavigation();
  const emailValidator = useEmailValidation();
  const keyboard = useKeyboard();
  const { colors } = useUniformTheme();

  const i18nStrings = useI18n(
    {
      header: 'pos.receipt-selection.header',
      subheader: 'pos.receipt-selection.sub-header',
      yesButton: 'pos.receipt-selection.yes-button',
      noButton: 'pos.receipt-selection.no-button',
      infoInputHeader: 'pos.receipt-selection.info-input.header',
      infoInputEmail: 'pos.receipt-selection.info-input.email',
      infoInputEmailPlaceholder: 'pos.receipt-selection.info-input.email-placeholder',
      acceptPaymentButton: 'pos.receipt-selection.accept-payment-button',
      placeOrderButton: 'pos.receipt-selection.place-order-button',
      infoInputErrorEmail: 'pos.receipt-selection.info-input.error.email',
      required: 'pos.receipt-selection.required',
    },
    []
  );

  const isPaidTransaction = totalPrice > 0;

  const [sendReceipt, setSendReceipt] = useState<boolean | undefined>(undefined);

  const [email, setEmail] = useState('');
  const [emailInputError, setEmailInputError] = useState(false);
  const [emailInputSelected, setEmailInputSelected] = useState(false);
  const isEmailValid = emailValidator(email);

  const onOptionSelected = useCallback((shouldSendReceipt: boolean): (() => void) => {
    return (): void => {
      setEmailInputError(false);
      setSendReceipt(shouldSendReceipt);
    };
  }, []);

  const onChangeEmail = useCallback((text: string) => {
    setEmailInputError(false);
    setEmail(text);
  }, []);

  const onBlurInfoInput = useCallback(() => {
    setEmailInputError(!isEmailValid);
    setEmailInputSelected(false);
  }, [isEmailValid]);

  const onFocusInfoInput = useCallback(() => {
    setEmailInputSelected(false);
  }, []);

  const onSubmitEditing = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  const onContinue = useCallback((): void => {
    const purchaserEmail = sendReceipt ? email : '';
    const path = isCashSale ? Navigation.AcceptCashPayment : Navigation.AcceptPayment;
    nav.navigate(path, {
      totalPrice,
      purchaserEmail,
    });
  }, [email, isCashSale, nav, sendReceipt, totalPrice]);

  const isContinueButtonDisabled = sendReceipt === undefined || (sendReceipt && !isEmailValid);

  const renderReceiptOption = useCallback(
    (
      onPress: () => void,
      label: string,
      selected: boolean,
      testId: string,
      children?: ReactElement
    ) => {
      return (
        <TouchableOpacity
          disabled={selected}
          style={[
            styles.receiptOptionContainer,
            selected ? styles.receiptOptionContainerSelected : {},
          ]}
          testID={testId}
          onPress={onPress}>
          <View>
            <Text style={styles.receiptOptionText}>{label}</Text>
          </View>
          {children}
        </TouchableOpacity>
      );
    },
    [styles.receiptOptionContainer, styles.receiptOptionContainerSelected, styles.receiptOptionText]
  );

  const renderEmailInput = (): ReactElement | undefined => {
    if (sendReceipt) {
      return (
        <View style={styles.infoInputContainer}>
          <View style={styles.infoInput}>
            <Text
              style={styles.infoInputHeader}
              testID={AppTestIDs.receiptSelectionEmailInputHeader}>
              {i18nStrings.infoInputEmail}
            </Text>
            <TextInput
              keyboardType="email-address"
              style={[
                styles.infoInputInput,
                emailInputSelected ? styles.infoInputInputSelected : {},
                emailInputError ? styles.infoInputInputError : {},
              ]}
              value={email}
              placeholder={i18nStrings.infoInputEmailPlaceholder}
              placeholderTextColor={colors.contentNonessential}
              autoComplete={'off'}
              autoCapitalize={'none'}
              autoCorrect={false}
              returnKeyType="done"
              onChangeText={onChangeEmail}
              onBlur={onBlurInfoInput}
              onFocus={onFocusInfoInput}
              blurOnSubmit={false}
              onSubmitEditing={onSubmitEditing}
              enterKeyHint={'done'}
              testID={AppTestIDs.receiptSelectionEmailInputInput}
            />
            {emailInputError && (
              <Text
                style={styles.infoInputErrorText}
                testID={AppTestIDs.receiptSelectionEmailInputError}>
                {i18nStrings.infoInputErrorEmail}
              </Text>
            )}
          </View>
        </View>
      );
    }
  };

  return (
    <SafeAreaView>
      <TouchableWithoutFeedback disabled={!keyboard.isVisible} onPress={Keyboard.dismiss}>
        <View
          style={[generalStyles.container, styles.container]}
          testID={AppTestIDs.receiptSelectionRoot}>
          <View style={styles.headerContainer}>
            <Text style={styles.header} testID={AppTestIDs.receiptSelectionHeader}>
              {i18nStrings.header}
            </Text>
            <Text style={styles.subHeader} testID={AppTestIDs.receiptSelectionSubHeader}>
              {i18nStrings.subheader}
            </Text>
          </View>
          <View />
          <View style={styles.contentContainer}>
            {renderReceiptOption(
              onOptionSelected(true),
              i18nStrings.yesButton,
              sendReceipt === true,
              AppTestIDs.receiptSelectionOptionYes(sendReceipt === true),
              renderEmailInput()
            )}
            {renderReceiptOption(
              onOptionSelected(false),
              i18nStrings.noButton,
              sendReceipt === false,
              AppTestIDs.receiptSelectionOptionNo(sendReceipt === false)
            )}
          </View>
          <View style={styles.footerWrapper}>
            <View style={styles.footerContainer}>
              <View style={styles.buttonContainer}>
                <Button
                  onPress={onContinue}
                  isDisabled={isContinueButtonDisabled}
                  text={
                    isPaidTransaction
                      ? i18nStrings.acceptPaymentButton
                      : i18nStrings.placeOrderButton
                  }
                  testID={AppTestIDs.receiptSelectionContinueButton(
                    isContinueButtonDisabled,
                    isPaidTransaction
                  )}
                  style={styles.button}
                />
              </View>
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
}
