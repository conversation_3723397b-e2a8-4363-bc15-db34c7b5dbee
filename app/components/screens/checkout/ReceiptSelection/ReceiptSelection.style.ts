import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    paddingTop: UniformSpace.one,
    height: '100%',
    width: '100%',
  },
  headerContainer: {
    marginBottom: UniformSpace.oneAndQuarter,
    paddingLeft: UniformSpace.oneAndHalf,
    paddingRight: UniformSpace.oneAndHalf,
  },
  header: {
    fontSize: 30,
    fontWeight: '300',
    lineHeight: 32,
    color: colors.contentContrast,
    paddingBottom: UniformSpace.one,
  },
  subHeader: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    color: colors.contentContrast,
    paddingBottom: UniformSpace.one,
  },
  footerWrapper: {
    backgroundColor: colors.bgLevel0,
    justifyContent: 'flex-end',
  },
  footerContainer: {
    paddingLeft: UniformSpace.oneAndHalf,
    paddingRight: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.one,
  },
  buttonContainer: {
    paddingTop: UniformSpace.oneAndHalf,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingLeft: UniformSpace.oneAndHalf,
    paddingRight: UniformSpace.oneAndHalf,
    gap: UniformSpace.threeQuarter,
  },
  receiptOptionContainer: {
    display: 'flex',
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: UniformSpace.threeQuarter,
    paddingHorizontal: UniformSpace.one,
    backgroundColor: colors.bgLevel0,
    gap: UniformSpace.half,
  },
  receiptOptionContainerSelected: {
    borderColor: colors.contentContrast,
  },
  receiptOptionText: {
    fontWeight: '700',
    fontSize: 16,
    lineHeight: 24,
    color: colors.baseWhite,
  },
  infoInputContainer: {
    display: 'flex',
    gap: UniformSpace.threeQuarter,
    paddingTop: UniformSpace.one,
    paddingBottom: UniformSpace.half,
  },
  infoInput: {
    display: 'flex',
    gap: UniformSpace.eighth,
  },
  infoInputHeader: {
    color: colors.contentDefault,
  },
  infoInputInput: {
    borderColor: colors.divider,
    borderWidth: 1,
    borderRadius: 4,
    paddingVertical: UniformSpace.one,
    paddingHorizontal: UniformSpace.oneAndQuarter,
    color: colors.contentContrast,
  },
  infoInputInputSelected: {
    borderColor: colors.contentContrast,
  },
  infoInputInputError: {
    borderColor: colors.utilityCritical,
  },
  infoInputErrorText: {
    fontSize: 12,
    color: colors.utilityCritical,
  },
  button: {
    backgroundColor: nonUniformColors.contentEmphasesBgContrast,
  },
}));
