import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    justifyContent: 'space-between',
    paddingTop: UniformSpace.one,
    height: '100%',
    width: '100%',
  },
  headerContainer: {
    marginBottom: UniformSpace.oneAndQuarter,
    padding: UniformSpace.one,
    marginHorizontal: UniformSpace.one,
    backgroundColor: nonUniformColors.contentEmphasesBg,
    borderRadius: 8,
  },
  header: {
    fontSize: 14,
    fontWeight: '700',
    color: nonUniformColors.contentStrongBG,
    textAlign: 'center',
  },
  dividerWrapper: {
    paddingBottom: UniformSpace.one,
    marginHorizontal: UniformSpace.one,
  },
  reviewOrderHeader: {
    color: nonUniformColors.contentStrongBG,
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
  },
  subHeader: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    color: colors.contentContrast,
    paddingBottom: UniformSpace.one,
  },
  footerWrapper: {
    backgroundColor: colors.bgLevel0,
    justifyContent: 'flex-end',
  },
  footerContainer: {
    paddingLeft: UniformSpace.threeQuarter,
    paddingRight: UniformSpace.threeQuarter,
    paddingBottom: UniformSpace.one,
  },
  contentContainer: {
    flex: 1,
    paddingLeft: UniformSpace.threeQuarter,
    paddingRight: UniformSpace.threeQuarter,
  },
  flatList: {
    marginTop: UniformSpace.one,
  },
  flatListContentContainer: {
    paddingBottom: UniformSpace.one,
  },
  reviewableItem: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: UniformSpace.quarter,
  },
  reviewableItemText: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 24,
    color: nonUniformColors.contentStrongBG,
  },
  reviewableItemNameQuantity: {
    width: '65%',
  },
  reviewableItemPrice: {
    maxWidth: '35%',
    alignItems: 'flex-end',
  },
  orderTotalContainer: {
    paddingTop: UniformSpace.one,
    paddingBottom: UniformSpace.oneAndQuarter,
  },
  acceptPaymentButtonContainer: {
    gap: UniformSpace.half,
  },
  processButton: {
    backgroundColor: nonUniformColors.contentEmphasesBgContrast,
  },
  editButton: {
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    padding: UniformSpace.oneAndHalf,
  },
  errorMessage: {
    color: colors.contentContrast,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    paddingBottom: UniformSpace.four,
  },
  linkText: {
    color: colors.linkArticleText,
    fontSize: 16,
    lineHeight: 24,
  },
}));
