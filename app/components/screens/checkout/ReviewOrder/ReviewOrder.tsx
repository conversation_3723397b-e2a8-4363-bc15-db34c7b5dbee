import React, { ReactElement, useCallback, useMemo } from 'react';
import { FlatList, ListRenderItem, SafeAreaView, Text, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';

import { HudlI18n, useI18n } from '@hudl/jarvis/i18n';
import { <PERSON><PERSON>, Divider, Spinner, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './ReviewOrder.style';
import { AppNav } from '../../../../Nav';
import { useAccessContext } from '../../../../context/AccessContext';
import { LineItemType } from '../../../../enums/shared';
import useTicketingPricingSummary from '../../../../gql/hooks/useTicketingPricingSummary';
import IconSignalDisconnected from '../../../../icons/IconSignalDisconnected';
import { ReviewableItem } from '../../../../types/shared';
import { Navigation } from '../../../../utils/Enums';
import {
  getLineItemFeeInfo,
  sumLineItemPrices,
  sumLineItemQuantities,
} from '../../../../utils/LineItemUtils';
import { getFormattedPrice } from '../../../../utils/NumberFormatUtils';
import { openSupportLink } from '../../../../utils/UrlUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';
import OrderTotal from '../../../OrderTotal/OrderTotal';
import { generalUniformStyles } from '../../../Styles';
import { GenericError } from '../../pointOfSale/GenericError/GenericError';

export function ReviewOrder(): ReactElement {
  const styles = useUniformStyles(styleSheet);
  const generalStyles = useUniformStyles(generalUniformStyles);
  const nav = AppNav.root.useNavigation();
  const {
    params: { isCustomSale, isCashSale },
  } = AppNav.root.useRoute(Navigation.ReviewOrder);
  const { eventInfo: event, organization } = useAccessContext();

  const currentItemSelections = useReactiveVar(checkoutItemSelections);
  const totalTicketQuantity = sumLineItemQuantities(currentItemSelections);

  const i18nStrings = useI18n(
    {
      header: 'pos.review-order.header',
      reviewOrder: 'pos.review-order.review-order',
      paidSubHeader: 'pos.review-order.paid-sub-header',
      freeSubHeader: 'pos.review-order.free-sub-header',
      itemsHeader: [
        'pos.review-order.items-header',
        { count: Object.keys(currentItemSelections).length },
      ],
      processCardButton: 'pos.review-order.process-card-button',
      processCashButton: 'pos.review-order.process-cash-button',
      editButton: 'pos.review-order.edit-button',
      errorHeader: 'pos.item-selection.pricing-summary-error.header',
      errorMessage: 'pos.item-selection.pricing-summary-error.message',
      errorTryAgain: 'pos.item-selection.pricing-summary-error.try-again',
      free: 'pos.review-order.free',
      orderTransactionFee: 'pos.review-order.order-transaction-fee',
    },
    []
  );

  const { pricingSummary, pricingSummaryLoading, pricingSummaryError, refetchPricingSummary } =
    useTicketingPricingSummary(currentItemSelections, event?.id ?? '', organization?.id ?? '', {
      notifyOnNetworkStatusChange: true,
      skip: isCashSale,
    });

  const totalPrice: number | undefined = pricingSummary?.totalInCents;

  const reviewableItems: ReviewableItem[] = currentItemSelections
    .filter((lineItem) => lineItem.quantitySelected > 0)
    .map((lineItem) => {
      const itemQuantity = lineItem.quantitySelected;
      const { shouldShowFee, hudlFeeInCents } = getLineItemFeeInfo(lineItem);
      return {
        id: lineItem.lineItemId ?? '',
        name: lineItem.name,
        totalPriceInCents:
          (lineItem.unitPriceInCents + (shouldShowFee ? hudlFeeInCents : 0)) * itemQuantity,
        quantity: itemQuantity,
        shouldShowFee: shouldShowFee,
        hudlFeeInCents: hudlFeeInCents,
        subtotalInCents: lineItem.unitPriceInCents,
      };
    });

  const totalSubtotal = useMemo(
    () => sumLineItemPrices(currentItemSelections),
    [currentItemSelections]
  );

  const onAcceptPaymentPress = useCallback((): void => {
    nav.navigate(Navigation.ReceiptSelection, {
      totalPrice: isCashSale ? totalSubtotal ?? 0 : totalPrice ?? 0,
      isCashSale,
    });
  }, [isCashSale, nav, totalPrice, totalSubtotal]);

  const onEditPressed = useCallback((): void => {
    if (nav.canGoBack()) {
      nav.goBack();
    }
  }, [nav]);

  const onErrorTryAgain = useCallback((): void => {
    refetchPricingSummary();
  }, [refetchPricingSummary]);

  const processButtonText = useMemo(
    () => (isCashSale ? i18nStrings.processCashButton : i18nStrings.processCardButton),
    [i18nStrings.processCardButton, i18nStrings.processCashButton, isCashSale]
  );

  const renderReviewableItem = useCallback<ListRenderItem<ReviewableItem>>(
    ({ item }) => {
      const name = item.quantity
        ? HudlI18n.t('pos.review-order.reviewable-item-name-quantity', {
            name: item.name,
            quantity: item.quantity,
          })
        : item.name;
      return (
        <View style={styles.reviewableItem} testID={AppTestIDs.reviewOrderItem(item.id)}>
          <View style={styles.reviewableItemNameQuantity}>
            <Text
              style={styles.reviewableItemText}
              testID={AppTestIDs.reviewOrderItemName(item.id)}>
              {name}
            </Text>
          </View>
          <View style={styles.reviewableItemPrice}>
            <Text
              style={styles.reviewableItemText}
              testID={AppTestIDs.reviewOrderItemPrice(item.id)}>
              {`${item.totalPriceInCents === 0 ? `${i18nStrings.free} - ` : ''}${getFormattedPrice(
                item.subtotalInCents * (item.quantity ?? 1)
              )}`}
            </Text>
          </View>
        </View>
      );
    },
    [
      i18nStrings.free,
      styles.reviewableItem,
      styles.reviewableItemNameQuantity,
      styles.reviewableItemPrice,
      styles.reviewableItemText,
    ]
  );

  if (pricingSummaryLoading && !isCashSale) {
    return (
      <View
        style={[generalStyles.container, styles.container, styles.loadingContainer]}
        testID={AppTestIDs.reviewOrderLoading}>
        <Spinner testID="item-selection-spinner" />
      </View>
    );
  }

  if ((pricingSummaryError || totalPrice === undefined) && !isCashSale) {
    return (
      <SafeAreaView style={styles.container} testID={AppTestIDs.reviewOrderErrorRoot}>
        <GenericError
          header={i18nStrings.errorHeader}
          subHeaderContent={
            <Text style={styles.errorMessage}>
              {i18nStrings.errorMessage[0]}
              <Text
                style={styles.linkText}
                onPress={openSupportLink}
                testID={AppTestIDs.reviewOrderErrorContactSupportLink}>
                {i18nStrings.errorMessage[1]}
              </Text>
              {i18nStrings.errorMessage[2]}
            </Text>
          }
          onPress={onErrorTryAgain}
          callToActionText={i18nStrings.errorTryAgain}
          icon={<IconSignalDisconnected height={128} width={128} />}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView
      style={[generalStyles.container, styles.container]}
      testID={AppTestIDs.reviewOrderRoot}>
      <View style={styles.headerContainer}>
        <Text style={styles.header} testID={AppTestIDs.posItemSelectionHeader}>
          {i18nStrings.header}
        </Text>
      </View>
      <View style={styles.dividerWrapper}>
        <Divider width={'two'} color={'level3-accent'} />
      </View>
      <Text style={styles.reviewOrderHeader} testID={AppTestIDs.reviewOrderHeader}>
        {i18nStrings.reviewOrder}
      </Text>
      <View style={styles.contentContainer}>
        <FlatList
          style={styles.flatList}
          data={reviewableItems}
          renderItem={renderReviewableItem}
          showsVerticalScrollIndicator={false}
          testID={AppTestIDs.reviewOrderItemList}
          contentContainerStyle={styles.flatListContentContainer}
          ListFooterComponent={
            <View style={styles.reviewableItem}>
              <View style={styles.reviewableItemNameQuantity}>
                <Text style={styles.reviewableItemText}>{i18nStrings.orderTransactionFee}</Text>
              </View>
              <View style={styles.reviewableItemPrice}>
                <Text
                  style={styles.reviewableItemText}
                  testID={AppTestIDs.reviewOrderOrderTransactionFee}>
                  {getFormattedPrice(!isCashSale ? pricingSummary?.feesInCents ?? 0 : 0)}
                </Text>
              </View>
            </View>
          }
        />
      </View>
      <View style={styles.footerWrapper}>
        <View style={styles.footerContainer}>
          <View style={styles.orderTotalContainer}>
            <OrderTotal
              totalItemCount={totalTicketQuantity}
              lineItemType={isCustomSale ? LineItemType.Custom : LineItemType.TicketType}
              pricingSummary={!isCashSale ? pricingSummary : undefined}
              pricingSummaryLoading={pricingSummaryLoading}
              showTotalPrice={true}
              isCustomSale={isCustomSale}
              subtotalPrice={totalSubtotal}
            />
          </View>
          <View style={styles.acceptPaymentButtonContainer}>
            <Button
              onPress={onAcceptPaymentPress}
              text={processButtonText}
              testID={AppTestIDs.reviewOrderProcessButton}
              style={styles.processButton}
            />
            <Button
              onPress={onEditPressed}
              text={i18nStrings.editButton}
              testID={AppTestIDs.reviewOrderEditButton}
              style={styles.editButton}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
