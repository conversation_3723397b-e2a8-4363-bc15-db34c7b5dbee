import React from 'react';
import { Linking } from 'react-native';

import { ApolloError, NetworkStatus } from '@apollo/client';
import { fireEvent, screen } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import { ReviewOrder } from './ReviewOrder';
import useTicketingPricingSummary from '../../../../gql/hooks/useTicketingPricingSummary';
import {
  mockEventInfo,
  mockLineItemSelection,
  mockOrganization,
  mockPricingSummary,
} from '../../../../gql/mockData/TestData';
import { EventInfo } from '../../../../models/EventInfo';
import enUS from '../../../../strings/en-US.json';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { LineItemSelection } from '../../../../types/shared';
import { processingFeeLineItemId } from '../../../../utils/Constants';
import { Navigation } from '../../../../utils/Enums';
import { setupAccessContextMocks } from '../../../../utils/TestUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';

const mockEvent: EventInfo = {
  ...mockEventInfo,
  ticketTypePricingSummaries: [
    {
      ...mockEventInfo.ticketTypePricingSummaries[0],
      shouldShowFee: true,
      hudlFeeInCents: 100,
    },
  ],
};

const mockParams = {
  event: mockEvent,
  organization: mockOrganization,
};

jest.mock('../../../../gql/hooks/useTicketedEventByIdWithAnalytics');

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
  }),
  useRoute: () => ({
    params: mockParams,
  }),
}));

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

jest.mock('../../../../components/queries/useAccessCodeHook');
setupAccessContextMocks();

const mockDefaultPricingSummaryGqlResult = {
  pricingSummary: mockPricingSummary,
  pricingSummaryLoading: false,
  pricingSummaryError: undefined,
  refetchPricingSummary: jest.fn(),
  networkStatus: NetworkStatus.ready,
};

jest.mock('../../../../gql/hooks/useTicketingPricingSummary');

const mockUseTicketingPricingSummary = useTicketingPricingSummary as jest.MockedFunction<
  typeof useTicketingPricingSummary
>;

const openSupportLinkSpy = jest.spyOn(Linking, 'openURL');

describe('ReviewOrder', () => {
  it('Renders without error', () => {
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...mockDefaultPricingSummaryGqlResult,
    }));

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderHeader)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderItemList)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderProcessButton)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderEditButton)).toBeTruthy();
  });

  it('Renders with error', () => {
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...mockDefaultPricingSummaryGqlResult,
      pricingSummaryError: new ApolloError({ errorMessage: 'error' }),
    }));

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderErrorRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderErrorContactSupportLink)).toBeTruthy();
  });

  it('Renders error if total price is null', () => {
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...mockDefaultPricingSummaryGqlResult,
      pricingSummary: undefined,
    }));

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderErrorRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderErrorContactSupportLink)).toBeTruthy();
  });

  it('Renders with loading', () => {
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...mockDefaultPricingSummaryGqlResult,
      pricingSummaryLoading: true,
    }));

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderLoading)).toBeTruthy();
  });

  it('Retries gql call when error retry button hit', async () => {
    const refetchFunc = jest.fn();
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...mockDefaultPricingSummaryGqlResult,
      pricingSummaryError: new ApolloError({ errorMessage: 'error' }),
      refetchPricingSummary: refetchFunc,
    }));

    renderWithOptions(<ReviewOrder />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.genericErrorCTAButton));

    expect(refetchFunc).toHaveBeenCalled();
  });

  it('Navigates to support url when support link on error page clicked', async () => {
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...mockDefaultPricingSummaryGqlResult,
      pricingSummaryError: new ApolloError({ errorMessage: 'error' }),
    }));

    renderWithOptions(<ReviewOrder />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.reviewOrderErrorContactSupportLink));

    expect(openSupportLinkSpy).toHaveBeenCalled();
  });
});

describe('Line item tests', () => {
  it('Renders with single paid line item', () => {
    const feeInCents = 123;
    const totalInCents = 1123;
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...{
        ...mockDefaultPricingSummaryGqlResult,
        pricingSummary: {
          ...mockPricingSummary,
          feesInCents: feeInCents,
          totalInCents: totalInCents,
        },
      },
    }));

    const lineItemSelections: LineItemSelection[] = [
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId1',
        quantitySelected: 1,
        unitPriceInCents: 100,
        name: 'Ticket Type 1',
        hudlFeeInCents: 100,
      },
    ];

    checkoutItemSelections(lineItemSelections);

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderItemList)).toBeTruthy();

    expect(screen.getByTestId(AppTestIDs.reviewOrderItem('lineItemId1'))).toBeTruthy();
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemName('lineItemId1')).props.children
    ).toEqual('1 x Ticket Type 1');
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemPrice('lineItemId1')).props.children
    ).toEqual('$1.00');

    expect(screen.getByTestId(AppTestIDs.reviewOrderOrderTransactionFee).props.children).toEqual(
      '$1.23'
    );

    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 1 ticket'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$11.23');
  });

  it('Renders with multiple line items', () => {
    const feeInCents = 5555;
    const totalInCents = 123456;
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...{
        ...mockDefaultPricingSummaryGqlResult,
        pricingSummary: {
          ...mockPricingSummary,
          feesInCents: feeInCents,
          totalInCents: totalInCents,
        },
      },
    }));

    const lineItemSelections: LineItemSelection[] = [
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId1',
        quantitySelected: 1,
        name: 'Ticket Type 1',
        unitPriceInCents: 100,
      },
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId2',
        quantitySelected: 2,
        name: 'Ticket Type 2',
        unitPriceInCents: 200,
      },
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId3',
        quantitySelected: 3,
        name: 'Ticket Type 3',
        unitPriceInCents: 300,
      },
    ];

    checkoutItemSelections(lineItemSelections);

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderItemList)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.reviewOrderItem('lineItemId1'))).toBeTruthy();
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemName('lineItemId1')).props.children
    ).toEqual('1 x Ticket Type 1');
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemPrice('lineItemId1')).props.children
    ).toEqual('$1.00');

    expect(screen.getByTestId(AppTestIDs.reviewOrderItem('lineItemId2'))).toBeTruthy();
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemName('lineItemId2')).props.children
    ).toEqual('2 x Ticket Type 2');
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemPrice('lineItemId2')).props.children
    ).toEqual('$4.00');

    expect(screen.getByTestId(AppTestIDs.reviewOrderItem('lineItemId3'))).toBeTruthy();
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemName('lineItemId3')).props.children
    ).toEqual('3 x Ticket Type 3');
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemPrice('lineItemId3')).props.children
    ).toEqual('$9.00');

    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 6 tickets'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$1,234.56');
  });

  it('Renders with single fee line item', () => {
    const feeInCents = 0;
    const totalInCents = 0;
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...{
        ...mockDefaultPricingSummaryGqlResult,
        pricingSummary: {
          ...mockPricingSummary,
          feesInCents: feeInCents,
          totalInCents: totalInCents,
        },
      },
    }));

    const lineItemSelections: LineItemSelection[] = [
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId4',
        quantitySelected: 1,
        name: 'Ticket Type 4',
        unitPriceInCents: 0,
      },
    ];

    checkoutItemSelections(lineItemSelections);

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderItemList)).toBeTruthy();

    expect(screen.getByTestId(AppTestIDs.reviewOrderItem('lineItemId4'))).toBeTruthy();
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemName('lineItemId4')).props.children
    ).toEqual('1 x Ticket Type 4');
    expect(
      screen.getByTestId(AppTestIDs.reviewOrderItemPrice('lineItemId4')).props.children
    ).toEqual('Free - $0.00');

    expect(
      screen.queryByTestId(AppTestIDs.reviewOrderItemName(processingFeeLineItemId))
    ).toBeFalsy();

    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 1 ticket'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$0.00');
  });

  it('Navigates to receipt selection on accept payment press', () => {
    const feeInCents = 123;
    const totalInCents = 1123;
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      ...{
        ...mockDefaultPricingSummaryGqlResult,
        pricingSummary: {
          ...mockPricingSummary,
          feesInCents: feeInCents,
          totalInCents: totalInCents,
        },
      },
    }));

    const lineItemSelections: LineItemSelection[] = [
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId1',
        quantitySelected: 1,
      },
    ];

    checkoutItemSelections(lineItemSelections);

    renderWithOptions(<ReviewOrder />, { withNavigationContainer: true, withAccessContext: true });

    expect(screen.getByTestId(AppTestIDs.reviewOrderProcessButton)).toBeTruthy();
    fireEvent.press(screen.getByTestId(AppTestIDs.reviewOrderProcessButton));

    expect(mockNavigate).toHaveBeenCalledWith(Navigation.ReceiptSelection, {
      totalPrice: totalInCents,
    });
  });
});
