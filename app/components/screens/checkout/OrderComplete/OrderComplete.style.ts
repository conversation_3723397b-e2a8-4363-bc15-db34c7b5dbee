import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    justifyContent: 'space-between',
    height: '100%',
    width: '100%',
  },
  headerContainer: {
    padding: UniformSpace.oneAndHalf,
    alignItems: 'center',
    gap: UniformSpace.oneAndQuarter,
  },
  header: {
    fontSize: 24,
    fontWeight: '700',
    color: nonUniformColors.contentStrongBG,
  },
  subHeader: {
    fontSize: 16,
    fontWeight: '700',
    color: nonUniformColors.contentStrongBG,
    paddingBottom: UniformSpace.one,
  },
  footerWrapper: {
    backgroundColor: colors.bgLevel0,
    justifyContent: 'flex-end',
  },
  footerContainer: {
    paddingHorizontal: UniformSpace.oneAndHalf,
    paddingTop: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.one,
  },
  contentContainer: {
    marginBottom: 'auto',
  },
  buttonsContainer: {
    gap: UniformSpace.one,
    flexDirection: 'row',
  },
  button: {
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
    flex: 1,
  },
  contentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: UniformSpace.oneAndHalf,
    paddingVertical: UniformSpace.half,
    width: '100%',
  },
  contentText: {
    fontSize: 16,
    fontWeight: '500',
    color: nonUniformColors.contentStrongBG,
  },
}));
