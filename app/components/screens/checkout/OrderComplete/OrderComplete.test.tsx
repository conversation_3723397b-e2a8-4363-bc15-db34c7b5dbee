import React from 'react';

import { fireEvent, screen, waitFor } from '@testing-library/react-native';

import { OrderComplete } from './OrderComplete';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { Navigation, Screens } from '../../../../utils/Enums';
import { AppTestIDs } from '../../../AppTestIDs';

const mockUseRoute = jest.fn();
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    reset: jest.fn(),
  }),
  useRoute: (...args: unknown[]) => mockUseRoute(...args),
  params: {
    lineItemType: 'TicketType',
    ticketCount: 3,
    totalAmount: 5000,
    ticketGroupReference: 'TG-123',
    usedCard: true,
  },
  useFocusEffect: jest.fn(),
}));

describe('OrderComplete', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRoute.mockReturnValue({
      params: {
        lineItemType: 'TicketType',
        ticketCount: 3,
        totalAmount: 5000,
        ticketGroupReference: 'TG-123',
        usedCard: true,
      },
    });
  });

  it('renders', () => {
    renderWithOptions(<OrderComplete />, { withNavigationContainer: true });

    expect(screen.getByTestId(AppTestIDs.orderCompleteRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderCompleteHeader)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderCompleteSubHeader)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderCompleteNewOrderButton)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderCompleteNavigateHome)).toBeTruthy();
  });

  it('navigates to new order: Tickets', () => {
    renderWithOptions(<OrderComplete />, {
      withNavigationContainer: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.orderCompleteNewOrderButton));
    expect(mockNavigate).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.ItemSelection);
  });

  it('navigates to new order: Custom Sale', () => {
    mockUseRoute.mockReturnValue({
      params: {
        lineItemType: 'Custom',
        ticketCount: 1,
        totalAmount: 1500,
        ticketGroupReference: 'TG-999',
        usedCard: false,
      },
    });

    renderWithOptions(<OrderComplete />, {
      withNavigationContainer: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.orderCompleteNewOrderButton));
    expect(mockNavigate).toHaveBeenCalled();
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.CustomSalesSelection);
  });

  it('navigates home', async () => {
    renderWithOptions(<OrderComplete />, {
      withNavigationContainer: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.orderCompleteNavigateHome));
    await waitFor(() => expect(mockNavigate).toHaveBeenCalledWith(Screens.Home));
  });
});
