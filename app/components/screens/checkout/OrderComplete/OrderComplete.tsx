import React, { ReactElement, useCallback, useMemo } from 'react';
import { SafeAreaView, Text, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './OrderComplete.style';
import { AppNav } from '../../../../Nav';
import { LineItemType } from '../../../../enums/shared';
import IconWhiteBgCheck from '../../../../icons/IconWhiteBgCheck';
import { Navigation, Screens } from '../../../../utils/Enums';
import { getFormattedPrice } from '../../../../utils/NumberFormatUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { useAndroidBackHandler } from '../../../../utils/useAndroidBackHandler';
import { AppTestIDs } from '../../../AppTestIDs';
import { generalUniformStyles } from '../../../Styles';

export function OrderComplete(): ReactElement {
  const generalStyles = useUniformStyles(generalUniformStyles);
  const styles = useUniformStyles(styleSheet);
  const nav = AppNav.sell.useNavigation();
  const rootNav = AppNav.root.useNavigation();
  const {
    params: { lineItemType, ticketCount, totalAmount, ticketGroupReference, usedCard },
  } = AppNav.root.useRoute(Navigation.OrderComplete);

  const i18nStrings = useI18n(
    {
      header: 'pos.order-complete.header',
      doneSelling: 'pos.order-complete.done-selling',
      sellMoreTickets: 'pos.order-complete.sell-more-tickets',
      sellMoreItems: 'pos.order-complete.sell-more-items',
      customItem: 'pos.order-complete.custom-item',
      ticketsSoldSingle: [
        'pos.order-complete.tickets-sold.one',
        { ticketCount: ticketCount ?? '' },
      ],
      ticketsSoldPlural: [
        'pos.order-complete.tickets-sold.other',
        { ticketCount: ticketCount ?? '' },
      ],
      orderNumber: 'pos.order-complete.order-number',
      paymentMethod: 'pos.order-complete.payment-method',
      totalAmount: 'pos.order-complete.total-amount',
      cash: 'pos.order-complete.cash',
      card: 'pos.order-complete.card',
    },
    []
  );

  const sellMoreText = useMemo(
    () =>
      lineItemType === LineItemType.Custom
        ? i18nStrings.sellMoreItems
        : i18nStrings.sellMoreTickets,
    [i18nStrings.sellMoreItems, i18nStrings.sellMoreTickets, lineItemType]
  );

  const subHeaderText = useMemo(() => {
    if (lineItemType === LineItemType.Custom) {
      return i18nStrings.customItem;
    } else {
      if (ticketCount === 1) {
        return i18nStrings.ticketsSoldSingle;
      }
      return i18nStrings.ticketsSoldPlural;
    }
  }, [
    i18nStrings.customItem,
    i18nStrings.ticketsSoldPlural,
    i18nStrings.ticketsSoldSingle,
    lineItemType,
    ticketCount,
  ]);

  const paymentMethod = useMemo(
    () => (usedCard ? i18nStrings.card : i18nStrings.cash),
    [i18nStrings.card, i18nStrings.cash, usedCard]
  );

  useAndroidBackHandler();

  const navigateToNewOrder = useCallback(() => {
    checkoutItemSelections([]);

    if (lineItemType === LineItemType.Custom) {
      nav.navigate(Navigation.CustomSalesSelection);
    } else {
      nav.navigate(Navigation.ItemSelection);
    }
  }, [lineItemType, nav]);

  const navigateHome = useCallback(() => {
    checkoutItemSelections([]);
    nav.reset({
      index: 0,
      routes: [{ name: Screens.Sell }],
    });
    setTimeout(() => rootNav.navigate(Screens.Home), 0);
  }, [nav, rootNav]);

  return (
    <SafeAreaView
      style={[generalStyles.container, styles.container]}
      testID={AppTestIDs.orderCompleteRoot}>
      <View style={styles.headerContainer}>
        <Text style={styles.header} testID={AppTestIDs.orderCompleteHeader}>
          {i18nStrings.header}
        </Text>
        <IconWhiteBgCheck height={'40'} width={'40'} />
        <Text style={styles.subHeader} testID={AppTestIDs.orderCompleteSubHeader}>
          {subHeaderText}
        </Text>
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.contentRow}>
          <Text style={styles.contentText}>{i18nStrings.orderNumber}</Text>
          <Text style={styles.contentText}>{ticketGroupReference}</Text>
        </View>
        <View style={styles.contentRow}>
          <Text style={styles.contentText}>{i18nStrings.paymentMethod}</Text>
          <Text style={styles.contentText}>{paymentMethod}</Text>
        </View>
        <View style={styles.contentRow}>
          <Text style={styles.contentText}>{i18nStrings.totalAmount}</Text>
          <Text style={styles.contentText}>{getFormattedPrice(totalAmount)}</Text>
        </View>
      </View>
      <View style={styles.footerWrapper}>
        <View style={styles.footerContainer}>
          <View style={styles.buttonsContainer}>
            <Button
              onPress={navigateHome}
              text={i18nStrings.doneSelling}
              testID={AppTestIDs.orderCompleteNavigateHome}
              style={styles.button}
            />
            <Button
              onPress={navigateToNewOrder}
              text={sellMoreText}
              testID={AppTestIDs.orderCompleteNewOrderButton}
              style={styles.button}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
