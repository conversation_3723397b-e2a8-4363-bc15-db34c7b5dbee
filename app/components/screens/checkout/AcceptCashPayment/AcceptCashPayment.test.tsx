import React from 'react';

import { waitFor } from '@testing-library/react-native';

import { AcceptCashPayment } from './AcceptCashPayment';
import useCashCheckoutWithTicketingLineItems from '../../../../gql/hooks/useCashCheckoutWithTicketingLineItems';
import {
  mockEventInfo,
  mockLineItemSelection,
  mockOrganization,
  mockVolunteerInfo,
} from '../../../../gql/mockData/TestData';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { LineItemSelection } from '../../../../types/shared';
import { Navigation } from '../../../../utils/Enums';
import { setupAccessContextMocks } from '../../../../utils/TestUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';

const mockParams = {
  volunteerInfo: mockVolunteerInfo,
  event: mockEventInfo,
  organization: mockOrganization,
  totalPrice: 1000,
  purchaserEmail: '',
};

const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    addListener: jest.fn(),
    canGoBack: () => true,
    goBack: mockGoBack,
  }),
  useRoute: () => ({
    params: mockParams,
  }),
}));

jest.mock('../../../../gql/hooks/useCashCheckoutWithTicketingLineItems');

const mockUseCashCheckoutWithTicketingLineItems =
  useCashCheckoutWithTicketingLineItems as jest.MockedFunction<
    typeof useCashCheckoutWithTicketingLineItems
  >;

const mockCheckoutGqlResult = {
  cashCheckoutWithTicketingLineItems: jest.fn().mockResolvedValue({
    data: {
      cashCheckoutWithTicketingLineItems: {
        ticketGroup: { ticketGroupReference: 'TG-123' },
        pricingSummary: { totalInCents: 1000 },
      },
    },
  }),
};

jest.mock('../../../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../../../components/queries/useAccessCodeHook');

setupAccessContextMocks();

describe('AcceptCashPayment Tests', () => {
  it('Payment is successfully completed', async () => {
    const lineItemSelections: LineItemSelection[] = [
      {
        ...mockLineItemSelection,
        lineItemId: 'lineItemId1',
        quantitySelected: 1,
        unitPriceInCents: 100,
        name: 'Ticket Type 1',
        hudlFeeInCents: 100,
      },
    ];
    checkoutItemSelections(lineItemSelections);

    mockUseCashCheckoutWithTicketingLineItems.mockImplementation(() => ({
      ...mockCheckoutGqlResult,
    }));

    renderWithOptions(<AcceptCashPayment />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
      withAccessContext: true,
    });

    await waitFor(() =>
      expect(mockNavigate).toHaveBeenCalledWith(Navigation.OrderComplete, {
        lineItemType: 'TicketType',
        ticketGroupReference: 'TG-123',
        ticketCount: 1,
        totalAmount: 1000,
        usedCard: false,
      })
    );
  });
});
