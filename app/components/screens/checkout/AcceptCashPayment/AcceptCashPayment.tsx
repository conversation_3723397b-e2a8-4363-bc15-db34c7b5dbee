import React, { ReactElement, useCallback, useEffect, useState } from 'react';
import { SafeAreaView, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';

import { useI18n } from '@hudl/jarvis/i18n';
import { Spinner } from '@hudl/rn-uniform';

import { styles } from './AcceptCashPayment.style';
import { AppNav } from '../../../../Nav';
import { useAccessContext } from '../../../../context/AccessContext';
import useCashCheckoutWithTicketingLineItems from '../../../../gql/hooks/useCashCheckoutWithTicketingLineItems';
import { Navigation } from '../../../../utils/Enums';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';
import PaymentError from '../AcceptPayment/Errors/PaymentError';

export function AcceptCashPayment(): ReactElement {
  const nav = AppNav.root.useNavigation();
  const {
    params: { totalPrice, purchaserEmail },
  } = AppNav.root.useRoute(Navigation.AcceptCashPayment);
  const { eventInfo: event } = useAccessContext();

  const i18nStrings = useI18n(
    {
      tryAgain: 'accept-payments.try-again',
      cashPaymentErrorHeader: 'accept-payments.cash-payment-error-header',
      cashPaymentErrorHelpText: 'accept-payments.cash-payment-error-help-text',
    },
    []
  );

  const [hasErrored, setHasErrored] = useState(false);

  const lineItemSelections = useReactiveVar(checkoutItemSelections);

  const { cashCheckoutWithTicketingLineItems } = useCashCheckoutWithTicketingLineItems(
    lineItemSelections,
    totalPrice,
    event?.id ?? '',
    purchaserEmail
  );

  const goBack = useCallback(() => {
    if (nav.canGoBack()) {
      nav.goBack();
    }
  }, [nav]);

  const navigateToOrderComplete = useCallback(
    (ticketGroupReference?: string) => {
      const ticketCount = lineItemSelections.reduce((sum, li) => sum + li.quantitySelected, 0);

      nav.navigate(Navigation.OrderComplete, {
        lineItemType: lineItemSelections.map((li) => li.lineItemType)[0],
        ticketGroupReference: ticketGroupReference,
        ticketCount: ticketCount,
        totalAmount: totalPrice,
        usedCard: false,
      });
    },
    [lineItemSelections, nav, totalPrice]
  );

  const handleOrder = useCallback(async () => {
    setHasErrored(false);
    const checkoutRes = await cashCheckoutWithTicketingLineItems();
    if (checkoutRes.errors) {
      setHasErrored(true);
      return;
    }
    setHasErrored(false);
    navigateToOrderComplete(
      checkoutRes.data?.cashCheckoutWithTicketingLineItems?.ticketGroup?.ticketGroupReference ?? ''
    );
  }, [cashCheckoutWithTicketingLineItems, navigateToOrderComplete]);

  useEffect(() => {
    handleOrder();
    // We only want to run this effect once with the initial values
    // so the dependency array is intentionally left empty.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (hasErrored) {
    return (
      <PaymentError
        retryPayment={handleOrder}
        goBack={goBack}
        strings={{
          tryAgain: i18nStrings.tryAgain,
          paymentErrorHeader: i18nStrings.cashPaymentErrorHeader,
          paymentErrorHelpText: i18nStrings.cashPaymentErrorHelpText,
        }}
      />
    );
  }

  return (
    <SafeAreaView>
      <View style={styles.container} testID={AppTestIDs.acceptCashPaymentsRoot}>
        <Spinner />
      </View>
    </SafeAreaView>
  );
}
