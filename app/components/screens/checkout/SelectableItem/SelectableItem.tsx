import React, { ReactElement } from 'react';
import { Text, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './SelectableItem.style';
import { LineItemSelection } from '../../../../types/shared';
import { ticketLowQuantityWarningThreshold } from '../../../../utils/Constants';
import { getFormattedPrice } from '../../../../utils/NumberFormatUtils';
import { AppTestIDs } from '../../../AppTestIDs';
import Counter from '../../../Counter/Counter';

export interface SelectableItemProps {
  item: LineItemSelection;
  count: number;
  remainingOrderLimit: number;
  onCountChange: (newCount: number) => void;
}

export function SelectableItem(props: SelectableItemProps): ReactElement {
  const { item, count, remainingOrderLimit, onCountChange } = props;
  const styles = useUniformStyles(styleSheet);

  const i18nStrings = useI18n(
    {
      free: 'pos.item-selection.free',
      soldOut: 'pos.item-selection.sold-out',
      ticketsRemaining: ['selectable-item.tickets-remaining', { count: item.quantityRemaining }],
    },
    [item]
  );

  const isSoldOut = item.quantityRemaining !== undefined && item.quantityRemaining <= 0;

  const remainingSelectableItemCount =
    item.quantityRemaining !== undefined
      ? Math.min(remainingOrderLimit, item.quantityRemaining - count)
      : remainingOrderLimit;

  const showLimitedQuantity =
    item.quantityRemaining !== undefined &&
    item.quantityRemaining <= ticketLowQuantityWarningThreshold;

  const limitedQuantityText = isSoldOut ? i18nStrings.soldOut : i18nStrings.ticketsRemaining;

  return (
    <View style={styles.selectableItemContainer} testID={AppTestIDs.selectableItemRoot}>
      <View style={styles.infoContainer}>
        <View style={styles.left}>
          <Text
            style={isSoldOut ? [styles.title, styles.titleDisabled] : styles.title}
            testID={AppTestIDs.selectableItemName}>
            {item.name}
          </Text>
          <View style={styles.priceContainer}>
            <Text
              style={isSoldOut ? [styles.price, styles.priceDisabled] : styles.price}
              testID={AppTestIDs.selectableItemPrice}>
              {`${item.unitPriceInCents === 0 ? `${i18nStrings.free} - ` : ''}${getFormattedPrice(
                item.unitPriceInCents
              )}`}
            </Text>
          </View>
          {showLimitedQuantity && (
            <View
              style={styles.limitedQuantityContainer}
              testID={
                isSoldOut
                  ? AppTestIDs.selectableItemSoldOut
                  : AppTestIDs.selectableItemLimitedQuantity
              }>
              <View style={styles.limitedQuantityContent}>
                <Text
                  style={
                    isSoldOut
                      ? [styles.limitedQuantityText, styles.soldOutText]
                      : styles.limitedQuantityText
                  }>
                  {limitedQuantityText}
                </Text>
              </View>
            </View>
          )}
        </View>
        <View style={styles.right}>
          <View style={styles.counterContainer}>
            <Counter
              onCountChange={onCountChange}
              itemId={item.lineItemId ?? ''}
              count={count}
              remaining={remainingSelectableItemCount}
              isDisabled={isSoldOut}
            />
          </View>
        </View>
      </View>
    </View>
  );
}
