import React from 'react';

import { screen } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import { SelectableItem } from './SelectableItem';
import { LineItemType } from '../../../../enums/shared';
import { mockLineItemSelection } from '../../../../gql/mockData/TestData';
import enUS from '../../../../strings/en-US.json';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { LineItemSelection } from '../../../../types/shared';
import { AppTestIDs } from '../../../AppTestIDs';
import { CounterProps } from '../../../Counter/Counter';

jest.mock('../../../Counter/Counter', () => {
  return {
    __esModule: true,
    default: (props: CounterProps) => {
      return `Counter: itemId:${props.itemId} count:${props.count} remaining:${props.remaining} isDisabled:${props.isDisabled}`;
    },
  };
});

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

describe('SelectableItem', () => {
  it('SelectableItem displays correctly', async () => {
    const item: LineItemSelection = {
      ...mockLineItemSelection,
      lineItemId: '1',
      lineItemType: LineItemType.TicketType,
      name: 'Test Item',
      unitPriceInCents: 1000,
      quantityRemaining: 100,
    };
    renderWithOptions(
      <SelectableItem item={item} count={0} remainingOrderLimit={10} onCountChange={jest.fn()} />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.selectableItemRoot}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.selectableItemName}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.selectableItemPrice}`)).toBeTruthy();
    expect(screen.queryByTestId(`${AppTestIDs.selectableItemLimitedQuantity}`)).toBeFalsy();
  });

  it('SelectableItem displays limited quantity', async () => {
    const item: LineItemSelection = {
      ...mockLineItemSelection,
      lineItemId: '1',
      lineItemType: LineItemType.TicketType,
      name: 'Test Item',
      unitPriceInCents: 1000,
      quantityRemaining: 5,
    };
    renderWithOptions(
      <SelectableItem item={item} count={0} remainingOrderLimit={10} onCountChange={jest.fn()} />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.selectableItemRoot}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.selectableItemName}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.selectableItemPrice}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.selectableItemLimitedQuantity}`)).toBeTruthy();
  });

  it('Sold out item displays correctly', async () => {
    const soldOutItem: LineItemSelection = {
      ...mockLineItemSelection,
      lineItemId: '1',
      lineItemType: LineItemType.TicketType,
      name: 'Test Item',
      unitPriceInCents: 1000,
      quantityRemaining: 0,
    };
    renderWithOptions(
      <SelectableItem
        item={soldOutItem}
        count={0}
        remainingOrderLimit={5}
        onCountChange={jest.fn()}
      />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(AppTestIDs.selectableItemSoldOut)).toBeDefined();
  });
});
