import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  selectableItemContainer: {
    borderRadius: 4,
    backgroundColor: nonUniformColors.containerBG,
    paddingVertical: UniformSpace.threeQuarter,
    paddingHorizontal: UniformSpace.one,
  },
  infoContainer: {
    flexDirection: 'row',
    width: '100%',
    flex: 1,
    flexGrow: 1,
  },
  left: {
    flex: 1,
    gap: UniformSpace.half,
  },
  right: {
    flex: 1,
    marginRight: 'auto',
  },
  title: {
    color: colors.baseWhite,
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 21,
  },
  titleDisabled: {
    color: colors.contentNonessential,
  },
  price: {
    color: colors.baseWhite,
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 21,
  },
  priceDisabled: {
    color: colors.contentNonessential,
  },
  limitedQuantityContainer: {
    display: 'flex',
    alignItems: 'flex-start',
  },
  limitedQuantityContent: {
    backgroundColor: colors.taggingBlack,
    borderRadius: 4,
    paddingTop: UniformSpace.quarter,
    paddingBottom: UniformSpace.quarter,
    paddingLeft: UniformSpace.half,
    paddingRight: UniformSpace.half,
    marginBottom: UniformSpace.quarter,
  },
  limitedQuantityText: {
    color: colors.baseWhite,
    fontSize: 12,
    fontWeight: '700',
    lineHeight: 18,
  },
  soldOutText: {
    color: colors.contentNonessential,
  },
  counterContainer: {
    display: 'flex',
    alignItems: 'flex-end',
  },
  serviceFeeText: {
    color: colors.contentSubtle,
  },
  priceContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.quarter,
  },
}));
