import React from 'react';
import { Linking } from 'react-native';

import { ApolloError } from '@apollo/client';
import { Reader } from '@stripe/stripe-terminal-react-native';
import { fireEvent, screen } from '@testing-library/react-native';

import { ItemSelection } from './ItemSelection';
import useLazyGetTicketingPricingSummary from '../../../../gql/hooks/useLazyGetTicketingPricingSummary';
import useTicketedEventByIdWithAnalytics from '../../../../gql/hooks/useTicketedEventByIdWithAnalytics';
import {
  mockEventInfo,
  mockLineItemSelection,
  mockOrganization,
  mockTicketedEvent,
} from '../../../../gql/mockData/TestData';
import { EventInfo } from '../../../../models/EventInfo';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { setupAccessContextMocks } from '../../../../utils/TestUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';

const mockEvent: EventInfo = {
  ...mockEventInfo,
  ticketTypePricingSummaries: [
    {
      ...mockEventInfo.ticketTypePricingSummaries[0],
      shouldShowFee: false,
    },
  ],
};
const mockConnectedReader: Reader.Type | null = null;

jest.mock('@stripe/stripe-terminal-react-native', () => ({
  useStripeTerminal: jest.fn().mockImplementation(() => ({
    connectedReader: mockConnectedReader,
  })),
  CommonError: {
    Canceled: 'canceled',
  },
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    addListener: jest.fn(),
  }),
}));

jest.mock('../../../../components/queries/useAccessCodeHook');
setupAccessContextMocks();

const mockParams = {
  event: mockEvent,
  organization: mockOrganization,
};
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    setOptions: jest.fn(),
    addListener: jest.fn(),
  }),
  useRoute: () => ({
    params: mockParams,
  }),
  useFocusEffect: jest.fn(),
}));

jest.mock('@react-navigation/elements', () => ({
  useHeaderHeight: jest.fn(() => 100),
}));

const openSupportLinkSpy = jest.spyOn(Linking, 'openURL');

const mockTicketedEventGqlResultDefault = {
  ticketedEvent: mockTicketedEvent,
  ticketedEventLoading: false,
  ticketedEventError: undefined,
  ticketedEventNetworkStatus: 7,
  refetchTicketedEvent: jest.fn(),
};

jest.mock('../SelectableItemList/SelectableItemList', () => ({
  SelectableItemList: () => 'ItemList',
}));

jest.mock('../../../../gql/hooks/useTicketedEventByIdWithAnalytics');

const mockUseTicketedEventByIdWithAnalytics =
  useTicketedEventByIdWithAnalytics as jest.MockedFunction<
    typeof useTicketedEventByIdWithAnalytics
  >;

const executePricingSummary = jest.fn().mockResolvedValue({
  data: {
    ticketingPricingSummary: {
      currency: 'USD',
      subtotalInCents: 100,
      feesInCents: 10,
      totalInCents: 110,
      shouldShowFee: false,
    },
  },
});
jest.mock('../../../../gql/hooks/useLazyGetTicketingPricingSummary');

const mockUseTicketingPricingSummary = useLazyGetTicketingPricingSummary as jest.MockedFunction<
  typeof useLazyGetTicketingPricingSummary
>;

describe('ItemSelection render tests', () => {
  it('Screen renders correctly', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(
      () => mockTicketedEventGqlResultDefault
    );

    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.getByTestId(AppTestIDs.posItemSelectionRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.posItemSelectionHeader)).toBeTruthy();
  });

  it('Review button enabled if selections', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(
      () => mockTicketedEventGqlResultDefault
    );

    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    checkoutItemSelections([
      { ...mockLineItemSelection, quantitySelected: 5, lineItemId: 'testTicketedEventId' },
    ]);

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.getByTestId(AppTestIDs.posCardReviewOrderButton(false))).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.posCashReviewOrderButton(false))).toBeTruthy();
  });

  it('Review button disabled if no selections', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(
      () => mockTicketedEventGqlResultDefault
    );
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    checkoutItemSelections([]);

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.getByTestId(AppTestIDs.posCardReviewOrderButton(true))).toBeTruthy();
  });

  it('Shows loading spinner on first load', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(() => ({
      ...mockTicketedEventGqlResultDefault,
      ticketedEventLoading: true,
    }));
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.getByTestId(AppTestIDs.posItemSelectionLoading)).toBeTruthy();
  });

  it('Shows error state on error', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(() => ({
      ...mockTicketedEventGqlResultDefault,
      ticketedEventError: {} as ApolloError,
    }));

    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.getByTestId(AppTestIDs.posItemSelectionError)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.posItemSelectionErrorContactSupportLink)).toBeTruthy();
  });

  it('Navigates to support url when support link on error page clicked', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(() => ({
      ...mockTicketedEventGqlResultDefault,
      ticketedEventError: {} as ApolloError,
    }));
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.posItemSelectionErrorContactSupportLink));

    expect(openSupportLinkSpy).toHaveBeenCalled();
  });

  it('Retries gql call when error retry button hit', async () => {
    const refetchFunc = jest.fn();
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(() => ({
      ...mockTicketedEventGqlResultDefault,
      ticketedEventError: {} as ApolloError,
      refetchTicketedEvent: refetchFunc,
    }));
    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));
    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    fireEvent.press(screen.getByTestId(AppTestIDs.genericErrorCTAButton));

    expect(refetchFunc).toHaveBeenCalled();
  });

  it('Shows ticket limit note when limit hit', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(
      () => mockTicketedEventGqlResultDefault
    );

    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    checkoutItemSelections([
      { ...mockLineItemSelection, quantitySelected: 99, lineItemId: 'testTicketedEventId' },
    ]);

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.getByTestId(AppTestIDs.posItemSelectionTicketLimitNote)).toBeTruthy();
  });

  it('Does not show ticket limit note when limit not hit', async () => {
    mockUseTicketedEventByIdWithAnalytics.mockImplementation(
      () => mockTicketedEventGqlResultDefault
    );

    mockUseTicketingPricingSummary.mockImplementation(() => ({
      executePricingSummaryQuery: executePricingSummary,
    }));

    checkoutItemSelections([
      { ...mockLineItemSelection, quantitySelected: 5, lineItemId: 'testTicketedEventId' },
    ]);

    renderWithOptions(<ItemSelection />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(screen.queryByTestId(AppTestIDs.posItemSelectionTicketLimitNote)).toBeFalsy();
  });
});
