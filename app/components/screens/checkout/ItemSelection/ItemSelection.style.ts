import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    height: '100%',
    width: '100%',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorContainer: {
    padding: UniformSpace.oneAndHalf,
  },
  headerContainer: {
    marginBottom: UniformSpace.oneAndQuarter,
    padding: UniformSpace.one,
    marginHorizontal: UniformSpace.oneAndHalf,
    backgroundColor: nonUniformColors.contentEmphasesBg,
    borderRadius: 8,
  },
  header: {
    fontSize: 14,
    fontWeight: '700',
    color: nonUniformColors.contentStrongBG,
  },
  footerWrapper: {
    backgroundColor: colors.bgLevel0,
  },
  footerContainer: {
    paddingLeft: UniformSpace.oneAndHalf,
    paddingRight: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.quarter,
  },
  contentContainer: {
    flex: 1,
    paddingLeft: UniformSpace.oneAndHalf,
    paddingRight: UniformSpace.oneAndHalf,
  },
  orderTotalContainer: {
    paddingTop: UniformSpace.one,
    paddingBottom: UniformSpace.oneAndQuarter,
  },
  ticketLimitNoteContainer: {
    backgroundColor: colors.bgLevel2,
    borderRadius: 8,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: UniformSpace.one,
    paddingTop: UniformSpace.one,
    paddingBottom: UniformSpace.one,
    paddingLeft: UniformSpace.oneAndHalf,
    paddingRight: UniformSpace.oneAndHalf,
  },
  ticketLimitNoteIcon: {
    paddingRight: UniformSpace.one,
  },
  ticketLimitNoteText: {
    color: colors.contentDefault,
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 21,
    paddingLeft: UniformSpace.half,
  },
  reviewOrderButtonContainer: {
    paddingTop: UniformSpace.half,
    flexDirection: 'row',
    gap: UniformSpace.half,
  },
  errorMessage: {
    color: colors.contentContrast,
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    paddingBottom: UniformSpace.four,
  },
  linkText: {
    color: colors.linkArticleText,
    fontSize: 16,
    lineHeight: 24,
  },
  button: {
    flex: 1,
    backgroundColor: nonUniformColors.contentEmphasesBgContrast,
  },
}));
