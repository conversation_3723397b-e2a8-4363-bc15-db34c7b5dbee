import React, {
  ReactElement,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Keyboard, KeyboardAvoidingView, Platform, SafeAreaView, Text, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';
import { useHeaderHeight } from '@react-navigation/elements';
import { useFocusEffect } from '@react-navigation/native';
import { useStripeTerminal } from '@stripe/stripe-terminal-react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, IconWarning, Spinner, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './ItemSelection.style';
import { AppNav } from '../../../../Nav';
import { useAccessContext } from '../../../../context/AccessContext';
import { LineItemType } from '../../../../enums/shared';
import useTicketedEventByIdWithAnalytics from '../../../../gql/hooks/useTicketedEventByIdWithAnalytics';
import { TicketType } from '../../../../gql/public/__generated__/graphql';
import IconSignalDisconnected from '../../../../icons/IconSignalDisconnected';
import { LineItemSelection } from '../../../../types/shared';
import { maximumTicketLimit } from '../../../../utils/Constants';
import { Navigation } from '../../../../utils/Enums';
import {
  convertTicketTypesToLineItemConfigs,
  sumLineItemPrices,
  sumLineItemQuantities,
} from '../../../../utils/LineItemUtils';
import { openSupportLink } from '../../../../utils/UrlUtils';
import { checkoutItemSelections } from '../../../../utils/stateVars';
import { AppTestIDs } from '../../../AppTestIDs';
import OrderTotal from '../../../OrderTotal/OrderTotal';
import { generalUniformStyles } from '../../../Styles';
import { useShowDiscoverReaders } from '../../pointOfSale/DiscoverReaders/DiscoverReadersModal';
import { GenericError } from '../../pointOfSale/GenericError/GenericError';
import { SelectableItemList } from '../SelectableItemList/SelectableItemList';

export function ItemSelection(): ReactElement {
  const nav = AppNav.root.useNavigation();
  const hasLoadedRef = useRef(false);

  const styles = useUniformStyles(styleSheet);
  const generalStyles = useUniformStyles(generalUniformStyles);

  const { showDiscoverReaders } = useShowDiscoverReaders();
  const { connectedReader } = useStripeTerminal();

  const { eventInfo: event } = useAccessContext();

  const i18nStrings = useI18n(
    {
      header: 'pos.item-selection.header',
      cashButton: 'pos.item-selection.cash-button',
      cardButton: 'pos.item-selection.card-button',
      errorHeader: 'pos.item-selection.error.header',
      errorMessage: 'pos.item-selection.error.message',
      errorTryAgain: 'pos.item-selection.error.try-again',
      ticketLimitNote: ['pos.item-selection.ticket-limit-note', { limit: maximumTicketLimit }],
      nonRefundable: 'pos.item-selection.non-refundable',
      errorPricingSummaryHeader: 'pos.item-selection.pricing-summary-error.header',
      errorPricingSummaryMessage: 'pos.item-selection.pricing-summary-error.message',
      errorPricingSummaryTryAgain: 'pos.item-selection.pricing-summary-error.try-again',
    },
    []
  );

  const currentItemSelections = useReactiveVar(checkoutItemSelections);
  const [pullToRefreshing, setPullToRefreshing] = useState(false);

  const headerHeight = useHeaderHeight();

  const { ticketedEvent, ticketedEventLoading, ticketedEventError, refetchTicketedEvent } =
    useTicketedEventByIdWithAnalytics(event?.id, {
      fetchPolicy: 'network-only',
      notifyOnNetworkStatusChange: true,
      onCompleted: () => {
        hasLoadedRef.current = true;
        setPullToRefreshing(false);
        const ticketTypes: TicketType[] = (ticketedEvent?.ticketTypes ?? []) as TicketType[];
        const lineItems: LineItemSelection[] =
          convertTicketTypesToLineItemConfigs(ticketTypes, ticketedEvent)?.sort(
            (a, b) => b?.unitPriceInCents - a?.unitPriceInCents
          ) ?? [];
        const lineItemQuantityRemaining: { [id: string]: number } = lineItems.reduce(
          (acc, li) => ({ ...acc, [String(li.lineItemId)]: li.quantityRemaining }),
          {}
        );

        const updatedSelections = lineItems.map((lineItem) => {
          const existingLineItem = currentItemSelections.find(
            (li) => li.lineItemId === lineItem.lineItemId
          );

          if (
            existingLineItem?.lineItemId &&
            (!lineItemQuantityRemaining[existingLineItem.lineItemId] ||
              existingLineItem.quantitySelected <=
                lineItemQuantityRemaining[existingLineItem.lineItemId])
          ) {
            return {
              ...lineItem,
              quantitySelected: existingLineItem.quantitySelected,
            };
          }
          return lineItem;
        });
        checkoutItemSelections(updatedSelections);
      },
    });

  const totalTicketQuantity = sumLineItemQuantities(currentItemSelections);
  const totalTicketsPrice = sumLineItemPrices(currentItemSelections);
  const ticketQuantityLimitHit = totalTicketQuantity >= maximumTicketLimit;
  const reviewButtonDisabled = totalTicketQuantity <= 0;

  useFocusEffect(
    useCallback(() => {
      refetchTicketedEvent();
    }, [refetchTicketedEvent])
  );

  useEffect(() => {
    const unsub = nav.addListener('beforeRemove', (e) => {
      const type = e?.data?.action?.type;
      if (type === 'GO_BACK' || type === 'POP_TO_TOP') {
        checkoutItemSelections([]);
      }
    });
    return unsub;
  }, [nav]);

  useEffect(() => {
    if (ticketQuantityLimitHit) {
      Keyboard.dismiss();
    }
  }, [ticketQuantityLimitHit]);

  const onCardReviewOrderPress = useCallback((): void => {
    if (connectedReader) {
      nav.navigate(Navigation.ReviewOrder, { isCustomSale: false });
    } else {
      showDiscoverReaders();
    }
  }, [connectedReader, nav, showDiscoverReaders]);

  const onCashReviewOrderPress = useCallback((): void => {
    nav.navigate(Navigation.ReviewOrder, {
      isCustomSale: false,
      isCashSale: true,
    });
  }, [nav]);

  const onErrorTryAgain = useCallback((): void => {
    refetchTicketedEvent();
  }, [refetchTicketedEvent]);

  const onPullToRefresh = useCallback((): void => {
    refetchTicketedEvent();
    setPullToRefreshing(true);
  }, [refetchTicketedEvent]);

  const renderTicketLimitNote = (): ReactElement => {
    return (
      <View
        style={styles.ticketLimitNoteContainer}
        testID={AppTestIDs.posItemSelectionTicketLimitNote}>
        <IconWarning style={styles.ticketLimitNoteIcon} size="large" color="warning" />
        <Text style={styles.ticketLimitNoteText}>{i18nStrings.ticketLimitNote}</Text>
      </View>
    );
  };

  const goBack = useCallback(() => {
    if (nav.canGoBack()) {
      nav.goBack();
    }
  }, [nav]);

  useLayoutEffect(() => {
    nav.setOptions({
      //CO: I would love to use the 'headerShown' option here but there appears to be a bug in the package with that
      headerStyle: { display: ticketedEventError ? 'none' : 'flex' },
    });
  }, [nav, ticketedEventError]);

  const isInitialLoad = useMemo(() => {
    return !hasLoadedRef.current && ticketedEventLoading;
  }, [ticketedEventLoading]);

  if (isInitialLoad && !pullToRefreshing) {
    return (
      <View
        style={[generalStyles.container, styles.container, styles.loadingContainer]}
        testID={AppTestIDs.posItemSelectionLoading}>
        <Spinner testID="item-selection-spinner" />
      </View>
    );
  }

  if (ticketedEventError) {
    return (
      <SafeAreaView>
        <View style={styles.container} testID={AppTestIDs.posItemSelectionError}>
          <GenericError
            header={i18nStrings.errorHeader}
            subHeaderContent={
              <Text style={styles.errorMessage}>
                {i18nStrings.errorMessage[0]}
                <Text
                  style={styles.linkText}
                  onPress={openSupportLink}
                  testID={AppTestIDs.posItemSelectionErrorContactSupportLink}>
                  {i18nStrings.errorMessage[1]}
                </Text>
                {i18nStrings.errorMessage[2]}
              </Text>
            }
            onPress={onErrorTryAgain}
            callToActionText={i18nStrings.errorTryAgain}
            icon={<IconSignalDisconnected height={128} width={128} />}
            xPress={goBack}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View
      style={[generalStyles.container, styles.container]}
      testID={AppTestIDs.posItemSelectionRoot}>
      <View style={styles.headerContainer}>
        <Text style={styles.header} testID={AppTestIDs.posItemSelectionHeader}>
          {i18nStrings.header}
        </Text>
      </View>
      <KeyboardAvoidingView
        style={styles.contentContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? headerHeight : headerHeight + 55}>
        <SelectableItemList
          items={currentItemSelections}
          totalItemSelectionLimit={maximumTicketLimit}
          onRefresh={onPullToRefresh}
          refreshing={ticketedEventLoading}
        />
      </KeyboardAvoidingView>
      <View style={styles.footerWrapper}>
        <View style={styles.footerContainer}>
          <View style={styles.orderTotalContainer}>
            <OrderTotal
              totalItemCount={totalTicketQuantity}
              lineItemType={LineItemType.TicketType}
              subtotalPrice={totalTicketsPrice}
              pricingSummaryLoading={false}
              showTotalPrice={false}
            />
          </View>
          {ticketQuantityLimitHit && renderTicketLimitNote()}
          <View style={styles.reviewOrderButtonContainer}>
            <Button
              onPress={onCashReviewOrderPress}
              text={i18nStrings.cashButton}
              isDisabled={reviewButtonDisabled}
              testID={AppTestIDs.posCashReviewOrderButton(reviewButtonDisabled)}
              style={styles.button}
            />
            <Button
              onPress={onCardReviewOrderPress}
              text={i18nStrings.cardButton}
              isDisabled={reviewButtonDisabled}
              testID={AppTestIDs.posCardReviewOrderButton(reviewButtonDisabled)}
              style={styles.button}
            />
          </View>
        </View>
      </View>
    </View>
  );
}
