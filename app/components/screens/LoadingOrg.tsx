import React, { useEffect } from 'react';
import { ImageBackground, StyleSheet, View } from 'react-native';

import { StackActions } from '@react-navigation/native';

import { <PERSON><PERSON>, Spinner, Text, UniformSpace } from '@hudl/rn-uniform';

import { AppNav } from '../../Nav';
import { useAccessContext } from '../../context/AccessContext';
import { Navigation } from '../../utils/Enums';
import { AppTestIDs } from '../AppTestIDs';

export const styles = StyleSheet.create({
  imageContainer: {
    height: '100%',
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  container: {
    width: '100%',
    justifyContent: 'center',
    flexDirection: 'row',
  },
});

export function LoadingOrg(): React.ReactElement {
  const nav = AppNav.root.useNavigation();
  const accessContext = useAccessContext();

  useEffect(() => {
    if (
      accessContext?.organization &&
      !accessContext.organizationError &&
      !accessContext.organizationLoading
    ) {
      nav.dispatch({
        ...StackActions.replace(Navigation.Root, {}),
      });
    }
  }, [nav, accessContext]);

  return (
    <View testID={AppTestIDs.loadingOrgRoot}>
      <ImageBackground
        style={styles.imageContainer}
        source={require('../../assets/images/LandingBackground.png')}
        resizeMethod={'scale'}
        resizeMode={'stretch'}>
        <View style={styles.container}>
          <States
            hasError={accessContext.organizationError}
            accessCode={accessContext?.volunteerInfo?.accessCode ?? ''}
          />
        </View>
      </ImageBackground>
    </View>
  );
}

function States({
  hasError,
  accessCode,
}: {
  hasError: boolean;
  accessCode: string;
}): React.ReactElement {
  const nav = AppNav.root.useNavigation();
  if (hasError) {
    return (
      <View style={{ marginHorizontal: UniformSpace.one }} testID={AppTestIDs.loadingOrgErrorState}>
        <Text>
          {`There was a problem loading this organization using the Access Code: '${accessCode}'`}{' '}
        </Text>
        <Button
          text={'Back'}
          onPress={nav.goBack}
          style={{ marginTop: UniformSpace.one }}
          testID={AppTestIDs.loadingOrgBackButton}
        />
      </View>
    );
  }

  return (
    <View>
      <Spinner testID={AppTestIDs.loadingOrgLoadingState} />
    </View>
  );
}
