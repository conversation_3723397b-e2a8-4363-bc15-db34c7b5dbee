import React, { useEffect, useState } from 'react';
import { AppState, StyleSheet } from 'react-native';

import { Camera, useCodeScanner } from 'react-native-vision-camera';

import { BarCodeReadEvent } from '../../../../types/shared';
import { useBackCameraDevice } from '../hooks/useBackCameraDevice';

type Props = {
  hasCameraPermission: boolean;
  onCodeScanned: (event: BarCodeReadEvent) => void;
};

export function QRCodeScanner(props: Props): React.ReactElement | null {
  const { hasCameraPermission, onCodeScanned } = props;
  const [appState, setAppState] = useState(AppState.currentState);
  const [cameraActive, setCameraActive] = useState(true);

  useEffect(() => {
    const subscription = AppState.addEventListener('change', (nextAppState) => {
      if (appState.match(/inactive|background/) && nextAppState === 'active') {
        setCameraActive(false);
        setTimeout(() => setCameraActive(true), 100);
      }
      setAppState(nextAppState);
    });

    return () => subscription.remove();
  }, [appState]);

  const backCameraDevice = useBackCameraDevice();

  const codeScanner = useCodeScanner({
    codeTypes: ['qr'],
    onCodeScanned: (codes) => {
      onCodeScanned(codes[0] as BarCodeReadEvent);
    },
  });

  if (!backCameraDevice || !hasCameraPermission || !cameraActive) {
    return null;
  }

  return (
    <Camera
      style={StyleSheet.absoluteFillObject}
      audio={false}
      device={backCameraDevice}
      isActive={true}
      torch="off"
      codeScanner={codeScanner}
    />
  );
}
