import CryptoES from 'crypto-es';

import { ScannedQRCode, Pass, Ticket, isPass, isTicket } from '../../../models/ScannedQRCode';

const seed =
  'VpXfLvMKTuXj+rBafwEAP25XbOkT6amhprTEUyFGvKTUBIzWOMmWt2VEmYcjjyAgsr3883Evm7Lg/FI6N+3pWQ==';

export function calculateHudlChecksum(scannedQRCode: ScannedQRCode): string {
  if (isPass(scannedQRCode)) {
    return calculateChecksumForPass(scannedQRCode);
  } else if (isTicket(scannedQRCode)) {
    return calculateChecksumForTicket(scannedQRCode);
  }
  return 'INVALID_HUDL_TICKET';
}

export function calculateChecksumForTicket(ticket: Ticket): string {
  const key = CryptoES.enc.Utf8.parse(seed);
  const message = CryptoES.enc.Utf8.parse(`${ticket.TicketId}-${ticket.TicketedEventId}`);

  const hashedData = CryptoES.HmacSHA256(message, key);
  return hashedData.toString(CryptoES.enc.Base64);
}

export function validateChecksumForTicket(ticket: Ticket): boolean {
  const calculatedChecksum = calculateChecksumForTicket(ticket);
  return calculatedChecksum === ticket.Checksum;
}

export function calculateChecksumForPass(pass: Pass): string {
  const key = CryptoES.enc.Utf8.parse(seed);
  const teams = (pass.Teams ?? []).sort((a, b) => (a > b ? -1 : 1)); // sort descending
  const dates = pass.StartDate && pass.EndDate ? [pass.StartDate, pass.EndDate] : [];

  // message format: `PassId-OrganizationId-teamIds-startDate-endDate`
  // 1. omit teamIds if none available.
  // 2. teamIds should be sorted in descending order
  // 3. omit dates if either is missing
  const concatMessage = [pass.PassId, pass.OrganizationId].concat(teams).concat(dates).join('-');

  const message = CryptoES.enc.Utf8.parse(concatMessage);

  const hashedData = CryptoES.HmacSHA256(message, key);
  return hashedData.toString(CryptoES.enc.Base64);
}

export function validateChecksumForPass(pass: Pass): boolean {
  const calculatedChecksum = calculateChecksumForPass(pass);
  return calculatedChecksum === pass.Checksum;
}
