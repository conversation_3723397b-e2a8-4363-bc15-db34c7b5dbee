import DeviceInfo from 'react-native-device-info';

import { ScannedQRCodeResult } from './ScannerUtils';
import {
  StoredResultGroup,
  byId,
  getResultGroup,
  saveResultGroup,
} from '../../../local_storage/ResultGroupStorage';
import { EventInfo } from '../../../models/EventInfo';
import { ScannedQRCode, isTicket, isPass } from '../../../models/ScannedQRCode';
import { Volunteer } from '../../../models/Volunteer';
import {
  QRCodeStoreStatus,
  ScannedQRCodeResultStatus,
  TicketingEntityType,
} from '../../../utils/Enums';

/**
 * Checks if the QR code has already been scanned. If it hasn't, it saves the ScannedQRCode to the group.
 */
export async function storeQRCodeResult(
  scannedQRCode: ScannedQRCode,
  currentEvent: EventInfo,
  volunteerInfo: Volunteer
): Promise<{
  storedStatus: QRCodeStoreStatus;
  result?: ScannedQRCodeResult;
}> {
  const { storedResult, resultGroup } = await checkForStoredResult(
    scannedQRCode,
    currentEvent,
    volunteerInfo
  );

  if (storedResult) {
    return { storedStatus: QRCodeStoreStatus.QRCodeAlreadyScanned, result: storedResult };
  }

  try {
    const result = await saveScannedQRCodeToGroup(
      scannedQRCode,
      currentEvent,
      volunteerInfo,
      resultGroup
    );
    return { storedStatus: QRCodeStoreStatus.Stored, result };
  } catch (error) {
    return { storedStatus: QRCodeStoreStatus.ErrorStoringResult };
  }
}

/**
 * Checks if the ScannedQRCode has already been stored.
 */
async function checkForStoredResult(
  scannedQRCode: ScannedQRCode,
  currentEvent: EventInfo,
  volunteerInfo: Volunteer
): Promise<{
  storedResult: ScannedQRCodeResult | undefined;
  resultGroup: StoredResultGroup | undefined;
}> {
  const resultGroup = await getResultGroup(currentEvent, volunteerInfo, DeviceInfo.getVersion());

  let storedResult: ScannedQRCodeResult | undefined;

  if (isTicket(scannedQRCode)) {
    storedResult = resultGroup?.results?.find((r) => r.id === scannedQRCode.TicketId);
    if (!storedResult) {
      storedResult = resultGroup?.uploadedResults?.find((r) => r.id === scannedQRCode.TicketId);
    }
  } else if (isPass(scannedQRCode)) {
    storedResult = resultGroup?.results?.find((r) => r.id === scannedQRCode.PassId);
    if (!storedResult) {
      storedResult = resultGroup?.uploadedResults?.find((r) => r.id === scannedQRCode.PassId);
    }
  } else {
    storedResult = undefined;
  }

  return { storedResult, resultGroup };
}

/**
 * Saves the scanned QR code to the group. If the group doesn't exist, it will create a new group.
 */
async function saveScannedQRCodeToGroup(
  scannedQRCode: ScannedQRCode,
  currentEvent: EventInfo,
  volunteerInfo: Volunteer,
  group?: StoredResultGroup
): Promise<ScannedQRCodeResult | undefined> {
  const result = createScannedQRCodeResult(scannedQRCode);
  if (!result) {
    return undefined;
  }

  const lastScanTime = new Date().toISOString();

  if (group) {
    group.results = [...group.results, ...[result]].sort(byId);
    group.lastScanTime = lastScanTime;
    saveResultGroup(group);
  } else {
    const newGroup: StoredResultGroup = {
      ticketedEventId: currentEvent.id,
      firstName: volunteerInfo.firstName,
      lastName: volunteerInfo.lastName,
      accessCode: volunteerInfo.accessCode,
      email: volunteerInfo.email,
      appVersion: DeviceInfo.getVersion(),
      results: [result],
      uploadedResults: [],
      lastScanTime: lastScanTime,
    };

    saveResultGroup(newGroup);
  }
  return result;
}

/**
 * Creates a ScannedQRCodeResult from a ScannedQRCode
 */
function createScannedQRCodeResult(scannedQRCode: ScannedQRCode): ScannedQRCodeResult | undefined {
  const scannedAt = new Date().toISOString();
  const id = isTicket(scannedQRCode) ? scannedQRCode.TicketId : scannedQRCode.PassId;
  const type = isTicket(scannedQRCode) ? TicketingEntityType.Ticket : TicketingEntityType.Pass;

  if (!id || !type) {
    return undefined;
  }

  return {
    id,
    qrCodeVersion: scannedQRCode.QRCodeVersion,
    scannedAt: scannedAt,
    status: ScannedQRCodeResultStatus.Redeemed,
    type,
  };
}
