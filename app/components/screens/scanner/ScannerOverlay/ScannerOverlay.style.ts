import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    flexDirection: 'column',
    width: '100%',
    height: '100%',
  },
  shadedArea: {
    flexGrow: 1,
    backgroundColor: 'black',
    opacity: 0.5,
    zIndex: -1,
  },
  centerContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'center',
  },
  center: {
    width: '70%',
    maxWidth: 400,
    aspectRatio: 1,
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  centerCenter: {
    width: '105%',
    aspectRatio: 1,
    borderRadius: 20,
    borderWidth: 10,
    zIndex: 1,
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  centerBoardColorIdle: {
    borderColor: 'white',
  },
  centerBoardColorComplete: {
    borderColor: colors.utilityConfirmation,
  },
  textContainer: {
    position: 'absolute',
    top: '10%',
    alignItems: 'center',
    width: '100%',
  },
  text: {
    fontWeight: '700',
    fontSize: 24,
    color: 'white',
  },
  textColorIdle: {
    color: 'white',
  },
  textColorComplete: {
    color: colors.utilityConfirmation,
  },
  backgroundComplete: {
    backgroundColor: colors.utilityConfirmation,
  },
  successIcon: {
    alignContent: 'center',
  },
  bottomSheet: {
    backgroundColor: colors.bgLevel1,
    height: 120,
    padding: UniformSpace.one,
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.half,
  },
}));
