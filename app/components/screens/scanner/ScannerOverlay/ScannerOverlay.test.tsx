import React from 'react';

import { screen, waitFor } from '@testing-library/react-native';

import { ScannerOverlay } from './ScannerOverlay';
import { ScannerState } from '../../../../enums/shared';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { AppTestIDs } from '../../../AppTestIDs';

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    addListener: jest.fn(),
  }),
}));

describe('ScannerOverlay', () => {
  it('renders correctly when visible', async () => {
    renderWithOptions(<ScannerOverlay state={ScannerState.Idle} />, {
      withNavigationContainer: true,
      withAppSessionProvider: true,
    });

    await waitFor(() => expect(screen.getByTestId(AppTestIDs.scannerOverlay)).toBeTruthy());
  });
});
