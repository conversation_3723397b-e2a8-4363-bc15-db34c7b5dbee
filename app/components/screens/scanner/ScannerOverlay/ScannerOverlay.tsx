import React, { useMemo } from 'react';
import { View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconConfirmation, useUniformStyles, Text } from '@hudl/rn-uniform';

import { styleSheet } from './ScannerOverlay.style';
import { ScannerState } from '../../../../enums/shared';
import { ScannerOverlayIconSize } from '../../../../utils/Constants';
import { AppTestIDs } from '../../../AppTestIDs';

export function ScannerOverlay({ state }: { state: ScannerState }): React.ReactElement {
  const styles = useUniformStyles(styleSheet);

  const strings = useI18n(
    {
      scanComplete: 'scanner-status.message.scan-complete',
      alignQRCode: 'scanner-status.message.align-qr-code',
      needHelp: 'scanner-status.button.need-help',
      finishScanning: 'scanner-status.button.finish-scanning',
    },
    []
  );

  const message = useMemo(() => {
    switch (state) {
      case ScannerState.Idle:
        return strings.alignQRCode;
      case ScannerState.ScanComplete:
        return strings.scanComplete;
    }
  }, [state, strings.alignQRCode, strings.scanComplete]);

  const centerBorderColorStyle = useMemo(() => {
    switch (state) {
      case ScannerState.Idle:
        return styles.centerBoardColorIdle;
      case ScannerState.ScanComplete:
        return styles.centerBoardColorComplete;
    }
  }, [state, styles.centerBoardColorComplete, styles.centerBoardColorIdle]);

  const textColorStyle = useMemo(() => {
    switch (state) {
      case ScannerState.Idle:
        return styles.textColorIdle;
      case ScannerState.ScanComplete:
        return styles.textColorComplete;
    }
  }, [state, styles.textColorComplete, styles.textColorIdle]);

  return (
    <View style={styles.container} testID={AppTestIDs.scannerOverlay}>
      <View style={[styles.textContainer]}>
        <Text style={[styles.text, textColorStyle]}>{message}</Text>
      </View>
      <View style={[styles.shadedArea]} />
      <View style={styles.centerContainer}>
        <View style={[styles.shadedArea]} />
        <View style={[styles.center]}>
          <View style={[styles.centerCenter, centerBorderColorStyle]}>
            {state === ScannerState.ScanComplete && (
              <View style={styles.successIcon}>
                <IconConfirmation
                  height={ScannerOverlayIconSize}
                  width={ScannerOverlayIconSize}
                  color="confirmation"
                />
              </View>
            )}
          </View>
        </View>
        <View style={[styles.shadedArea]} />
      </View>
      <View style={[styles.shadedArea]} />
    </View>
  );
}
