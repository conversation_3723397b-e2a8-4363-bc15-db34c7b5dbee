import React, { useMemo } from 'react';

import ScannerAlertComponent, {
  ScannerAlertType,
} from '../../../dialogs/alert/ScannerAlert.component';

export function useScannerAlert(
  scannerAlertState: ScannerAlertType | undefined,
  onCloseScannerAlert: () => void
): React.ReactElement {
  return useMemo(() => {
    if (scannerAlertState === undefined) {
      return <></>;
    }
    const isScannerAlertOpen = scannerAlertState !== undefined;

    return (
      <ScannerAlertComponent
        isOpen={isScannerAlertOpen}
        alertType={scannerAlertState}
        onCancel={onCloseScannerAlert}
      />
    );
  }, [onCloseScannerAlert, scannerAlertState]);
}
