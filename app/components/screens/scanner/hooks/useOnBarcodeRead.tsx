import { useCallback } from 'react';
import { Vibration } from 'react-native';

import { ScannerState } from '../../../../enums/shared';
import { EventInfo } from '../../../../models/EventInfo';
import { isPass } from '../../../../models/ScannedQRCode';
import { Volunteer } from '../../../../models/Volunteer';
import { BarCodeReadEvent } from '../../../../types/shared';
import { QRCodeScanStatus } from '../../../../utils/Enums';
import { ScannerAlertType } from '../../../dialogs/alert/ScannerAlert.component';
import { getHudlTicketScanStatus } from '../ScannerUtils';

export function useOnBarcodeRead(
  overlayState: ScannerState,
  scannerAlertState: ScannerAlertType | undefined,
  setOverlayState: (state: ScannerState) => void,
  setScannerAlertState: (state: ScannerAlertType | undefined) => void,
  event: EventInfo,
  volunteerInfo: Volunteer
): (readEvent: BarCodeReadEvent) => void {
  return useCallback(
    async (readEvent: BarCodeReadEvent) => {
      if (overlayState !== ScannerState.Idle || scannerAlertState !== undefined) {
        return;
      }

      const { status, parsedQRCode, result } = await getHudlTicketScanStatus(
        readEvent.value,
        event,
        volunteerInfo
      );

      switch (status) {
        case QRCodeScanStatus.Valid:
          Vibration.vibrate();
          setOverlayState(ScannerState.ScanComplete);
          setScannerAlertState(undefined);
          break;

        case QRCodeScanStatus.InvalidChecksum:
        case QRCodeScanStatus.NotRecognized:
          setScannerAlertState({ __type: 'NotRecognized' });
          setOverlayState(ScannerState.Idle);
          break;

        case QRCodeScanStatus.AlreadyScanned:
          setOverlayState(ScannerState.Idle);
          setScannerAlertState({
            __type: 'AlreadyScanned',
            timeStamp: result?.scannedAt ?? '',
          });
          break;

        case QRCodeScanStatus.IncorrectEvent:
          setOverlayState(ScannerState.Idle);
          setScannerAlertState({ __type: 'IncorrectEvent', currentEventName: event.name });
          break;

        case QRCodeScanStatus.IncorrectParticipants:
          setOverlayState(ScannerState.Idle);
          setScannerAlertState({ __type: 'EventNotIncluded', currentEventName: event.name });
          break;

        case 'error':
          setOverlayState(ScannerState.Idle);
          setScannerAlertState({ __type: 'ErrorScanning' });
          break;

        case QRCodeScanStatus.OutsideOfDateRange:
          setOverlayState(ScannerState.Idle);
          if (isPass(parsedQRCode) && parsedQRCode.StartDate && parsedQRCode.EndDate) {
            setScannerAlertState({
              __type: 'InvalidPassDates',
              startDate: parsedQRCode.StartDate,
              endDate: parsedQRCode.EndDate,
            });
          }
          break;
      }
    },
    [event, overlayState, scannerAlertState, setOverlayState, setScannerAlertState, volunteerInfo]
  );
}
