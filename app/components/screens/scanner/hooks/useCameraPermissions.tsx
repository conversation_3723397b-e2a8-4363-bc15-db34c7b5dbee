import { useCallback, useEffect, useRef } from 'react';

import { AppSettings } from '@hudl/jarvis/device';
import { useI18n } from '@hudl/jarvis/i18n';

import { AppTestIDs } from '../../../AppTestIDs';
import { useFanModalContext } from '../../../modal/ModalProvider';

export function useCameraPermissions(shouldShowModal: boolean): void {
  const modalContext = useFanModalContext();
  const hasShownModal = useRef(false);

  const strings = useI18n(
    {
      header: 'camera-permission-requester.header',
      subtext: 'camera-permission-requester.subtext',
      openSettingsButton: 'camera-permission-requester.open-settings-button',
      cancelButton: 'camera-permission-requester.cancel-button',
    },
    []
  );

  const openRequesterModal = useCallback(() => {
    modalContext.showModal(
      strings.header,
      strings.subtext,
      'normal',
      true,
      [
        {
          buttonStyle: 'standard',
          buttonType: 'primary',
          testID: AppTestIDs.cameraPermissionsModalButton,
          text: strings.openSettingsButton,
          onPress: AppSettings.openAppSettings,
        },
      ],
      strings.cancelButton
    );
  }, [modalContext, strings]);

  useEffect(() => {
    if (shouldShowModal && !hasShownModal.current) {
      openRequesterModal();
      hasShownModal.current = true;
    }
  }, [shouldShowModal, openRequesterModal]);
}
