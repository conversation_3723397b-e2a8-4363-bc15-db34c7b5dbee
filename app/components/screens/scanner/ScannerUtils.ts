import Base64 from '@hudl/jarvis/util/Base64';

import { calculateHudlChecksum } from './Checksum';
import { storeQRCodeResult } from './StoredResultUtils';
import { EventInfo } from '../../../models/EventInfo';
import { ScannedQRCode, isTicket, isPass } from '../../../models/ScannedQRCode';
import { Volunteer } from '../../../models/Volunteer';
import {
  QRCodeScanStatus,
  QRCodeStoreStatus,
  ScannedQRCodeResultStatus,
  TicketingEntityType,
} from '../../../utils/Enums';
import { safeJsonParse } from '../../../utils/safe-parse';
import { validatePassForEvent } from '../../../validators/PassValidator';
import { validateTicketForEvent } from '../../../validators/TicketValidator';

export interface ScannedQRCodeResult {
  id: string;
  qrCodeVersion: string;
  scannedAt: string;
  status: ScannedQRCodeResultStatus;
  type: TicketingEntityType;
}

export interface ScanResult {
  status: QRCodeScanStatus;
  parsedQRCode?: ScannedQRCode;
  result?: ScannedQRCodeResult;
}
/**
 * 1) Base64 decode the QR code
 * 2) Parse the QR code to determine if its a ticket or pass
 * 3) Validate the checksum to ensure its a Hudl ticketing QR code
 * 4a) If it's a ticket, validate its for the same event
 * 4b) If it's a V1 pass, validate the event date is in range of the pass start and end dates.
 * 4c) If it's a V2 pass, validate the pass config id is in a list of valid pass config id for the event.
 * 5) Attempt to store the ticket
 * @param data the raw QR code data
 * @param currentEvent the event to check against
 * @param volunteerInfo the volunteer storing the data
 * @returns the scan result
 */
export async function getHudlTicketScanStatus(
  data: string,
  currentEvent: EventInfo,
  volunteerInfo: Volunteer
): Promise<ScanResult> {
  let decodedData;
  try {
    decodedData = Base64.decode(data);
  } catch {
    decodedData = '';
  }

  const parsedQRCode = parseIntoHudlQRCodeData(decodedData);

  if (!parsedQRCode) {
    return { status: QRCodeScanStatus.NotRecognized, parsedQRCode };
  }

  const isValidChecksum = parsedQRCode.Checksum === calculateHudlChecksum(parsedQRCode);

  if (!isValidChecksum) {
    return { status: QRCodeScanStatus.InvalidChecksum, parsedQRCode };
  }

  const qrCodeStatusForEvent = checkQRCodeStatusForEvent(parsedQRCode, currentEvent);

  switch (qrCodeStatusForEvent) {
    case QRCodeScanStatus.CorrectEvent:
      break;
    case QRCodeScanStatus.IncorrectEvent:
      return { status: QRCodeScanStatus.IncorrectEvent, parsedQRCode };
    case QRCodeScanStatus.IncorrectParticipants:
      return { status: QRCodeScanStatus.IncorrectParticipants, parsedQRCode };
    case QRCodeScanStatus.OutsideOfDateRange:
      return { status: QRCodeScanStatus.OutsideOfDateRange, parsedQRCode };
  }

  const { storedStatus, result } = await storeQRCodeResult(
    parsedQRCode,
    currentEvent,
    volunteerInfo
  );

  switch (storedStatus) {
    case QRCodeStoreStatus.Stored:
      return { status: QRCodeScanStatus.Valid, parsedQRCode, result };
    case QRCodeStoreStatus.QRCodeAlreadyScanned:
      return { status: QRCodeScanStatus.AlreadyScanned, parsedQRCode, result };
    case QRCodeStoreStatus.ErrorStoringResult:
      return { status: QRCodeScanStatus.Error };
  }
}

function parseIntoHudlQRCodeData(data: string): ScannedQRCode | undefined {
  const ticket = safeJsonParse(isTicket, data);
  if (ticket.parsed) {
    return ticket.parsed;
  }

  const pass = safeJsonParse(isPass, data);
  if (pass.parsed) {
    return pass.parsed;
  }

  return undefined;
}

/**
 * Ensures the QR code data is valid for the currently selected event
 */
function checkQRCodeStatusForEvent(
  scannedQRCode: ScannedQRCode,
  currentEvent: EventInfo
): QRCodeScanStatus {
  if (isTicket(scannedQRCode)) {
    return validateTicketForEvent(scannedQRCode, currentEvent);
  } else if (isPass(scannedQRCode)) {
    return validatePassForEvent(scannedQRCode, currentEvent);
  }

  return QRCodeScanStatus.IncorrectEvent;
}
