import { faker } from '@faker-js/faker';
import AsyncStorage from '@react-native-async-storage/async-storage';

import Base64 from '@hudl/jarvis/util/Base64';

import { mockedTicketedEvent } from '../../../../__mocks__/TicketedEvent/TicketedEvent';
import { QRCodeScanStatus } from '../../../../utils/Enums';
import { eventToEventInfo } from '../../../../utils/EventUtils';
import { getHudlTicketScanStatus } from '../ScannerUtils';
import {
  ORGANIZATION_ID,
  TEAMS,
  TICKET_EVENT_ID,
  mockPass,
  mockTicket,
  testVolunteerInfo,
} from '../__mocks__/Constants';

jest.mock('@hudl/jarvis/logging', () => ({
  HudlLogger: {
    logError: jest.fn(),
    logInfo: jest.fn(),
    logWarn: jest.fn(),
  },
}));

describe('Scanning status', (): void => {
  beforeEach(async (): Promise<void> => {
    AsyncStorage.clear();
  });

  it('is not recognized', async (): Promise<void> => {
    const status = await getHudlTicketScanStatus(
      'randomData',
      eventToEventInfo(mockedTicketedEvent()),
      testVolunteerInfo
    );

    expect(status.status).toEqual(QRCodeScanStatus.NotRecognized);
    expect(status.parsedQRCode).toBeUndefined();
  });

  it('from ticket is stored', async (): Promise<void> => {
    const ticketQRCode = Base64.encode(
      JSON.stringify(mockTicket({ TicketedEventId: TICKET_EVENT_ID }))
    );

    const event = eventToEventInfo(
      mockedTicketedEvent({
        id: TICKET_EVENT_ID,
        organizationId: ORGANIZATION_ID,
        date: faker.date.soon().toUTCString(),
      })
    );
    const firstScan = await getHudlTicketScanStatus(ticketQRCode, event, testVolunteerInfo);

    const secondScan = await getHudlTicketScanStatus(ticketQRCode, event, testVolunteerInfo);

    expect(firstScan.status).toEqual(QRCodeScanStatus.Valid);
    expect(firstScan.parsedQRCode).toBeDefined();

    expect(secondScan.status).toEqual(QRCodeScanStatus.AlreadyScanned);
    expect(secondScan.parsedQRCode).toBeDefined();
  });

  it('from pass is stored', async (): Promise<void> => {
    const event = eventToEventInfo(
      mockedTicketedEvent({
        id: TICKET_EVENT_ID,
        organizationId: ORGANIZATION_ID,
        participatingTeamIds: TEAMS,
        date: faker.date.soon().toUTCString(),
      })
    );

    const passQRCode = Base64.encode(
      JSON.stringify(
        mockPass({
          OrganizationId: ORGANIZATION_ID,
          Teams: TEAMS,
          StartDate: faker.date.past(1, event.date).toUTCString(),
          EndDate: faker.date.future(1, event.date).toUTCString(),
        })
      )
    );

    const firstScan = await getHudlTicketScanStatus(passQRCode, event, testVolunteerInfo);

    const secondScan = await getHudlTicketScanStatus(passQRCode, event, testVolunteerInfo);

    expect(firstScan.status).toEqual(QRCodeScanStatus.Valid);
    expect(firstScan.parsedQRCode).toBeDefined();

    expect(secondScan.status).toEqual(QRCodeScanStatus.AlreadyScanned);
    expect(secondScan.parsedQRCode).toBeDefined();
  });

  it('from pass and ticket is stored', async (): Promise<void> => {
    const ticketQRCode = Base64.encode(
      JSON.stringify(mockTicket({ TicketedEventId: TICKET_EVENT_ID }))
    );

    const event = eventToEventInfo(
      mockedTicketedEvent({
        id: TICKET_EVENT_ID,
        organizationId: ORGANIZATION_ID,
        date: faker.date.soon().toUTCString(),
      })
    );

    const passQRCode = Base64.encode(
      JSON.stringify(
        mockPass({
          OrganizationId: ORGANIZATION_ID,
          StartDate: faker.date.past(1, event.date).toUTCString(),
          EndDate: faker.date.future(1, event.date).toUTCString(),
        })
      )
    );

    const passScan = await getHudlTicketScanStatus(passQRCode, event, testVolunteerInfo);

    const ticketScan = await getHudlTicketScanStatus(ticketQRCode, event, testVolunteerInfo);

    expect(passScan.status).toEqual(QRCodeScanStatus.Valid);
    expect(passScan.parsedQRCode).toBeDefined();

    expect(ticketScan.status).toEqual(QRCodeScanStatus.Valid);
    expect(ticketScan.parsedQRCode).toBeDefined();
  });

  it('incorrect event', async (): Promise<void> => {
    const ticketQRCode = Base64.encode(
      JSON.stringify(mockTicket({ TicketedEventId: 'incorrect_EVENT_id' }))
    );
    const event = eventToEventInfo(
      mockedTicketedEvent({ id: TICKET_EVENT_ID, organizationId: ORGANIZATION_ID })
    );
    const ticketScan = await getHudlTicketScanStatus(ticketQRCode, event, testVolunteerInfo);

    expect(ticketScan.status).toEqual(QRCodeScanStatus.IncorrectEvent);
  });

  it('incorrect-participants', async (): Promise<void> => {
    const event = eventToEventInfo(
      mockedTicketedEvent({
        id: TICKET_EVENT_ID,
        organizationId: ORGANIZATION_ID,
        participatingTeamIds: TEAMS,
        date: faker.date.soon().toUTCString(),
      })
    );

    const incorrectORPassQRCode = Base64.encode(
      JSON.stringify(
        mockPass({
          OrganizationId: 'incorrect_org_id',
          StartDate: faker.date.past(1, event.date).toUTCString(),
          EndDate: faker.date.future(1, event.date).toUTCString(),
        })
      )
    );

    const incorrectTeamsPassQRCode = Base64.encode(
      JSON.stringify(
        mockPass({
          OrganizationId: ORGANIZATION_ID,
          StartDate: faker.date.past(2).toISOString(),
          EndDate: faker.date.future(1).toISOString(),
          Teams: ['incorrect_teamId'],
        })
      )
    );

    const incorrectOrgScan = await getHudlTicketScanStatus(
      incorrectORPassQRCode,
      event,
      testVolunteerInfo
    );

    const incorrectTeamsScan = await getHudlTicketScanStatus(
      incorrectTeamsPassQRCode,
      event,
      testVolunteerInfo
    );

    expect(incorrectOrgScan.status).toEqual(QRCodeScanStatus.IncorrectParticipants);
    expect(incorrectTeamsScan.status).toEqual(QRCodeScanStatus.IncorrectParticipants);
  });

  const validEventStartEndDateInputs = [
    ['2023-10-10T00:00:00.000Z', '2023-10-10T00:00:00.000Z', '2023-10-15T00:00:00.000Z'], // Start date is same as event date
    ['2023-10-10T00:00:00.000Z', '2023-10-01T00:00:00.000Z', '2023-10-10T00:00:00.000Z'], // End date is same as event date
    ['2023-10-10T00:00:00.000Z', '2023-10-10T00:00:00.000Z', '2023-10-10T00:00:00.000Z'], // Start date and end date are same as event date
  ];

  it.each(validEventStartEndDateInputs)(
    'valid date ranges return good',
    async (eventDate, passStartDate, passEndDate): Promise<void> => {
      const event = eventToEventInfo(
        mockedTicketedEvent({
          id: TICKET_EVENT_ID,
          organizationId: ORGANIZATION_ID,
          participatingTeamIds: TEAMS,
          date: new Date(eventDate).toUTCString(),
          timeZoneIdentifier: 'Etc/UTC',
        })
      );

      const passQRCode = Base64.encode(
        JSON.stringify(
          mockPass({
            OrganizationId: ORGANIZATION_ID,
            StartDate: new Date(passStartDate).toUTCString(),
            EndDate: new Date(passEndDate).toUTCString(),
          })
        )
      );

      const passScan = await getHudlTicketScanStatus(passQRCode, event, testVolunteerInfo);

      expect(passScan.status).toEqual(QRCodeScanStatus.Valid);
    }
  );

  const invalidEventStartEndDateInputs = [
    ['2023-10-10T00:00:00.000Z', '2023-10-12T00:00:00.000Z', '2023-10-15T00:00:00.000Z'], // Start date is after event date
    ['2023-10-10T00:00:00.000Z', '2023-10-02T00:00:00.000Z', '2023-10-05T00:00:00.000Z'], // End date is before event date
  ];

  it.each(invalidEventStartEndDateInputs)(
    'outside-date-range',
    async (eventDate, passStartDate, passEndDate): Promise<void> => {
      const event = eventToEventInfo(
        mockedTicketedEvent({
          id: TICKET_EVENT_ID,
          organizationId: ORGANIZATION_ID,
          participatingTeamIds: TEAMS,
          date: new Date(eventDate).toUTCString(),
          timeZoneIdentifier: 'Etc/UTC',
        })
      );

      const passQRCode = Base64.encode(
        JSON.stringify(
          mockPass({
            OrganizationId: ORGANIZATION_ID,
            StartDate: new Date(passStartDate).toUTCString(),
            EndDate: new Date(passEndDate).toUTCString(),
          })
        )
      );

      const passScan = await getHudlTicketScanStatus(passQRCode, event, testVolunteerInfo);

      expect(passScan.status).toEqual(QRCodeScanStatus.OutsideOfDateRange);
    }
  );
});
