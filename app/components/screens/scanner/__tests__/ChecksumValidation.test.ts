import {
  calculateChecksumForPass,
  calculateChecksumForTicket,
  validateChecksumForPass,
  validateChecksumForTicket,
} from '../Checksum';
import {
  EXPECTED_TICKET_HASH_OUTPUT,
  PASS,
  PASS_NO_DATES,
  PASS_NO_TEAMS,
  PASS_NO_TEAMS_NO_DATES,
  testTicket,
} from '../__mocks__/Constants';

describe('Checksum', (): void => {
  it('can calculate checksum for Ticket', async (): Promise<void> => {
    const calculated = calculateChecksumForTicket(testTicket);
    expect(calculated).toBe(EXPECTED_TICKET_HASH_OUTPUT);
  });

  it('validate checksum for Ticket', async (): Promise<void> => {
    const isValidChecksum = validateChecksumForTicket(testTicket);
    expect(isValidChecksum).toBe(true);
  });

  it('can calculate checksum for Pass - No Teams - No Dates', async (): Promise<void> => {
    const calculated = calculateChecksumForPass(PASS_NO_TEAMS_NO_DATES);
    expect(calculated).toBe(PASS_NO_TEAMS_NO_DATES.Checksum);
  });

  it('can calculate checksum for Pass - No dates', async (): Promise<void> => {
    const calculated = calculateChecksumForPass(PASS_NO_DATES);
    expect(calculated).toBe(PASS_NO_DATES.Checksum);
  });

  it('can calculate checksum for Pass - No Teams', async (): Promise<void> => {
    const calculated = calculateChecksumForPass(PASS_NO_TEAMS);
    expect(calculated).toBe(PASS_NO_TEAMS.Checksum);
  });

  it('can calculate checksum for Pass', async (): Promise<void> => {
    const calculated = calculateChecksumForPass(PASS);
    expect(calculated).toBe(PASS.Checksum);
  });

  it('validate checksum for Pass', async (): Promise<void> => {
    const isValidChecksum = validateChecksumForPass(PASS);
    expect(isValidChecksum).toBe(true);
  });
});
