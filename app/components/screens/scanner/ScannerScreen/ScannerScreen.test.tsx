/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { View } from 'react-native';

import { screen } from '@testing-library/react-native';

import ScannerScreen from './ScannerScreen';
import { renderWithOptions } from '../../../../test/renderHelpers';
import { CameraPermissionStatus } from '../../../../utils/Enums';
import { setupAccessContextMocks } from '../../../../utils/TestUtils';
import { AppTestIDs } from '../../../AppTestIDs';
import { FanModalProvider } from '../../../modal/ModalProvider';

const mockRequestCameraPermission = jest.fn();

function MockCameraComponent(): React.ReactElement {
  return <View testID={AppTestIDs.scannerContainer} />;
}
(MockCameraComponent as any).getCameraPermissionStatus = mockRequestCameraPermission;
(MockCameraComponent as any).requestCameraPermission = mockRequestCameraPermission;

jest.mock('react-native-vision-camera', () => {
  return {
    Camera: MockCameraComponent,
    useCameraDevices: () => [{ position: 'back' }],
    useCodeScanner: () => ({}),
  };
});

const mockOpenSettings = jest.fn();
jest.mock('@hudl/jarvis/device', () => ({
  AppSettings: {
    openAppSettings: mockOpenSettings,
  },
}));

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    addListener: jest.fn(),
  }),
}));

jest.mock('../../../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../../queries/useAccessCodeHook');
setupAccessContextMocks();

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

describe('ScannerScreen tests', () => {
  it('Renders correctly: permissions granted', async () => {
    mockRequestCameraPermission.mockReturnValue(CameraPermissionStatus.Granted);
    renderWithOptions(
      <FanModalProvider>
        <ScannerScreen />
      </FanModalProvider>,
      {
        withAccessContext: true,
        withNavigationContainer: true,
      }
    );

    expect(screen.getByTestId(AppTestIDs.scannerRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.scannerContainer)).toBeTruthy();
  });

  it('Renders correctly: permissions denied', async () => {
    mockRequestCameraPermission.mockReturnValue(CameraPermissionStatus.Denied);
    renderWithOptions(
      <FanModalProvider>
        <ScannerScreen />
      </FanModalProvider>,
      {
        withAccessContext: true,
        withNavigationContainer: true,
      }
    );

    expect(screen.getByTestId(AppTestIDs.scannerRoot)).toBeTruthy();
    expect(screen.queryByTestId(AppTestIDs.scannerContainer)).toBeFalsy();
  });
});
