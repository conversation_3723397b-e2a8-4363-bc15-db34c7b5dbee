import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';

import { useIsFocused } from '@react-navigation/native';
import { Camera } from 'react-native-vision-camera';

import { useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './ScannerScreen.style';
import { useAccessContext } from '../../../../context/AccessContext';
import { ScannerState } from '../../../../enums/shared';
import { EventInfo } from '../../../../models/EventInfo';
import { Volunteer } from '../../../../models/Volunteer';
import { CameraPermissionStatus } from '../../../../utils/Enums';
import { AppTestIDs } from '../../../AppTestIDs';
import { ScannerAlertType } from '../../../dialogs/alert/ScannerAlert.component';
import { QRCodeScanner } from '../QRCodeScanner/QRCodeScanner';
import { ScannerOverlay } from '../ScannerOverlay/ScannerOverlay';
import { useCameraPermissions } from '../hooks/useCameraPermissions';
import { useOnBarcodeRead } from '../hooks/useOnBarcodeRead';
import { useScannerAlert } from '../hooks/useScannerAlert';

export default function ScannerScreen(): React.ReactElement {
  const styles = useUniformStyles(styleSheet);

  const accessContext = useAccessContext();
  const isFocused = useIsFocused();
  const { eventInfo, volunteerInfo } = accessContext;

  const [overlayState, setOverlayState] = useState<ScannerState>(ScannerState.Idle);
  const [scannerAlertState, setScannerAlertState] = useState<ScannerAlertType | undefined>(
    undefined
  );
  const [permissionRequested, setPermissionRequested] = useState(false);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  const [shouldShowRequesterModal, setShouldShowRequesterModal] = useState(false);

  const [cameraPermissionStatus, setCameraPermissionStatus] = useState<CameraPermissionStatus>(
    () => Camera.getCameraPermissionStatus() as CameraPermissionStatus
  );

  const onBarCodeRead = useOnBarcodeRead(
    overlayState,
    scannerAlertState,
    setOverlayState,
    setScannerAlertState,
    eventInfo ?? ({} as EventInfo),
    volunteerInfo ?? ({} as Volunteer)
  );

  useEffect(() => {
    if (overlayState === ScannerState.ScanComplete) {
      setTimeout(() => setOverlayState(ScannerState.Idle), 2500);
    }
  }, [overlayState]);

  const hasCameraPermission = useMemo(
    () => cameraPermissionStatus === CameraPermissionStatus.Granted,
    [cameraPermissionStatus]
  );

  useEffect(() => {
    const handleCameraPermission = async (): Promise<void> => {
      if (!hasCameraPermission && !permissionRequested && !isRequestingPermission) {
        setIsRequestingPermission(true);
        setPermissionRequested(true);

        const newStatus = await Camera.requestCameraPermission();

        setIsRequestingPermission(false);
        setCameraPermissionStatus(newStatus as CameraPermissionStatus);
        setShouldShowRequesterModal(newStatus === CameraPermissionStatus.Denied);
        return;
      }

      if (!hasCameraPermission && permissionRequested && !isRequestingPermission) {
        setShouldShowRequesterModal(true);
      }
    };

    handleCameraPermission();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasCameraPermission, permissionRequested]);

  useCameraPermissions(shouldShowRequesterModal);

  const onCloseScannerAlert = useCallback(() => {
    setScannerAlertState(undefined);
  }, []);

  const scannerAlert = useScannerAlert(scannerAlertState, onCloseScannerAlert);

  if (!isFocused) {
    return <View />;
  }

  return (
    <View style={[styles.container]} testID={AppTestIDs.scannerRoot}>
      <QRCodeScanner hasCameraPermission={hasCameraPermission} onCodeScanned={onBarCodeRead} />
      <ScannerOverlay state={overlayState} />
      {scannerAlert}
    </View>
  );
}
