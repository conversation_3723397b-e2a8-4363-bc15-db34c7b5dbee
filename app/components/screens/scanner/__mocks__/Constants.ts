import { faker } from '@faker-js/faker';

import { Pass, Ticket } from '../../../../models/ScannedQRCode';
import { Volunteer } from '../../../../models/Volunteer';
import { calculateChecksumForPass, calculateChecksumForTicket } from '../Checksum';

export const TICKET_ID = 'ticketID123';
export const TICKET_EVENT_ID = 'ticketedEventID456';
export const EXPECTED_TICKET_HASH_OUTPUT = 'GRLV0jP61c0JQ7kyDrv56Sx696RXsE76QMFdKVQhCOM=';

export const PASS_ID = 'ticketedEventID456';
export const ORGANIZATION_ID = 'organizationID1234';
export const TEAMS = ['teamID0287346', 'teamID2453456', 'teamID7252782'];
export const START_DATE = 'startDate';
export const END_DATE = 'endDate';

export const QR_CODE_VERSION = '1';

export const GOOD_TICKET =
  'ewogICJUaWNrZXRlZEV2ZW50SWQiOiAidGlja2V0ZWRFdmVudElENDU2IiwKICAiVGlja2V0SWQiOiAidGlja2V0SUQxMjMiLAogICJUaWNrZXRlZEV2ZW50TmFtZSI6ICJTb21lIFRpY2tldCBOYW1lIiwKICAiUVJDb2RlVmVyc2lvbiI6ICIxLjAuMCIsCiAgIkNoZWNrc3VtIjogIkdSTFYwalA2MWMwSlE3a3lEcnY1NlN4Njk2UlhzRTc2UU1GZEtWUWhDT009Igp9';

export const GOOD_PASS =
  'eyJQYXNzSWQiOiJ0aWNrZXRlZEV2ZW50SUQ0NTYiLCJPcmdhbml6YXRpb25JZCI6Im9yZ2FuaXphdGlvbklEMTIzNCIsIlRlYW1zIjpbInRlYW1JRDAyODczNDYiLCJ0ZWFtSUQyNDUzNDU2IiwidGVhbUlENzI1Mjc4MiJdLCJTdGFydERhdGUiOiIyMDIyLTA5LTI3VDA2OjE1OjA1Ljg2MVoiLCJFbmREYXRlIjoiMjAyMy0wOS0yN1QwNjoxNTowNS44NjFaIiwiUVJDb2RlVmVyc2lvbiI6IjEiLCJDaGVja3N1bSI6InNjWk1Bc3BMdTNzNFZxM3Q4R3orRzA4MEtFMmQ3SjFVTGF0R2o1bWpMNXc9In0=';
export const testTicket: Ticket = {
  TicketedEventId: TICKET_EVENT_ID,
  TicketId: TICKET_ID,
  TicketedEventName: '',
  QRCodeVersion: QR_CODE_VERSION,
  Checksum: EXPECTED_TICKET_HASH_OUTPUT,
};

export const PASS_NO_TEAMS_NO_DATES: Pass = {
  PassId: PASS_ID,
  OrganizationId: ORGANIZATION_ID,
  QRCodeVersion: QR_CODE_VERSION,
  Checksum: 'jZ+8Pr4IhB0L7wvBd+0u2bqfHJMZW6NTON2DcItj/jI=',
};

export const PASS_NO_TEAMS: Pass = {
  PassId: PASS_ID,
  OrganizationId: ORGANIZATION_ID,
  StartDate: START_DATE,
  EndDate: END_DATE,
  QRCodeVersion: QR_CODE_VERSION,
  Checksum: '5ltMmFCRr4Uusx8gWjRMkLMgXxXRgocD4l/eXlsnNQg=',
};

export const PASS_NO_DATES: Pass = {
  PassId: PASS_ID,
  OrganizationId: ORGANIZATION_ID,
  Teams: TEAMS,
  QRCodeVersion: QR_CODE_VERSION,
  Checksum: '01vlnW7fRFr/wJ68Jrr5HVu98OUSBiAC5i/sg1/7fmM=',
};

export const PASS: Pass = {
  PassId: PASS_ID,
  OrganizationId: ORGANIZATION_ID,
  Teams: TEAMS,
  StartDate: START_DATE,
  EndDate: END_DATE,
  QRCodeVersion: QR_CODE_VERSION,
  Checksum: '9T5Gsn4mkjTMwdG6RaTqDlY4/eKZd8nPWcH11XrivZM=',
};

export const testVolunteerInfo: Volunteer = {
  firstName: 'James',
  lastName: 'Robinson',
  accessCode: 'abcde',
  email: '<EMAIL>',
};

interface MockPass {
  PassId?: string;
  OrganizationId?: string;
  Teams?: string[];
  StartDate?: string;
  EndDate?: string;
  QRCodeVersion?: string;
  Checksum?: string;
}

export function mockPass(mock?: MockPass): Pass | string {
  const pass: Pass = {
    PassId: mock?.PassId ?? faker.datatype.uuid(),
    OrganizationId: mock?.OrganizationId ?? faker.datatype.uuid(),
    QRCodeVersion: mock?.QRCodeVersion ?? '1',
    Checksum: mock?.Checksum ?? '',
    Teams: mock?.Teams,
    StartDate: mock?.StartDate,
    EndDate: mock?.EndDate,
  };

  const checksum = calculateChecksumForPass(pass);
  pass.Checksum = checksum;

  return pass;
}

interface MockTicket {
  TicketId?: string;
  TicketedEventId?: string;
  TicketedEventName?: string;
  QRCodeVersion?: string;
  Checksum?: string;
}
export function mockTicket(mock?: MockTicket): Ticket | string {
  const ticket: Ticket = {
    TicketId: mock?.TicketId ?? faker.datatype.uuid(),
    TicketedEventId: mock?.TicketedEventId ?? faker.datatype.uuid(),
    QRCodeVersion: mock?.QRCodeVersion ?? '1',
    Checksum: mock?.Checksum ?? '',
    TicketedEventName: mock?.TicketedEventName ?? faker.name.jobTitle(),
  };

  const checksum = calculateChecksumForTicket(ticket);
  ticket.Checksum = checksum;

  return ticket;
}
