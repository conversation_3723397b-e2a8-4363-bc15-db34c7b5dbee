import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    height: '100%',
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
  },
  contentContainer: {
    padding: UniformSpace.oneAndHalf,
  },
  radioGroup: {
    gap: UniformSpace.half,
  },
  radioButton: {
    fontWeight: 'bold',
    borderWidth: 1,
    borderRadius: 4,
    padding: UniformSpace.one,
    backgroundColor: colors.bgLevel0,
  },
  header: {
    color: colors.baseWhite,
    fontSize: 18,
    fontWeight: '700',
    paddingBottom: UniformSpace.two,
  },
  footer: {
    backgroundColor: colors.bgLevel0,
    padding: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.one,
  },
  button: {
    backgroundColor: nonUniformColors.contentEmphasesBgContrast,
  },
  cancelOrder: {
    color: nonUniformColors.contentForegroundSubtle,
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
    paddingTop: UniformSpace.one,
  },
}));
