import React from 'react';

import { fireEvent, screen } from '@testing-library/react-native';

import { CustomSaleSelection } from './CustomSaleSelection';
import { renderWithOptions } from '../../../test/renderHelpers';
import { CustomSalesType, Navigation } from '../../../utils/Enums';
import { customSaleType } from '../../../utils/stateVars';
import { AppTestIDs } from '../../AppTestIDs';

const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    goBack: mockGoBack,
    canGoBack: () => true,
  }),
  useRoute: () => ({}),
}));

beforeEach(() => {
  customSaleType(CustomSalesType.Concessions);
});

describe('CustomSaleSelection', () => {
  it('Renders and concessions is defaulted to', () => {
    renderWithOptions(<CustomSaleSelection />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.customSalesSelectionRoot)).toBeTruthy();
    expect(customSaleType()).toBe(CustomSalesType.Concessions);
  });

  it('Clicking apparel changes selected value', () => {
    renderWithOptions(<CustomSaleSelection />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.customSalesSelectionRoot)).toBeTruthy();
    fireEvent.press(screen.getByTestId('custom-sale-selection-radio-group-radio-option-Apparel'));
    expect(customSaleType()).toBe(CustomSalesType.Apparel);
  });

  it('Clicking other changes selected value', () => {
    renderWithOptions(<CustomSaleSelection />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.customSalesSelectionRoot)).toBeTruthy();
    fireEvent.press(screen.getByTestId('custom-sale-selection-radio-group-radio-option-Other'));
    expect(customSaleType()).toBe(CustomSalesType.Other);
  });

  it('Clicking continue navigates', () => {
    renderWithOptions(<CustomSaleSelection />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.customSalesSelectionRoot)).toBeTruthy();
    fireEvent.press(screen.getByTestId(AppTestIDs.customSaleSelectionContinueButton));
    expect(mockNavigate).toHaveBeenCalledWith(Navigation.CustomSales);
  });
});
