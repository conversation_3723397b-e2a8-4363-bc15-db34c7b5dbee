import React, { useCallback } from 'react';
import { SafeAreaView, Text, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Radio, RadioGroup, useUniformStyles, Button } from '@hudl/rn-uniform';

import { styleSheet } from './CustomSaleSelection.style';
import { AppNav } from '../../../Nav';
import { CustomSalesType, Navigation } from '../../../utils/Enums';
import { customSaleType } from '../../../utils/stateVars';
import { AppTestIDs } from '../../AppTestIDs';

export function CustomSaleSelection(): React.ReactElement {
  const nav = AppNav.root.useNavigation();
  const styles = useUniformStyles(styleSheet);
  const [radioValue, setRadioValue] = React.useState(CustomSalesType.Concessions as string);

  const i18nStrings = useI18n(
    {
      concessions: 'custom-sales-selection.concessions',
      apparel: 'custom-sales-selection.apparel',
      other: 'custom-sales-selection.other',
      itemSelectionHeader: 'custom-sales-selection.select-item-sell-header',
      continue: 'custom-sales-selection.continue',
      cancelOrder: 'custom-sales.cancel-order',
    },
    []
  );

  const onRadioButtonPress = useCallback((value: string): void => {
    customSaleType(value as CustomSalesType);
    setRadioValue(value);
  }, []);

  const radioButtons = [
    {
      label: i18nStrings.concessions,
      value: CustomSalesType.Concessions,
    },
    {
      label: i18nStrings.apparel,
      value: CustomSalesType.Apparel,
    },
    {
      label: i18nStrings.other,
      value: CustomSalesType.Other,
    },
  ];

  const renderRadioButtons = (): React.ReactElement[] => {
    return radioButtons.map((radioButton) => {
      return (
        <Radio
          key={radioButton.value}
          label={radioButton.label}
          value={radioButton.value}
          style={styles.radioButton}
          testID={AppTestIDs.customSaleSelectionRadioGroupOption(radioButton.value)}
        />
      );
    });
  };

  const onContinuePress = useCallback(() => {
    nav.navigate(Navigation.CustomSales);
  }, [nav]);

  return (
    <SafeAreaView>
      <View style={styles.container} testID={AppTestIDs.customSalesSelectionRoot}>
        <View style={styles.contentContainer}>
          <Text style={styles.header}>{i18nStrings.itemSelectionHeader}</Text>
          <RadioGroup
            valueChecked={radioValue}
            onPress={onRadioButtonPress}
            style={styles.radioGroup}
            testID={AppTestIDs.customSaleSelectionRadioGroup}>
            {renderRadioButtons()}
          </RadioGroup>
        </View>
        <View style={styles.footer}>
          <Button
            text={i18nStrings.continue}
            onPress={onContinuePress}
            testID={AppTestIDs.customSaleSelectionContinueButton}
            style={styles.button}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
