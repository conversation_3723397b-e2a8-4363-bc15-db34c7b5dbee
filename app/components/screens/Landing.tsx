import React, { ReactElement, useCallback, useEffect, useState } from 'react';
import { ImageBackground, View } from 'react-native';
import { StyleSheet } from 'react-native';

import { useIsFocused } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Path, Svg, SvgProps } from 'react-native-svg';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, IconDropper, UniformSpace } from '@hudl/rn-uniform';

import { AppNav } from '../../Nav';
import { attemptDeleteGroups } from '../../local_storage/LocalStorageHooks';
import { getCurrentVolunteer } from '../../local_storage/VolunteerStorage';
import { Volunteer } from '../../models/Volunteer';
import { useAppSession } from '../../session/AppSession';
import { Navigation } from '../../utils/Enums';
import { AppTestIDs } from '../AppTestIDs';
import { DeveloperOptions } from '../DeveloperOptions/DeveloperOptions';
import { HeaderLeftIcon } from '../NavigationHeaders/DefaultHeader';

export const styles = StyleSheet.create({
  imageContainer: {
    height: '100%',
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  container: {
    marginHorizontal: UniformSpace.one,
    flexDirection: 'column',
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    textAlign: 'center',
  },
  logoContainer: {
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
  },
  logoSubContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: UniformSpace.one,
  },
  buttonContainer: {
    flexDirection: 'column',
    marginBottom: UniformSpace.one,
  },
  developerOptionsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignContent: 'center',
    marginTop: UniformSpace.four,
  },
});

export function Landing(): React.ReactElement {
  const nav = AppNav.root.useNavigation();
  const { isDevelopmentEnvironment } = useAppSession();

  const onGetStartedPressed = useCallback(() => {
    nav.navigate(Navigation.VolunteerInfo, {
      leftIcon: HeaderLeftIcon.Dismiss,
      navigateToTop: true,
    });
  }, [nav]);

  const [currentUser, setCurrentVolunteer] = useState<Volunteer | undefined>(undefined);
  const onGetContinueAsPressed = useCallback(() => {
    nav.navigate(Navigation.VolunteerInfo, {
      currentUser,
      leftIcon: HeaderLeftIcon.Dismiss,
      navigateToTop: true,
    });
  }, [currentUser, nav]);

  const strings = useI18n(
    {
      continue: 'landing.continue',
      getStarted: 'landing.get-started',
    },
    []
  );

  const isFocused = useIsFocused();

  useEffect(() => {
    getCurrentVolunteer().then(setCurrentVolunteer);
  }, [isFocused]);

  useEffect(() => {
    if (isFocused) {
      attemptDeleteGroups(isDevelopmentEnvironment).then((r) => {
        if (r.requireUpload) {
          nav.navigate(Navigation.UploadTickets);
        }
      });
    }
  }, [currentUser?.email, isFocused, nav, isDevelopmentEnvironment]);

  return (
    <ImageBackground
      style={styles.imageContainer}
      source={require('../../assets/images/LandingBackground.png')}
      resizeMethod={'scale'}
      resizeMode={'stretch'}
      testID={AppTestIDs.landingRoot}>
      <SafeAreaView style={styles.container}>
        {isDevelopmentEnvironment && (
          <View style={styles.developerOptionsContainer}>
            <DeveloperOptions icon={<IconDropper color="subtle" />} title="Developer Options" />
          </View>
        )}
        <View style={styles.logoContainer} />
        <View style={styles.logoContainer}>
          <View style={styles.logoSubContainer}>
            <LandingIcon />
          </View>
          <View style={styles.logoSubContainer}>
            <LandingText />
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <Button
            text={strings.getStarted}
            onPress={onGetStartedPressed}
            space={['halfBottom', 'halfTop']}
            testID={AppTestIDs.landingGetStarted}
          />
          {currentUser && (
            <Button
              text={strings.continue}
              onPress={onGetContinueAsPressed}
              space={['halfBottom', 'halfTop']}
              buttonType={'secondary'}
              testID={AppTestIDs.landingContinue}
            />
          )}
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
}

function LandingIcon({ style }: SvgProps): ReactElement {
  return (
    <Svg
      width="87"
      height="87"
      viewBox="0 0 87 87"
      fill="none"
      style={style}
      fillRule="evenodd"
      clipRule="evenodd">
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M83.0566 3.13963C83.4249 3.13963 83.7234 3.43815 83.7234 3.8064V12.5465C83.7234 13.4135 84.4262 14.1163 85.2932 14.1163C86.1602 14.1163 86.863 13.4135 86.863 12.5465V3.8064C86.863 1.70418 85.1588 0 83.0566 0H74.3165C73.4495 0 72.7467 0.702829 72.7467 1.56981C72.7467 2.4368 73.4495 3.13963 74.3165 3.13963H83.0566ZM3.13963 3.8064C3.13963 3.43815 3.43815 3.13963 3.8064 3.13963H12.5465C13.4135 3.13963 14.1163 2.4368 14.1163 1.56982C14.1163 0.702832 13.4135 2.99965e-06 12.5465 2.99965e-06H3.8064C1.70418 2.99965e-06 0 1.70419 0 3.8064V12.5465C0 13.4135 0.702831 14.1163 1.56982 14.1163C2.4368 14.1163 3.13963 13.4135 3.13963 12.5465V3.8064ZM3.13963 82.881C3.13963 83.2493 3.43815 83.5478 3.8064 83.5478H12.5465C13.4135 83.5478 14.1163 84.2506 14.1163 85.1176C14.1163 85.9846 13.4135 86.6874 12.5465 86.6874H3.8064C1.70418 86.6874 0 84.9832 0 82.881V74.1409C0 73.2739 0.702829 72.5711 1.56981 72.5711C2.4368 72.5711 3.13963 73.2739 3.13963 74.1409V82.881ZM83.7234 82.881C83.7234 83.2493 83.4249 83.5478 83.0566 83.5478H74.3165C73.4495 83.5478 72.7467 84.2506 72.7467 85.1176C72.7467 85.9846 73.4495 86.6874 74.3165 86.6874H83.0566C85.1588 86.6874 86.863 84.9832 86.863 82.881V74.1409C86.863 73.2739 86.1602 72.5711 85.2932 72.5711C84.4262 72.5711 83.7234 73.2739 83.7234 74.1409V82.881ZM73.1532 15.3002C73.1532 14.3734 72.4019 13.622 71.475 13.622H15.4926C14.5657 13.622 13.8143 14.3734 13.8143 15.3002V32.9797C13.8143 33.7896 14.4 34.4684 15.1651 34.7341C18.7752 35.9876 21.3665 39.4192 21.3665 43.4561C21.3665 47.4929 18.7752 50.9245 15.1651 52.1781C14.4 52.4437 13.8143 53.1225 13.8143 53.9324V71.2826C13.8143 72.2095 14.5657 72.9609 15.4926 72.9609H71.475C72.4019 72.9609 73.1532 72.2095 73.1532 71.2826V53.8477C73.1532 53.0545 72.5906 52.385 71.8474 52.1079C68.3393 50.8002 65.8408 47.4199 65.8408 43.456C65.8408 39.4922 68.3393 36.1119 71.8474 34.8042C72.5906 34.5271 73.1532 33.8576 73.1532 33.0644V15.3002ZM49.9584 47.0962C49.9754 47.1185 50.0022 47.131 50.0303 47.1296L50.059 47.1246C52.2063 46.3713 54.1002 45.0327 55.5269 43.2601C55.5388 43.2453 55.5454 43.2271 55.5457 43.2082L55.5517 43.1136C55.5585 43.0643 55.5585 43.0149 55.5585 42.9646C55.5577 42.4253 55.5207 41.8865 55.4478 41.3521C55.444 41.3246 55.4539 41.2969 55.4743 41.278C56.8546 39.9825 57.3024 37.9749 56.6034 36.2157C55.9045 34.4565 54.2009 33.3036 52.3079 33.3088C51.8717 33.3092 51.438 33.3735 51.0205 33.4995C51.012 33.5007 51.0034 33.5007 50.995 33.4995C50.9755 33.4996 50.9566 33.4929 50.9413 33.4807C47.9995 31.1509 44.1366 30.3327 40.5028 31.2697C40.4638 31.2792 40.4369 31.3147 40.4381 31.3548C40.4375 31.3966 40.4675 31.4328 40.5088 31.4399C43.0462 31.9167 45.3695 33.1792 47.1499 35.049C47.4617 35.379 47.7558 35.7251 48.0312 36.086C48.0499 36.1103 48.0541 36.1428 48.0422 36.1711C47.8139 36.729 47.6982 37.3265 47.7016 37.9293C47.7113 39.7198 48.7523 41.3443 50.3751 42.1012C50.4026 42.1137 50.4215 42.14 50.4245 42.1701C50.5947 43.8019 50.4316 45.4512 49.9452 47.018C49.9364 47.0447 49.9414 47.0739 49.9584 47.0962ZM34.8588 51.3994C34.8749 51.4159 34.897 51.4251 34.9201 51.4249C34.9484 51.426 34.9754 51.413 34.9922 51.3901C35.009 51.3672 35.0131 51.3375 35.0035 51.3108C34.145 48.8762 34.0741 46.2333 34.8009 43.7563C34.9209 43.3434 35.0648 42.933 35.2266 42.5362C35.2381 42.5076 35.2641 42.4874 35.2947 42.4835C37.5618 42.156 39.2493 40.2208 39.2649 37.9302C39.2647 37.7743 39.2565 37.6186 39.2402 37.4636C39.2368 37.433 39.2502 37.4031 39.275 37.3852C40.6175 36.4003 42.1488 35.703 43.7731 35.3367C43.8052 35.3297 43.8304 35.3049 43.8378 35.2729C43.8456 35.2416 43.8351 35.2087 43.8106 35.1877C42.0804 33.6975 39.9669 32.7223 37.7102 32.373C37.6956 32.3728 37.6812 32.3763 37.6684 32.3832C37.0924 32.7019 36.544 33.0678 36.0286 33.4773C36.0068 33.4948 35.9779 33.5004 35.9511 33.4926C33.9026 32.8894 31.7078 33.7719 30.6472 35.6254C29.5866 37.4789 29.9377 39.8182 31.4957 41.2787C31.516 41.2976 31.5259 41.3253 31.5221 41.3528C31.018 45.0378 32.2503 48.7482 34.8588 51.3994ZM39.0392 54.2193C39.5393 56.2938 41.3966 57.7549 43.5305 57.7526C45.6691 57.7504 47.5248 56.2763 48.0109 54.1936C48.0172 54.1666 48.0363 54.1444 48.062 54.134C51.4967 52.7077 54.0903 49.7886 55.1024 46.21C55.1136 46.1714 55.0962 46.1302 55.0607 46.1112C55.0481 46.1045 55.0341 46.1009 55.0198 46.101C54.9946 46.1008 54.9707 46.1117 54.9543 46.1308C53.2742 48.0957 51.0178 49.4815 48.5056 50.0916C48.0867 50.192 47.6559 50.2729 47.2353 50.33H47.225C47.1984 50.3302 47.1732 50.3179 47.1569 50.2968C45.6363 48.3476 42.848 47.947 40.8401 49.3892C40.8255 49.3998 40.8079 49.4054 40.7899 49.4054C40.7779 49.4055 40.766 49.4032 40.7549 49.3985C39.2344 48.7298 37.8676 47.7554 36.7396 46.5361C36.7213 46.5177 36.6956 46.5085 36.6698 46.5114H36.6528C36.6216 46.5203 36.5982 46.546 36.5923 46.5778C36.1699 48.8116 36.3796 51.119 37.1977 53.2401C37.2045 53.2579 37.2171 53.2729 37.2334 53.2827C37.7937 53.6219 38.3806 53.9153 38.9882 54.1597C39.0139 54.1701 39.0329 54.1923 39.0392 54.2193Z"
        fill="white"
      />
    </Svg>
  );
}

function LandingText({ style }: SvgProps): ReactElement {
  return (
    <Svg
      width="231"
      height="76"
      viewBox="0 0 231 76"
      fill="none"
      style={style}
      fillRule="evenodd"
      clipRule="evenodd">
      <Path
        d="M19.2276 6.50746H11.4676V34.5298H7.76003V6.50746H0V3.31723H19.2276V6.50746Z"
        fill="white"
      />
      <Path
        d="M25.8978 4.39501C25.8978 5.60213 24.8631 6.59368 23.6129 6.59368C22.3626 6.59368 21.328 5.60213 21.328 4.39501C21.328 3.18789 22.3626 2.15322 23.6129 2.15322C24.8631 2.15322 25.8978 3.18789 25.8978 4.39501ZM25.4235 34.5298H21.8453V11.2928H25.4235V34.5298Z"
        fill="white"
      />
      <Path
        d="M44.6202 33.4089C43.0682 34.4867 41.0419 35.004 38.8433 35.004C33.8855 35.004 28.8846 31.4258 28.8846 22.8897C28.8846 14.3537 33.8855 10.8186 38.8433 10.8186C41.0419 10.8186 43.0682 11.3359 44.6202 12.4137L43.3699 15.0435C42.2491 14.2675 40.8264 13.8364 39.2313 13.8364C35.9117 13.8364 32.5921 16.3368 32.5921 22.8466C32.5921 29.3564 35.9548 31.9431 39.2313 31.9431C40.8264 31.9431 42.2491 31.4689 43.3699 30.6929L44.6202 33.4089Z"
        fill="white"
      />
      <Path
        d="M66.131 34.5298H61.863L51.1714 22.588H51.0852V34.5298H47.55V0.687439H51.0852V21.9413H51.1714L61.2163 11.2928H65.5275L55.0945 22.2862L66.131 34.5298Z"
        fill="white"
      />
      <Path
        d="M85.3586 20.7773C85.3586 21.2515 85.3586 21.7689 85.3155 22.3293L69.4506 24.6142C69.8386 29.572 72.8132 32.0724 77.2537 32.0724C79.5817 32.0724 82.0391 31.512 83.5911 30.6498L84.712 33.3658C83.0306 34.3142 80.1422 35.004 77.0813 35.004C70.1404 35.004 65.9586 30.5204 65.9586 22.8897C65.9586 15.7333 69.7955 10.8186 76.0897 10.8186C81.8235 10.8186 85.3586 14.5693 85.3586 20.7773ZM81.9097 20.3031C81.8666 15.7333 79.5386 13.5777 76.0466 13.5777C71.951 13.5777 69.3212 16.8542 69.235 22.1137L81.9097 20.3031Z"
        fill="white"
      />
      <Path
        d="M98.4571 34.4005C97.9828 34.616 97.0775 34.7885 96.0859 34.7885C92.2921 34.7885 89.9641 32.7191 89.9641 28.4942V14.0088H86.6015V11.2928H89.9641V5.68835L93.4561 4.52434V11.2928H98.4571V14.0088H93.4561V28.3218C93.4561 30.6929 95.0513 31.5982 96.9482 31.5982C97.5086 31.5982 98.1122 31.512 98.4571 31.3827V34.4005Z"
        fill="white"
      />
      <Path
        d="M131.962 34.5298H127.995L123.124 24.3555C122.046 22.0706 120.451 20.8204 117.821 20.8204H114.631V34.5298H111.01V4.52434C113.208 3.48967 116.355 2.843 119.416 2.843C126.659 2.843 130.41 6.46435 130.41 11.6808C130.41 16.6386 127.349 19.0097 123.21 19.7426V19.8288C124.805 20.6048 125.926 22.1137 126.874 24.0538L131.962 34.5298ZM126.788 11.8101C126.788 8.14569 124.417 5.77457 119.459 5.77457C117.649 5.77457 115.752 6.07635 114.631 6.55057V18.0182H118.726C123.727 18.0182 126.788 16.3368 126.788 11.8101Z"
        fill="white"
      />
      <Path
        d="M152.265 20.7773C152.265 21.2515 152.265 21.7689 152.222 22.3293L136.357 24.6142C136.745 29.572 139.72 32.0724 144.16 32.0724C146.488 32.0724 148.946 31.512 150.498 30.6498L151.619 33.3658C149.937 34.3142 147.049 35.004 143.988 35.004C137.047 35.004 132.865 30.5204 132.865 22.8897C132.865 15.7333 136.702 10.8186 142.996 10.8186C148.73 10.8186 152.265 14.5693 152.265 20.7773ZM148.816 20.3031C148.773 15.7333 146.445 13.5777 142.953 13.5777C138.858 13.5777 136.228 16.8542 136.142 22.1137L148.816 20.3031Z"
        fill="white"
      />
      <Path
        d="M171.659 33.3658C170.021 34.2711 166.701 35.004 163.64 35.004C157.26 35.004 154.07 32.288 154.07 27.8907C154.07 21.2515 161.226 20.26 168.253 19.872V18.1044C168.253 14.6555 165.968 13.5777 162.735 13.5777C160.579 13.5777 157.906 14.2675 156.398 15.0004L155.449 12.4568C157.217 11.6377 160.148 10.8186 163.037 10.8186C168.253 10.8186 171.659 12.8879 171.659 18.4062V33.3658ZM168.253 31.5551V22.3724C163.08 22.6742 157.475 22.976 157.475 27.8044C157.475 30.5204 159.502 32.288 163.554 32.288C165.322 32.288 167.434 31.9431 168.253 31.5551Z"
        fill="white"
      />
      <Path
        d="M193.444 33.3658C192.064 34.1849 189.047 35.004 185.986 35.004C178.916 35.004 174.949 30.3911 174.949 23.1915C174.949 16.0782 178.743 11.0773 185.684 11.0773C187.279 11.0773 189.004 11.379 189.866 11.6808V0.687439H193.444V33.3658ZM189.866 31.3396V14.483C189.176 14.1813 187.365 13.9226 185.943 13.9226C181.071 13.9226 178.528 17.8457 178.528 23.1915C178.528 28.5804 181.157 32.0724 185.943 32.0724C187.408 32.0724 189.219 31.6844 189.866 31.3396Z"
        fill="white"
      />
      <Path
        d="M216.107 20.7773C216.107 21.2515 216.107 21.7689 216.064 22.3293L200.199 24.6142C200.587 29.572 203.562 32.0724 208.002 32.0724C210.33 32.0724 212.787 31.512 214.339 30.6498L215.46 33.3658C213.779 34.3142 210.891 35.004 207.83 35.004C200.889 35.004 196.707 30.5204 196.707 22.8897C196.707 15.7333 200.544 10.8186 206.838 10.8186C212.572 10.8186 216.107 14.5693 216.107 20.7773ZM212.658 20.3031C212.615 15.7333 210.287 13.5777 206.795 13.5777C202.699 13.5777 200.07 16.8542 199.983 22.1137L212.658 20.3031Z"
        fill="white"
      />
      <Path
        d="M230.147 10.991L229.457 14.0088C228.854 13.8795 228.121 13.8364 227.388 13.8364C225.793 13.8364 224.068 14.095 222.99 14.5693V34.5298H219.369V12.4568C221.481 11.5084 224.672 10.8186 227.862 10.8186C228.724 10.8186 229.5 10.9048 230.147 10.991Z"
        fill="white"
      />
    </Svg>
  );
}
