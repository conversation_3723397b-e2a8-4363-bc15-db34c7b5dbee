import React from 'react';

import { ApolloError, NetworkStatus } from '@apollo/client';
import { fireEvent, screen } from '@testing-library/react-native';

import { PurchaserList } from './PurchaserList';
import useGetAllAssociatedTicketGroupsForTicketedEventId from '../../../gql/hooks/useGetAllAssociatedTicketGroupsForTicketedEventId';
import { mockEventInfo, mockOrganization, mockVolunteerInfo } from '../../../gql/mockData/TestData';
import { TicketGroup } from '../../../gql/public/__generated__/graphql';
import { renderWithOptions } from '../../../test/renderHelpers';
import { setupAccessContextMocks } from '../../../utils/TestUtils';
import { AppTestIDs } from '../../AppTestIDs';

const mockTicketGroups: TicketGroup[] = [
  {
    id: '1',
    tickets: [],
    passes: [],
    createdAt: '2021-08-01T00:00:00Z',
    updatedAt: '2021-08-01T00:00:00Z',
    lastName: 'Doe',
    firstName: 'John',
    email: '<EMAIL>',
  },
  {
    id: '2',
    tickets: [],
    passes: [],
    createdAt: '2021-08-01T00:00:00Z',
    updatedAt: '2021-08-01T00:00:00Z',
    lastName: 'Doe',
    firstName: 'Jane',
    email: '<EMAIL>',
  },
];

const mockTicketGroupsForEventGqlResult = {
  ticketGroups: mockTicketGroups,
  ticketGroupsLoading: false,
  ticketGroupError: undefined,
  ticketGroupNetworkStatus: NetworkStatus.ready,
  refetchTicketGroups: jest.fn(),
  fetchMoreTicketGroups: jest.fn(),
};

jest.mock('../../../gql/hooks/useGetAllAssociatedTicketGroupsForTicketedEventId');

const mockUseGetAllAssociatedTicketGroupsForTicketedEventId =
  useGetAllAssociatedTicketGroupsForTicketedEventId as jest.MockedFunction<
    typeof useGetAllAssociatedTicketGroupsForTicketedEventId
  >;

const mockParams = {
  volunteerInfo: mockVolunteerInfo,
  event: mockEventInfo,
  organization: mockOrganization,
};

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    dispatch: jest.fn(),
    addListener: jest.fn(),
  }),
  useRoute: () => ({
    params: mockParams,
  }),
}));

jest.mock('../../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../queries/useAccessCodeHook');
setupAccessContextMocks();

describe('PurchaserList Tests', () => {
  it('Renders empty state', () => {
    mockUseGetAllAssociatedTicketGroupsForTicketedEventId.mockImplementation(() => ({
      ...mockTicketGroupsForEventGqlResult,
      ticketGroups: [],
    }));

    renderWithOptions(<PurchaserList />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(mockUseGetAllAssociatedTicketGroupsForTicketedEventId).toHaveBeenCalled();
    expect(screen.getByTestId(AppTestIDs.purchaserListEmptyState)).toBeDefined();
  });

  it('Renders error state', () => {
    mockUseGetAllAssociatedTicketGroupsForTicketedEventId.mockImplementation(() => ({
      ...mockTicketGroupsForEventGqlResult,
      ticketGroupError: new ApolloError({ errorMessage: 'error' }),
    }));

    renderWithOptions(<PurchaserList />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(mockUseGetAllAssociatedTicketGroupsForTicketedEventId).toHaveBeenCalled();
    expect(screen.getByTestId(AppTestIDs.purchaserListErrorState)).toBeDefined();
  });

  it('Renders purchasers', () => {
    mockUseGetAllAssociatedTicketGroupsForTicketedEventId.mockImplementation(() => ({
      ...mockTicketGroupsForEventGqlResult,
    }));

    renderWithOptions(<PurchaserList />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(mockUseGetAllAssociatedTicketGroupsForTicketedEventId).toHaveBeenCalled();
    expect(screen.getByTestId(AppTestIDs.purchaserListSearchInput)).toBeDefined();
    expect(screen.getByText('<EMAIL>')).toBeDefined();
    expect(screen.getByText('Doe, Jane')).toBeDefined();
  });

  it('Filters purchasers on change', () => {
    mockUseGetAllAssociatedTicketGroupsForTicketedEventId.mockImplementation(() => ({
      ...mockTicketGroupsForEventGqlResult,
    }));

    renderWithOptions(<PurchaserList />, {
      withNavigationContainer: true,
      withAccessContext: true,
    });

    expect(mockUseGetAllAssociatedTicketGroupsForTicketedEventId).toHaveBeenCalled();
    expect(screen.getByTestId(AppTestIDs.purchaserListSearchInput)).toBeDefined();
    fireEvent.changeText(screen.getByTestId(AppTestIDs.purchaserListSearchInput), 'hudl');
    expect(screen.queryByText('Doe, Jane')).toBeNull();
    expect(screen.getByText('Doe, John')).toBeTruthy();
  });
});
