/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { ReactElement, useCallback, useMemo, useState } from 'react';
import {
  FlatList,
  Keyboard,
  ListRenderItem,
  RefreshControl,
  TouchableWithoutFeedback,
  View,
} from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import {
  IconCritical,
  IconInformation,
  SearchInput,
  Text,
  useUniformStyles,
  Button,
  Spinner,
} from '@hudl/rn-uniform';

import { styleSheet } from './PurchaserList.style';
import { useAccessContext } from '../../../context/AccessContext';
import useGetAllAssociatedTicketGroupsForTicketedEventId from '../../../gql/hooks/useGetAllAssociatedTicketGroupsForTicketedEventId';
import {
  GetAssociatedTicketGroupsWithAccessCodeInput,
  TicketGroup,
  TicketGroupSortType,
} from '../../../gql/public/__generated__/graphql';
import { Source } from '../../../utils/Enums';
import { useKeyboard } from '../../../utils/useKeyboardHook';
import { AppTestIDs } from '../../AppTestIDs';
import ContactSupportText from '../../ContactSupportText/ContactSupportText';
import { PurchaserListItem } from '../../PurchaserListItem/PurchaserListItem';

export function PurchaserList(): ReactElement {
  const styles = useUniformStyles(styleSheet);
  const [pullToRefreshing, setPullToRefreshing] = useState(false);
  const [paginationLoading, setPaginationLoading] = useState(false);
  const accessContext = useAccessContext();
  const { volunteerInfo, eventInfo } = accessContext;

  const keyboard = useKeyboard();

  const i18nStrings = useI18n(
    {
      subHeader: 'purchaser-list.sub-header',
      searchName: 'purchaser-list.search-name',
      noPurchasers: 'purchaser-list.no-purchasers',
      error: 'purchaser-list.error-message',
      reload: 'purchaser-list.reload',
      noResultsFound: 'purchaser-list.no-results-found',
    },
    []
  );
  const [searchText, setSearchText] = React.useState('');

  const input: GetAssociatedTicketGroupsWithAccessCodeInput = useMemo(
    () => ({
      ticketedEventId: eventInfo?.id ?? '',
      organizationId: eventInfo?.organizationId ?? '',
      accessCode: volunteerInfo?.accessCode ?? '',
      sortByAscending: true,
      sortType: TicketGroupSortType.TicketGroupPurchaserLastName,
      validSources: [Source.Web, Source.Complimentary, Source.Transfer],
    }),
    [eventInfo?.id, eventInfo?.organizationId, volunteerInfo?.accessCode]
  );

  const getPaginationInput = (endCursor: string): GetAssociatedTicketGroupsWithAccessCodeInput => {
    return {
      ...input,
      after: endCursor,
    };
  };

  const {
    ticketGroups,
    ticketGroupsLoading,
    ticketGroupError,
    refetchTicketGroups,
    fetchMoreTicketGroups,
  } = useGetAllAssociatedTicketGroupsForTicketedEventId(input, {
    notifyOnNetworkStatusChange: true,
    fetchPolicy: 'network-only',
    onCompleted: (data) => {
      const paginationInput = getPaginationInput(
        data?.associatedTicketGroupsForTicketedEventId?.pageInfo?.endCursor
      );
      setPaginationLoading(true);
      if (data?.associatedTicketGroupsForTicketedEventId?.pageInfo?.hasNextPage) {
        fetchMoreTicketGroups({
          variables: {
            input: paginationInput,
          },
          updateQuery: (prev: any, { fetchMoreResult }: { fetchMoreResult: any }) => {
            if (!fetchMoreResult) {
              return prev;
            }
            return {
              associatedTicketGroupsForTicketedEventId: {
                items: [
                  ...prev.associatedTicketGroupsForTicketedEventId.items,
                  ...fetchMoreResult.associatedTicketGroupsForTicketedEventId.items,
                ],
                pageInfo: fetchMoreResult.associatedTicketGroupsForTicketedEventId.pageInfo,
              },
            };
          },
        });
      } else {
        setTimeout(() => {
          pullToRefreshing && setPullToRefreshing(false);
        }, 500);
        setPaginationLoading(false);
      }
    },
  });

  // Logic to remove duplicate purchasers is in place due to an issue with pagination where duplicate documents are being returned.
  // The duplication is not consistent, but when it occurs, it causes issues with items being rendered in a Flat List due to the key prop.
  // It was out of scope to investigate the root cause of the duplication, so this is a temporary fix to release the purchaser list.
  const uniqueTicketGroupMap =
    ticketGroups && new Map(ticketGroups?.map((item) => [item.id, item]));

  const uniqueTicketGroups = ticketGroups && Array.from(uniqueTicketGroupMap.values());

  const filteredPurchaserList = uniqueTicketGroups?.filter((purchaser) => {
    const fullName = `${purchaser.firstName} ${purchaser.lastName}`.toLowerCase();
    const email = purchaser?.email?.toLowerCase();
    if (
      searchText === '' ||
      fullName.includes(searchText.toLowerCase()) ||
      email?.includes(searchText.toLowerCase())
    ) {
      return purchaser;
    }
    return;
  });

  const onSearchInputChange = useCallback((e: string) => {
    setSearchText(e);
  }, []);

  const onPullToRefresh = useCallback((): void => {
    refetchTicketGroups();
    setPullToRefreshing(true);
  }, [refetchTicketGroups]);

  const onReload = useCallback((): void => {
    refetchTicketGroups();
  }, [refetchTicketGroups]);

  const renderPurchaserListItem = useCallback<ListRenderItem<TicketGroup>>(({ item }) => {
    return <PurchaserListItem key={item.id} ticketGroup={item} />;
  }, []);

  const renderLoadingFooter = useCallback(() => {
    return (
      <View style={styles.loadingFooter} testID={AppTestIDs.loadingOrgBackButton}>
        <Spinner />
      </View>
    );
  }, [styles.loadingFooter]);

  const renderItemSeparator = useCallback(() => {
    return <View style={styles.spacer} />;
  }, [styles.spacer]);

  const renderEmptyListComponent = useCallback(() => {
    return searchText ? (
      <View style={styles.emptySearchContainer} testID={AppTestIDs.purchaserListNoResultsFound}>
        <Text alignment="center">{i18nStrings.noResultsFound}</Text>
      </View>
    ) : null;
  }, [searchText, i18nStrings.noResultsFound, styles.emptySearchContainer]);

  const dismissKeyboard = useCallback(() => {
    Keyboard.dismiss();
  }, []);

  if ((ticketGroupsLoading && !pullToRefreshing) || (paginationLoading && !pullToRefreshing)) {
    return (
      <View style={styles.emptyListContainer}>
        <Spinner />
      </View>
    );
  }

  if (ticketGroupError) {
    return (
      <View style={styles.emptyListContainer} testID={AppTestIDs.purchaserListErrorState}>
        <IconCritical size="large" color="critical" />
        <ContactSupportText
          testIds={[
            AppTestIDs.purchaserListContactSupport,
            AppTestIDs.purchaserListContactSupportLink,
          ]}
          supportTextArray={i18nStrings.error}
          textStyle={styles.contactSupportTextStyle}
        />
        <Button onPress={onReload} text={i18nStrings.reload} buttonType="secondary" />
      </View>
    );
  }

  if (ticketGroups.length === 0) {
    return (
      <View style={styles.emptyListContainer} testID={AppTestIDs.purchaserListEmptyState}>
        <IconInformation size="large" />
        <Text alignment="center">{i18nStrings.noPurchasers}</Text>
      </View>
    );
  }

  return (
    <TouchableWithoutFeedback
      onPress={dismissKeyboard}
      disabled={!keyboard.isVisible}
      style={styles.purchaserListTouchContainer}
      testID={AppTestIDs.purchaserListContainer}>
      <View style={styles.purchaserListContainer}>
        <Text>{i18nStrings.subHeader}</Text>
        <View style={styles.searchInput}>
          <SearchInput
            placeholder={i18nStrings.searchName}
            onChangeText={onSearchInputChange}
            testID={AppTestIDs.purchaserListSearchInput}
          />
        </View>
        <View style={styles.itemContainer}>
          <FlatList
            data={filteredPurchaserList}
            renderItem={renderPurchaserListItem}
            ItemSeparatorComponent={renderItemSeparator}
            ListEmptyComponent={renderEmptyListComponent}
            ListFooterComponent={
              ticketGroupsLoading && pullToRefreshing
                ? renderLoadingFooter()
                : renderItemSeparator()
            }
            refreshControl={
              <RefreshControl
                refreshing={pullToRefreshing}
                onRefresh={onPullToRefresh}
                tintColor="#fff"
              />
            }
          />
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
