import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  purchaserListTouchContainer: {
    height: '100%',
    width: '100%',
    display: 'flex',
  },
  purchaserListContainer: {
    height: '100%',
    width: '100%',
    padding: UniformSpace.one,
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.one,
  },
  searchInput: {
    display: 'flex',
    height: 40,
  },
  itemContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.one,
    height: '90%',
    paddingBottom: UniformSpace.four,
  },
  emptyListContainer: {
    height: '100%',
    width: '100%',
    padding: UniformSpace.one,
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.one,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactSupportTextStyle: {
    textAlign: 'center',
    color: colors.utilityCritical,
  },
  loadingFooter: {
    width: '100%',
    minHeight: 300,
    justifyContent: 'center',
    flexDirection: 'row',
  },
  spacer: {
    marginBottom: UniformSpace.one,
  },
  emptySearchContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: UniformSpace.two,
    paddingHorizontal: UniformSpace.one,
  },
}));
