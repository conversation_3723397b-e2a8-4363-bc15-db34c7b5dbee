import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../utils/ColorUtils';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  count: {
    marginRight: UniformSpace.half,
    marginLeft: UniformSpace.half,
    display: 'flex',
    justifyContent: 'center',
    color: colors.baseWhite,
    fontSize: 14,
    fontWeight: '700',
    minWidth: 30,
    paddingLeft: UniformSpace.half,
    paddingRight: UniformSpace.half,
    textAlign: 'center',
  },
  countBlurred: {
    color: colors.contentNonessential,
  },
  countDisabled: {
    color: colors.contentNonessential,
    borderColor: colors.contentNonessential,
  },
  counterContainer: {
    display: 'flex',
    flexDirection: 'row',
  },
  plusButton: {
    backgroundColor: nonUniformColors.contentEmphasesBgContrast,
  },
}));
