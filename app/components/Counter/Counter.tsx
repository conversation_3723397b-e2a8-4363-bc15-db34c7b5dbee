import React, { ReactElement, useCallback, useState } from 'react';
import { StyleProp, TextInput, TextStyle, View } from 'react-native';

import { Button, IconAdd, IconSubtract, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './Counter.style';
import { CounterAction } from '../../utils/Enums';
import { AppTestIDs } from '../AppTestIDs';

export type CounterProps = {
  itemId: string;
  onCountChange: (newCount: number) => void;
  count: number;
  remaining: number;
  isDisabled: boolean;
};

function Counter(props: CounterProps): ReactElement {
  const { onCountChange, count, remaining, itemId, isDisabled } = props;
  const styles = useUniformStyles(styleSheet);
  const [inputFocused, setInputFocused] = useState(false);

  const onIncDec = useCallback(
    (action: CounterAction): (() => void) => {
      return (): void => {
        switch (action) {
          case CounterAction.Increment:
            onCountChange(count + 1);
            break;
          case CounterAction.Decrement:
            onCountChange(count - 1);
            break;
        }
      };
    },
    [count, onCountChange]
  );

  const onCountTyping = useCallback(
    (newCount: string): void => {
      const newCountInt = +newCount;
      if (isNaN(newCountInt)) {
        onCountChange(0);
        return;
      }
      const maxSettableForLineItem = remaining + count;

      if (newCountInt > maxSettableForLineItem) {
        onCountChange(maxSettableForLineItem);
        return;
      }
      onCountChange(newCountInt);
    },
    [count, onCountChange, remaining]
  );

  const handleOnFocusChange = useCallback((isFocused: boolean): (() => void) => {
    return (): void => {
      setInputFocused(isFocused);
    };
  }, []);

  const decrementDisabled = isDisabled || count === 0;
  const incrementDisabled = isDisabled || remaining === 0;
  const inputDisabled = isDisabled || (count === 0 && remaining === 0);

  const getInputStyles = useCallback((): StyleProp<TextStyle>[] => {
    if (inputDisabled) {
      return [styles.count, styles.countDisabled];
    }
    if (count === 0 && !inputFocused) {
      return [styles.count, styles.countBlurred];
    }
    return [styles.count];
  }, [count, inputDisabled, inputFocused, styles.count, styles.countBlurred, styles.countDisabled]);

  const getCountValue = useCallback((): string => {
    const defaultValue = inputFocused ? '' : '0';
    return count > 0 ? count.toString() : defaultValue;
  }, [count, inputFocused]);

  const countDigitLength = count.toString().length;

  return (
    <View style={styles.counterContainer} testID={AppTestIDs.counterRoot(itemId)}>
      <Button
        buttonType="subtle"
        icon={<IconSubtract size="small" />}
        isDisabled={decrementDisabled}
        onPress={onIncDec(CounterAction.Decrement)}
        testID={AppTestIDs.counterDecrement(itemId, decrementDisabled)}
      />
      <TextInput
        keyboardType="number-pad"
        inputMode="numeric"
        style={getInputStyles()}
        value={getCountValue()}
        autoComplete={'off'}
        autoCapitalize={'none'}
        autoCorrect={false}
        editable={!inputDisabled}
        returnKeyType="done"
        onChangeText={onCountTyping}
        testID={AppTestIDs.counterInput(itemId, inputDisabled)}
        onFocus={handleOnFocusChange(true)}
        onBlur={handleOnFocusChange(false)}
        selection={{ start: countDigitLength, end: countDigitLength }}
      />
      <Button
        icon={<IconAdd size="small" />}
        onPress={onIncDec(CounterAction.Increment)}
        isDisabled={incrementDisabled}
        testID={AppTestIDs.counterIncrement(itemId, incrementDisabled)}
        style={styles.plusButton}
      />
    </View>
  );
}

export default Counter;
