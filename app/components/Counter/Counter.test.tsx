import React from 'react';

import { fireEvent, screen } from '@testing-library/react-native';

import Counter from './Counter';
import { renderWithOptions } from '../../test/renderHelpers';
import { AppTestIDs } from '../AppTestIDs';

describe('Counter', () => {
  it('Counter displays correctly', async () => {
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={jest.fn()}
        count={1}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.counterRoot(itemId)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterInput(itemId, false)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterIncrement(itemId, false)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterDecrement(itemId, false)}`)).toBeTruthy();
  });

  it('Counter displays correctly when disabled', async () => {
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={jest.fn()}
        count={1}
        remaining={10}
        isDisabled={true}
      />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.counterRoot(itemId)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterInput(itemId, true)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterIncrement(itemId, true)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterDecrement(itemId, true)}`)).toBeTruthy();
  });

  it('Counter displays correctly when remaining is 0', async () => {
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={jest.fn()}
        count={1}
        remaining={0}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.counterRoot(itemId)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterInput(itemId, false)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterIncrement(itemId, true)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterDecrement(itemId, false)}`)).toBeTruthy();
  });

  it('Counter displays correctly when count is 0', async () => {
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={jest.fn()}
        count={0}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.counterRoot(itemId)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterInput(itemId, false)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterIncrement(itemId, false)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterDecrement(itemId, true)}`)).toBeTruthy();
  });

  it('Counter displays correctly when count is 0 and remaining is 0', async () => {
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={jest.fn()}
        count={0}
        remaining={0}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    expect(screen.getByTestId(`${AppTestIDs.counterRoot(itemId)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterInput(itemId, true)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterIncrement(itemId, true)}`)).toBeTruthy();
    expect(screen.getByTestId(`${AppTestIDs.counterDecrement(itemId, true)}`)).toBeTruthy();
  });

  it('Counter calls onChange with count - 1 when decrement is pressed', async () => {
    const onChange = jest.fn();
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={onChange}
        count={1}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    fireEvent.press(screen.getByTestId(`${AppTestIDs.counterDecrement(itemId, false)}`));

    expect(onChange).toHaveBeenCalledWith(0);
  });

  it('Counter calls onChange with count + 1 when increment is pressed', async () => {
    const onChange = jest.fn();
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={onChange}
        count={1}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    fireEvent.press(screen.getByTestId(`${AppTestIDs.counterIncrement(itemId, false)}`));

    expect(onChange).toHaveBeenCalledWith(2);
  });

  it('Counter calls onChange with newCount when input is changed', async () => {
    const onChange = jest.fn();
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={onChange}
        count={1}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    fireEvent.changeText(screen.getByTestId(`${AppTestIDs.counterInput(itemId, false)}`), '5');

    expect(onChange).toHaveBeenCalledWith(5);
  });

  it('Counter calls onChange with 0 when input is changed to NaN', async () => {
    const onChange = jest.fn();
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={onChange}
        count={1}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    fireEvent.changeText(screen.getByTestId(`${AppTestIDs.counterInput(itemId, false)}`), 'a');

    expect(onChange).toHaveBeenCalledWith(0);
  });

  it('Counter calls onChange with remaining when input is changed to a number greater than remaining + count', async () => {
    const onChange = jest.fn();
    const itemId = 'testItem';
    renderWithOptions(
      <Counter
        itemId={itemId}
        onCountChange={onChange}
        count={1}
        remaining={10}
        isDisabled={false}
      />,
      {
        withNavigationContainer: false,
      }
    );

    fireEvent.changeText(screen.getByTestId(`${AppTestIDs.counterInput(itemId, false)}`), '15');

    expect(onChange).toHaveBeenCalledWith(11);
  });
});
