import React, { ReactElement } from 'react';
import { View } from 'react-native';

import { Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './ReaderDisplay.style';
import { ReaderMessage } from '../../types/shared';
import { AppTestIDs } from '../AppTestIDs';

type Props = {
  inputMessage: ReaderMessage;
};

export function ReaderDisplayInput(props: Props): ReactElement {
  const { inputMessage } = props;
  const styles = useUniformStyles(stylesheet);

  return (
    <View
      style={[styles.readerDisplayContainer, styles.readerDisplayBackground]}
      testID={AppTestIDs.readerDisplayInputRoot}>
      {inputMessage.icon}
      <Text alignment="center" testID={AppTestIDs.readerDisplayInputMessage}>
        {inputMessage.message}
      </Text>
    </View>
  );
}

export default ReaderDisplayInput;
