import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const stylesheet = UniformStyleSheet.create((colors) => ({
  readerDisplayContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: UniformSpace.one,
    width: '80%',
    paddingTop: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.oneAndHalf,
    padding: UniformSpace.two,
    borderRadius: UniformSpace.threeQuarter,
    marginLeft: 'auto',
    marginRight: 'auto',
    minHeight: 250,
    maxHeight: 250,
    textAlign: 'center',
  },
  readerDisplayBackground: {
    backgroundColor: colors.bgLevel0,
  },
}));
