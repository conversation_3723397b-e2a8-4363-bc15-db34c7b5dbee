import React, { ReactElement } from 'react';
import { View } from 'react-native';

import { Reader } from '@stripe/stripe-terminal-react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';
import { IconCritical, IconWarning, useUniformStyles, Text } from '@hudl/rn-uniform';

import { stylesheet } from './ReaderDisplay.style';
import IconCreditCard from '../../icons/IconCreditCard';
import IconRemoveCard from '../../icons/IconRemoveCard';
import { ReaderMessage } from '../../types/shared';
import { boldSubstring } from '../../utils/TextUtils';
import { AppTestIDs } from '../AppTestIDs';

type Props = {
  displayType: Reader.DisplayMessage;
};

// Pre-compute the bolded strings for each display message so that they aren't re-calculated on each render
const boldedMessages = {
  insertCard: boldSubstring('reader-display-message.insert-card', true),
  insertOrSwipeCard: boldSubstring('reader-display-message.insert-or-swipe-card', true),
  checkMobileDevice: <Text>{HudlI18n.t('reader-display-message.check-mobile-device')}</Text>,
  removeCard: boldSubstring('reader-display-message.remove-card', false),
  retryCard: boldSubstring('reader-display-message.retry-card', false),
  swipeCard: boldSubstring('reader-display-message.swipe-card', false),
  cardRemovedTooEarly: boldSubstring('reader-display-message.card-removed-too-early', false),
  multipleContactlessCardsDetected: boldSubstring(
    'reader-display-message.multiple-contactless-cards-detected',
    false
  ),
  tryAnotherCard: boldSubstring('reader-display-message.try-another-card', false),
  tryAnotherReadMethod: boldSubstring('reader-display-message.try-another-read-method', false),
};

export function ReaderDisplayMessage(props: Props): ReactElement {
  const { displayType } = props;
  const styles = useUniformStyles(stylesheet);

  const displayMessages: Record<Reader.DisplayMessage, ReaderMessage> = {
    insertCard: {
      icon: <IconCreditCard height={'96'} width={'96'} />,
      message: boldedMessages.insertCard,
      background: true,
    },
    insertOrSwipeCard: {
      icon: <IconCreditCard height={'96'} width={'96'} />,
      message: boldedMessages.insertOrSwipeCard,
      background: true,
    },
    checkMobileDevice: {
      icon: <IconCreditCard height={'96'} width={'96'} />,
      message: boldedMessages.checkMobileDevice,
    },
    removeCard: {
      icon: <IconRemoveCard height={'96'} width={'128'} />,
      message: boldedMessages.removeCard,
      background: true,
    },
    retryCard: {
      icon: <IconWarning height={'128'} width={'128'} color={'warning'} />,
      message: boldedMessages.retryCard,
    },
    swipeCard: {
      icon: <IconWarning height={'128'} width={'128'} color={'warning'} />,
      message: boldedMessages.swipeCard,
    },
    cardRemovedTooEarly: {
      icon: <IconWarning height={'128'} width={'128'} color={'warning'} />,
      message: boldedMessages.cardRemovedTooEarly,
    },
    multipleContactlessCardsDetected: {
      icon: <IconCritical height={'128'} width={'128'} color="critical" />,
      message: boldedMessages.multipleContactlessCardsDetected,
    },
    tryAnotherCard: {
      icon: <IconCritical height={'128'} width={'128'} color="critical" />,
      message: boldedMessages.tryAnotherCard,
    },
    tryAnotherReadMethod: {
      icon: <IconCritical height={'128'} width={'128'} color="critical" />,
      message: boldedMessages.tryAnotherReadMethod,
    },
  };

  const display = displayMessages[displayType];

  return (
    <View
      style={[
        styles.readerDisplayContainer,
        display.background ? styles.readerDisplayBackground : null,
      ]}
      testID={AppTestIDs.readerDisplayMessageRoot}>
      {display.icon}
      <Text alignment="center" testID={AppTestIDs.readerDisplayMessageMessage}>
        {display.message}
      </Text>
    </View>
  );
}

export default ReaderDisplayMessage;
