import React from 'react';

import { screen } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import ReaderDisplayMessage from './ReaderDisplayMessage';
import enUS from '../../strings/en-US.json';
import { renderWithOptions } from '../../test/renderHelpers';
import { AppTestIDs } from '../AppTestIDs';

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

describe('ReaderDisplayMessage tests', () => {
  it('Reader display message renders correctly', async () => {
    renderWithOptions(<ReaderDisplayMessage displayType={'insertCard'} />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.readerDisplayMessageRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.readerDisplayMessageMessage)).toBeTruthy();
  });
});
