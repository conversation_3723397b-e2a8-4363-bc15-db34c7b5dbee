import React from 'react';

import { screen } from '@testing-library/react-native';

import { IconCritical, Text } from '@hudl/rn-uniform';

import ReaderDisplayInput from './ReaderDisplayInput';
import { renderWithOptions } from '../../test/renderHelpers';
import { ReaderMessage } from '../../types/shared';
import { AppTestIDs } from '../AppTestIDs';

describe('ReaderDisplayInput tests', () => {
  it('Reader display input renders correctly', async () => {
    const displayMessage: ReaderMessage = {
      message: <Text>Test message</Text>,
      icon: <IconCritical testID="testing" />,
    };

    renderWithOptions(<ReaderDisplayInput inputMessage={displayMessage} />, {
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.readerDisplayInputRoot)).toBeTruthy();
    expect(screen.getByTestId('testing')).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.readerDisplayInputMessage)).toBeTruthy();
  });
});
