import { useMemo } from 'react';

import { useQuery } from '@apollo/client';

import {
  GetTicketedEventAccessCodeInput,
  TicketedEventAccessCode,
} from '../../gql/public/__generated__/graphql';
import GetAccessCode from '../../gql/public/queries/GetAccessCode';

export function useAccessCodeHook(input: GetTicketedEventAccessCodeInput): {
  accessCode: TicketedEventAccessCode | undefined;
  hasError: boolean;
  isLoading: boolean;
} {
  const { data, error, loading } = useQuery(GetAccessCode, {
    variables: {
      input: input,
    },
    skip: !input.accessCode,
  });

  const accessCodeWasNotFound = useMemo(() => {
    return data?.ticketedEventAccessCode === null;
  }, [data?.ticketedEventAccessCode]);

  return {
    accessCode: data?.ticketedEventAccessCode as TicketedEventAccessCode,
    hasError: error !== undefined || accessCodeWasNotFound,
    isLoading: loading,
  };
}
