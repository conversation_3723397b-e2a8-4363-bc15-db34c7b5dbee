import { ReactElement } from 'react';
import React from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';

import { useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './ListItemSeparator.style';

interface ListItemSeparatorProps {
  styleOverride: StyleProp<ViewStyle>;
}

export default function ListItemSeparator(props: ListItemSeparatorProps): ReactElement {
  const { styleOverride } = props;
  const styles = useUniformStyles(styleSheet);
  return <View style={[styles.spacer, styleOverride]} />;
}
