import React from 'react';
import { Platform } from 'react-native';

import { Directions, Gesture, GestureDetector } from 'react-native-gesture-handler';

export function ModalSwipeGestureHandler({
  children,
  iosFling,
  androidFling,
  hide,
}: React.PropsWithChildren & {
  hide: () => void;
  iosFling: boolean;
  androidFling: boolean;
}): React.ReactElement {
  const fling = Gesture.Fling()
    .direction(Directions.DOWN)
    .onEnd(() => {
      hide();
    })
    .runOnJS(true);

  if ((Platform.OS === 'android' && androidFling) || (Platform.OS === 'ios' && iosFling)) {
    return <GestureDetector gesture={fling}>{children}</GestureDetector>;
  } else {
    return <>{children}</>;
  }
}
