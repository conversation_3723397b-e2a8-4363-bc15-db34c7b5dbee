import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const generalUniformStyles = UniformStyleSheet.create((colors) => ({
  container: {
    backgroundColor: colors.bgLevel1,
  },
  headerText: {
    fontSize: 30,
    fontWeight: '300',
    lineHeight: 32,
    color: colors.contentContrast,
    paddingVertical: UniformSpace.one,
  },
  contentText: {
    fontSize: 16,
    fontWeight: '400',
    lineHeight: 24,
    color: colors.contentContrast,
    marginBottom: UniformSpace.two,
  },
  textInputTitle: {
    fontSize: 16,
    color: colors.contentContrast,
    paddingBottom: 4,
  },
  textReadDot: {
    fontSize: 20,
    color: colors.utilityCritical,
  },
  textInput: {
    height: 40,
    borderWidth: 1,
    borderColor: colors.keyboardShortcutBorder,
    borderRadius: 4,
    padding: 10,
    color: colors.contentContrast,
    backgroundColor: colors.bgLevel0,
  },
  accentHeaderContainer: {
    backgroundColor: colors.bgLevel0,
  },
}));
