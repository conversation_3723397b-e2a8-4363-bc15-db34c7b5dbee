import React, { ReactElement, useCallback } from 'react';
import { Text, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './OrderTotal.style';
import { LineItemType } from '../../enums/shared';
import { TicketingPricingSummary } from '../../gql/public/__generated__/graphql';
import { getFormattedPrice } from '../../utils/NumberFormatUtils';
import { AppTestIDs } from '../AppTestIDs';

export type OrderTotalProps = {
  totalItemCount: number;
  lineItemType: LineItemType;
  pricingSummary?: TicketingPricingSummary;
  pricingSummaryLoading?: boolean;
  isCustomSale?: boolean;
  showTotalPrice: boolean;
  subtotalPrice?: number;
};

function OrderTotal(props: OrderTotalProps): ReactElement {
  const {
    totalItemCount,
    lineItemType,
    pricingSummary,
    pricingSummaryLoading,
    isCustomSale,
    showTotalPrice,
    subtotalPrice,
  } = props;
  const styles = useUniformStyles(styleSheet);

  const i18nStrings = useI18n(
    {
      orderTotal: ['pos.order-total.order-total', { count: totalItemCount }],
      tickets: ['pos.order-total.tickets', { count: totalItemCount }],
      nonRefundable: 'pos.order-total.non-refundable',
      additionalFees: 'pos.order-total.additional-fees',
    },
    [totalItemCount]
  );

  const getOrderCountText = useCallback((): string => {
    return lineItemType === LineItemType.TicketType ? i18nStrings.tickets : '';
  }, [lineItemType, i18nStrings.tickets]);

  const renderPricing = useCallback(() => {
    if (pricingSummaryLoading) {
      return (
        <View style={styles.skeletonContainer} testID={AppTestIDs.orderTotalLoading}>
          <View style={styles.skeletonWide} />
        </View>
      );
    }

    let price = subtotalPrice ?? 0;
    if (pricingSummary?.totalInCents && pricingSummary?.subtotalInCents) {
      price = (showTotalPrice ? pricingSummary.totalInCents : pricingSummary.subtotalInCents) ?? 0;
    }

    return (
      <View style={styles.pricingContainer}>
        <Text style={styles.orderTotalText} testID={AppTestIDs.orderTotalPrice}>
          {getFormattedPrice(price)}
        </Text>
      </View>
    );
  }, [
    pricingSummary,
    pricingSummaryLoading,
    showTotalPrice,
    styles.orderTotalText,
    styles.pricingContainer,
    styles.skeletonContainer,
    styles.skeletonWide,
    subtotalPrice,
  ]);

  return (
    <View style={styles.container} testID={AppTestIDs.orderTotalRoot}>
      <View style={styles.ticketsAndPriceContainer}>
        <View style={styles.pricingContainer}>
          <View style={styles.ticketCountContainer}>
            <Text style={styles.order} testID={AppTestIDs.orderTotalNumberOfItems}>
              {isCustomSale
                ? i18nStrings.orderTotal
                : `${i18nStrings.orderTotal} ${getOrderCountText()}`}
            </Text>
          </View>
        </View>
        {renderPricing()}
      </View>
      <View style={styles.subtextWrapper}>
        <Text style={styles.subtext} testID={AppTestIDs.orderTotalSubtext}>
          {showTotalPrice ? i18nStrings.nonRefundable : i18nStrings.additionalFees}
        </Text>
      </View>
    </View>
  );
}

export default OrderTotal;
