import React from 'react';

import { render, screen } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import OrderTotal from './OrderTotal';
import { LineItemType } from '../../enums/shared';
import { TicketingPricingSummary } from '../../gql/public/__generated__/graphql';
import enUS from '../../strings/en-US.json';
import { AppTestIDs } from '../AppTestIDs';

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

const pricingSummary: TicketingPricingSummary = {
  totalInCents: 500,
  subtotalWithHudlFeeInCents: 400,
  paymentProcessingFeeInCents: 100,
  hudlFeeInCents: 50,
  subtotalInCents: 350,
  feesInCents: 150,
  shouldShowFee: true,
  currency: 'USD',
};

describe('Order total tests', () => {
  it('Total displays correctly with multiple tickets', async () => {
    render(
      <OrderTotal
        totalItemCount={5}
        lineItemType={LineItemType.TicketType}
        pricingSummary={pricingSummary}
        pricingSummaryLoading={false}
        isCustomSale={false}
        showTotalPrice={true}
      />
    );

    expect(screen.getByTestId(AppTestIDs.orderTotalRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 5 tickets'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$5.00');
    expect(screen.getByTestId(AppTestIDs.orderTotalSubtext).props.children).toEqual(
      'All sales are non-refundable.'
    );
  });

  it('Total displays correctly with single ticket', async () => {
    render(
      <OrderTotal
        totalItemCount={1}
        lineItemType={LineItemType.TicketType}
        pricingSummary={pricingSummary}
        pricingSummaryLoading={false}
        showTotalPrice={true}
        isCustomSale={false}
      />
    );

    expect(screen.getByTestId(AppTestIDs.orderTotalRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 1 ticket'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$5.00');
    expect(screen.getByTestId(AppTestIDs.orderTotalSubtext).props.children).toEqual(
      'All sales are non-refundable.'
    );
  });

  it('Total skeleton when pricing summary is loading', async () => {
    render(
      <OrderTotal
        totalItemCount={1}
        lineItemType={LineItemType.TicketType}
        pricingSummary={pricingSummary}
        pricingSummaryLoading={true}
        showTotalPrice={true}
        isCustomSale={false}
      />
    );

    expect(screen.getByTestId(AppTestIDs.orderTotalLoading)).toBeTruthy();
  });

  it('Is Custom Sale', async () => {
    render(
      <OrderTotal
        totalItemCount={5}
        lineItemType={LineItemType.TicketType}
        pricingSummary={pricingSummary}
        pricingSummaryLoading={false}
        isCustomSale={true}
        showTotalPrice={true}
      />
    );

    expect(screen.getByTestId(AppTestIDs.orderTotalRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 5'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$5.00');
    expect(screen.getByTestId(AppTestIDs.orderTotalSubtext).props.children).toEqual(
      'All sales are non-refundable.'
    );
  });

  it('Does Not Show Total Price', async () => {
    render(
      <OrderTotal
        totalItemCount={5}
        lineItemType={LineItemType.TicketType}
        pricingSummary={pricingSummary}
        pricingSummaryLoading={false}
        isCustomSale={false}
        showTotalPrice={false}
      />
    );

    expect(screen.getByTestId(AppTestIDs.orderTotalRoot)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.orderTotalNumberOfItems).props.children).toEqual(
      'Order Total: 5 tickets'
    );
    expect(screen.getByTestId(AppTestIDs.orderTotalPrice).props.children).toEqual('$3.50');
    expect(screen.getByTestId(AppTestIDs.orderTotalSubtext).props.children).toEqual(
      'Additional fees may be applied at checkout. All sales are non-refundable.'
    );
  });
});
