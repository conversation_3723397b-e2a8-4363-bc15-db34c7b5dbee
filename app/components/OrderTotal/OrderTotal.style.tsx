import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
  },
  ticketsAndPriceContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: UniformSpace.quarter,
  },
  ticketCountContainer: {
    display: 'flex',
    alignItems: 'flex-start',
  },
  ticketTotalContainer: {
    display: 'flex',
    gap: UniformSpace.quarter,
    alignItems: 'flex-start',
  },
  ticketPriceContainer: {
    display: 'flex',
    gap: UniformSpace.quarter,
    alignItems: 'flex-end',
  },
  order: {
    color: colors.baseWhite,
    fontWeight: '700',
    fontSize: 18,
    lineHeight: 24,
  },
  subtextWrapper: {
    display: 'flex',
    alignItems: 'center',
  },
  subtext: {
    color: colors.baseWhite,
    fontStyle: 'italic',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 21,
    textAlign: 'center',
  },
  skeletonWide: {
    backgroundColor: colors.bgLevel3,
    height: UniformSpace.one,
    borderRadius: UniformSpace.quarter,
    width: UniformSpace.four,
  },
  pricingContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.quarter,
  },
  orderTotalText: {
    color: colors.baseWhite,
    fontWeight: '700',
    fontSize: 18,
  },
  skeletonContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-end',
    gap: UniformSpace.quarter,
  },
}));
