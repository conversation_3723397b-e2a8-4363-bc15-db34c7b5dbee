import React from 'react';

import { fireEvent, screen } from '@testing-library/react-native';

import { IconConfirm } from '@hudl/rn-uniform';

import { EventActionItem } from './EventActionItem';
import { renderWithOptions } from '../../test/renderHelpers';

const onItemPressMock = jest.fn();

describe('EventActionItem Tests', () => {
  it('should render EventActionItem', () => {
    renderWithOptions(
      <EventActionItem
        icon={<IconConfirm />}
        label="Confirmation"
        onItemPress={onItemPressMock}
        qaId="confirmation-item"
      />
    );

    expect(screen.getByText('Confirmation')).toBeTruthy();
    expect(screen.getByTestId('confirmation-item')).toBeTruthy();
  });

  it('Triggers mock on press', () => {
    renderWithOptions(
      <EventActionItem
        icon={<IconConfirm />}
        label="Confirmation"
        onItemPress={onItemPressMock}
        qaId="confirmation-item"
      />
    );

    expect(screen.getByText('Confirmation')).toBeTruthy();
    fireEvent.press(screen.getByTestId('confirmation-item'));
    expect(onItemPressMock).toHaveBeenCalledTimes(1);
  });
});
