import React, { ReactElement } from 'react';
import { TouchableOpacity, View } from 'react-native';

import { Text, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './EventActionItem.style';

type Props = {
  icon: ReactElement;
  label: string;
  onItemPress: () => void;
  qaId: string;
  isDisabled?: boolean;
};

export function EventActionItem(props: Props): ReactElement {
  const { icon, label, onItemPress, qaId, isDisabled } = props;
  const styles = useUniformStyles(styleSheet);

  return (
    <View
      style={
        isDisabled ? styles.eventActionItemContainerDisabled : styles.eventActionItemContainer
      }>
      <TouchableOpacity
        onPress={onItemPress}
        testID={qaId}
        style={styles.touchableOpacity}
        disabled={isDisabled}>
        <View style={styles.eventActionItemContent}>
          {icon}
          <Text>{label}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
}
