import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  eventActionItemContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.bgLevel0,
    borderRadius: UniformSpace.half,
  },
  eventActionItemContainerDisabled: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.bgLevel0,
    borderRadius: UniformSpace.half,
    pointerEvents: 'none',
    opacity: 0.5,
  },
  eventActionItemContent: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: UniformSpace.half,
  },
  touchableOpacity: {
    width: '100%',
    padding: UniformSpace.one,
  },
}));
