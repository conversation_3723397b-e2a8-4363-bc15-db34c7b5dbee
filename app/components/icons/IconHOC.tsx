import React, { ComponentType } from 'react';
import { View } from 'react-native';

import { IconFootball } from '@hudl/rn-uniform';

type IconProps = Parameters<typeof IconFootball>[0];
export type ResizableIconProps = Omit<IconProps, 'size'> & {
  size?: number | IconProps['size'];
};

export function makeIconResizable<P extends IconProps>(
  Icon: ComponentType<P>
): React.ComponentType<ResizableIconProps> {
  return function EnhancedComponent({ style, size, ...props }: ResizableIconProps) {
    return (
      <View style={[style, typeof size === 'number' && { height: size, width: size }]}>
        <Icon {...(props as P)} size={typeof size === 'number' ? 'custom' : size} />
      </View>
    );
  };
}
