import React from 'react';
import { StyleSheet, View } from 'react-native';

import {
  IconAustralianFootball,
  IconBaseball,
  IconBasketball,
  IconCheer,
  IconCricket,
  IconDance,
  IconFieldHockey,
  IconFootball,
  IconGolf,
  IconGymnastics,
  IconIceHockey,
  IconLacrosse,
  IconNetball,
  IconOtherSport,
  IconRugby,
  IconSoccer,
  IconSoftball,
  IconSwimming,
  IconTennis,
  IconTrackAndField,
  IconVolleyball,
  IconWaterPolo,
  IconWrestling,
  UniformSpace,
  useEnvironment,
} from '@hudl/rn-uniform';

import { makeIconResizable, ResizableIconProps } from './IconHOC';
import { Sport } from '../../gql/hudl/__generated__/graphql';

const SPORT_ICON_LOOKUP = {
  [Sport.AustralianRulesFootball]: makeIconResizable(IconAustralianFootball),
  [Sport.Basketball]: makeIconResizable(IconBasketball),
  [Sport.Baseball]: makeIconResizable(IconBaseball),
  [Sport.CheerAndSpirit]: makeIconResizable(IconCheer),
  [Sport.Cricket]: makeIconResizable(IconCricket),
  [Sport.DanceAndDrill]: makeIconResizable(IconDance),
  [Sport.FieldHockey]: makeIconResizable(IconFieldHockey),
  [Sport.Football]: makeIconResizable(IconFootball),
  [Sport.Golf]: makeIconResizable(IconGolf),
  [Sport.Gymnastics]: makeIconResizable(IconGymnastics),
  [Sport.IceHockey]: makeIconResizable(IconIceHockey),
  [Sport.Lacrosse]: makeIconResizable(IconLacrosse),
  [Sport.Netball]: makeIconResizable(IconNetball),
  [Sport.Rugby]: makeIconResizable(IconRugby),
  [Sport.SwimmingAndDiving]: makeIconResizable(IconSwimming),
  [Sport.Tennis]: makeIconResizable(IconTennis),
  [Sport.Track]: makeIconResizable(IconTrackAndField),
  [Sport.Soccer]: makeIconResizable(IconSoccer),
  [Sport.Softball]: makeIconResizable(IconSoftball),
  [Sport.Volleyball]: makeIconResizable(IconVolleyball),
  [Sport.WaterPolo]: makeIconResizable(IconWaterPolo),
  [Sport.Wrestling]: makeIconResizable(IconWrestling),
};

interface Props extends ResizableIconProps {
  sport?: Sport;
  wrapped?: boolean;
}

const styles = StyleSheet.create({
  iconWrapper: {
    padding: UniformSpace.eighth,
    borderRadius: 50,
  },
  opaqueDark: {
    backgroundColor: 'rgba(255, 255, 255, .15)',
  },
  opaqueLight: {
    backgroundColor: 'rgba(0, 0, 0, .15)',
  },
});

/**
 A React component that renders a Hudl Sport icon.
 @param {Sport} [props.sport] - The sport, defaults to other
 @param {boolean} [props.wrapped] - Whether there should be a circle around the icon, defaults to false
 @returns {React.ReactElement} A React element representing the IconSport component.
 */

export default function IconSport({
  sport = Sport.Other,
  wrapped = false,
  ...props
}: Props): React.ReactElement {
  const environmentOpacity = useEnvironment() === 'dark' ? styles.opaqueDark : styles.opaqueLight;

  const Icon =
    SPORT_ICON_LOOKUP[sport?.toUpperCase() as keyof typeof SPORT_ICON_LOOKUP] ?? IconOtherSport;
  return wrapped ? (
    <View style={[styles.iconWrapper, environmentOpacity]}>
      <Icon {...props} />
    </View>
  ) : (
    <Icon {...props} />
  );
}
