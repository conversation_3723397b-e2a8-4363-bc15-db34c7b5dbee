import React, { ReactElement, useCallback, useEffect, useRef, useState } from 'react';
import { ScrollView, TouchableOpacity, View } from 'react-native';

import { useReactiveVar } from '@apollo/client';
import { BottomSheetModal } from '@gorhom/bottom-sheet';

import { DevMenu } from '@hudl/jarvis/device';
import { Divider, Headline, useUniformStyles } from '@hudl/rn-uniform';

import { DeveloperOptionButton } from './DeveloperOptionItems/DeveloperOptionButton';
import { DeveloperOptionInput } from './DeveloperOptionItems/DeveloperOptionInput';
import { DeveloperOptionToggle } from './DeveloperOptionItems/DeveloperOptionToggle';
import { DeveloperOptionValue } from './DeveloperOptionItems/DeveloperOptionValue';
import { styleSheet } from './DeveloperOptions.style';
import { clearScannedTickets } from './developerOptionUtils';
import { useAppSession } from '../../session/AppSession';
import { DeveloperOptionKeys } from '../../types/shared';
import { developerOptionFlags } from '../../utils/stateVars';
import BottomSheet from '../BottomSheet/BottomSheet';

export interface DeveloperOptionsProps {
  icon: ReactElement;
  title?: string;
}

export function DeveloperOptions(props: DeveloperOptionsProps): React.ReactElement {
  const { icon, title } = props;
  const styles = useUniformStyles(styleSheet);
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const currentDeveloperOptionFlags = useReactiveVar(developerOptionFlags);

  const toggleDeveloperOptionEnabled = useCallback(
    (property: DeveloperOptionKeys): (() => void) => {
      return () => {
        const currentValue = currentDeveloperOptionFlags[property].enabled;
        developerOptionFlags({
          ...currentDeveloperOptionFlags,
          [property]: { ...currentDeveloperOptionFlags[property], enabled: !currentValue },
        });
      };
    },
    [currentDeveloperOptionFlags]
  );

  const setDeveloperOptionValue = useCallback(
    (property: DeveloperOptionKeys): ((value: string | undefined) => void) => {
      return (value: string | undefined) => {
        developerOptionFlags({
          ...currentDeveloperOptionFlags,
          [property]: { ...currentDeveloperOptionFlags[property], value },
        });
      };
    },
    [currentDeveloperOptionFlags]
  );

  const setShowModal = useCallback((show: boolean): (() => void) => {
    return () => setModalVisible(show);
  }, []);

  const session = useAppSession();

  const setNewBaseUrl = useCallback(
    (newUrl: string | undefined): void => {
      if (newUrl === undefined) {
        return;
      }
      session.setBaseURL(newUrl);
    },
    [session]
  );

  const renderOptions = (): ReactElement => {
    return (
      <View style={styles.container}>
        <ScrollView style={styles.scrollview}>
          <View style={styles.scrollContainer}>
            <Headline level="1" style={styles.title}>
              {'Developer Options'}
            </Headline>
            <DeveloperOptionButton
              style={styles.button}
              label={'Disable Developer Mode'}
              successLabel="Disabled"
              onPress={session.toggleDevelopmentEnvironment}
            />
            <Divider width="one" style={styles.divider} />

            <Headline level="2" style={styles.header}>
              {'Branch'}
            </Headline>
            <DeveloperOptionValue title={'Current URL'} value={session.baseURL} />
            <DeveloperOptionInput
              style={styles.input}
              label={'Set Branch URL'}
              value={session.baseURL}
              suggestedOptions={[
                { label: 'Thor Master', value: 'https://master.thorhudl.com/' },
                { label: 'Prod', value: 'https://www.hudl.com/' },
              ]}
              setValue={setNewBaseUrl}
              keyboardType="url"
            />
            <Divider width="one" style={styles.divider} />
            <Headline level="2" style={styles.header}>
              {'Scanned Tickets'}
            </Headline>
            <DeveloperOptionButton
              style={styles.button}
              label={'Clear Scanned Tickets'}
              successLabel="Cleared"
              onPress={clearScannedTickets}
            />
            <Divider width="one" style={styles.divider} />
            <Headline level="2" style={styles.header}>
              {'Card Readers'}
            </Headline>
            <DeveloperOptionToggle
              style={styles.toggle}
              label={'Simulate Card Reader'}
              checked={currentDeveloperOptionFlags.simulateReader.enabled}
              onToggle={toggleDeveloperOptionEnabled('simulateReader')}
            />
            <MiniDivider />
            <DeveloperOptionValue
              title="Simulated Card #"
              value={currentDeveloperOptionFlags.simulatedCardNumber.value ?? 'Not Set'}
            />
            <DeveloperOptionToggle
              style={styles.toggle}
              label={'Simulate Card'}
              checked={currentDeveloperOptionFlags.simulatedCardNumber.enabled}
              onToggle={toggleDeveloperOptionEnabled('simulatedCardNumber')}
            />
            <DeveloperOptionInput
              style={styles.input}
              label={'Set Simulated Card Number'}
              value={currentDeveloperOptionFlags.simulatedCardNumber.value}
              suggestedOptions={[
                { label: 'Valid', value: '****************' },
                { label: 'Decline', value: '****************' },
                { label: 'Insufficient Funds', value: '****************' },
              ]}
              setValue={setDeveloperOptionValue('simulatedCardNumber')}
              keyboardType="numeric"
            />
            <MiniDivider />

            <DeveloperOptionValue
              title="Simulated Reader Update Type"
              value={currentDeveloperOptionFlags.simulateReaderUpdate.value ?? 'Not Set'}
            />
            <DeveloperOptionToggle
              style={styles.toggle}
              label={'Simulate Card Reader Update'}
              checked={currentDeveloperOptionFlags.simulateReaderUpdate.enabled}
              onToggle={toggleDeveloperOptionEnabled('simulateReaderUpdate')}
            />
            <DeveloperOptionInput
              style={styles.input}
              label={'Set Simulated Reader Update Type'}
              value={currentDeveloperOptionFlags.simulateReaderUpdate.value}
              suggestedOptions={[
                { label: 'None', value: 'none' },
                { label: 'Available', value: 'available' },
                { label: 'Required', value: 'required' },
                { label: 'Low Battery', value: 'lowBattery' },
              ]}
              setValue={setDeveloperOptionValue('simulateReaderUpdate')}
            />

            <Divider width="one" style={styles.divider} />
            <Headline level="2" style={styles.header}>
              {'Crash Simulation'}
            </Headline>
            <DeveloperOptionButton
              style={styles.button}
              label="Force JS Crash"
              successLabel="Disabled"
              onPress={DevMenu.jsCrash}
            />
            <DeveloperOptionButton
              style={styles.button}
              label="Force Native Crash"
              successLabel="Disabled"
              onPress={DevMenu.nativeCrash}
            />
          </View>
        </ScrollView>
      </View>
    );
  };

  useEffect(() => {
    if (modalVisible) {
      bottomSheetRef?.current?.present?.();
    } else {
      bottomSheetRef?.current?.close?.();
    }
  }, [modalVisible]);

  const onAnimate = useCallback((fromIndex: number, toIndex: number): void => {
    if (fromIndex > toIndex && toIndex === -1) {
      setModalVisible(false);
    }
  }, []);

  const renderBottomSheet = (children: ReactElement): ReactElement => {
    return (
      <BottomSheet
        ref={bottomSheetRef}
        containerStyle={styles.container}
        snapPoints={['85%']}
        onBackdropPress={setShowModal(false)}
        onClose={setShowModal(false)}
        onAnimate={onAnimate}>
        <View style={styles.content}>{children}</View>
      </BottomSheet>
    );
  };

  return (
    <View>
      <TouchableOpacity
        onPress={setShowModal(!modalVisible)}
        style={styles.developerOptionsClickable}>
        {title && <Headline level="2">{title}</Headline>}
        {icon}
      </TouchableOpacity>
      {renderBottomSheet(renderOptions())}
    </View>
  );
}

function MiniDivider(): ReactElement {
  const styles = useUniformStyles(styleSheet);
  return (
    <View style={styles.miniDivider}>
      <Divider width="one" />
    </View>
  );
}
