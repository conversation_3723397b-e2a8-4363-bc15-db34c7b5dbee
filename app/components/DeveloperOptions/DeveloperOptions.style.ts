import { Dimensions } from 'react-native';

import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

const deviceHeight = Dimensions.get('window').height;

export const styleSheet = UniformStyleSheet.create((colors) => ({
  developerOptionsClickable: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: UniformSpace.half,
  },
  container: {
    paddingHorizontal: UniformSpace.one,
  },
  scrollview: {
    flexGrow: 1,
    height: deviceHeight * 0.8,
    paddingHorizontal: UniformSpace.one,
  },
  scrollContainer: {
    marginBottom: UniformSpace.two,
  },
  content: {
    height: '100%',
    width: '100%',
  },
  title: {
    alignSelf: 'center',
    marginBottom: UniformSpace.one,
  },
  header: {
    paddingBottom: UniformSpace.threeQuarter,
  },
  text: {
    marginBottom: UniformSpace.half,
    fontSize: 16,
    color: colors.contentNonessential,
  },
  button: {
    marginBottom: UniformSpace.threeQuarter,
  },
  toggle: {
    marginBottom: UniformSpace.threeQuarter,
  },
  input: {
    marginBottom: UniformSpace.threeQuarter,
  },
  divider: {
    marginBottom: UniformSpace.one,
  },
  miniDivider: {
    marginBottom: UniformSpace.threeQuarter,
    width: '25%',
    alignSelf: 'center',
  },
}));
