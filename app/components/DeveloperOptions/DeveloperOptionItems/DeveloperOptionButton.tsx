import React, { useCallback, useState } from 'react';
import { StyleProp, ViewStyle } from 'react-native';

import { Button, ButtonType } from '@hudl/rn-uniform';

interface DeveloperOptionButtonProps {
  label: string;
  successLabel: string;
  buttonType?: ButtonType;
  style?: StyleProp<ViewStyle>;
  onPress?: () => void;
  showSuccessToast?: (message: string) => void;
}

export function DeveloperOptionButton(props: DeveloperOptionButtonProps): React.ReactElement {
  const { label, successLabel, buttonType, style, onPress, showSuccessToast } = props;
  const [buttonLabel, setButtonLabel] = useState(label);
  const [buttonStatus, setButtonStatus] = useState(buttonType);

  const onPressHandler = useCallback(() => {
    if (onPress) {
      onPress();
      setButtonStatus('confirm');
      setButtonLabel(successLabel);
      setTimeout(() => {
        setButtonStatus(buttonType);
        setButtonLabel(label);
      }, 1500);
      showSuccessToast?.('Success');
    }
  }, [buttonType, label, onPress, showSuccessToast, successLabel]);

  return (
    <Button style={style} buttonType={buttonStatus} text={buttonLabel} onPress={onPressHandler} />
  );
}
