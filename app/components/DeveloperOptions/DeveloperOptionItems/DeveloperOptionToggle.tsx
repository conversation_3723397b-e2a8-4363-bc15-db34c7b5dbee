import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';

import { Checkbox } from '@hudl/rn-uniform';

interface DeveloperOptionButtonProps {
  label: string;
  checked: boolean;
  onToggle: () => void;
  style?: StyleProp<ViewStyle>;
}
export function DeveloperOptionToggle(props: DeveloperOptionButtonProps): React.ReactElement {
  const { label, checked, style, onToggle } = props;
  return (
    <Checkbox
      size="large"
      value={label}
      label={label}
      isChecked={checked}
      style={style}
      onPress={onToggle}
    />
  );
}
