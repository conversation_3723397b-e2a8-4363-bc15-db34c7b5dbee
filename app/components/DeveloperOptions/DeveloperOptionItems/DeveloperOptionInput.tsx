import React, { useCallback, useState } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  StyleProp,
  Text,
  View,
  ViewStyle,
} from 'react-native';

import { TextInput } from 'react-native-gesture-handler';
import Modal from 'react-native-modal';

import {
  Button,
  ButtonType,
  IconEdit,
  IconUIDismissSmall,
  UniformSpace,
  UniformStyleSheet,
  useUniformStyles,
  useUniformTheme,
} from '@hudl/rn-uniform';

const styleSheet = UniformStyleSheet.create((colors) => ({
  modalContent: {
    display: 'flex',
    justifyContent: 'center',
    backgroundColor: colors.bgLevel2,
    paddingHorizontal: UniformSpace.one,
    paddingTop: UniformSpace.half,
    paddingBottom: UniformSpace.one,
    borderRadius: 4,
    width: '100%',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: UniformSpace.quarter,
  },
  modalHeader: {
    color: colors.contentContrast,
    fontSize: 22,
  },
  input: {
    borderColor: colors.contentNonessential,
    borderWidth: 1,
    borderRadius: 4,
    padding: UniformSpace.half,
    marginBottom: UniformSpace.quarter,
    width: '100%',
    color: colors.contentDefault,
    fontSize: 16,
  },
  setValueButton: {
    marginTop: UniformSpace.half,
  },
}));

interface DeveloperOptionButtonProps {
  label: string;
  value: string | undefined;
  setValue: (value: string | undefined) => void;
  suggestedOptions?: { label: string; value: string }[];
  style?: StyleProp<ViewStyle>;
  keyboardType?: 'default' | 'numeric' | 'email-address' | 'number-pad' | 'url';
}

export function DeveloperOptionInput(props: DeveloperOptionButtonProps): React.ReactElement {
  const { label, value, setValue, suggestedOptions, style, keyboardType } = props;
  const { colors } = useUniformTheme();
  const styles = useUniformStyles(styleSheet);
  const setValueLabel = 'Set Value';
  const valueSetLabel = 'Value Set';

  const [inputModalOpen, setInputModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const [setValueButtonStatus, setSetValueButtonStatus] = useState<ButtonType>('primary');
  const [setValueButtonLabel, setSetValueButtonLabel] = useState(setValueLabel);

  const setModalVisibility = useCallback((isVisible: boolean): (() => void) => {
    return () => setInputModalOpen(isVisible);
  }, []);

  const updateInputValue = useCallback((newValue: string): (() => void) => {
    return () => setInputValue(newValue);
  }, []);

  const updateValue = useCallback((): void => {
    setSetValueButtonStatus('confirm');
    setValue(inputValue);
    setSetValueButtonLabel(valueSetLabel);

    setTimeout(() => {
      Keyboard.dismiss();
      setInputModalOpen(false);
      setSetValueButtonStatus('primary');
      setSetValueButtonLabel(setValueLabel);
    }, 800);
  }, [inputValue, setValue]);

  const renderSuggestedOption = (optionLabel: string, optionValue: string): React.ReactElement => {
    return (
      <Button
        key={optionLabel}
        buttonType="secondary"
        buttonStyle="minimal"
        text={optionLabel}
        onPress={updateInputValue(optionValue)}
      />
    );
  };

  const renderInputModal = (): React.ReactElement => {
    return (
      <Modal
        isVisible={inputModalOpen}
        animationIn="fadeIn"
        animationInTiming={400}
        animationOut="fadeOutDown"
        animationOutTiming={200}
        hasBackdrop={true}
        backdropColor={colors.bgScrim}
        backdropOpacity={1}
        swipeDirection="down"
        onBackButtonPress={setModalVisibility(false)}
        onBackdropPress={setModalVisibility(false)}
        onSwipeComplete={setModalVisibility(false)}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          keyboardVerticalOffset={150}>
          <View style={styles.modalContent}>
            <View style={styles.headerContainer}>
              <Text style={styles.modalHeader}>{label}</Text>
              <Button
                buttonType="secondary"
                buttonStyle="minimal"
                icon={<IconUIDismissSmall />}
                onPress={setModalVisibility(false)}
              />
            </View>
            <TextInput
              keyboardType={keyboardType}
              style={styles.input}
              value={inputValue}
              onChangeText={setInputValue}
              autoCapitalize="none"
              autoComplete="off"
              autoCorrect={false}
              returnKeyType="done"
            />
            {suggestedOptions?.map((option) => renderSuggestedOption(option.label, option.value))}
            <Button
              style={styles.setValueButton}
              text={setValueButtonLabel}
              buttonType={setValueButtonStatus}
              onPress={updateValue}
            />
          </View>
        </KeyboardAvoidingView>
      </Modal>
    );
  };

  return (
    <View style={[style]}>
      <Button
        buttonType="secondary"
        text={label}
        icon={<IconEdit />}
        onPress={setModalVisibility(true)}
      />
      {renderInputModal()}
    </View>
  );
}
