import React from 'react';
import { StyleProp, Text, View, ViewStyle } from 'react-native';

import { UniformSpace, UniformStyleSheet, useUniformStyles } from '@hudl/rn-uniform';

interface DeveloperOptionValueProps {
  title: string;
  value: string | undefined;
  style?: StyleProp<ViewStyle>;
}

const styleSheet = UniformStyleSheet.create((colors) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'row',
    marginBottom: UniformSpace.threeQuarter,
  },
  title: {
    color: colors.contentSubtle,
    fontSize: 16,
    fontWeight: 'bold',
  },
  value: {
    color: colors.contentDefault,
    fontSize: 16,
  },
}));

export function DeveloperOptionValue(props: DeveloperOptionValueProps): React.ReactElement {
  const { title, value, style } = props;
  const styles = useUniformStyles(styleSheet);

  return (
    <View style={[styles.wrapper, style]}>
      <Text style={styles.title}>{`${title}: `}</Text>
      <Text style={styles.value}>{value}</Text>
    </View>
  );
}
