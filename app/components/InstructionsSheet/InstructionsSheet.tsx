import React, { ReactElement, useCallback, useEffect, useMemo, useRef } from 'react';
import { View } from 'react-native';

import { BottomSheetModal } from '@gorhom/bottom-sheet';

import { useUniformStyles, Text, Headline, Button } from '@hudl/rn-uniform';

import { styleSheet } from './InstructionsSheet.style';
import BottomSheet from '../../components/BottomSheet/BottomSheet';
import { AppTestIDs } from '../AppTestIDs';

type Props = {
  modalVisible: boolean;
  instructions: string[];
  headerText: string;
  buttonText: string;
  setModalVisible: (visible: boolean) => void;
};

export default function InstructionsSheet(props: Props): React.ReactElement {
  const styles = useUniformStyles(styleSheet);

  const { modalVisible, instructions, buttonText, headerText, setModalVisible } = props;
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const closeModal = useCallback(() => setModalVisible(false), [setModalVisible]);

  const renderInstruction = useCallback(
    (content: string, index: number): React.ReactElement => {
      return (
        <View key={index} style={styles.instructionWrapper}>
          <Text style={styles.instructionContent}>{`${index + 1}.`}</Text>
          <Text style={styles.instructionContent}>{content}</Text>
        </View>
      );
    },
    [styles.instructionWrapper, styles.instructionContent]
  );

  const helperContent = useMemo(() => {
    return (
      <View style={styles.container} testID={AppTestIDs.instructionsBottomSheet}>
        <View style={styles.instructionContainer}>
          <Headline style={styles.instructionsHeader}>{headerText}</Headline>
          <View style={styles.instructionsWrapper}>{instructions.map(renderInstruction)}</View>
        </View>
        <Button
          onPress={closeModal}
          text={buttonText}
          testID={AppTestIDs.closeInstructionsButton}
        />
      </View>
    );
  }, [
    styles.container,
    styles.instructionContainer,
    styles.instructionsHeader,
    styles.instructionsWrapper,
    headerText,
    instructions,
    renderInstruction,
    closeModal,
    buttonText,
  ]);

  useEffect(() => {
    if (modalVisible) {
      bottomSheetRef?.current?.present?.();
    } else {
      bottomSheetRef?.current?.close?.();
    }
  }, [modalVisible]);

  const onAnimate = useCallback(
    (fromIndex: number, toIndex: number): void => {
      if (fromIndex > toIndex && toIndex === -1) {
        setModalVisible(false);
      }
    },
    [setModalVisible]
  );

  const renderBottomSheet = (children: ReactElement): ReactElement => {
    return (
      <BottomSheet
        ref={bottomSheetRef}
        containerStyle={styles.container}
        enableDynamicSizing={true}
        onBackdropPress={closeModal}
        onClose={closeModal}
        onAnimate={onAnimate}>
        <View style={styles.content}>{children}</View>
      </BottomSheet>
    );
  };

  return renderBottomSheet(helperContent);
}
