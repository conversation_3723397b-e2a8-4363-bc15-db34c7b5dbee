import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    padding: UniformSpace.one,
    paddingTop: 0,
    justifyContent: 'space-between',
    gap: UniformSpace.oneAndHalf,
  },
  instructionContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.oneAndHalf,
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
  },
  instructionsHeader: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.contentDefault,
  },
  instructionsWrapper: {
    gap: UniformSpace.half,
  },
  instructionWrapper: {
    display: 'flex',
    flexDirection: 'row',
    gap: UniformSpace.half,
    paddingLeft: UniformSpace.half,
    paddingRight: UniformSpace.half,
  },
  instructionContent: {
    fontSize: 16,
    fontWeight: '300',
    color: colors.contentDefault,
  },
}));
