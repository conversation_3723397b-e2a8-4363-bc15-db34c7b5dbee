/* eslint-disable jest/no-disabled-tests -- skipping tests until <PERSON><PERSON><PERSON> fixes the bottom sheet mock*/
import React from 'react';

import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { screen } from '@testing-library/react-native';

import InstructionsSheet from './InstructionsSheet';
import { renderWithOptions } from '../../test/renderHelpers';
import { AppTestIDs } from '../AppTestIDs';

jest.mock('react-native-safe-area-context', () => {
  const inset = { top: 0, right: 0, bottom: 0, left: 0 };
  return {
    SafeAreaProvider: jest.fn().mockImplementation(({ children }) => children),
    SafeAreaConsumer: jest.fn().mockImplementation(({ children }) => children(inset)),
    useSafeAreaInsets: jest.fn().mockImplementation(() => inset),
  };
});

// jest.mock('@gorhom/bottom-sheet', () => require('@gorhom/bottom-sheet/mock'));

const mockInstructions = ['instruction 1', 'instruction 2', 'instruction 3', 'instruction 4'];

describe('InstructionsSheet', () => {
  it.skip('renders correctly when visible', async () => {
    renderWithOptions(
      <BottomSheetModalProvider>
        <InstructionsSheet
          modalVisible={true}
          instructions={mockInstructions}
          setModalVisible={jest.fn()}
          headerText={'header text'}
          buttonText={'button text'}
        />
      </BottomSheetModalProvider>,
      {
        withSafeAreaProvider: true,
      }
    );

    expect(screen.getByTestId(AppTestIDs.instructionsBottomSheet)).toBeTruthy();
    expect(screen.getByText('1.')).toBeTruthy();
  });

  it.skip('renders all instructions with correct numbering and text', async () => {
    renderWithOptions(
      <BottomSheetModalProvider>
        <InstructionsSheet
          modalVisible={true}
          instructions={mockInstructions}
          setModalVisible={jest.fn()}
          headerText="header text"
          buttonText="button text"
        />
      </BottomSheetModalProvider>,
      { withSafeAreaProvider: true }
    );

    expect(screen.getByTestId(AppTestIDs.instructionsBottomSheet)).toBeTruthy();

    mockInstructions.forEach((text) => {
      expect(screen.getByText(text)).toBeTruthy();
    });

    expect(screen.queryByText(`${mockInstructions.length + 1}.`)).toBeFalsy();
  });
});
