import React from 'react';

import { render, screen, waitFor } from '@testing-library/react-native';

import ScannerUploadStatus from './ScannerUploadStatus';
import { renderWithOptions } from '../../test/renderHelpers';
import { AppTestIDs } from '../AppTestIDs';

describe('ScannerUploadStatus', () => {
  it('renders correctly when does not need upload', () => {
    render(<ScannerUploadStatus ticketsToUpload={0} refreshUploads={jest.fn()} />);

    expect(screen.getByTestId(AppTestIDs.scannerUploadStatus)).toBeDefined();
    expect(screen.getByTestId(AppTestIDs.scannerUploadStatusNoTicketsToUpload)).toBeDefined();
  });

  it('renders correctly when needs upload', async () => {
    renderWithOptions(<ScannerUploadStatus ticketsToUpload={5} refreshUploads={jest.fn()} />, {
      withAppSessionProvider: true,
    });

    await waitFor(() => expect(screen.getByTestId(AppTestIDs.scannerUploadStatus)).toBeDefined());
    expect(screen.getByTestId(AppTestIDs.scannerUploadStatusTicketsToUpload)).toBeDefined();
    expect(screen.getByTestId(AppTestIDs.uploadRemainingTicketsButton)).toBeDefined();
  });
});
