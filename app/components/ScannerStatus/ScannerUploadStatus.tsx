import React, { useCallback, useMemo } from 'react';
import { View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconConfirmation, IconCritical, Text, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './ScannerUploadStatus.style';
import { UploadTicketsButton } from '../../ticket-uploading/UploadRemainingTicketsModal';
import { AppTestIDs } from '../AppTestIDs';

enum ScannerUploadStatusType {
  UploadNeeded = 'uploadNeeded',
  UploadNotNeeded = 'uploadNotNeeded',
}

type ScannerUploadStatus = {
  icon: React.ReactElement;
  header: string;
  helperContent: string;
  testId: string;
  actionButton?: React.ReactElement;
};

type Props = {
  ticketsToUpload: number;
  refreshUploads: () => void;
};

export default function ScannerUploadStatus(props: Props): React.ReactElement {
  const { ticketsToUpload, refreshUploads } = props;
  const styles = useUniformStyles(styleSheet);

  const strings = useI18n(
    {
      uploadNeededHeader: 'scanner-upload-status.upload-needed-header',
      uploadNeededHelper: [
        'scanner-upload-status.upload-needed-helper',
        { count: ticketsToUpload },
      ],
      uploadNotNeededHeader: 'scanner-upload-status.upload-not-needed-header',
      uploadNotNeededHelper: 'scanner-upload-status.upload-not-needed-helper',
      uploadDataButton: 'scanner-upload-status.upload-data-button',
    },
    [ticketsToUpload]
  );

  const onUploadingComplete = useCallback(() => {
    refreshUploads();
  }, [refreshUploads]);

  const statuses: Record<ScannerUploadStatusType, ScannerUploadStatus> = useMemo(
    () => ({
      [ScannerUploadStatusType.UploadNotNeeded]: {
        icon: <IconConfirmation color="confirmation" />,
        header: strings.uploadNotNeededHeader,
        testId: AppTestIDs.scannerUploadStatusNoTicketsToUpload,
        helperContent: strings.uploadNotNeededHelper,
      },
      [ScannerUploadStatusType.UploadNeeded]: {
        icon: <IconCritical color="warning" />,
        header: strings.uploadNeededHeader,
        helperContent: strings.uploadNeededHelper,
        testId: AppTestIDs.scannerUploadStatusTicketsToUpload,
        actionButton: (
          <UploadTicketsButton
            mainButtonText={strings.uploadDataButton}
            onFinished={onUploadingComplete}
          />
        ),
      },
    }),
    [
      strings.uploadNeededHeader,
      strings.uploadNeededHelper,
      strings.uploadNotNeededHeader,
      strings.uploadNotNeededHelper,
      strings.uploadDataButton,
      onUploadingComplete,
    ]
  );

  const currentStatus = useMemo(
    () =>
      ticketsToUpload > 0
        ? statuses[ScannerUploadStatusType.UploadNeeded]
        : statuses[ScannerUploadStatusType.UploadNotNeeded],
    [statuses, ticketsToUpload]
  );

  return (
    <View style={styles.container} testID={AppTestIDs.scannerUploadStatus}>
      <View>
        <View style={styles.iconContainer}>{currentStatus.icon}</View>
      </View>
      <View style={styles.contentContainer} testID={currentStatus.testId}>
        <Text style={styles.header}>{currentStatus.header}</Text>
        <Text>{currentStatus.helperContent}</Text>
        <View style={styles.actionButton}>{currentStatus.actionButton}</View>
      </View>
    </View>
  );
}
