import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    gap: UniformSpace.one,
    width: '90%',
  },
  contentContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.quarter,
  },
  iconContainer: {
    backgroundColor: colors.bgLevel2,
    padding: UniformSpace.quarter,
    borderRadius: UniformSpace.quarter,
  },
  header: {
    fontWeight: 'bold',
  },
  actionButton: {
    display: 'flex',
    flexDirection: 'column',
    width: 'auto',
    minWidth: 124,
    alignSelf: 'flex-start',
  },
}));
