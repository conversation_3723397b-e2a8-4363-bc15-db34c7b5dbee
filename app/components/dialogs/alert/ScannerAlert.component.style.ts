import { StyleSheet } from 'react-native';

import { ColorsLight } from '@hudl/rn-uniform';

const colors = ColorsLight;

const styles = StyleSheet.create({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  alert: {
    paddingTop: 16,
    paddingRight: 16,
    paddingBottom: 8,
    paddingLeft: 16,
    maxWidth: 270,
    backgroundColor: colors.bgLevel0,
    borderRadius: 4,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: 16,
    },
    shadowRadius: 32,
    shadowOpacity: 0.3,
    alignItems: 'center',
  },
  footer: {
    display: 'flex',
    alignContent: 'stretch',
    marginTop: 16,
  },
  action: {
    marginBottom: 8,
  },
});

export default styles;
