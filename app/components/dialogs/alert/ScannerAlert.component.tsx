import React, { ReactElement, useMemo } from 'react';
import { View, ViewProps } from 'react-native';

import moment from 'moment';
import Modal from 'react-native-modal';

import { useI18n } from '@hudl/jarvis/i18n';
import {
  Action,
  Button,
  ColorsLight,
  IconColor,
  IconCritical,
  Text,
  UniformSpace,
  withEnvironment,
} from '@hudl/rn-uniform';

import styles from './ScannerAlert.component.style';

export interface NotRecognized {
  __type: 'NotRecognized';
}

export interface ErrorScanning {
  __type: 'ErrorScanning';
}

export interface InvalidPassDates {
  __type: 'InvalidPassDates';
  startDate: string;
  endDate: string;
}

export interface AlreadyScanned {
  __type: 'AlreadyScanned';
  timeStamp: string;
}

export interface IncorrectEvent {
  __type: 'IncorrectEvent';
  currentEventName: string;
}

export interface EventNotIncluded {
  __type: 'EventNotIncluded';
  currentEventName: string;
}

export type ScannerAlertType =
  | NotRecognized
  | AlreadyScanned
  | IncorrectEvent
  | EventNotIncluded
  | InvalidPassDates
  | ErrorScanning;

interface Props extends ViewProps {
  isOpen: boolean;
  onCancel: () => void;
  onAfterOpen?: () => void;
  alertType: ScannerAlertType;
}

/**
 * Interrupt with a mandatory acknowledgement or decision.
 *
 * Documentation: http://uniform.hudl.com/components/alert/code
 */
function ScannerAlert({
  isOpen = false,
  alertType,
  onCancel,
  onAfterOpen,
  ...props
}: Props): ReactElement {
  const alertIconColor = useAlertIconColor(alertType);
  const alertHeader = useAlertHeader(alertType);
  const alertMessage = useAlertMessage(alertType);
  const alertAction = useAlertAction(alertType, onCancel);

  const alertActions = useMemo((): React.ReactElement[] => {
    return [alertAction].map(
      (item, key): React.ReactElement => (
        <View style={styles.action} key={key}>
          <Button
            buttonType={item.buttonType}
            status={item.status}
            size="medium"
            text={item.text}
            isBlock={true}
            testID={item.testID}
            accessibilityLabel={item.accessibilityLabel}
            onPress={item.onPress}
          />
        </View>
      )
    );
  }, [alertAction]);

  return (
    <Modal
      isVisible={isOpen}
      animationIn="fadeIn"
      animationInTiming={400}
      animationOut="fadeOutDown"
      animationOutTiming={200}
      hasBackdrop={true}
      backdropColor={ColorsLight.bgScrim}
      backdropOpacity={1}
      swipeDirection="down"
      style={styles.container}
      onBackButtonPress={onCancel}
      onBackdropPress={onCancel}
      onSwipeComplete={onCancel}
      onModalShow={onAfterOpen}>
      <View style={[styles.alert]} {...props}>
        <IconCritical color={alertIconColor} style={{ marginBottom: UniformSpace.one }} />
        <Text alignment="center">{alertHeader}</Text>
        <Text alignment="center">{alertMessage}</Text>
        <View style={styles.footer}>{alertActions}</View>
      </View>
    </Modal>
  );
}

function useAlertIconColor(alertType: ScannerAlertType): IconColor {
  return useMemo(() => {
    switch (alertType.__type) {
      case 'NotRecognized':
      case 'AlreadyScanned':
      case 'ErrorScanning':
        return 'critical';

      case 'EventNotIncluded':
      case 'IncorrectEvent':
      case 'InvalidPassDates':
        return 'warning';
    }
  }, [alertType.__type]);
}

function useAlertMessage(alertType: ScannerAlertType): string {
  const strings = useI18n(
    {
      ticketNotRecognizedMessage: 'scanner-status.message.ticket-not-recognized',
      ticketAlreadyScannedMessageStart: 'scanner-status.message.ticket-already-scanned-start',
      ticketAlreadyScannedMessageEnd: 'scanner-status.message.ticket-already-scanned-end',

      incorrectEventStartMessage: 'scanner-status.message.incorrect-event-start',

      errorScanningTicketMessage: 'scanner-status.message.error-scanning-ticket',
      eventNotIncluded: 'scanner-status.message.event-not-included',
      invalidPassDates: 'scanner-status.message.invalid-pass-dates',
    },
    []
  );

  const dateFormat = 'ddd, MMM Do';
  return useMemo(() => {
    switch (alertType.__type) {
      case 'NotRecognized':
        return strings.ticketNotRecognizedMessage;

      case 'AlreadyScanned': // CO: This will likely changes to also include the time this ticket was scanned
        return `${strings.ticketAlreadyScannedMessageStart}${moment(alertType.timeStamp).format(
          dateFormat
        )}. ${strings.ticketAlreadyScannedMessageEnd}`;

      case 'IncorrectEvent':
        return `${strings.incorrectEventStartMessage} "${alertType.currentEventName ?? ''}"`;

      case 'EventNotIncluded':
        return `${strings.eventNotIncluded} ${alertType.currentEventName}`;

      case 'InvalidPassDates':
        return `${strings.invalidPassDates}${moment
          .utc(alertType.startDate)
          .format(dateFormat)} - ${moment.utc(alertType.endDate).format(dateFormat)}`;

      case 'ErrorScanning':
        return strings.errorScanningTicketMessage;
    }
  }, [alertType, strings]);
}

function useAlertHeader(alertType: ScannerAlertType): string {
  const strings = useI18n(
    {
      ticketNotRecognizedHeader: 'scanner-status.header.ticket-not-recognized',
      ticketAlreadyScannedHeader: 'scanner-status.header.ticket-already-scanned',
      incorrectEventHeader: 'scanner-status.header.incorrect-event',
      errorScanningTicketHeader: 'scanner-status.header.error-scanning-ticket',
      eventNotIncluded: 'scanner-status.header.event-not-included',
      invalidPassDates: 'scanner-status.header.invalid-pass-dates',
    },
    []
  );
  return useMemo(() => {
    switch (alertType.__type) {
      case 'NotRecognized':
        return strings.ticketNotRecognizedHeader;

      case 'AlreadyScanned':
        return strings.ticketAlreadyScannedHeader;

      case 'IncorrectEvent':
        return strings.incorrectEventHeader;

      case 'EventNotIncluded':
      case 'InvalidPassDates':
        return strings.eventNotIncluded;

      case 'ErrorScanning':
        return strings.errorScanningTicketHeader;
    }
  }, [alertType.__type, strings]);
}

function useAlertAction(alertType: ScannerAlertType, onPress: () => void): Action {
  return useMemo(() => {
    switch (alertType.__type) {
      case 'IncorrectEvent':
      case 'AlreadyScanned':
      case 'NotRecognized':
      case 'InvalidPassDates':
      case 'EventNotIncluded':
        return { onPress: onPress, text: 'Scan Another', testID: 'SCANNER_ALERT_CTA_BUTTON' };

      case 'ErrorScanning':
        return { onPress: onPress, text: 'Retry Scan', testID: 'SCANNER_ALERT_CTA_BUTTON' };
    }
  }, [alertType.__type, onPress]);
}
// Alert doesn't support dark mode yet so forcing a light environment
export default withEnvironment(ScannerAlert, 'light');
