import { FlatListProps } from 'react-native';

import { Sport } from '../../gql/hudl/__generated__/graphql';
import { TicketedEvent } from '../../gql/public/__generated__/graphql';

export interface TicketedEventListProps
  extends Omit<FlatListProps<TicketedEvent>, 'data' | 'renderItem'> {
  staticItems?: TicketedEvent[];
  orgId: string;
  orgAbbreviation?: string;
  sport?: Sport;
  onItemPressed: (event: TicketedEvent) => void;
}
