import React, { useCallback, useMemo, useState } from 'react';
import { FlatList, ListRenderItem, RefreshControl, View } from 'react-native';
import { StyleSheet } from 'react-native';

import { subHours } from 'date-fns';

import { useI18n } from '@hudl/jarvis/i18n';
import { Spinner, Text } from '@hudl/rn-uniform';

import TicketedEventItem from './TicketedEventItem';
import { TicketedEventListProps } from './TicketedEventListTypes';
import { useTicketedEventsByOrganizationId } from '../../gql/hooks/useTicketedEventsByOrganizationId';
import { Sport } from '../../gql/hudl/__generated__/graphql';
import { TicketedEvent, TicketedEventSortType } from '../../gql/public/__generated__/graphql';
import { upcomingEventsThresholdInHours } from '../../utils/Constants';
import { TicketedEventStatus } from '../../utils/Enums';
import { AppTestIDs } from '../AppTestIDs';

export const styles = StyleSheet.create({
  spacer: {
    marginBottom: 10,
  },
  loadingFooter: {
    width: '100%',
    minHeight: 300,
    justifyContent: 'center',
    flexDirection: 'row',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default function TicketedEventList({
  orgId,
  staticItems,
  onItemPressed,
  ...props
}: TicketedEventListProps): React.ReactElement {
  const [pullToRefreshing, setPullToRefreshing] = useState(false);

  const strings = useI18n(
    {
      emptyState: 'organization-events.empty-state',
      errorState: 'organization-events.error-state',
    },
    []
  );

  const renderItem = useCallback<ListRenderItem<TicketedEvent>>(
    ({ item }) => {
      return <RenderTicketedEvent onItemPressed={onItemPressed} event={item} />;
    },
    [onItemPressed]
  );

  const onCompleted = useCallback((): void => {
    setPullToRefreshing(false);
  }, []);

  const startDate = useMemo(
    () => subHours(new Date(), upcomingEventsThresholdInHours).toISOString(),
    []
  );

  const {
    events,
    isLoading,
    hasError,
    fetchMore,
    refetch: refetchTicketedEvents,
  } = useTicketedEventsByOrganizationId({
    input: {
      organizationId: orgId,
      sortType: TicketedEventSortType.TicketedEventDate,
      sortByAscending: true,
      startDate: startDate,
      ticketedEventStatuses: [TicketedEventStatus.Upcoming],
    },
    staticItems,
    onCompleted,
  });

  const onFetchMore = useCallback(() => {
    fetchMore();
  }, [fetchMore]);

  const onPullToRefresh = useCallback((): void => {
    refetchTicketedEvents();
    setPullToRefreshing(true);
  }, [refetchTicketedEvents]);

  const emptyState = useCallback((): React.ReactElement => {
    return (
      <View style={styles.emptyState}>
        <Text>{strings.emptyState}</Text>
      </View>
    );
  }, [strings.emptyState]);

  if (hasError) {
    return (
      <View testID={AppTestIDs.orgEventsErrorState}>
        <Text>{strings.errorState}</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={events}
      renderItem={renderItem}
      ItemSeparatorComponent={ItemSeparator}
      onEndReached={onFetchMore}
      ListEmptyComponent={emptyState}
      ListFooterComponent={isLoading && !pullToRefreshing ? LoadingFooter : ItemSeparator}
      refreshControl={
        <RefreshControl refreshing={isLoading} onRefresh={onPullToRefresh} tintColor="#fff" />
      }
      {...props}
    />
  );
}

function ItemSeparator(): React.ReactElement {
  return <View style={styles.spacer} />;
}

function RenderTicketedEvent({
  event,
  onItemPressed,
}: {
  event: TicketedEvent;
  onItemPressed: (event: TicketedEvent) => void;
}): React.ReactElement {
  const sport = (event.sport as Sport) ?? Sport.Other;
  const name = event.name ?? '';
  const date = event.date;

  const onPress = useCallback(() => {
    onItemPressed(event);
  }, [event, onItemPressed]);

  return (
    <TicketedEventItem
      sport={sport}
      teamName={name}
      date={date}
      onPress={onPress}
      testID={AppTestIDs.orgEvent(event.id)}
    />
  );
}

function LoadingFooter(): React.ReactElement {
  return (
    <View style={styles.loadingFooter} testID={AppTestIDs.loadingOrgBackButton}>
      <Spinner />
    </View>
  );
}
