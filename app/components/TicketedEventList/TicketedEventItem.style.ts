import { StyleSheet } from 'react-native';

import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import Font from '../../fonts/Font';

export const rnStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  flagContainer: {
    flexDirection: 'row',
    marginRight: 0,
    marginLeft: 'auto',
  },
  contentLeft: {
    minWidth: 40,
    minHeight: 80,
    justifyContent: 'space-evenly',
  },
  contentRight: {
    flex: 1,
    paddingTop: UniformSpace.quarter,
    justifyContent: 'space-between',
  },
  contentRightCentered: {
    justifyContent: 'center',
  },
  contentRightRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: UniformSpace.half,
    marginEnd: UniformSpace.two,
  },
  contentRightBottomRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
});

export const uniStyles = UniformStyleSheet.create((colors) => ({
  touchable: {
    borderRadius: UniformSpace.half,
    paddingVertical: UniformSpace.threeQuarter,
    paddingHorizontal: UniformSpace.one,
    backgroundColor: colors.bgLevel0,
  },
  label: {
    marginBottom: UniformSpace.half,
    fontSize: 12,
    color: colors.contentSubtle,
  },
  contentLeftText: {
    color: colors.contentDefault,
    fontFamily: Font.teko.regular,
    fontSize: 20,
    height: 18,
    lineHeight: 26,
    textAlign: 'right',
    textTransform: 'uppercase',
  },
  contentLeftTextBold: {
    color: colors.contentContrast,
    fontFamily: Font.teko.semiBold,
    fontSize: 32,
    height: 29,
    lineHeight: 42,
    textAlign: 'right',
  },
  liveText: {
    color: colors.buttonDestroy,
    fontWeight: 'bold',
  },
  vsOrAtText: {
    fontWeight: 'bold',
    fontFamily: Font.teko.semiBold,
    color: colors.contentContrast,
    fontSize: 16,
    height: 14,
    lineHeight: 21,
    marginBottom: 4,
    marginLeft: 1,
  },
  text: {
    color: colors.contentDefault,
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
  },
  textBold: {
    fontSize: 16,
    color: colors.contentContrast,
    fontWeight: 'bold',
  },
}));
