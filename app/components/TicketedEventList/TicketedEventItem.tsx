import React, { useMemo } from 'react';
import { Text as RNText, TouchableHighlight, TouchableHighlightProps, View } from 'react-native';

import moment from 'moment';

import { Divider, useUniformStyles, useUniformTheme, withSpace } from '@hudl/rn-uniform';

import { rnStyles, uniStyles } from './TicketedEventItem.style';
import { Sport } from '../../gql/hudl/__generated__/graphql';
import SpacedView from '../SpacedView';
import IconSport from '../icons/IconSport';

type ScheduleItemStyles = { styles: ReturnType<typeof uniStyles> };

export type ScheduleItemFlag = 'view' | 'hasTickets' | 'isStreaming';
export type ScheduleItemLocation = 'home' | 'away' | 'neutral';
export type ScheduleItemStatus = 'live' | 'scheduled' | 'win' | 'draw' | 'loss';
export type ScheduleItemOutcome = 'unknown' | 'win' | 'draw' | 'loss';
export type ScheduleItemGameType =
  | 'regularSeason'
  | 'postSeason'
  | 'camp'
  | 'scrimmage'
  | 'tournament'
  | 'coach';

export interface ScheduleItemProps extends TouchableHighlightProps {
  date: Date;
  flags?: ScheduleItemFlag[];
  label?: string;
  location?: ScheduleItemLocation;
  opponentName?: string;
  score?: [number, number];
  sport: Sport;
  status?: ScheduleItemStatus;
  teamName?: string;
  orgAbbreviation?: string;
}

const Text = withSpace(RNText);

export default function TicketedEventItem({
  date,
  flags = [],
  label,
  score = [0, 0],
  sport,
  teamName,
  orgAbbreviation,
  ...props
}: ScheduleItemProps): React.ReactElement | null {
  const styles = useUniformStyles(uniStyles);
  const { colors } = useUniformTheme();
  const { dayOfWeek, dayOfMonth, month, startTime } = useScheduleDateStrings(date);

  return (
    <TouchableHighlight underlayColor={colors.bgLevel0Accent} style={styles.touchable} {...props}>
      <View>
        {!!label && <Text style={styles.label}>{label}</Text>}
        <View style={rnStyles.container}>
          <View style={rnStyles.contentLeft}>
            <Text style={styles.contentLeftText}>{dayOfWeek}</Text>
            <Text style={styles.contentLeftTextBold}>{dayOfMonth}</Text>
            <Text style={styles.contentLeftText}>{month}</Text>
          </View>
          <Divider
            width="one"
            space={['threeQuarterLeft', 'threeQuarterRight']}
            orientation="vertical"
            color="divider"
          />
          <View style={[rnStyles.contentRight, !teamName && rnStyles.contentRightCentered]}>
            <TeamRow {...{ teamName, sport, styles, orgAbbreviation }} />
            <StatusRow {...{ flags, score, styles, startTime }} />
          </View>
        </View>
      </View>
    </TouchableHighlight>
  );
}

function TeamRow({
  teamName,
  sport,
  orgAbbreviation,
  styles,
}: Pick<ScheduleItemProps, 'teamName' | 'sport' | 'orgAbbreviation'> &
  ScheduleItemStyles): React.ReactElement | null {
  if (!teamName) {
    return null;
  }

  return (
    <View style={rnStyles.contentRightRow}>
      {orgAbbreviation && (
        <Text numberOfLines={1} style={styles.textBold} space={['halfRight']}>
          {orgAbbreviation}
        </Text>
      )}
      <SpacedView space={['halfRight']}>
        <IconSport sport={sport} wrapped={true} size={'small'} />
      </SpacedView>
      <Text numberOfLines={2} style={styles.textBold}>
        {teamName}
      </Text>
    </View>
  );
}

function StatusRow({
  startTime,
  styles,
}: Pick<ScheduleItemProps, 'status' | 'flags' | 'score' | 'location'> &
  ScheduleItemStyles & {
    startTime: string;
  }): React.ReactElement {
  return (
    <View style={rnStyles.contentRightBottomRow}>
      <Text style={styles.text}>{`@ ${startTime}`}</Text>
    </View>
  );
}

function useScheduleDateStrings(date: Date): {
  startTime: string;
  dayOfWeek: string;
  dayOfMonth: string;
  month: string;
} {
  return useMemo(() => {
    const m = moment(date);

    return {
      startTime: m.format('h:mmA'),
      dayOfWeek: m.calendar({
        sameDay: '[Today]',
        nextDay: 'ddd',
        lastDay: 'ddd',
        nextWeek: 'ddd',
        lastWeek: 'ddd',
        sameElse: 'ddd',
      }),
      dayOfMonth: m.format('D'),
      month: m.format('MMM'),
    };
  }, [date]);
}
