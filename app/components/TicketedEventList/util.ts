import {
  ScheduleItemFlag,
  ScheduleItemGameType,
  ScheduleItemLocation,
  ScheduleItemStatus,
} from './TicketedEventItem';
import { ScheduleEntryPublicSummary } from '../../gql/public/__generated__/graphql';

/**
 * https://github.com/hudl/hudl-schedules/blob/8d1c290bbaa04e223e6f5bab8af78513bb151329/src/Hudl.Schedules/Enums/ScheduleEntryLocation.cs#L3-L8
 * @param scheduleEntryLocation
 * @returns
 */
export function scheduleEntrylocationToScheduleItemLocation(
  scheduleEntryLocation: number
): ScheduleItemLocation {
  switch (scheduleEntryLocation) {
    case 0:
      return 'neutral';
    case 1:
      return 'home';
    case 2:
      return 'away';
    default:
      return 'neutral';
  }
}

/**
 * https://github.com/hudl/hudl-schedules/blob/8d1c290bbaa04e223e6f5bab8af78513bb151329/src/Hudl.Schedules/Enums/ScheduleEntryOutcome.cs#L3-L9
 * @param scheduleEntryOutcome
 * @param broadcastStatus
 * @returns
 */
export function scheduleEntryOutcomeToScheduleItemStatus(
  scheduleEntryOutcome: number,
  broadcastStatus?: string
): ScheduleItemStatus {
  if (broadcastStatus === 'Streaming') {
    return 'live';
  }

  switch (scheduleEntryOutcome) {
    case 0:
      return 'scheduled';
    case 1:
      return 'win';
    case 2:
      return 'loss';
    case 3:
      return 'draw';
    default:
      return 'scheduled';
  }
}

export function flagsFromBroadcastStatus(broadcastStatus?: string): ScheduleItemFlag[] {
  if (!broadcastStatus) {
    return [];
  }
  if (broadcastStatus === 'Archived') {
    return ['view'];
  }
  if (broadcastStatus === 'Streaming') {
    return ['isStreaming', 'view'];
  }
  return [];
}

export function getFlagsFromScheduleEntryPublicSummary(
  scheduleEntryPublicSummary: ScheduleEntryPublicSummary
): ScheduleItemFlag[] {
  const flags: ScheduleItemFlag[] = [];

  flagsFromBroadcastStatus(scheduleEntryPublicSummary.broadcastStatus ?? undefined).forEach(
    (flag) => flags.push(flag)
  );

  return flags;
}

/**
 * https://github.com/hudl/hudl-graphql/blob/efe6f2e721f9ba0ef4594d4e5fc4e061201cd95c/src/Schemas/Hudl.GraphQL.Schemas.Schedules/Types/GameType.cs#L3-L11
 * @param scheduleEntryPublicSummary
 * @returns
 */

export function scheduleEntryPublicSummaryToScheduleItemGameType(
  scheduleEntryPublicSummary: ScheduleEntryPublicSummary
): ScheduleItemGameType {
  switch (scheduleEntryPublicSummary.gameType) {
    case 0:
      return 'regularSeason';
    case 1:
      return 'postSeason';
    case 2:
      return 'camp';
    case 3:
      return 'scrimmage';
    case 4:
      return 'tournament';
    case 5:
      return 'coach';
    default:
      return 'camp';
  }
}
