import React, {
  ForwardedRef,
  ReactNode,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';

import {
  BottomSheetModal,
  BottomSheetBackdrop,
  BottomSheetBackgroundProps,
  BottomSheetProps,
  BottomSheetView,
  SNAP_POINT_TYPE,
} from '@gorhom/bottom-sheet';
import { BottomSheetMethods } from '@gorhom/bottom-sheet/lib/typescript/types';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { ColorsDark, UniformSpace } from '@hudl/rn-uniform';

import { useBottomSheetBackHandler } from './useBottomSheetBackHandler';

const styles = StyleSheet.create({
  bottomSheet: {
    borderRadius: UniformSpace.one,
    overflow: 'hidden',
  },
  container: {
    backgroundColor: ColorsDark.bgLevel1,
  },
  handle: {
    backgroundColor: ColorsDark.bgLevel1,
  },
  handleIndicator: {
    marginVertical: UniformSpace.one,
    backgroundColor: ColorsDark.divider,
    width: UniformSpace.two,
  },
});

/** Time between switching between two bottom sheets */
export const BOTTOM_SHEET_TRANSITION_DELAY = 75;

interface BottomSheet extends BottomSheetProps {
  children: ReactNode;
  containerStyles?: ViewStyle;
  onBackPress?: () => boolean;
  onBackdropPress: () => void;
}

function BottomSheet(
  {
    children,
    containerStyles,
    enableDynamicSizing,
    onChange,
    onBackPress,
    onBackdropPress,
    ...otherProps
  }: BottomSheet,
  ref: ForwardedRef<BottomSheetMethods | undefined>
): React.ReactElement {
  const insets = useSafeAreaInsets();
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  useImperativeHandle(ref, () => bottomSheetRef.current ?? undefined);

  const defaultBackPress = useCallback(() => {
    bottomSheetRef?.current?.dismiss?.();
    return true;
  }, []);

  const bottomSheetBackHandler = useBottomSheetBackHandler(onBackPress ?? defaultBackPress);
  const handleChange = useCallback(
    (index: number, position: number, type: SNAP_POINT_TYPE) => {
      bottomSheetBackHandler?.(index, position, type);
      onChange?.(index, position, type);
    },
    [bottomSheetBackHandler, onChange]
  );

  const Backdrop = useCallback(
    (backgroundProps: BottomSheetBackgroundProps) => (
      <BottomSheetBackdrop
        {...backgroundProps}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        opacity={0.5}
        onPress={onBackdropPress}
      />
    ),
    [onBackdropPress]
  );

  const safeAreaPadding = useMemo(
    () => ({
      paddingBottom: insets.bottom,
      flex: enableDynamicSizing ? undefined : 1,
    }),
    [insets, enableDynamicSizing]
  );

  return (
    <BottomSheetModal
      index={0}
      ref={bottomSheetRef}
      style={styles.bottomSheet}
      enablePanDownToClose={true}
      enableContentPanningGesture={false}
      enableDismissOnClose={true}
      enableDynamicSizing={enableDynamicSizing}
      enableOverDrag={false}
      handleStyle={styles.handle}
      handleIndicatorStyle={styles.handleIndicator}
      backdropComponent={Backdrop}
      backgroundComponent={backgroundComponent}
      onChange={handleChange}
      {...otherProps}>
      <BottomSheetView style={[styles.container, safeAreaPadding, containerStyles]}>
        {children}
      </BottomSheetView>
    </BottomSheetModal>
  );
}

/**
 * On Android, there is a ghost line that appears below the handler for the bottom sheet. It glitches during snap movements and is inserted for no reason.
 * By adding a custom backgroundComponent into the bottom sheet, it seems to solve the issue by removing the ghost line
 * https://github.com/gorhom/react-native-bottom-sheet/issues/159
 *
 * @returns An empty View
 */
function backgroundComponent(): React.ReactElement {
  return <View />;
}

export default forwardRef(BottomSheet);
