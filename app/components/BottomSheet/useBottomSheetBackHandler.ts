import { useCallback, useRef } from 'react';
import { BackHandler, NativeEventSubscription } from 'react-native';

import { BottomSheetModalProps, SNAP_POINT_TYPE } from '@gorhom/bottom-sheet';

/**
 * hook that dismisses the bottom sheet on the hardware back button press if it is visible
 * @param bottomSheetRef ref to the bottom sheet which is going to be closed/dismissed on the back press
 */
export const useBottomSheetBackHandler = (
  onBackPress?: () => boolean
): ((index: number, position: number, type: SNAP_POINT_TYPE) => void) => {
  const backHandlerSubscriptionRef = useRef<NativeEventSubscription | null>(null);
  return useCallback<NonNullable<BottomSheetModalProps['onChange']>>(
    (index, _position, _type) => {
      const isBottomSheetVisible = index >= 0;
      if (isBottomSheetVisible && !backHandlerSubscriptionRef.current) {
        backHandlerSubscriptionRef.current = BackHandler.addEventListener('hardwareBackPress', () =>
          onBackPress?.()
        );
      } else if (!isBottomSheetVisible) {
        backHandlerSubscriptionRef.current?.remove();
        backHandlerSubscriptionRef.current = null;
      }
    },
    [onBackPress]
  );
};
