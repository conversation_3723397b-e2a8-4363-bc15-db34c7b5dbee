import { StyleSheet } from 'react-native';

export const gradientStyles = StyleSheet.create({
  topGradient: {
    position: 'absolute',
    width: '50%',
    height: '80%',
    flex: 1,
    opacity: 1,
  },
  topLeftGradient: {
    left: 0,
  },
  topRightGradient: {
    right: 0,
  },
  gradientOverlay: {
    position: 'absolute',
    backgroundColor: 'black',
    opacity: 0.3,
    width: '100%',
    height: '100%',
  },
  container: {
    position: 'absolute',
    height: '100%',
    width: '100%',
  },
  topBackgroundGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'absolute',
    opacity: 0.7,
  },
  bottomBackgroundGradient: {
    flex: 1,
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  radialGradient: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
});
