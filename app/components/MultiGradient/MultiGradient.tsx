import React, { ReactElement, useMemo } from 'react';
import { View } from 'react-native';

import { useUniformTheme } from '@hudl/rn-uniform';

import MarqueeGradient from './MarqueeGradient';
import { gradientStyles } from './MultiGradient.style';
import { formatColorHex } from '../../utils/ColorUtils';

export interface MultiGradientProps {
  primaryColor: string | undefined;
  secondaryColor: string | undefined;
}

function MultiGradient({ primaryColor, secondaryColor }: MultiGradientProps): ReactElement {
  const { colors } = useUniformTheme();

  const primaryStyle = useMemo(() => {
    const color = formatColorHex(primaryColor ?? colors.baseBlack);
    return [gradientStyles.topGradient, gradientStyles.topLeftGradient, { backgroundColor: color }];
  }, [colors.baseBlack, primaryColor]);

  const secondaryStyle = useMemo(() => {
    const color = formatColorHex(secondaryColor ?? colors.baseBlack);
    return [
      gradientStyles.topGradient,
      gradientStyles.topRightGradient,
      { backgroundColor: color },
    ];
  }, [colors.baseBlack, secondaryColor]);

  return (
    <View style={gradientStyles.container}>
      <View style={primaryStyle}>
        <MarqueeGradient type="left" styles={gradientStyles} />
      </View>

      <View style={secondaryStyle}>
        <MarqueeGradient type="right" styles={gradientStyles} />
      </View>
      <View style={gradientStyles.gradientOverlay} />
    </View>
  );
}

export default MultiGradient;
