import React, { ReactElement } from 'react';
import { ImageStyle, TextStyle, ViewStyle } from 'react-native';

import LinearGradient from 'react-native-linear-gradient';

import { useUniformTheme } from '@hudl/rn-uniform';
export interface MarqueeGradientProps {
  type: 'left' | 'right' | 'vertical';
  styles: { [key: string]: ViewStyle | TextStyle | ImageStyle };
}

function MarqueeGradient({ type, styles }: MarqueeGradientProps): ReactElement {
  const { colors } = useUniformTheme();
  return (
    <>
      {type === 'left' && (
        <>
          <LinearGradient
            colors={['transparent', colors.baseBlack]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.radialGradient}
          />
          <LinearGradient
            colors={['transparent', colors.baseBlack]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
            style={styles.radialGradient}
          />
          <LinearGradient
            colors={['transparent', colors.baseBlack]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={styles.radialGradient}
          />
        </>
      )}
      {type === 'right' && (
        <>
          <LinearGradient
            colors={['transparent', colors.baseBlack]}
            start={{ x: 1, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={styles.radialGradient}
          />
          <LinearGradient
            colors={['transparent', colors.baseBlack]}
            start={{ x: 1, y: 0 }}
            end={{ x: 0, y: 0 }}
            style={styles.radialGradient}
          />
          <LinearGradient
            colors={['transparent', colors.baseBlack]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            style={styles.radialGradient}
          />
        </>
      )}
      {type === 'vertical' && (
        <>
          <LinearGradient
            style={styles.topBackgroundGradient}
            colors={['transparent', colors.baseBlack]}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
          />
          <LinearGradient
            style={styles.bottomBackgroundGradient}
            colors={['transparent', colors.baseBlack]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />
        </>
      )}
    </>
  );
}

export default MarqueeGradient;
