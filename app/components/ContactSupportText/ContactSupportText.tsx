import React from 'react';
import { StyleProp, Text, TextStyle } from 'react-native';

import { useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './ContactSupportText.style';
import { openSupportLink } from '../../utils/UrlUtils';

type Props = {
  supportTextArray: string;
  testIds: string[];
  textStyle?: StyleProp<TextStyle>;
};

export function ContactSupportText(props: Props): React.ReactElement {
  const { supportTextArray, testIds, textStyle } = props;
  const styles = useUniformStyles(stylesheet);

  return (
    <Text style={[styles.reactNativeText, textStyle]} testID={testIds[0]}>
      {supportTextArray[0]}
      <Text style={styles.linkText} onPress={openSupportLink} testID={testIds[1]}>
        {supportTextArray[1]}
      </Text>
    </Text>
  );
}

export default ContactSupportText;
