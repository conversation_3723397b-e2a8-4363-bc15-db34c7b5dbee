import React, { useCallback, useMemo } from 'react';
import { ViewStyle } from 'react-native';

import { Route } from '@react-navigation/native';
import { NativeStackHeaderProps } from '@react-navigation/native-stack';

import { IconUIDismissLarge, IconUINavigationBack } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

import { GenericHeader } from './GenericHeader';
import { DefaultHeaderParams } from '../../Nav';

export enum HeaderLeftIcon {
  Back = 'back',
  Dismiss = 'dismiss',
}

export default function DefaultHeader({
  route,
  navigation,
  back,
  options,
}: NativeStackHeaderProps): React.ReactElement {
  const headerParams = getDefaultHeaderParams(route);

  let leftIconComponent: React.FunctionComponent<IconProps>;
  switch (headerParams?.leftIcon) {
    case HeaderLeftIcon.Dismiss:
      leftIconComponent = IconUIDismissLarge;
      break;
    case HeaderLeftIcon.Back:
    default:
      leftIconComponent = IconUINavigationBack;
      break;
  }

  const headerRight = useMemo(() => {
    return options.headerRight?.({ canGoBack: false });
  }, [options]);

  const onLeftIconPress = useCallback(() => {
    headerParams?.navigateToTop ? navigation.popToTop() : back && navigation.goBack();
  }, [headerParams, navigation, back]);

  if (headerParams?.hideLeftIcon) {
    return (
      <GenericHeader
        title={options.title}
        rightComponent={headerRight}
        onLeftIconPress={onLeftIconPress}
      />
    );
  }

  return (
    <GenericHeader
      title={options.title}
      style={options.headerStyle as ViewStyle}
      onLeftIconPress={onLeftIconPress}
      rightComponent={headerRight}
      LeftIconComponent={leftIconComponent}
    />
  );
}

function getDefaultHeaderParams(
  route: Route<string, object | undefined>
): DefaultHeaderParams | undefined {
  return route.params ? (route.params as DefaultHeaderParams) : undefined;
}
