import React from 'react';
import { Text, TouchableOpacity, View, ViewStyle } from 'react-native';

import { SafeAreaView } from 'react-native-safe-area-context';

import { IconDropper, UniformSpace, UniformStyleSheet, useUniformStyles } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

import { useAppSession } from '../../session/AppSession';
import { AppTestIDs } from '../AppTestIDs';
import { DeveloperOptions } from '../DeveloperOptions/DeveloperOptions';

const uniStyles = UniformStyleSheet.create((colors) => ({
  container: {
    backgroundColor: colors.bgLevel1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: UniformSpace.one,
    justifyContent: 'space-between',
    paddingTop: UniformSpace.oneAndHalf,
  },
  leftIcon: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: UniformSpace.half,
  },
  rightIcon: {
    alignItems: 'flex-end',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.contentDefault,
  },
  right: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: UniformSpace.half,
  },
}));

interface Props {
  title?: string;
  rightComponent?: React.ReactNode;
  LeftIconComponent?: React.FunctionComponent<IconProps>;
  style?: ViewStyle;
  onLeftIconPress?: () => void;
}

export function GenericHeader({
  title,
  rightComponent,
  LeftIconComponent,
  style,
  onLeftIconPress,
}: Props): React.ReactElement {
  const styles = useUniformStyles(uniStyles);
  const { isDevelopmentEnvironment } = useAppSession();

  return (
    <SafeAreaView edges={['top']} style={[styles.container, style]}>
      <View style={styles.leftIcon}>
        {onLeftIconPress && LeftIconComponent && (
          <TouchableOpacity onPress={onLeftIconPress} testID={AppTestIDs.headerBackButton}>
            <LeftIconComponent color={'default'} />
          </TouchableOpacity>
        )}
        <Text style={styles.title}>{title}</Text>
      </View>
      <View style={styles.right}>
        {rightComponent}
        <View style={styles.rightIcon}>
          {isDevelopmentEnvironment && <DeveloperOptions icon={<IconDropper color="subtle" />} />}
        </View>
      </View>
    </SafeAreaView>
  );
}
