import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  purchaserListItemContainer: {
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: colors.bgLevel0,
    padding: UniformSpace.one,
    gap: UniformSpace.one,
    borderRadius: UniformSpace.half,
  },
  purchaserSubInfo: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.half,
  },
  purchaserName: {
    fontWeight: '700',
  },
}));
