import React from 'react';

import { screen } from '@testing-library/react-native';

import { PurchaserListItem } from './PurchaserListItem';
import { TicketGroup } from '../../gql/public/__generated__/graphql';
import { renderWithOptions } from '../../test/renderHelpers';

const ticketGroup: TicketGroup = {
  id: '1',
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  tickets: [
    {
      createdAt: '2021-08-10T00:00:00Z',
      id: '1',
      updatedAt: '2021-08-10T00:00:00Z',
      ticketedEventId: '1',
      refundable: false,
    },
  ],
  passes: [],
  createdAt: '2021-08-10T00:00:00Z',
  updatedAt: '2021-08-10T00:00:00Z',
};

describe('PurchaserListItem Tests', () => {
  it('should render PurchaserListItem with tickets', () => {
    renderWithOptions(<PurchaserListItem ticketGroup={ticketGroup} />);

    expect(screen.getByText('<PERSON><PERSON>, <PERSON>')).toBeTruthy();
    expect(screen.getByText('<EMAIL>')).toBeTruthy();
  });

  it('should render PurchaserListItem with passes', () => {
    renderWithOptions(
      <PurchaserListItem
        ticketGroup={{
          ...ticketGroup,
          tickets: [],
          passes: [
            {
              createdAt: '2021-08-10T00:00:00Z',
              updatedAt: '2021-08-10T00:00:00Z',
              id: '1',
              passConfigId: '1',
            },
          ],
        }}
      />
    );

    expect(screen.getByText('Doe, John')).toBeTruthy();
    expect(screen.getByText('<EMAIL>')).toBeTruthy();
  });
});
