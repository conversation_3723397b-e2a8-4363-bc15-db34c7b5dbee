import React, { ReactElement } from 'react';
import { View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Text, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './PurchaserListItem.style';
import { TicketGroup } from '../../gql/public/__generated__/graphql';

type Props = {
  ticketGroup: TicketGroup;
};

export function PurchaserListItem(props: Props): ReactElement {
  const styles = useUniformStyles(styleSheet);
  const { ticketGroup } = props;

  const i18nStrings = useI18n(
    {
      tickets: ['purchaser-list-item.tickets', { count: ticketGroup.tickets?.length ?? 0 }],
      passes: ['purchaser-list-item.passes', { count: ticketGroup.passes?.length ?? 0 }],
    },
    [ticketGroup]
  );

  return (
    <View style={styles.purchaserListItemContainer}>
      <Text style={styles.purchaserName}>
        {ticketGroup.lastName}, {ticketGroup.firstName}
      </Text>
      <View style={styles.purchaserSubInfo}>
        <Text>{ticketGroup.email}</Text>
        <Text>
          {(ticketGroup.passes?.length ?? 0) > 0 ? i18nStrings.passes : i18nStrings.tickets}
        </Text>
      </View>
    </View>
  );
}
