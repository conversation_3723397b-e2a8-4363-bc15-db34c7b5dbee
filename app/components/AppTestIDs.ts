export const AppTestIDs = {
  // Landing Screen [Landing.tsx]
  landingRoot: 'landingRoot',
  landingGetStarted: 'landingGetStarted',
  landingContinue: 'Org_landingContinueTeam',

  // Volunteer Info Screen [VolunteerInfo.tsx]
  volunteerInfoRoot: 'volunteerInfoRoot',
  volunteerInfoFirstNameInput: 'volunteerInfoFirstNameInput',
  volunteerInfoLastNameInput: 'volunteerInfoLastNameInput',
  volunteerInfoAccessCodeInput: 'volunteerInfoAccessCodeInput',
  volunteerInfoEmailInput: 'volunteerInfoEmailInput',
  volunteerInfoContinue: 'volunteerInfoContinue',
  volunteerInfoYes: 'volunteerInfoYes',
  volunteerInfoNo: 'volunteerInfoNo',

  // Loading Org Screen [LoadingOrg.tsx]
  loadingOrgRoot: 'loadingOrgRoot',
  loadingOrgErrorState: 'loadingOrgBack',
  loadingOrgBackButton: 'loadingOrgBack',
  loadingOrgLoadingState: 'loadingOrgSpinner',

  // Organization Event List Screen [OrganizationEvents.tsx]
  orgEventsRoot: 'orgEventsRoot',
  orgEventsHeader: 'orgEventsHeader',
  orgEventsLoadingState: 'orgEventsLoadingState',
  orgEventsErrorState: 'orgEventsErrorState',
  orgEvent: (id: string) => `Org_Event_${id}`,

  // Event Details Screen [EventConfirmation.tsx]
  confirmEventRoot: 'confirmEventRoot',
  confirmEventScanTickets: 'confirmEventScanTickets',
  confirmEventFinishScanning: 'confirmEventFinishScanning',

  // Scanner Screen [ScannerScreen.tsx]
  scannerRoot: 'scanner-root',
  scannerOverlay: 'scanner-overlay',
  scannerContainer: 'scanner-container',

  // Scanner Overlay [ScannerOverlay.tsx]
  needHelpButton: 'need-help-button',

  // Event Action Selection Screen [EventActionSelection.tsx]
  eventActionSelectionRoot: 'event-action-selection-root',
  eventActionSelectionBackToAllEventsButton: 'event-action-selection-back-to-all-events-button',
  eventActionSelectionCheckInFans: 'event-action-selection-check-in-fans',
  eventActionSelectionContactSupport: 'event-action-selection-contact-support',
  eventActionSelectionScanTickets: 'event-action-selection-scan-tickets',
  eventActionSelectionSellTickets: 'event-action-selection-sell-tickets',
  eventActionSelectionCustomSales: 'event-action-selection-custom-sales',

  // POS Item Selection Screen [PosItemSelection.tsx]
  posItemSelectionRoot: 'item-selection-root',
  posItemSelectionLoading: 'item-selection-loading',
  posItemSelectionError: 'item-selection-error',
  posItemSelectionHeader: 'item-selection-header',
  posCashReviewOrderButton: (disabled: boolean) =>
    `cash-review-order-button${disabled ? '-disabled' : ''}`,
  posCardReviewOrderButton: (disabled: boolean) =>
    `card-review-order-button${disabled ? '-disabled' : ''}`,
  posItemSelectionErrorContactSupportLink: 'item-selection-error-contact-support-link',
  posItemSelectionTicketLimitNote: 'item-selection-ticket-limit-note',

  // Selectable Item List Component [SelectableItemList.tsx]
  selectableItemListRoot: 'selectable-item-list-root',
  selectableItemListList: 'selectable-item-list-list',

  // Selectable Item Component [SelectableItem.tsx]
  selectableItemRoot: 'selectable-item-root',
  selectableItemName: 'selectable-item-name',
  selectableItemPrice: 'selectable-item-price',
  selectableItemLimitedQuantity: 'selectable-item-limited-quantity',
  selectableItemSoldOut: 'selectable-item-sold-out',

  // Counter Component [Counter.tsx]
  counterRoot: (itemId: string) => `counter-root-${itemId}`,
  counterIncrement: (itemId: string, disabled: boolean) =>
    `counter-increment-${itemId}${disabled ? '-disabled' : ''}`,
  counterDecrement: (itemId: string, disabled: boolean) =>
    `counter-decrement-${itemId}${disabled ? '-disabled' : ''}`,
  counterInput: (itemId: string, disabled: boolean) =>
    `counter-input-${itemId}${disabled ? '-disabled' : ''}`,

  // Review Order Screen [ReviewOrder.tsx]
  reviewOrderRoot: 'review-order-root',
  reviewOrderLoading: 'review-order-loading',
  reviewOrderHeader: 'review-order-header',
  reviewOrderItemList: 'review-order-item-list',
  reviewOrderItem: (id: string) => `review-order-item-${id}`,
  reviewOrderItemName: (id: string) => `review-order-item-name-${id}`,
  reviewOrderItemPrice: (id: string) => `review-order-item-price-${id}`,
  reviewOrderProcessButton: `accept-payments-process-button`,
  reviewOrderEditButton: `accept-payments-edit-button`,
  reviewOrderErrorRoot: 'review-order-error-root',
  reviewOrderErrorContactSupportLink: 'review-order-error-contact-support-link',
  reviewOrderOrderTransactionFee: 'review-order-order-transaction-fee',

  // Receipt Selection Screen [ReceiptSelection.tsx]
  receiptSelectionRoot: 'receipt-selection-root',
  receiptSelectionHeader: 'receipt-selection-header',
  receiptSelectionSubHeader: 'receipt-selection-sub-header',
  receiptSelectionOptionYes: (selected: boolean) =>
    `receipt-selection-option-yes${selected ? '-selected' : ''}`,
  receiptSelectionOptionNo: (selected: boolean) =>
    `receipt-selection-option-no${selected ? '-selected' : ''}`,
  receiptSelectionEmailInputHeader: `receipt-selection-email-input-header`,
  receiptSelectionEmailInputInput: `receipt-selection-email-input-input`,
  receiptSelectionEmailInputError: `receipt-selection-email-input-error`,
  receiptSelectionContinueButton: (disabled: boolean, isPaidTransaction: boolean) =>
    `receipt-selection-continue-button${isPaidTransaction ? '-paid' : '-free'}${
      disabled ? '-disabled' : ''
    }`,

  // Discover Readers Landing Screen [DiscoverReadersLanding.tsx]
  discoverReadersLandingRoot: 'discover-readers-landing-root',
  discoverReadersLandingTextContent: 'discover-readers-text-content',
  discoverReadersLandingHeader: 'discover-readers-landing-header',
  discoverReadersLandingConnectSubHeader: 'discover-readers-landing-connect-sub-header',
  discoverReadersLandingEnableSubHeader: 'discover-readers-landing-enable-sub-header',
  discoverReadersLandingFindReadersButton: 'discover-readers-landing-find-readers-button',
  discoverReadersLandingInitializationSpinner: 'discover-readers-landing-initialization-spinner',
  connectionErrorSubHeader: 'connection-error-sub-header',
  connectionErrorContactSupport: 'connection-error-contact-support',
  connectionErrorContactSupportLink: 'connection-error-contact-support-link',

  // Discover Nearby Readers Screen [DiscoverNearbyReaders.tsx]
  discoverNearbyReadersLoadingIcon: 'discover-nearby-readers-loading-icon',
  discoverNearbyReadersContinueButton: (isDisabled: boolean) =>
    `connect-reader-continue-button${isDisabled ? '-disabled' : ''}`,

  // Connect Reader Landing Screen [ConnectReader.tsx]
  connectReaderLandingRoot: 'connect-reader-landing-root',
  connectReaderDiscoveredReader: (serialNumber: string, isDisabled: boolean) =>
    `reader-${serialNumber}${isDisabled ? '-disabled' : ''}`,
  connectReaderConnectedReader: (serialNumber: string) => `connected-reader-${serialNumber}`,
  connectReaderContactSupport: 'connect-reader-contact-support',
  connectReaderContactSupportLink: 'connect-reader-contact-support-link',
  connectReaderErrorConnectingReaders: 'connect-reader-error-connecting-readers',
  connectReaderDisconnectButton: 'connect-reader-disconnect-button',
  connectReaderErrorBatteryCriticallyLow: 'connect-reader-error-battery-critically-low',

  // Generic Error Screen [GenericError.tsx]
  genericErrorRoot: 'generic-error-root',
  genericErrorCTAButton: 'generic-error-cta-button',
  genericErrorIcon: 'generic-error-icon',
  genericErrorHeader: 'generic-error-header',
  genericErrorSubHeader: 'generic-error-sub-header',

  // Order Total Component [OrderTotal.tsx]
  orderTotalRoot: 'order-total-root',
  orderTotalNumberOfItems: 'order-total-number-of-items',
  orderTotalPrice: 'order-total-price',
  orderTotalSubtext: 'order-total-subtext',
  orderTotalLoading: 'order-total-loading',

  // Battery Icon [BatteryLevelUtils.tsx]
  batteryLevelIcon: (percentage: number, charging: boolean) =>
    `battery-level-icon-${percentage}${charging ? '-charging' : ''}`,

  // Accept Payments [AcceptPayments.tsx]
  acceptPaymentsRoot: 'accept-payments-root',
  acceptPaymentsLoadingHeader: 'accept-payments-loading-header',
  acceptPaymentsLoadingHelpText: 'accept-payments-loading-help-text',
  acceptPaymentsAcceptPayment: 'accept-payments-accept-payment',
  acceptPaymentsContactSupport: 'accept-payments-contact-support',
  acceptPaymentsContactSupportLink: 'accept-payments-contact-support-link',
  acceptPaymentsDeclinedByStripe: 'accept-payments-declined-by-stripe',
  acceptPaymentsDeclinedByStripeTryAgain: 'accept-payments-declined-by-stripe-try-again',
  acceptPaymentsLocationDisabled: 'accept-payments-location-disabled',
  acceptPaymentsPaymentsError: 'accept-payments-payments-error',

  // Accept Cash Payments [AcceptCashPayments.tsx]
  acceptCashPaymentsRoot: 'accept-cash-payments-root',

  // Order Complete [OrderComplete.tsx]
  orderCompleteRoot: 'order-complete-root',
  orderCompleteHeader: 'order-complete-header',
  orderCompleteSubHeader: 'order-complete-sub-header',
  orderCompleteNavigateHome: 'order-complete-navigate-home',
  orderCompleteNewOrderButton: 'order-complete-new-order-button',

  // Reader Display Input [ReaderDisplayInput.tsx]
  readerDisplayInputRoot: 'reader-display-input-root',
  readerDisplayInputMessage: 'reader-display-input-message',

  // Reader Display Message [ReaderDisplayMessage.tsx]
  readerDisplayMessageRoot: 'reader-display-message-root',
  readerDisplayMessageMessage: 'reader-display-message-message',

  // Purchaser List [PurchaserList.tsx]
  purchaserListContainer: 'purchaser-list-container',
  purchaserListContactSupport: 'purchaser-list-contact-support',
  purchaserListContactSupportLink: 'purchaser-list-contact-support-link',
  purchaserListSearchInput: 'purchaser-list-search-input',
  purchaserListEmptyState: 'purchaser-list-empty-state',
  purchaserListErrorState: 'purchaser-list-error-state',
  purchaserListNoResultsFound: 'purchaser-list-no-results-found',

  // Contact Support [ContactSupport.tsx]
  contactSupportPhone: 'contact-support-phone',
  contactSupportEmail: 'contact-support-email',
  contactSupportTutorials: 'contact-support-tutorials',

  // Custom Sale Calculator [CustomSaleCalculator.tsx]
  customSaleCalculator: 'custom-sale-calculator',
  customSaleCalculatorButton: (value: string) => `custom-sale-calculator-button-${value}`,
  customSaleCalculatorErrorMin: 'custom-sale-calculator-error-minimum',
  customSaleCalculatorErrorMax: 'custom-sale-calculator-error-maximum',
  customSaleCalculatorCheckoutButtonCash: (disabled: boolean) =>
    `custom-sale-calculator-checkout-button-cash${disabled ? '-disabled' : ''}`,
  customSaleCalculatorCheckoutButtonCard: (disabled: boolean) =>
    `custom-sale-calculator-checkout-button-card${disabled ? '-disabled' : ''}`,
  customSaleCalculatorCancelOrderButton: 'custom-sale-calculator-cancel-order-button',
  customSaleCalculatorHeader: 'custom-sale-calculator-header',
  customSaleCalculatorTotal: 'custom-sale-calculator-total',
  customSaleCalculatorScroll: 'custom-sale-calculator-scroll',

  // Custom Sale Selection [CustomSaleSelection.tsx]
  customSalesSelectionRoot: 'custom-sales-selection-root',
  customSaleSelectionContinueButton: 'custom-sale-selection-continue-button',
  customSaleSelectionRadioGroup: 'custom-sale-selection-radio-group',
  customSaleSelectionRadioGroupOption: (value: string) =>
    `custom-sale-selection-radio-group-radio-option-${value}`,
  customSaleSelectionCancelOrderButton: 'custom-sale-selection-cancel-button',

  // Instructions [InstructionsSheet.tsx]
  instructionsBottomSheet: 'instructions-bottom-sheet',
  closeInstructionsButton: 'close-instructions-button',

  // Upload Tickets Button [UploadRemainingTicketsModal.tsx]
  uploadRemainingTicketsButton: 'upload-remaining-tickets-button',

  // Event Selection
  homePageSelectAndSwitchEventButton: 'home-page-select-and-switch-event-button',
  homePageEventTitle: 'home-page-event-title',
  homePageEventDetails: 'home-page-event-details',

  // Sign Out
  homePageSignOutButton: 'home-page-sign-out-button',
  signOutModalButton: 'sign-out-modal-sign-out',

  // Scanner Upload Status
  scannerUploadStatus: 'scanner-upload-status',
  scannerUploadStatusTicketsToUpload: 'scanner-upload-status-tickets-to-upload',
  scannerUploadStatusNoTicketsToUpload: 'scanner-upload-status-no-tickets-to-upload',

  // Reader Connection
  readerConnectionWrapperClose: 'reader-connection-wrapper-close',
  readerConnectionWrapperButton: 'reader-connection-wrapper-button',
  readerConnectionLoadingContainer: 'reader-connection-loading-container',
  readerConnectionListContainer: 'reader-connection-list-container',
  homePageFindNearbyReadersButton: 'home-page-find-nearby-readers-button',

  // Camera Permissions
  cameraPermissionsModalButton: 'camera-permissions-modal-button',

  // Menu Button
  scanTicketsMenuButton: 'scan-tickets-menu-button',
  searchForOrderMenuButton: 'search-for-order-menu-button',

  // Bottom Sheet
  homeTabBottomSheet: 'homeTab',
  sellTabBottomSheet: 'sellTab',
  checkInTabBottomSheet: 'checkInTab',

  // Sell Tab
  sellRootContainer: 'sell-root-container',
  ticketsButton: 'tickets-button',
  customSalesButton: 'custom-sales-button',
  infoIcon: 'info-icon',

  // Header
  headerBackButton: 'header-back-button',

  // Home Page Scroll
  homePageScroll: 'home-page-scroll',
};
