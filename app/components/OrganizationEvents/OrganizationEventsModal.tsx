import React, { useCallback } from 'react';

import { OrganizationEvents } from './OrganizationEvents';
import { AppNav } from '../../Nav';
import { useAccessContext } from '../../context/AccessContext';
import { TicketedEvent } from '../../gql/public/__generated__/graphql';
import { Navigation, Screens } from '../../utils/Enums';
import { eventToEventInfo } from '../../utils/EventUtils';
import { checkoutItemSelections } from '../../utils/stateVars';
import { ModalSwipeGestureHandler } from '../Shared/ModalUtils/ModalSwipe';

export function OrganizationEventsModal(): React.ReactElement {
  const accessContext = useAccessContext();

  const nav = AppNav.root.useNavigation();
  const sellNav = AppNav.sell.useNavigation();
  const checkInNav = AppNav.checkIn.useNavigation();

  const hide = useCallback(() => {
    if (nav.canGoBack()) {
      nav.goBack();
    }
  }, [nav]);

  const eventSelected = useCallback(
    (event: TicketedEvent) => {
      accessContext.setEventInfo(eventToEventInfo(event));
      if (accessContext.eventInfo?.id !== event.id) {
        sellNav.reset({ index: 0, routes: [{ name: Screens.Sell }] });
        checkInNav.reset({ index: 0, routes: [{ name: Screens.CheckIn }] });
        checkoutItemSelections([]);
      }
      nav.goBack();
    },
    [accessContext, nav, sellNav, checkInNav]
  );

  if (!accessContext.organization) {
    return <></>;
  }

  return (
    <ModalSwipeGestureHandler hide={hide} iosFling={true} androidFling={true}>
      <OrganizationEvents
        organization={accessContext.organization}
        onEventSelected={eventSelected}
        onClose={hide}
      />
    </ModalSwipeGestureHandler>
  );
}

export function useShowOrganizationEvents(): {
  showOrganizationEvents: () => void;
} {
  const nav = AppNav.root.useNavigation();
  const showOrganizationEvents = useCallback(() => {
    return nav.navigate(Navigation.OrganizationEvents, {});
  }, [nav]);
  return { showOrganizationEvents };
}
