import React from 'react';
import { Text, TouchableOpacity, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import {
  IconUIDismissMedium,
  UniformSpace,
  UniformStyleSheet,
  useUniformStyles,
} from '@hudl/rn-uniform';

import { School, TicketedEvent } from '../../gql/public/__generated__/graphql';
import { nonUniformColors } from '../../utils/ColorUtils';
import { AppTestIDs } from '../AppTestIDs';
import { generalUniformStyles } from '../Styles';
import TicketedEventList from '../TicketedEventList/TicketedEventList';

const uniStyles = UniformStyleSheet.create(() => ({
  container: {
    height: '100%',
    marginTop: UniformSpace.four,
    paddingVertical: UniformSpace.oneAndHalf,
    paddingHorizontal: UniformSpace.quarter,
  },
  header: {
    flexDirection: 'row',
    marginBottom: UniformSpace.one,
    alignItems: 'center',
    gap: UniformSpace.one,
  },
  list: {
    marginHorizontal: UniformSpace.one,
  },
  headerText: {
    color: nonUniformColors.contentStrongBG,
    fontSize: 24,
    fontWeight: '700',
  },
  contentContainerStyle: {
    paddingBottom: 70,
    flexGrow: 1,
  },
}));

interface Props {
  organization: School;
  onEventSelected: (event: TicketedEvent) => void;
  onClose: () => void;
}

export function OrganizationEvents({
  organization,
  onEventSelected,
  onClose,
}: Props): React.ReactElement {
  const styles = useUniformStyles(uniStyles);
  const generalStyles = useUniformStyles(generalUniformStyles);

  const strings = useI18n(
    {
      header: 'organization-events.header',
    },
    []
  );

  return (
    <View style={[generalStyles.container, styles.container]} testID={AppTestIDs.orgEventsRoot}>
      <TicketedEventList
        style={styles.list}
        orgId={organization.id}
        onItemPressed={onEventSelected}
        contentContainerStyle={styles.contentContainerStyle}
        ListHeaderComponent={
          <View style={styles.header} testID={AppTestIDs.orgEventsHeader}>
            <TouchableOpacity onPress={onClose}>
              <IconUIDismissMedium />
            </TouchableOpacity>
            <Text style={styles.headerText}>{strings.header}</Text>
          </View>
        }
      />
    </View>
  );
}
