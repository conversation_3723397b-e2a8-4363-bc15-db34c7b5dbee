import React, { ReactElement, useMemo } from 'react';
import { View } from 'react-native';

import Modal from 'react-native-modal';

import { useUniformStyles, Button, Text, useUniformTheme } from '@hudl/rn-uniform';

import { uniStyles } from './Modal.style';
import { useFanModalContext } from './ModalProvider';

export function FanModal(): ReactElement {
  const {
    isOpen,
    title,
    text,
    actions,
    showCancelAction,
    cancelText,
    onModalWillHide,
    closeModal,
    modalType,
  } = useFanModalContext();
  const { colors } = useUniformTheme();
  const styles = useUniformStyles(uniStyles);
  const modalActions = useMemo((): React.ReactElement[] => {
    return (actions ?? []).map(
      (item, key): React.ReactElement => (
        <View style={styles.action} key={key}>
          <Button
            buttonType={item.buttonType}
            status={item.status}
            size="medium"
            text={item.text}
            isBlock={true}
            testID={item.testID}
            accessibilityLabel={item.accessibilityLabel}
            onPress={item.onPress}
          />
        </View>
      )
    );
  }, [actions, styles.action]);

  const modalStyle = useMemo(() => {
    switch (modalType) {
      case 'confirm':
        return styles.modalConfirm;
      case 'deny':
        return styles.modalDeny;
      case 'normal':
        return styles.modal;
    }
  }, [modalType, styles.modal, styles.modalConfirm, styles.modalDeny]);

  return (
    <Modal
      isVisible={isOpen}
      animationIn="fadeIn"
      animationInTiming={400}
      animationOut="fadeOutDown"
      animationOutTiming={200}
      hasBackdrop={true}
      backdropColor={colors.bgScrim}
      backdropOpacity={1}
      swipeDirection="down"
      style={styles.container}
      onBackButtonPress={closeModal}
      onBackdropPress={closeModal}
      onSwipeComplete={closeModal}
      onModalHide={onModalWillHide}>
      <View style={modalStyle}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{text}</Text>
        <View style={styles.footer}>
          {modalActions}
          {showCancelAction && (
            <View style={styles.action}>
              <Button
                buttonType="cancel"
                size="medium"
                text={cancelText}
                isBlock={true}
                onPress={closeModal}
                buttonStyle={'minimal'}
              />
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}
