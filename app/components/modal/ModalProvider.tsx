import React, { useCallback, useContext, useState } from 'react';

import { Action } from '@hudl/rn-uniform';

export type ModalType = 'confirm' | 'deny' | 'normal';
interface ModalProps {
  modalType: ModalType;
  isOpen: boolean;
  title: string;
  text: string;
  actions?: Action[];
  showCancelAction?: boolean;
  cancelText?: string;
  showModal: (
    modalTitle: string,
    content: string,
    type: ModalType,
    showCancel?: boolean,
    btnActions?: Action[],
    cancelTxt?: string
  ) => void;
  closeModal: () => void;
  onModalWillHide: () => void;
}
export const FanModalContext = React.createContext<ModalProps | undefined>(undefined);

export function useFanModalContext(): ModalProps {
  const context = useContext(FanModalContext);

  if (!context) {
    throw new Error('Please wrap root component in a FanModalProvider');
  }

  return context;
}

export function FanModalProvider({ children }: React.PropsWithChildren): React.ReactElement | null {
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [text, setText] = useState('');
  const [actions, setActions] = useState<Action[] | undefined>(undefined);
  const [showCancelAction, setShowCancelAction] = useState<boolean | undefined>(undefined);
  const [cancelText, setCancelText] = useState<string | undefined>('Cancel');
  const [modalType, setModalType] = useState<ModalType>('normal');

  const closeModal = useCallback(() => {
    setIsOpen(false);
  }, []);
  const onModalWillHide = useCallback(() => {
    setIsOpen(false);
    setTitle('');
    setText('');
    setActions([]);
    setShowCancelAction(false);
    setCancelText('Cancel');
  }, []);
  const showModal = useCallback(
    (
      modalTitle: string,
      content: string,
      type: ModalType,
      showCancel?: boolean,
      btnActions?: Action[],
      cancelTxt?: string
    ) => {
      setIsOpen(true);
      setTitle(modalTitle);
      setText(content);
      setActions(btnActions);
      setShowCancelAction(showCancel);
      cancelTxt && setCancelText(cancelTxt);
      setModalType(type);
    },
    []
  );

  return (
    <FanModalContext.Provider
      value={{
        showModal,
        isOpen: isOpen,
        title: title,
        text: text,
        actions: actions,
        cancelText: cancelText,
        showCancelAction: showCancelAction,
        closeModal,
        onModalWillHide,
        modalType,
      }}>
      {children}
    </FanModalContext.Provider>
  );
}

export function withFanModalProvider<P extends object>(Component: React.ComponentType<P>) {
  return function EnhancedComponent(props: P) {
    return (
      <FanModalProvider>
        <Component {...props} />
      </FanModalProvider>
    );
  };
}
