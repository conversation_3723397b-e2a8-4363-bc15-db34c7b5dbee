import { UniformStyleSheet, UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../utils/ColorUtils';

export const uniStyles = UniformStyleSheet.create((colors) => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  modal: {
    padding: UniformSpace.oneAndHalf,
    paddingBottom: UniformSpace.one,
    width: '100%',
    maxWidth: 400,
    backgroundColor: nonUniformColors.popoverBG,
    borderRadius: 14,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: UniformSpace.one,
    },
    shadowRadius: UniformSpace.two,
    shadowOpacity: 0.3,
  },
  modalConfirm: {
    padding: UniformSpace.oneAndHalf,
    backgroundColor: colors.buttonConfirm,
    borderRadius: 4,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: UniformSpace.one,
    },
    shadowRadius: UniformSpace.two,
    shadowOpacity: 0.3,
  },
  modalDeny: {
    padding: UniformSpace.oneAndHalf,
    backgroundColor: colors.buttonDestroy,
    borderRadius: 4,
    shadowColor: 'black',
    shadowOffset: {
      width: 0,
      height: UniformSpace.one,
    },
    shadowRadius: UniformSpace.two,
    shadowOpacity: 0.3,
  },
  title: {
    display: 'flex',
    alignSelf: 'center',
    fontWeight: '700',
    color: colors.contentContrast,
    fontSize: 18,
  },
  message: {
    paddingTop: UniformSpace.one,
    alignSelf: 'center',
  },
  footer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    marginTop: UniformSpace.one,
  },
  action: {
    marginBottom: UniformSpace.half,
  },
}));
