import React, { ReactElement } from 'react';

import { Button, UniformSpace, UniformStyleSheet, useUniformStyles } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../utils/ColorUtils';

const uniStyles = UniformStyleSheet.create(() => ({
  button: {
    width: '100%',
    backgroundColor: nonUniformColors.containerBG,
    paddingVertical: UniformSpace.one,
    borderRadius: 8,
  },
}));

interface Props {
  text: string;
  icon: ReactElement;
  onPress: () => void;
  qaId?: string;
  isDisabled?: boolean;
}

export default function MenuButton({
  text,
  icon,
  onPress,
  qaId,
  isDisabled,
}: Props): React.ReactElement {
  const styles = useUniformStyles(uniStyles);

  return (
    <Button
      style={styles.button}
      onPress={onPress}
      buttonType={'subtle'}
      icon={icon}
      text={text}
      testID={`${qaId}${isDisabled ? '-disabled' : ''}`}
      isDisabled={isDisabled}
    />
  );
}
