import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

export const styleSheet = UniformStyleSheet.create((colors) => ({
  tooltipContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.half,
    paddingTop: UniformSpace.one,
    paddingBottom: UniformSpace.oneAndQuarter,
  },
  tooltipHeader: {
    color: colors.contentDefault,
    fontSize: 16,
    fontWeight: '700',
  },
  tooltipText: {
    color: colors.contentDefault,
  },
  tooltipHeaderContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: UniformSpace.half,
  },
  reactNativeText: {
    color: colors.contentDefault,
    fontSize: 16,
    lineHeight: 24,
  },
  linkText: {
    color: colors.linkArticleText,
    fontSize: 16,
    lineHeight: 24,
  },
}));
