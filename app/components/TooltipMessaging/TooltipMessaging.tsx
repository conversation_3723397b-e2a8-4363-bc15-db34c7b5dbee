import React, { ReactElement } from 'react';
import { Text, View } from 'react-native';

import { Button, IconUIDismissMedium, useUniformStyles } from '@hudl/rn-uniform';

import { styleSheet } from './TooltipMessaging.style';
import { TooltipElement } from '../../types/shared';

type Props = {
  currentlyOpenToolTip: TooltipElement | undefined;
  closeToolTip: () => void;
};

export function TooltipMessaging(props: Props): ReactElement {
  const styles = useUniformStyles(styleSheet);
  const { currentlyOpenToolTip, closeToolTip } = props;

  return (
    <View style={styles.tooltipContainer}>
      <View style={styles.tooltipHeaderContainer}>
        <Text style={styles.tooltipHeader}>{currentlyOpenToolTip?.tooltipHeader}</Text>
        <Button
          icon={<IconUIDismissMedium />}
          onPress={closeToolTip}
          buttonStyle="minimal"
          buttonType="secondary"
        />
      </View>
      <Text style={[styles.reactNativeText]}>{currentlyOpenToolTip?.tooltipText}</Text>
    </View>
  );
}
