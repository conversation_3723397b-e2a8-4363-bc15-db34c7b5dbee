import {
  CanadaIsoAlpha2,
  HudlHQLocationCanadaIdProd,
  HudlHQLocationCanadaIdThor,
  HudlHQLocationIdProd,
  HudlHQLocationIdThor,
} from './Constants';
import { School } from '../gql/public/__generated__/graphql';

export const getLocationIdForSchool = (
  school: School,
  isDevelopmentEnvironment: boolean
): string => {
  const isCanada = school?.countryIsoAlpha2 === CanadaIsoAlpha2;

  if (isDevelopmentEnvironment) {
    return isCanada ? HudlHQLocationCanadaIdThor : HudlHQLocationIdThor;
  }

  return isCanada ? HudlHQLocationCanadaIdProd : HudlHQLocationIdProd;
};
