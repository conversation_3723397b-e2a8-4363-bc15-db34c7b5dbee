import React from 'react';
import { ReactElement } from 'react';
import { StyleSheet, Text } from 'react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

const styles = StyleSheet.create({
  reactNativeText: {
    fontSize: 16,
    lineHeight: 24,
  },
  boldText: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: 'bold',
  },
});

export const boldSubstring = (
  textArray: string,
  firstHalf: boolean,
  translate: boolean = true
): ReactElement => {
  const translationArray = translate ? HudlI18n.t(textArray) : textArray;
  return (
    <Text>
      <Text style={firstHalf ? styles.boldText : styles.reactNativeText}>
        {translationArray[0]}
      </Text>
      <Text style={firstHalf ? styles.reactNativeText : styles.boldText}>
        {translationArray[1]}
      </Text>
    </Text>
  );
};
