import React from 'react';

import {
  IconBattery,
  IconBattery0,
  IconBattery0Charge,
  IconBattery100,
  IconBattery100Charge,
  IconBattery25,
  IconBattery50,
  IconBattery50Charge,
  IconBattery75,
  IconColor,
  Size,
} from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

import { AppTestIDs } from '../components/AppTestIDs';

export const getBatteryLevelIcon = (
  batteryLevel: number,
  isCharging: boolean,
  size: Size,
  color: IconColor
): React.ReactElement<IconProps> => {
  if (batteryLevel < 0.25) {
    if (isCharging) {
      return (
        <IconBattery0Charge
          size={size}
          color="critical"
          testID={AppTestIDs.batteryLevelIcon(0, true)}
        />
      );
    }
    return (
      <IconBattery0 size={size} color="critical" testID={AppTestIDs.batteryLevelIcon(0, false)} />
    );
  }
  if (batteryLevel < 0.5) {
    if (isCharging) {
      return (
        <IconBattery50Charge
          size={size}
          color={color}
          testID={AppTestIDs.batteryLevelIcon(50, true)}
        />
      );
    }
    return (
      <IconBattery25 size={size} color={color} testID={AppTestIDs.batteryLevelIcon(25, false)} />
    );
  }

  if (batteryLevel < 0.75) {
    if (isCharging) {
      return (
        <IconBattery50Charge
          size={size}
          color={color}
          testID={AppTestIDs.batteryLevelIcon(50, true)}
        />
      );
    }
    return (
      <IconBattery50 size={size} color={color} testID={AppTestIDs.batteryLevelIcon(50, false)} />
    );
  }

  if (batteryLevel < 0.99) {
    if (isCharging) {
      return (
        <IconBattery50Charge
          size={size}
          color={color}
          testID={AppTestIDs.batteryLevelIcon(50, true)}
        />
      );
    }
    return (
      <IconBattery75 size={size} color={color} testID={AppTestIDs.batteryLevelIcon(75, false)} />
    );
  }

  if (batteryLevel > 0.99) {
    if (isCharging) {
      return (
        <IconBattery100Charge
          size={size}
          color={color}
          testID={AppTestIDs.batteryLevelIcon(100, true)}
        />
      );
    }
    return (
      <IconBattery100 size={size} color={color} testID={AppTestIDs.batteryLevelIcon(100, false)} />
    );
  }

  return <IconBattery />;
};
