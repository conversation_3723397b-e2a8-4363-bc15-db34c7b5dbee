import { TicketType, TicketedEvent } from '../gql/public/__generated__/graphql';

export function getTicketTypePrice(
  ticketType: TicketType | undefined,
  ticketedEvent: TicketedEvent | undefined
): number | undefined {
  const ttrPriceOverride = ticketedEvent?.ticketTypeReferences?.find(
    (ttr) => ttr?.ticketTypeId === ticketType?.id
  )?.priceOverride;
  return ttrPriceOverride !== undefined && ttrPriceOverride !== null
    ? ttrPriceOverride
    : ticketType?.priceInCents;
}
