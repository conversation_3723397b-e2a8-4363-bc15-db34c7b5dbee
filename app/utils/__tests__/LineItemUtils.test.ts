import { LineItemType } from '../../enums/shared';
import {
  mockLineItemSelection,
  mockTicketType,
  mockTicketedEvent,
} from '../../gql/mockData/TestData';
import { PassConfig, TicketType, TicketedEvent } from '../../gql/public/__generated__/graphql';
import { LineItemSelection } from '../../types/shared';
import { CustomSalesType } from '../Enums';
import {
  convertTicketTypesToLineItemConfigs,
  sumLineItemQuantities,
  sumLineItemPrices,
  convertPassConfigToLineItemConfig,
  lineItemDictToPricingSummaryInput,
} from '../LineItemUtils';

describe('LineItemUtils', () => {
  describe('convertTicketTypesToLineItemConfigs', () => {
    it('should convert ticket types to line item configs', () => {
      const ticketTypes: TicketType[] = [
        { ...mockTicketType, id: 't1', name: 'Ticket 1', priceInCents: 100 },
        { ...mockTicketType, id: 't2', name: 'Ticket 2', priceInCents: 200 },
        { ...mockTicketType, id: 't3', name: 'Ticket 3', priceInCents: 300 },
      ];
      const ticketedEvent: TicketedEvent = {
        ...mockTicketedEvent,
        quantityRemainingForTicketTypes: [
          { referenceId: 't1', quantityRemaining: 1 },
          { referenceId: 't2', quantityRemaining: 2 },
        ],
      };

      const result = convertTicketTypesToLineItemConfigs(ticketTypes, ticketedEvent);

      expect(result).toEqual([
        {
          lineItemId: 't1',
          lineItemType: LineItemType.TicketType,
          name: 'Ticket 1',
          unitPriceInCents: 100,
          quantityRemaining: 1,
          quantitySelected: 0,
        },
        {
          lineItemId: 't2',
          lineItemType: LineItemType.TicketType,
          name: 'Ticket 2',
          unitPriceInCents: 200,
          quantityRemaining: 2,
          quantitySelected: 0,
        },
        {
          lineItemId: 't3',
          lineItemType: LineItemType.TicketType,
          name: 'Ticket 3',
          unitPriceInCents: 300,
          quantityRemaining: undefined,
          quantitySelected: 0,
        },
      ]);
    });

    it('empty ticket types and no event', () => {
      const ticketTypes: TicketType[] = [];

      const result = convertTicketTypesToLineItemConfigs(ticketTypes, undefined);

      expect(result).toEqual([]);
    });
  });

  describe('convertPassConfigToLineItemConfigs', () => {
    it('should convert pass config to line item config', () => {
      const passConfig: PassConfig = {
        id: 'p1',
        name: 'Pass 1',
        priceInCents: 100,
        organizationId: '1',
        passCount: 10,
        passConfigStatus: 'Upcoming',
        quantityRemaining: { referenceId: '1', quantityRemaining: 10 },
        createdAt: '2021-01-01T00:00:00Z',
        updatedAt: '2021-01-01T00:00:00Z',
      };

      const result = convertPassConfigToLineItemConfig(passConfig);

      expect(result).toEqual({
        lineItemId: 'p1',
        name: 'Pass 1',
        lineItemType: LineItemType.PassConfig,
        unitPriceInCents: 100,
        quantityRemaining: 10,
        quantitySelected: 0,
      });
    });
  });

  describe('sumLineItemQuantities', () => {
    it('should sum line item quantities', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 5,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 1,
        },
      ];

      const result = sumLineItemQuantities(lineItemSelections);
      expect(result).toEqual(6);
    });

    it('should sum line item quantities with empty quantities', () => {
      const lineItemSelections: LineItemSelection[] = [];

      const result = sumLineItemQuantities(lineItemSelections);
      expect(result).toEqual(0);
    });

    it('should sum line item quantities with 0 quantities', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 0,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 0,
        },
      ];

      const result = sumLineItemQuantities(lineItemSelections);
      expect(result).toEqual(0);
    });
  });

  describe('sumLineItemPrices', () => {
    it('should sum line item prices', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 2,
          unitPriceInCents: 100,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 6,
          unitPriceInCents: 200,
        },
      ];

      const result = sumLineItemPrices(lineItemSelections);
      expect(result).toEqual(1400);
    });

    it('should sum line item prices with empty quantities', () => {
      const lineItemSelections: LineItemSelection[] = [];

      const result = sumLineItemPrices(lineItemSelections);
      expect(result).toEqual(0);
    });

    it('should sum line item prices with 0 quantities', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 0,
          unitPriceInCents: 100,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 0,
          unitPriceInCents: 200,
        },
      ];

      const result = sumLineItemPrices(lineItemSelections);
      expect(result).toEqual(0);
    });

    it('should sum line item prices with 0 priceInCents', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 5,
          unitPriceInCents: 0,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 2,
          unitPriceInCents: 0,
        },
      ];

      const result = sumLineItemPrices(lineItemSelections);
      expect(result).toEqual(0);
    });
  });

  describe('lineItemDictToPricingSummaryInput', () => {
    it('should convert line item quantities to pricing summary input', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 1,
          unitPriceInCents: 10,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 2,
          unitPriceInCents: 10,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '3',
          quantitySelected: 3,
          unitPriceInCents: 10,
        },
      ];

      const referenceId = 'r1';
      const organizationId = '1';

      const result = lineItemDictToPricingSummaryInput(
        lineItemSelections,
        organizationId,
        referenceId
      );

      expect(result).toEqual([
        {
          lineItemId: '1',
          quantity: 1,
          lineItemType: LineItemType.TicketType,
          referenceId: 'r1',
          organizationId: '1',
          customPriceInCents: undefined,
          lineItemCategory: undefined,
        },
        {
          lineItemId: '2',
          quantity: 2,
          lineItemType: LineItemType.TicketType,
          referenceId: 'r1',
          organizationId: '1',
          customPriceInCents: undefined,
          lineItemCategory: undefined,
        },
        {
          lineItemId: '3',
          quantity: 3,
          lineItemType: LineItemType.TicketType,
          referenceId: 'r1',
          organizationId: '1',
          customPriceInCents: undefined,
          lineItemCategory: undefined,
        },
      ]);
    });

    it('should convert line item quantities to pricing summary input: Custom Line Items', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 1,
          unitPriceInCents: 10,
          lineItemType: LineItemType.Custom,
          lineItemCategory: CustomSalesType.Concessions,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 2,
          unitPriceInCents: 10,
          lineItemType: LineItemType.Custom,
          lineItemCategory: CustomSalesType.Concessions,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '3',
          quantitySelected: 3,
          unitPriceInCents: 10,
          lineItemType: LineItemType.Custom,
          lineItemCategory: CustomSalesType.Concessions,
        },
      ];

      const referenceId = 'r1';
      const organizationId = '1';

      const result = lineItemDictToPricingSummaryInput(
        lineItemSelections,
        organizationId,
        referenceId
      );

      expect(result).toEqual([
        {
          lineItemId: '1',
          quantity: 1,
          lineItemType: LineItemType.Custom,
          referenceId: undefined,
          organizationId: '1',
          customPriceInCents: 10,
          lineItemCategory: CustomSalesType.Concessions,
        },
        {
          lineItemId: '2',
          quantity: 2,
          lineItemType: LineItemType.Custom,
          referenceId: undefined,
          organizationId: '1',
          customPriceInCents: 10,
          lineItemCategory: CustomSalesType.Concessions,
        },
        {
          lineItemId: '3',
          quantity: 3,
          lineItemType: LineItemType.Custom,
          referenceId: undefined,
          organizationId: '1',
          customPriceInCents: 10,
          lineItemCategory: CustomSalesType.Concessions,
        },
      ]);
    });

    it('should convert line item quantities to pricing summary input with empty quantities', () => {
      const lineItemSelections: LineItemSelection[] = [];

      const referenceId = 'r1';
      const organizationId = '1';

      const result = lineItemDictToPricingSummaryInput(
        lineItemSelections,
        organizationId,
        referenceId
      );
      expect(result).toEqual([]);
    });

    it('should convert line item quantities to pricing summary input with 0 quantities', () => {
      const lineItemSelections: LineItemSelection[] = [
        {
          ...mockLineItemSelection,
          lineItemId: '1',
          quantitySelected: 0,
          unitPriceInCents: 10,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '2',
          quantitySelected: 0,
          unitPriceInCents: 10,
        },
        {
          ...mockLineItemSelection,
          lineItemId: '3',
          quantitySelected: 0,
          unitPriceInCents: 10,
        },
      ];

      const referenceId = 'r1';
      const organizationId = '1';

      const result = lineItemDictToPricingSummaryInput(
        lineItemSelections,
        organizationId,
        referenceId
      );
      expect(result).toEqual([
        {
          lineItemId: '1',
          quantity: 0,
          lineItemType: LineItemType.TicketType,
          referenceId: 'r1',
          customPriceInCents: undefined,
          organizationId: '1',
          lineItemCategory: undefined,
        },
        {
          lineItemId: '2',
          quantity: 0,
          lineItemType: LineItemType.TicketType,
          referenceId: 'r1',
          customPriceInCents: undefined,
          organizationId: '1',
          lineItemCategory: undefined,
        },
        {
          lineItemId: '3',
          quantity: 0,
          lineItemType: LineItemType.TicketType,
          referenceId: 'r1',
          customPriceInCents: undefined,
          organizationId: '1',
          lineItemCategory: undefined,
        },
      ]);
    });
  });
});
