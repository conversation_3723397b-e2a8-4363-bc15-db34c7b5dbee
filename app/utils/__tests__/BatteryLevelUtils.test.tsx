import { screen } from '@testing-library/react-native';

import { AppTestIDs } from '../../components/AppTestIDs';
import { renderWithOptions } from '../../test/renderHelpers';
import { getBatteryLevelIcon } from '../BatteryLevelUtils';

describe('Battery Level', () => {
  it('Battery level 0', async () => {
    renderWithOptions(getBatteryLevelIcon(0.0, false, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(0, false))).toBeTruthy();
  });

  it('Battery level 0 charging', async () => {
    renderWithOptions(getBatteryLevelIcon(0.0, true, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(0, true))).toBeTruthy();
  });
  it('Battery level 25', async () => {
    renderWithOptions(getBatteryLevelIcon(0.25, false, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(25, false))).toBeTruthy();
  });

  it('Battery level 25 charging', async () => {
    renderWithOptions(getBatteryLevelIcon(0.25, true, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(50, true))).toBeTruthy();
  });

  it('Battery level 50', async () => {
    renderWithOptions(getBatteryLevelIcon(0.5, false, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(50, false))).toBeTruthy();
  });

  it('Battery level 50 charging', async () => {
    renderWithOptions(getBatteryLevelIcon(0.5, true, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(50, true))).toBeTruthy();
  });

  it('Battery level 75', async () => {
    renderWithOptions(getBatteryLevelIcon(0.75, false, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(75, false))).toBeTruthy();
  });

  it('Battery level 75 charging', async () => {
    renderWithOptions(getBatteryLevelIcon(0.5, true, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(50, true))).toBeTruthy();
  });

  it('Battery level 100', async () => {
    renderWithOptions(getBatteryLevelIcon(1, false, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(100, false))).toBeTruthy();
  });

  it('Battery level 100 charging', async () => {
    renderWithOptions(getBatteryLevelIcon(1, true, 'large', 'black'), {
      withNavigationContainer: false,
    });

    expect(screen.getByTestId(AppTestIDs.batteryLevelIcon(100, true))).toBeTruthy();
  });
});
