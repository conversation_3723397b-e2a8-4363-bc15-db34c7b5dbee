import {
  capitalizeFirstLetter,
  constructPOSDescriptionString,
  isNullOrEmpty,
  isValidEmail,
} from '../StringUtils';

describe('isValidEmail', (): void => {
  it('is valid email', (): void => {
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
    ];

    const result = validEmails.map((e) => isValidEmail(e));
    const anyFalse = result.some((b) => b === false);

    expect(anyFalse).toBe(false);
  });

  it('Not Valid email', (): void => {
    const invalidEmails = ['workingExample', 'workingExample@aol@.com', '123234@'];

    const result = invalidEmails.map((e) => isValidEmail(e));
    const anyFalse = result.some((b) => b === false);

    expect(anyFalse).toBe(true);
  });
});

describe('isNullOrEmpty', (): void => {
  it('is null or empty', (): void => {
    const nullOrEmpty = [undefined, '', null];

    const result = nullOrEmpty.map((e) => isNullOrEmpty(e));
    const anyFalse = result.some((b) => b === false);

    expect(anyFalse).toBe(false);
  });

  it('Not null or empty', (): void => {
    const notNullOrEmpty = ['workingExample', 'workingExample@aol@.com', '123234@'];

    const result = notNullOrEmpty.map((e) => isNullOrEmpty(e));
    const anyTrue = result.some((b) => b === true);
    expect(anyTrue).toBe(false);
  });
});

describe('capitalizeFirstLetter', (): void => {
  it('should capitalize first letter', (): void => {
    expect(capitalizeFirstLetter('hello')).toEqual('Hello');
  });

  it('should capitalize first letter again', (): void => {
    expect(capitalizeFirstLetter('world')).toEqual('World');
  });
});

describe('constructPOSDescriptionString', (): void => {
  it('should return correct POS description string', (): void => {
    const orgId = '123';
    expect(constructPOSDescriptionString(orgId)).toEqual('Point of Sale | Organization: 123');
  });
});
