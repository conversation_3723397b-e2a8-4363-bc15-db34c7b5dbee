import { Reader } from '@stripe/stripe-terminal-react-native';

import { mockReader } from '../../gql/mockData/TestData';
import { StripeBBPOSReaderLabel, StripeM2ReaderLabel } from '../Constants';
import { ReaderDeviceType } from '../Enums';
import { getReaderDisplayName } from '../ReaderUtils';

describe('getReaderDisplayName', () => {
  it('M2 Reader has correct display name', () => {
    const m2Reader = {
      ...mockReader,
      deviceType: ReaderDeviceType.StripeM2Reader,
    };
    expect(getReaderDisplayName(m2Reader)).toEqual(StripeM2ReaderLabel);
  });

  it('BBPOS Reader has correct display name', () => {
    const bbposReader = {
      ...mockReader,
      deviceType: ReaderDeviceType.BBPOSChipper2X,
    };
    expect(getReaderDisplayName(bbposReader)).toEqual(StripeBBPOSReaderLabel);
  });

  it('Defaults to device type', () => {
    const reader = {
      ...mockReader,
      deviceType: 'chipper1X' as Reader.DeviceType,
    };
    expect(getReaderDisplayName(reader)).toEqual('chipper1X');
  });
});
