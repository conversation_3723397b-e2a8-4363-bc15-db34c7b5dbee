import { DeviceEventEmitter } from 'react-native';

import { act, renderHook } from '@testing-library/react-native';

import { useKeyboard } from '../useKeyboardHook';

describe('useKeyboard', () => {
  it('should return an object with height and isVisible properties', () => {
    const { result } = renderHook(() => useKeyboard());
    expect(result.current).toHaveProperty('height');
    expect(result.current).toHaveProperty('isVisible');
  });

  it('should return an object with height 0 and isVisible false when keyboard is hidden', () => {
    const { result } = renderHook(() => useKeyboard());
    act(() => {
      DeviceEventEmitter.emit('keyboardDidHide', {});
    });

    expect(result.current.height).toBe(0);
    expect(result.current.isVisible).toBe(false);
  });

  it('should return an object with height greater than 0 and isVisible true when keyboard is shown', () => {
    const { result } = renderHook(() => useKeyboard());
    act(() => {
      DeviceEventEmitter.emit('keyboardDidShow', { endCoordinates: { height: 100 } });
    });

    expect(result.current.height).toBe(100);
    expect(result.current.isVisible).toBe(true);
  });

  it('should return an object with height 0 and isVisible false when keyboard is shown and then hidden', () => {
    const { result } = renderHook(() => useKeyboard());
    act(() => {
      DeviceEventEmitter.emit('keyboardDidShow', { endCoordinates: { height: 100 } });
    });
    act(() => {
      DeviceEventEmitter.emit('keyboardDidHide', {});
    });

    expect(result.current.height).toBe(0);
    expect(result.current.isVisible).toBe(false);
  });
});
