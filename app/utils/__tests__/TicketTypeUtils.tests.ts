import { mockTicketType, mockTicketedEvent } from '../../gql/mockData/TestData';
import { getTicketTypePrice } from '../TicketTypeUtils';

describe('Ticket Type Utils Tests', () => {
  it('Should return the price override if it exists', () => {
    const ticketTypeId = 'ticketTypeId1';
    const ticketedEvent = {
      ...mockTicketedEvent,
      ticketTypeReferences: [{ ticketTypeId: ticketTypeId, priceOverride: 300 }],
    };
    const ticketType = { ...mockTicketType, id: ticketTypeId, priceInCents: 200 };
    const price = getTicketTypePrice(ticketType, ticketedEvent);
    expect(price).toEqual(ticketedEvent.ticketTypeReferences[0].priceOverride);
  });

  it('Should return the base price if the price override is undefined', () => {
    const ticketTypeId = 'ticketTypeId1';
    const ticketedEvent = {
      ...mockTicketedEvent,
      ticketTypeReferences: [{ ticketTypeId: ticketTypeId, priceOverride: undefined }],
    };
    const ticketType = { ...mockTicketType, id: ticketTypeId, priceInCents: 200 };
    const price = getTicketTypePrice(ticketType, ticketedEvent);
    expect(price).toEqual(ticketType.priceInCents);
  });
});
