import { getFormattedBatteryLevel, getFormattedPrice } from '../NumberFormatUtils';

describe('getFormattedPriceInCents', () => {
  it('Outputs formatted price', async () => {
    expect(getFormattedPrice(1000)).toBe('$10.00');
  });

  it('Outputs formatted price cents', async () => {
    expect(getFormattedPrice(11111)).toBe('$111.11');
  });

  it('Outputs formatted price zero', async () => {
    expect(getFormattedPrice(0)).toBe('$0.00');
  });

  it('Outputs formatted price commas', async () => {
    expect(getFormattedPrice(100000000)).toBe('$1,000,000.00');
  });
});

describe('getFormattedBatteryLevel', () => {
  it('Outputs formatted battery level: 25%', async () => {
    expect(getFormattedBatteryLevel(0.254848645684)).toBe('25%');
  });

  it('Outputs formatted battery level: 100%', async () => {
    expect(getFormattedBatteryLevel(1)).toBe('100%');
  });

  it('Outputs formatted battery level: 0%', async () => {
    expect(getFormattedBatteryLevel(0.0)).toBe('0%');
  });

  it('Outputs formatted battery level: undefined', async () => {
    expect(getFormattedBatteryLevel(undefined)).toBe('N/A');
  });
});
