import { mockEventInfo } from '../../gql/mockData/TestData';
import { EventInfo } from '../../models/EventInfo';
import {
  formatTicketedEventDateTime,
  getDateWithoutTime,
  getTicketedEventDate,
  getZonedDateTimeFromUTC,
} from '../TicketedEventDateUtils';

const date = new Date(2023, 1, 15, 13, 12, 0, 0);

describe('getTicketedEventDate tests', () => {
  const eventInfo = {
    ...mockEventInfo,
    date: date.toUTCString(),
    timeZoneIdentifier: 'Etc/UTC',
  } as EventInfo;
  it('Date is retrieved correctly', async () => {
    expect(getTicketedEventDate(eventInfo)).toStrictEqual(date);
  });
});

describe('formatTicketedEventDateTime tests', () => {
  it('Time displays correctly with UTC time zone', async () => {
    const eventInfo = {
      ...mockEventInfo,
      date: date.toUTCString(),
      timezoneIdentifier: 'Etc/UTC',
    } as EventInfo;
    const dateFormat = 'EEEE, MMM do @ h:mma z';
    expect(formatTicketedEventDateTime(eventInfo, dateFormat)).toBe(
      'Wednesday, Feb 15th @ 1:12PM UTC'
    );
  });

  it('Time displays correctly with non-UTC time zone', async () => {
    const eventInfo = {
      ...mockEventInfo,
      date: date.toUTCString(),
      timezoneIdentifier: 'America/Chicago',
    } as EventInfo;
    const dateFormat = 'EEEE, MMM do @ h:mma z';
    expect(formatTicketedEventDateTime(eventInfo, dateFormat)).toBe(
      'Wednesday, Feb 15th @ 7:12AM CST'
    );
  });
});

describe('DateTimeUtils - getZonedDateTimeFromUtc', () => {
  it('returns expected date with timezone', () => {
    const inputDate = new Date('2024-12-25T05:00:00.000Z');
    const inputTimezone = 'America/New_York';
    const outputDate = getZonedDateTimeFromUTC(inputDate, inputTimezone);
    const expectedOutput = `2024-12-25T00:00:00.000Z`;
    expect(outputDate.toISOString()).toEqual(expectedOutput);
  });

  it('returns expected date without timezone', () => {
    const inputDate = new Date('2024-12-25T05:00:00.000Z');
    const outputDate = getZonedDateTimeFromUTC(inputDate, undefined);
    const expectedOutput = `2024-12-25T05:00:00.000Z`;
    expect(outputDate.toISOString()).toEqual(expectedOutput);
  });
});

describe('DateTimeUtils - getDateWithoutTime', () => {
  it('returns the date without the time', () => {
    const inputDate = '2023-12-25T05:00:00.000Z';
    const inputTimezone = 'America/New_York';
    const output = getDateWithoutTime(inputDate, inputTimezone);
    expect(output).toEqual(new Date('2023-12-25'));
  });
});
