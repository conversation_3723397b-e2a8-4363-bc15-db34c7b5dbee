import { screen } from '@testing-library/react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import { AppTestIDs } from '../../components/AppTestIDs';
import enUS from '../../strings/en-US.json';
import { renderWithOptions } from '../../test/renderHelpers';
import {
  appendOrToArray,
  constructReaderInputOptions,
  formatReaderInputOptions,
  getReaderInputOptions,
} from '../ReaderDisplayUtils';

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

describe('appendOrToArray', (): void => {
  it('1 item', (): void => {
    expect(appendOrToArray(['test'])).toEqual('test');
  });

  it('2 items', (): void => {
    expect(appendOrToArray(['test', 'test'])).toEqual('test or test');
  });

  it('3 item', (): void => {
    expect(appendOrToArray(['test', 'test', 'test'])).toEqual('test, test or test');
  });
});

describe('constructReaderInputOptions', (): void => {
  it('Maps correctly', (): void => {
    expect(constructReaderInputOptions(['insertCard', 'swipeCard', 'tapCard'])).toEqual([
      'insert',
      'swipe',
      'tap',
    ]);
  });
});

describe('formatReaderInputOptions', (): void => {
  it('Maps correctly: 3 items', (): void => {
    expect(formatReaderInputOptions(['insertCard', 'swipeCard', 'tapCard'])).toEqual(
      'Insert, swipe or tap'
    );
  });

  it('Maps correctly: 2 items', (): void => {
    expect(formatReaderInputOptions(['insertCard', 'tapCard'])).toEqual('Insert or tap');
  });
});

describe('getReaderInputOptions', (): void => {
  it('Renders input options', (): void => {
    renderWithOptions(getReaderInputOptions(['insertCard', 'swipeCard', 'tapCard']));

    expect(screen.getByTestId(AppTestIDs.readerDisplayInputMessage)).toBeTruthy();
    expect(screen.getByText('Insert, swipe or tap')).toBeTruthy();
    expect(screen.getByText('to purchase tickets.')).toBeTruthy();
  });
});
