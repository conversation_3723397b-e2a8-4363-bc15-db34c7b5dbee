import { useEmailValidation } from '../emailValidationHook';

describe('Email validation hook tests', () => {
  // Email list source:
  // https://gist.github.com/cjaoude/fd9910626629b53c4d25
  // The users regex doesn't perfectly validate these lists but we're comfortable with the edge cases
  // given that email addresses can't fully be validated with regex
  // Commented-out examples show the edge cases that don't pass
  const validEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    // 'email@***************',
    'email@[***************]',
    '"email"@example.com',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ];

  const invalidEmails = [
    'plainaddress',
    '#@%^%#$@#$@#.com',
    '@example.com',
    '<PERSON> <<EMAIL>>',
    'email.example.com',
    'email@<EMAIL>',
    '.<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    // 'あいうえお@example.com',
    '<EMAIL> (Joe Smith)',
    'email@example',
    // '<EMAIL>',
    // '<EMAIL>',
    'email@111.222.333.44444',
    '<EMAIL>',
    '<EMAIL>]',
    '”(),:;<>[]@example.com',
    // 'just”not”<EMAIL>',
    'this is"really"<EMAIL>',
  ];

  const isValidEmailAddress = useEmailValidation();

  test.each(validEmails)('Valid email address (%s) returns true', (address) => {
    expect(isValidEmailAddress(address)).toBeTruthy();
  });

  test.each(invalidEmails)('Invalid email address (%s) returns false', (address) => {
    expect(isValidEmailAddress(address)).toBeFalsy();
  });
});
