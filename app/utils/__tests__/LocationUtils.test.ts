import { School } from '../../gql/public/__generated__/graphql';
import {
  HudlHQLocationCanadaIdProd,
  HudlHQLocationCanadaIdThor,
  HudlHQLocationIdProd,
  HudlHQLocationIdThor,
} from '../Constants';
import { getLocationIdForSchool } from '../LocationUtils';

describe('LocationUtils', () => {
  const canadaSchool: School = {
    countryIsoAlpha2: 'CA',
    canUserEditSchool: true,
    id: '123',
    isActive: true,
    isEnabled: true,
    orgClassificationId: 1,
    organizationPrograms: [],
  };

  const usSchool: School = {
    ...canadaSchool,
    countryIsoAlpha2: 'US',
  };

  it('should return the correct location: Thor Canada', () => {
    expect(getLocationIdForSchool(canadaSchool, true)).toBe(HudlHQLocationCanadaIdThor);
  });

  it('should return the correct location: Prod Canada', () => {
    expect(getLocationIdForSchool(canadaSchool, false)).toBe(HudlHQLocationCanadaIdProd);
  });

  it('should return the correct location: Thor US', () => {
    expect(getLocationIdForSchool(usSchool, true)).toBe(HudlHQLocationIdThor);
  });

  it('should return the correct location: Prod US', () => {
    expect(getLocationIdForSchool(usSchool, false)).toBe(HudlHQLocationIdProd);
  });
});
