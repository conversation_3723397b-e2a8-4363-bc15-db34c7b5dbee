import { makeVar } from '@apollo/client';

import { CustomSalesType } from './Enums';
import { DeveloperOptionData, DeveloperOptionKeys, LineItemSelection } from '../types/shared';

export const checkoutItemSelections = makeVar<LineItemSelection[]>([]);
export const customSaleType = makeVar<CustomSalesType>(CustomSalesType.Concessions);

const defaultDeveloperOptionFlags: Record<DeveloperOptionKeys, DeveloperOptionData> = {
  simulateReader: { enabled: false },
  simulatedCardNumber: { enabled: false },
  simulateReaderUpdate: { enabled: false },
};
export const developerOptionFlags = makeVar<Record<DeveloperOptionKeys, DeveloperOptionData>>(
  defaultDeveloperOptionFlags
);
