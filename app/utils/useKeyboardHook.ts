import { useEffect, useState } from 'react';
import { Keyboard, KeyboardEvent } from 'react-native';

interface KeyboardState {
  isVisible: boolean;
  height: number;
}

export const useKeyboard = (): KeyboardState => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  function onKeyboardDidShow(e: KeyboardEvent): void {
    setKeyboardHeight(e.endCoordinates.height);
  }

  function onKeyboardDidHide(): void {
    setKeyboardHeight(0);
  }

  useEffect(() => {
    const keyboardDidShowSubscription = Keyboard.addListener('keyboardDidShow', onKeyboardDidShow);
    const keyboardDidHideSubscription = Keyboard.addListener('keyboardDidHide', onKeyboardDidHide);
    return () => {
      keyboardDidShowSubscription.remove();
      keyboardDidHideSubscription.remove();
    };
  }, []);

  return {
    height: keyboardHeight,
    isVisible: keyboardHeight !== 0,
  };
};
