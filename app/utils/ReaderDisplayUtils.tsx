import React, { ReactElement } from 'react';

import { Reader } from '@stripe/stripe-terminal-react-native';

import { HudlI18n } from '@hudl/jarvis/i18n';

import { capitalizeFirstLetter } from './StringUtils';
import { boldSubstring } from './TextUtils';
import ReaderDisplayInput from '../components/ReaderDisplay/ReaderDisplayInput';
import IconCreditCard from '../icons/IconCreditCard';
import { ReaderMessage } from '../types/shared';

export function appendOrToArray(input: string[]): string {
  const formattedArray = input.reduce((accumulator, currentValue, index, array) => {
    const separator = index < array.length - 1 ? ', ' : ' or ';
    return accumulator + separator + currentValue;
  });

  return formattedArray;
}

export function constructReaderInputOptions(input: Reader.InputOptions[]): string[] {
  const inputMap: { [key: string]: string } = {
    insertCard: 'insert',
    swipeCard: 'swipe',
    tapCard: 'tap',
  };

  return input.map((str) => inputMap[str] || '');
}

export function formatReaderInputOptions(input: Reader.InputOptions[]): string {
  const options = constructReaderInputOptions(input);
  const formattedInput = appendOrToArray(options);

  return capitalizeFirstLetter(formattedInput);
}

export function getReaderInputOptions(input: Reader.InputOptions[]): ReactElement {
  const formattedInput = formatReaderInputOptions(input);
  const text = boldSubstring(
    HudlI18n.t('accept-payments.reader-input-options', {
      readerInputOptions: formattedInput,
    }),
    true,
    false
  );
  const readerDisplay: ReaderMessage = {
    message: text,
    icon: <IconCreditCard height={'128'} width={'128'} />,
  };
  return <ReaderDisplayInput inputMessage={readerDisplay} />;
}
