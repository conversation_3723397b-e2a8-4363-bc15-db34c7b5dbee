import { Linking } from 'react-native';

export const ticketingSupportPhoneNumber = '************';

export const getSupportUrl = (): string => {
  return 'https://www.hudl.com/support/contact';
};

export const getTutorialsUrl = (): string => {
  return 'https://support.hudl.com/s/topic/0TOVY0000000P3H4AU/ticketreaderapp';
};

export const openSupportLink = (): void => {
  Linking.openURL(getSupportUrl());
};

export const openTutorialsLink = (): void => {
  Linking.openURL(getTutorialsUrl());
};

export const openPhoneLink = (phoneNumber: string): void => {
  Linking.openURL(`tel:${phoneNumber}`);
};
