import { getTicketTypePrice } from './TicketTypeUtils';
import { LineItemType } from '../enums/shared';
import {
  PassConfig,
  TicketType,
  TicketedEvent,
  TicketingPricingLineItemInput,
} from '../gql/public/__generated__/graphql';
import { EventInfo } from '../models/EventInfo';
import { LineItemSelection } from '../types/shared';

export const convertTicketTypesToLineItemConfigs = (
  ticketTypes: TicketType[],
  ticketedEvent: TicketedEvent | undefined
): LineItemSelection[] => {
  if (!ticketTypes || ticketedEvent === undefined) {
    return [];
  }
  return ticketTypes?.map((tt: TicketType) => {
    const ticketTypePricingSummary = ticketedEvent?.ticketTypePricingSummaries?.find(
      (tps) => tps?.ticketTypeId === tt.id
    );
    return {
      lineItemId: tt.id,
      lineItemType: LineItemType.TicketType,
      name: tt.name as string,
      unitPriceInCents: getTicketTypePrice(tt, ticketedEvent) ?? 0,
      quantityRemaining: ticketedEvent?.quantityRemainingForTicketTypes?.filter(
        (qr) => qr?.referenceId === tt.id
      )[0]?.quantityRemaining,
      quantitySelected: 0,
      shouldShowFee: ticketTypePricingSummary?.shouldShowFee,
      hudlFeeInCents: ticketTypePricingSummary?.hudlFeeInCents,
    } as LineItemSelection;
  });
};

export const convertPassConfigToLineItemConfig = (passConfig: PassConfig): LineItemSelection => {
  return {
    lineItemId: passConfig?.id,
    name: passConfig?.name as string,
    lineItemType: LineItemType.PassConfig,
    unitPriceInCents: passConfig?.priceInCents,
    quantitySelected: 0,
    quantityRemaining: passConfig?.quantityRemaining?.quantityRemaining,
  };
};

export const sumLineItemQuantities = (lineItems: LineItemSelection[]): number => {
  return lineItems
    .filter((lineItem) => lineItem.quantitySelected)
    .reduce((acc, lineItem) => acc + lineItem.quantitySelected, 0);
};

export const sumLineItemPrices = (lineItems: LineItemSelection[]): number => {
  return lineItems
    .filter((lineItem) => lineItem.unitPriceInCents && lineItem.quantitySelected)
    .reduce((acc, lineItem) => acc + lineItem.unitPriceInCents * lineItem.quantitySelected, 0);
};

export const lineItemDictToPricingSummaryInput = (
  lineItemSelections: LineItemSelection[],
  organizationId: string,
  referenceId?: string
): TicketingPricingLineItemInput[] =>
  lineItemSelections.map((lineItem) => ({
    lineItemId: lineItem.lineItemId,
    quantity: lineItem.quantitySelected,
    lineItemType: lineItem.lineItemType,
    referenceId: lineItem.lineItemType !== LineItemType.Custom ? referenceId : undefined,
    organizationId: organizationId,
    lineItemCategory: lineItem.lineItemCategory,
    customPriceInCents:
      lineItem.lineItemType === LineItemType.Custom ? lineItem.unitPriceInCents : undefined,
  }));

export const getLineItemFeeInfo = (
  lineItem: LineItemSelection
): { shouldShowFee: boolean; hudlFeeInCents: number } => {
  const shouldShowFee = lineItem?.shouldShowFee ?? true;
  const hudlFeeInCents = lineItem?.hudlFeeInCents ?? 0;

  return { shouldShowFee, hudlFeeInCents };
};

export const getPricingSummaryEventInfo = (
  ticketedEvent?: EventInfo
): { summaryShouldShowFee: boolean; summaryHudlFeeInCents: number } => {
  const summaryShouldShowFee =
    ticketedEvent?.ticketTypePricingSummaries.some(
      (ticketTypePricingSummary) => ticketTypePricingSummary?.shouldShowFee
    ) ?? true;
  const pricedLineItem = ticketedEvent?.ticketTypePricingSummaries.find(
    (lic) => lic.priceInCents > 0
  );
  let summaryHudlFeeInCents = 0;
  if (pricedLineItem) {
    summaryHudlFeeInCents = pricedLineItem.hudlFeeInCents ?? 0;
  }

  return { summaryShouldShowFee, summaryHudlFeeInCents };
};
