// Taken from hudl-users: https://github.com/hudl/hudl-users/blob/e24d5b34219c1556506b8398fde9d20dddc1c8d6/src/client-app/components/LoginPage/LoginHelp/index.tsx#LL25C20-L25C175
const emailValidationRegex =
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
export const useEmailValidation = () => (email: string) =>
  emailValidationRegex.test(email.toLowerCase());
