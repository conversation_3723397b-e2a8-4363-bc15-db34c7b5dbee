import { useAccessCodeHook } from '../components/queries/useAccessCodeHook';
import useTicketedEventByIdWithAnalytics from '../gql/hooks/useTicketedEventByIdWithAnalytics';
import { mockTicketedEvent } from '../gql/mockData/TestData';
import { TicketedEventAccessCode } from '../gql/public/__generated__/graphql';

export function setupAccessContextMocks(): void {
  jest.mock('../gql/hooks/useTicketedEventByIdWithAnalytics');
  const mockUseTicketedEventByIdWithAnalytics =
    useTicketedEventByIdWithAnalytics as jest.MockedFunction<
      typeof useTicketedEventByIdWithAnalytics
    >;

  jest.mock('../components/queries/useAccessCodeHook');
  const mockUseAccessCode = useAccessCodeHook as jest.MockedFunction<typeof useAccessCodeHook>;

  mockUseTicketedEventByIdWithAnalytics.mockImplementation(() => ({
    ticketedEvent: mockTicketedEvent,
    ticketedEventLoading: false,
    ticketedEventError: undefined,
    ticketedEventNetworkStatus: 7,
    refetchTicketedEvent: jest.fn(),
  }));

  mockUseAccessCode.mockImplementation(() => ({
    accessCode: { accessCode: 'e6g32' } as TicketedEventAccessCode,
    hasError: false,
    isLoading: false,
  }));
}
