import { Reader } from '@stripe/stripe-terminal-react-native';

export enum Navigation {
  Root = 'root',
  Landing = 'landing',
  VolunteerInfo = 'volunteerInfo',
  LoadingOrg = 'loadingOrg',
  OrganizationEvents = 'organizationEvents',
  EventConfirmation = 'eventConfirmation',
  Scanner = 'scanner',
  UploadTickets = 'uploadTickets',
  DiscoverReaders = 'discoverReaders',
  POSOnboarding = 'posOnboarding',
  ItemSelection = 'itemSelection',
  ReviewOrder = 'reviewOrder',
  ReceiptSelection = 'receiptSelection',
  AcceptPayment = 'acceptPayment',
  AcceptCashPayment = 'acceptCashPayment',
  OrderComplete = 'orderComplete',
  PurchaserList = 'purchaserList',
  CustomSales = 'customSales',
  CustomSalesSelection = 'customSalesSelection',
  HomeTab = 'homeTab',
  SellTab = 'sellTab',
  CheckInTab = 'checkInTab',
  CheckIn = 'checkIn',
  Sell = 'sell',
}

export enum Screens {
  Home = 'home',
  Sell = 'sell',
  CheckIn = 'checkIn',
}

export enum StripeCommonErrorCodes {
  BluetoothAccessDenied = 'BluetoothAccessDenied',
  BluetoothDisabled = 'BluetoothDisabled',
  AndroidBluetoothError = 'READER_ERROR.BLUETOOTH_ERROR',
  UserErrorCanceled = 'USER_ERROR.CANCELED',
  BluetoothConnectionFailedBatteryCriticallyLow = 'BluetoothConnectionFailedBatteryCriticallyLow',
  DeclinedByStripeAPI = 'DeclinedByStripeAPI',
  AndroidDeclinedByStripeAPI = 'PAYMENT_ERROR.DECLINED_BY_STRIPE_API',
  AlreadyDiscovering = 'AlreadyDiscovering',
  LocationServicesDisabled = 'LocationServicesDisabled',
  ReaderSoftwareUpdateFailedBatteryLow = 'ReaderSoftwareUpdateFailedBatteryLow',
}

export enum ReaderDeviceType {
  StripeM2Reader = 'stripeM2',
  BBPOSChipper2X = 'chipper2X',
}

export enum DiscoveryMethod {
  BluetoothScan = 'bluetoothScan',
}

export enum PlatformOS {
  Android = 'android',
  IOS = 'ios',
}

export enum CounterAction {
  Increment,
  Decrement,
}

export enum SeverityLevel {
  Error = 'error',
}

export enum PaymentIntentStatus {
  Succeeded = 'succeeded',
}

export type DisplayMessage = Reader.DisplayMessage;

export enum Environment {
  PRODUCTION = 'production',
  DEVELOPMENT = 'development',
}

export enum QRCodeScanStatus {
  Valid = 'valid',
  CorrectEvent = 'correctEvent',
  IncorrectEvent = 'incorrectEvent',
  IncorrectParticipants = 'incorrectParticipants',
  OutsideOfDateRange = 'outsideOfDateRange',
  InvalidChecksum = 'invalidChecksum',
  AlreadyScanned = 'alreadyScanned',
  Error = 'error',
  NotRecognized = 'notRecognized',
}

export enum QRCodeStoreStatus {
  Stored = 'stored',
  QRCodeAlreadyScanned = 'qrCodeAlreadyScanned',
  ErrorStoringResult = 'errorStoringResult',
}

export enum ScannedQRCodeResultStatus {
  Redeemed = 'Redeemed',
}

export enum TicketingEntityType {
  Ticket = 'Ticket',
  Pass = 'Pass',
}

export enum PassQRCodeVersion {
  V1 = '1',
  V2 = '2',
}

export enum TicketQRCodeVersion {
  V1 = '1',
  V2 = '2',
  V3 = '3',
}

export enum Source {
  Web = 'Web',
  Complimentary = 'Complimentary',
  Transfer = 'Transfer',
  POS = 'POS',
}

export enum CustomSalesType {
  Concessions = 'Concessions',
  Apparel = 'Apparel',
  Other = 'Other',
}

export enum TicketedEventStatus {
  Unknown = 'Unknown',
  Draft = 'Draft',
  Upcoming = 'Upcoming',
  Open = 'Open',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Archived = 'Archived',
  Rescheduled = 'Rescheduled',
  Cancelled = 'Cancelled',
}

export enum DiscoverReadersStage {
  Initializing = 'initializing',
  Finding = 'finding',
  Selecting = 'selecting',
  Selected = 'selected',
}

export enum DiscoverReadersError {
  SystemConnection = 'system-connection',
  BluetoothError = 'bluetooth-error',
  NoReadersFound = 'no-readers-found',
  ConnectionError = 'connection-error',
  LowBattery = 'low-battery',
}

export enum CameraPermissionStatus {
  Granted = 'granted',
  NotDetermined = 'not-determined',
  Denied = 'denied',
  Restricted = 'restricted',
}
