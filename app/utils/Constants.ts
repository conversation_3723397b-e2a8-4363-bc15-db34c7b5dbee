export const StripeM2ReaderLabel = 'Stripe M2 Reader';
export const StripeBBPOSReaderLabel = 'Stripe BBPOS Chipper 2X BT Reader';
export const maximumTicketLimit = 99;
export const ticketLowQuantityWarningThreshold = 10;
export const fallbackTimezone = 'Etc/UTC';
export const HudlHQLocationIdThor = 'tml_F810YwTMMQZEK3';
export const HudlHQLocationCanadaIdThor = 'tml_F810oAuZU8iSNq';
export const HudlHQLocationCanadaIdProd = 'tml_Fv0ggAwT58OFb0';
export const HudlHQLocationIdProd = 'tml_FgKJGw9LrLNEfk';
export const processingFeeLineItemId = 'orderProcessingFee';
export const PointOfSale = 'PointOfSale';
export const CheckoutMutationRetryLimit = 3;
export const ScannerOverlayIconSize = 144;
export const CanadaIsoAlpha2 = 'CA';
export const USIsoAlpha2 = 'US';
export const upcomingEventsThresholdInHours = 20;

// #region Developer Options
export const DeveloperModeAccessCode = 'hud1d3v';
export const ThorMasterUrl = 'https://master.thorhudl.com/';

// #endregion
