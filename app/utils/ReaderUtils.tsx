import { Reader } from '@stripe/stripe-terminal-react-native';

import { StripeBBPOSReader<PERSON>abel, StripeM2Reader<PERSON>abel } from './Constants';
import { ReaderDeviceType } from './Enums';

export const getReaderDisplayName = (reader: Reader.Type): string => {
  switch (reader?.deviceType) {
    case ReaderDeviceType.StripeM2Reader:
      return StripeM2ReaderLabel;
    case ReaderDeviceType.BBPOSChipper2X:
      return StripeBBPOSReaderLabel;
    default:
      return reader?.deviceType;
  }
};

export const getEstimatedUpdateDuration = (update: Reader.SoftwareUpdate): string => {
  const estimatedTimeMessages = {
    estimate1To2Minutes: 'Estimated time: 1-2 minutes',
    estimate2To5Minutes: 'Estimated time: 2-5 minutes',
    estimate5To15Minutes: 'Estimated time: 5-15 minutes',
    estimateLessThan1Minute: 'Estimated time: Less than 1 minute',
  };

  return estimatedTimeMessages[update.estimatedUpdateTime] ?? 'Unknown estimated time';
};

export const formatSoftwareUpdateProgress = (installProgress: string): string => {
  const progressNumber = parseFloat(installProgress);
  const percent = progressNumber * 100;
  return `${percent.toFixed(0)}%`;
};
