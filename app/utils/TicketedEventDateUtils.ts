import { formatInTimeZone, utcToZonedTime } from 'date-fns-tz';

import { fallbackTimezone } from './Constants';
import { EventInfo } from '../models/EventInfo';

export const ticketedEventDateFormat = 'EEEE, MMM do @ h:mma z';

export const getTicketedEventDate = (ticketedEventInfo: EventInfo): Date => {
  const { date } = ticketedEventInfo;
  return new Date(date);
};

export const formatTicketedEventDateTime = (
  ticketedEventInfo: EventInfo,
  formatString: string
): string | undefined => {
  const dateTime = getTicketedEventDate(ticketedEventInfo);
  const dateTimeString = formatInTimeZone(
    dateTime,
    ticketedEventInfo.timezoneIdentifier ?? fallbackTimezone,
    formatString
  );

  return dateTimeString;
};

export const getZonedDateTimeFromUTC = (
  dateTime: Date,
  timeZoneIdentifier: string | undefined
): Date => {
  if (!timeZoneIdentifier) {
    return dateTime;
  }
  return utcToZonedTime(dateTime, timeZoneIdentifier);
};

export const getNowDateWithoutTime = (): Date => {
  const nowDate = new Date();
  return new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate());
};

export const getDateWithoutTime = (date: string, timeZoneIdentifier?: string | null): Date => {
  const zoneDate = getZonedDateTimeFromUTC(new Date(date), timeZoneIdentifier ?? fallbackTimezone);
  return new Date(zoneDate.getFullYear(), zoneDate.getMonth(), zoneDate.getDate());
};
