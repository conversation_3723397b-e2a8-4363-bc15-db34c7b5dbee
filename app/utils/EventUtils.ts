import moment from 'moment';

import { TicketedEvent, TicketTypePricingSummary } from '../gql/public/__generated__/graphql';
import { EventInfo } from '../models/EventInfo';

export function eventToEventInfo(event: TicketedEvent): EventInfo {
  // Note: If you are wondering why we cant just pass TicketedEvent to other screens its because some of the data (mainly date)
  // cannot be serialized do the data is likely to get lost when opening/closing the app. So we make an object where all the props can
  // be properly serialized :/ woof
  return {
    id: event.id,
    description: event.description ?? '',
    date: moment(event.date).toISOString(),
    timezoneIdentifier: event.timeZoneIdentifier ?? 'Etc/UTC',
    name: event.name ?? '',
    organizationId: event.organizationId,
    participatingTeamIds: (event.participatingTeamIds as string[]) ?? [],
    venueConfigurationId: event.venueConfigurationId ?? undefined,
    associatedPassConfigIds:
      event.associatedPassConfigs
        ?.filter((pc) => !!pc)
        .map((config) => config?.id)
        .filter((id): id is string => !!id) ?? [],
    ticketTypePricingSummaries:
      event.ticketTypePricingSummaries?.filter(
        (summary): summary is TicketTypePricingSummary => summary !== null
      ) ?? [],
    sport: event.sport ?? undefined,
  };
}
