import { BackHandler, Platform } from 'react-native';

import { useFocusEffect } from '@react-navigation/native';

import { PlatformOS } from './Enums';

export const useAndroidBackHandler = (): void => {
  useFocusEffect(() => {
    const onBackPress = (): boolean => {
      if (Platform.OS === PlatformOS.Android) {
        return true;
      }
      return false;
    };

    const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);

    return () => subscription.remove();
  });
};
