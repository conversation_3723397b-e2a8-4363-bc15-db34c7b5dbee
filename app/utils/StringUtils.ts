export function isValidEmail(s: string | undefined): boolean {
  if (!s || s.length === 0) {
    return false;
  }

  const reg = /^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;

  return reg.test(s);
}

export function isNullOrEmpty(s: string | undefined | null): boolean {
  return !s || s.length === 0;
}

export function capitalizeFirstLetter(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

export function constructPOSDescriptionString(orgId: string): string {
  return `Point of Sale | Organization: ${orgId}`;
}
