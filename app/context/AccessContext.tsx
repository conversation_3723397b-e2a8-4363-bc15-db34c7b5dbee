import React, { ReactNode, useCallback, useContext, useMemo, useState } from 'react';

import { logError } from '../common/Logging';
import { useAccessCodeHook } from '../components/queries/useAccessCodeHook';
import useTicketedEventByIdWithAnalytics from '../gql/hooks/useTicketedEventByIdWithAnalytics';
import { School } from '../gql/public/__generated__/graphql';
import { EventInfo } from '../models/EventInfo';
import { Volunteer } from '../models/Volunteer';
import { eventToEventInfo } from '../utils/EventUtils';

export interface AccessData {
  volunteerInfo: Volunteer | undefined;
  eventInfo: EventInfo | undefined;
  organization: School | undefined;
  organizationLoading: boolean;
  organizationError: boolean;
  setVolunteerInfo: (volunteerInfo?: Volunteer) => void;
  setEventInfo: (eventInfo?: EventInfo) => void;
  refetchTicketedEvent: () => Promise<EventInfo | undefined>;
}

export const AccessContext = React.createContext<AccessData | undefined>(undefined);

export function AccessProvider({ children }: { children: ReactNode }): React.ReactElement | null {
  const [volunteerInfo, setVolunteerInfo] = useState<Volunteer | undefined>();
  const [eventInfo, setEventInfo] = useState<EventInfo | undefined>();

  const {
    accessCode,
    hasError: orgError,
    isLoading: orgLoading,
  } = useAccessCodeHook({
    accessCode: volunteerInfo?.accessCode,
  });

  const { refetchTicketedEvent: refetchEvent } = useTicketedEventByIdWithAnalytics(eventInfo?.id);

  const refetchTicketedEvent = useCallback(async () => {
    if (!eventInfo?.id) {
      return;
    }

    try {
      const result = await refetchEvent();
      if (result?.data) {
        const updatedEventInfo = eventToEventInfo(result.data);
        setEventInfo(updatedEventInfo);
        return updatedEventInfo;
      }
    } catch (err) {
      logError(`Failed to refetch ticketed event: ${err}`, 'refetchEvent');
    }
  }, [eventInfo?.id, refetchEvent]);

  const contextValue = useMemo(
    () => ({
      volunteerInfo,
      eventInfo,
      organization: accessCode?.organization ?? undefined,
      organizationLoading: orgLoading,
      organizationError: orgError,
      setVolunteerInfo,
      setEventInfo,
      refetchTicketedEvent,
    }),
    [volunteerInfo, eventInfo, accessCode?.organization, orgLoading, orgError, refetchTicketedEvent]
  );

  return <AccessContext.Provider value={contextValue}>{children}</AccessContext.Provider>;
}

export const useAccessContext = (): AccessData => {
  const context = useContext(AccessContext);
  if (!context) {
    throw new Error('useAccessContext must be used within an AccessProvider');
  }
  return context;
};
