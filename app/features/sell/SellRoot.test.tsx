import React from 'react';

import { screen } from '@testing-library/react-native';

import SellRoot from './SellRoot';
import { AppTestIDs } from '../../components/AppTestIDs';
import { renderWithOptions } from '../../test/renderHelpers';
import { setupAccessContextMocks } from '../../utils/TestUtils';

jest.mock('../../gql/hooks/useTicketedEventByIdWithAnalytics');
jest.mock('../../components/queries/useAccessCodeHook');
setupAccessContextMocks();

const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

describe('SellRoot', () => {
  it('Renders correctly', () => {
    renderWithOptions(<SellRoot />, {
      withAccessContext: true,
      withNavigationContainer: true,
    });

    expect(screen.getByTestId(AppTestIDs.sellRootContainer)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.ticketsButton)).toBeTruthy();
    expect(screen.getByTestId(AppTestIDs.customSalesButton)).toBeTruthy();
  });
});
