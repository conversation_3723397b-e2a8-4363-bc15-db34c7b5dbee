import React, { useCallback } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconTicket, UniformSpace, UniformStyleSheet, useUniformStyles } from '@hudl/rn-uniform';

import { AppNav } from '../../Nav';
import { AppTestIDs } from '../../components/AppTestIDs';
import MenuButton from '../../components/Buttons/MenuButton';
import { useShowOrganizationEvents } from '../../components/OrganizationEvents/OrganizationEventsModal';
import { useAccessContext } from '../../context/AccessContext';
import IconTransparentTag from '../../icons/IconTransparentTag';
import { nonUniformColors } from '../../utils/ColorUtils';
import { Navigation } from '../../utils/Enums';

const uniStyles = UniformStyleSheet.create(() => ({
  container: {
    padding: UniformSpace.one,
    justifyContent: 'space-between',
    gap: UniformSpace.one,
  },
  iconStyle: {
    color: nonUniformColors.contentBaseForeground,
  },
}));

export default function SellRoot(): React.ReactElement {
  const styles = useUniformStyles(uniStyles);
  const { showOrganizationEvents } = useShowOrganizationEvents();
  const accessContext = useAccessContext();
  const sellNav = AppNav.sell.useNavigation();
  const isReservedSeatingEvent = accessContext.eventInfo?.venueConfigurationId !== undefined;

  const strings = useI18n(
    {
      tickets: 'sell.tickets',
      passes: 'sell.passes',
      customSales: 'sell.custom-sales',
    },
    []
  );

  const ticketsPressed = useCallback(() => {
    if (!accessContext.eventInfo) {
      showOrganizationEvents();
      return;
    }
    sellNav.navigate(Navigation.ItemSelection);
  }, [accessContext.eventInfo, sellNav, showOrganizationEvents]);

  const customPressed = useCallback(() => {
    if (!accessContext.eventInfo) {
      showOrganizationEvents();
      return;
    }
    sellNav.navigate(Navigation.CustomSalesSelection);
  }, [accessContext.eventInfo, sellNav, showOrganizationEvents]);

  return (
    <View style={styles.container} testID={AppTestIDs.sellRootContainer}>
      <MenuButton
        text={strings.tickets}
        icon={<IconTicket style={styles.iconStyle as StyleProp<ViewStyle>} />}
        qaId={AppTestIDs.ticketsButton}
        onPress={ticketsPressed}
        isDisabled={isReservedSeatingEvent}
      />
      <MenuButton
        text={strings.customSales}
        icon={<IconTransparentTag />}
        qaId={AppTestIDs.customSalesButton}
        onPress={customPressed}
      />
    </View>
  );
}
