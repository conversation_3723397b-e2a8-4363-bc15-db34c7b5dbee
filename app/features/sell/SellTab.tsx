import React, { useCallback, useMemo, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconInformation, useUniformStyles } from '@hudl/rn-uniform';

import SellRoot from './SellRoot';
import { stylesheet } from './SellTab.style';
import { AppNav } from '../../Nav';
import { useDefaultStackScreenOptions } from '../../NavOptions';
import { AppTestIDs } from '../../components/AppTestIDs';
import InstructionsSheet from '../../components/InstructionsSheet/InstructionsSheet';
import DefaultHeader from '../../components/NavigationHeaders/DefaultHeader';
import { CustomSaleCalculator } from '../../components/screens/CustomSaleCalculator/CustomSaleCalculator';
import { CustomSaleSelection } from '../../components/screens/CustomSaleSelection/CustomSaleSelection';
import { AcceptCashPayment } from '../../components/screens/checkout/AcceptCashPayment/AcceptCashPayment';
import { AcceptPayment } from '../../components/screens/checkout/AcceptPayment/AcceptPayment';
import { ItemSelection } from '../../components/screens/checkout/ItemSelection/ItemSelection';
import { OrderComplete } from '../../components/screens/checkout/OrderComplete/OrderComplete';
import { ReceiptSelection } from '../../components/screens/checkout/ReceiptSelection/ReceiptSelection';
import { ReviewOrder } from '../../components/screens/checkout/ReviewOrder/ReviewOrder';
import { Navigation, Screens } from '../../utils/Enums';

const Stack = AppNav.sell.createStackNavigator();

export default function SellTab(): React.ReactElement {
  const styles = useUniformStyles(stylesheet);
  const screenOptions = useDefaultStackScreenOptions();

  const [instructionsOpen, setInstructionsOpen] = useState(false);

  const strings = useI18n(
    {
      tabTitle: 'sell.tab-title',
      ticketsTitle: 'sell.tickets',
      instructionsTitle: 'ticketing-instructions.title',
      step1: 'ticketing-instructions.step-1',
      step2: 'ticketing-instructions.step-2',
      step3: 'ticketing-instructions.step-3',
      step4: 'ticketing-instructions.step-4',
      instructionsCloseButton: 'ticketing-instructions.close-button',
    },
    []
  );

  const instructions = useMemo(
    () => [strings.step1, strings.step2, strings.step3, strings.step4],
    [strings.step1, strings.step2, strings.step3, strings.step4]
  );

  const openBottomSheet = useCallback(() => setInstructionsOpen(true), []);

  const headerRight = useCallback(() => {
    return (
      <TouchableOpacity onPress={openBottomSheet} testID={AppTestIDs.infoIcon}>
        <IconInformation />
      </TouchableOpacity>
    );
  }, [openBottomSheet]);

  return (
    <View style={styles.container}>
      <Stack.Navigator
        screenOptions={{
          ...screenOptions,
          headerShown: true,
          header: DefaultHeader,
        }}>
        <Stack.Screen
          name={Screens.Sell}
          component={SellRoot}
          initialParams={{ hideLeftIcon: true }}
          options={{
            title: strings.tabTitle,
          }}
        />
        <Stack.Screen
          name={Navigation.ItemSelection}
          component={ItemSelection}
          options={{
            title: strings.ticketsTitle,
            headerRight,
          }}
        />
        <Stack.Screen
          name={Navigation.ReviewOrder}
          component={ReviewOrder}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Navigation.ReceiptSelection}
          component={ReceiptSelection}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen name={Navigation.CustomSalesSelection} component={CustomSaleSelection} />
        <Stack.Screen
          name={Navigation.CustomSales}
          component={CustomSaleCalculator}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Navigation.AcceptPayment}
          component={AcceptPayment}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Navigation.AcceptCashPayment}
          component={AcceptCashPayment}
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name={Navigation.OrderComplete}
          component={OrderComplete}
          options={{
            headerShown: false,
          }}
        />
      </Stack.Navigator>
      <InstructionsSheet
        modalVisible={instructionsOpen}
        instructions={instructions}
        headerText={strings.instructionsTitle}
        buttonText={strings.instructionsCloseButton}
        setModalVisible={setInstructionsOpen}
      />
    </View>
  );
}
