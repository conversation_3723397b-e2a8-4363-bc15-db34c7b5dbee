import React, { ReactElement, useCallback } from 'react';
import { Text, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './ContactSupport.style';
import { AppTestIDs } from '../../../../components/AppTestIDs';
import {
  openPhoneLink,
  openSupportLink,
  openTutorialsLink,
  ticketingSupportPhoneNumber,
} from '../../../../utils/UrlUtils';

export function ContactSupport(): ReactElement {
  const i18nStrings = useI18n(
    {
      supportTutorials: 'contact-support.support-tutorials',
      havingTrouble: 'contact-support.having-trouble',
    },
    []
  );
  const styles = useUniformStyles(stylesheet);

  const callSupport = useCallback(() => {
    openPhoneLink(ticketingSupportPhoneNumber);
  }, []);

  return (
    <View style={styles.contactSupportContent}>
      <Text style={[styles.reactNativeText]}>
        {i18nStrings.supportTutorials[0]}
        <Text
          style={styles.linkText}
          onPress={openTutorialsLink}
          testID={AppTestIDs.contactSupportTutorials}>
          {i18nStrings.supportTutorials[1]}
        </Text>
        {i18nStrings.supportTutorials[2]}
      </Text>
      <Text style={[styles.reactNativeText]}>
        {i18nStrings.havingTrouble[0]}
        <Text style={styles.linkText} onPress={callSupport} testID={AppTestIDs.contactSupportPhone}>
          {i18nStrings.havingTrouble[1]}
        </Text>
        {i18nStrings.havingTrouble[2]}
        <Text
          style={styles.linkText}
          onPress={openSupportLink}
          testID={AppTestIDs.contactSupportEmail}>
          {i18nStrings.havingTrouble[3]}
        </Text>
        {i18nStrings.havingTrouble[4]}
      </Text>
    </View>
  );
}
