import { UniformSpace, UniformStyleSheet } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const stylesheet = UniformStyleSheet.create((colors) => ({
  reactNativeText: {
    color: colors.baseWhite,
    fontSize: 14,
    lineHeight: 24,
    fontWeight: '500',
  },
  linkText: {
    color: nonUniformColors.contentEmphasis,
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 24,
  },
  contactSupportContent: {
    display: 'flex',
    flexDirection: 'column',
    borderRadius: UniformSpace.half,
  },
}));
