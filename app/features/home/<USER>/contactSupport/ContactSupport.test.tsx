import React from 'react';
import { Linking } from 'react-native';

import { fireEvent, screen } from '@testing-library/react-native';

import { ContactSupport } from './ContactSupport';
import { AppTestIDs } from '../../../../components/AppTestIDs';
import { renderWithOptions } from '../../../../test/renderHelpers';

const spy = jest.spyOn(Linking, 'openURL');

describe('ContactSupport Tests', () => {
  it('Renders contact support screen and opens link: tutorials', () => {
    renderWithOptions(<ContactSupport />);

    expect(screen.getByTestId(AppTestIDs.contactSupportTutorials)).toBeDefined();
    fireEvent.press(screen.getByTestId(AppTestIDs.contactSupportTutorials));
    expect(spy).toHaveBeenCalledWith(
      'https://support.hudl.com/s/topic/0TOVY0000000P3H4AU/ticketreaderapp'
    );
  });

  it('Renders contact support screen and opens link: email', () => {
    renderWithOptions(<ContactSupport />);

    expect(screen.getByTestId(AppTestIDs.contactSupportTutorials)).toBeDefined();
    fireEvent.press(screen.getByTestId(AppTestIDs.contactSupportEmail));
    expect(spy).toHaveBeenCalledWith('https://www.hudl.com/support/contact');
  });

  it('Renders contact support screen and opens link: phone', () => {
    renderWithOptions(<ContactSupport />);

    expect(screen.getByTestId(AppTestIDs.contactSupportTutorials)).toBeDefined();
    fireEvent.press(screen.getByTestId(AppTestIDs.contactSupportPhone));
    expect(spy).toHaveBeenCalledWith('tel:************');
  });
});
