import React, { useCallback } from 'react';
import { View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, UniformStyleSheet, useUniformStyles } from '@hudl/rn-uniform';

import HomeRoot from './HomeRoot';
import { AppNav } from '../../Nav';
import { useDefaultStackScreenOptions } from '../../NavOptions';
import { AppTestIDs } from '../../components/AppTestIDs';
import DefaultHeader from '../../components/NavigationHeaders/DefaultHeader';
import { useFanModalContext } from '../../components/modal/ModalProvider';
import { useAccessContext } from '../../context/AccessContext';
import { removeCurrentVolunteer } from '../../local_storage/VolunteerStorage';
import { Navigation, Screens } from '../../utils/Enums';

const Stack = AppNav.home.createStackNavigator();

const uniStyles = UniformStyleSheet.create(() => ({
  button: {
    bottom: 4,
    height: 30,
  },
}));

export default function HomeTab(): React.ReactElement {
  const styles = useUniformStyles(uniStyles);
  const screenOptions = useDefaultStackScreenOptions();
  const modalContext = useFanModalContext();
  const accessContext = useAccessContext();
  const nav = AppNav.root.useNavigation();

  const strings = useI18n(
    {
      home: 'tab.home',
      signOutTitle: 'sign-out.title',
      signOutSubtext: 'sign-out.subtext',
      signOutButton: 'sign-out.confirmation-button',
      cancel: 'sign-out.cancel',
    },
    []
  );

  const signOut = useCallback(() => {
    removeCurrentVolunteer();
    accessContext.setVolunteerInfo(undefined);
    accessContext.setEventInfo(undefined);
    nav.navigate(Navigation.Landing);
    modalContext.closeModal();
  }, [modalContext, nav, accessContext]);

  const openSignOutModal = useCallback(() => {
    modalContext.showModal(
      strings.signOutTitle,
      strings.signOutSubtext,
      'normal',
      true,
      [
        {
          buttonStyle: 'standard',
          buttonType: 'primary',
          text: strings.signOutButton,
          testID: AppTestIDs.signOutModalButton,
          onPress: signOut,
        },
      ],
      strings.cancel
    );
  }, [modalContext, signOut, strings]);

  const headerRight = useCallback(() => {
    return (
      <View style={styles.button}>
        <Button
          onPress={openSignOutModal}
          buttonStyle={'minimal'}
          buttonType={'subtle'}
          text={strings.signOutTitle}
          size={'small'}
          testID={AppTestIDs.homePageSignOutButton}
        />
      </View>
    );
  }, [openSignOutModal, strings.signOutTitle, styles.button]);

  return (
    <Stack.Navigator
      screenOptions={{
        ...screenOptions,
        headerShown: true,
        header: DefaultHeader,
      }}>
      <Stack.Screen
        name={Screens.Home}
        component={HomeRoot}
        initialParams={{ hideLeftIcon: true }}
        options={{
          title: strings.home,
          headerRight,
        }}
      />
    </Stack.Navigator>
  );
}
