import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView, View } from 'react-native';

import { useFocusEffect } from '@react-navigation/native';
import { useStripeTerminal } from '@stripe/stripe-terminal-react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './HomeRoot.style';
import { ContactSupport } from './sections/contactSupport/ContactSupport';
import EventSelection from './sections/eventSelection/EventSelection';
import ReaderConnection from './sections/readerConnection/ReaderConnection';
import { AppTestIDs } from '../../components/AppTestIDs';
import { useShowOrganizationEvents } from '../../components/OrganizationEvents/OrganizationEventsModal';
import ScannerUploadStatus from '../../components/ScannerStatus/ScannerUploadStatus';
import { useShowDiscoverReaders } from '../../components/screens/pointOfSale/DiscoverReaders/DiscoverReadersModal';
import { useAccessContext } from '../../context/AccessContext';
import { getAllRemainingScanResultGroups } from '../../local_storage/ResultGroupStorage';
import { useAppSession } from '../../session/AppSession';
import { useUploadResultsInIntervals } from '../../ticket-uploading/TicketUploadingHooks';

export default function HomeRoot(): React.ReactElement {
  const styles = useUniformStyles(stylesheet);
  const accessContext = useAccessContext();
  const { eventInfo } = accessContext;
  const session = useAppSession();

  const [uploadsFetched, setUploadsFetched] = useState(false);
  const [uploadsNeeded, setUploadsNeeded] = useState(0);
  const { showOrganizationEvents } = useShowOrganizationEvents();
  const { showDiscoverReaders } = useShowDiscoverReaders();
  const {
    disconnectReader,
    connectedReader,
    initialize: initializeStripeReader,
    isInitialized: isStripeReaderInitialized,
  } = useStripeTerminal();

  const strings = useI18n(
    {
      currentEvent: 'home-root.current-event',
      readerConnection: 'home-root.reader-connection',
      contactSupport: 'home-root.contact-support',
    },
    []
  );

  useEffect(() => {
    if (!isStripeReaderInitialized) {
      initializeStripeReader();
    }
  }, [initializeStripeReader, isStripeReaderInitialized]);

  useEffect(() => {
    if (!accessContext.eventInfo) {
      showOrganizationEvents();
    }
  }, [showOrganizationEvents, accessContext.eventInfo]);

  const fetchUploadsNeeded = useCallback(async () => {
    const resultGroups = await getAllRemainingScanResultGroups();
    setUploadsNeeded(resultGroups.numberOfResults);
    setUploadsFetched(true);
  }, []);

  useFocusEffect(
    useCallback(() => {
      fetchUploadsNeeded();
    }, [fetchUploadsNeeded])
  );

  const { processComplete } = useUploadResultsInIntervals(session.gqlClients.public, eventInfo?.id);

  useEffect(() => {
    if (processComplete) {
      fetchUploadsNeeded();
    }
  }, [processComplete, fetchUploadsNeeded]);

  const safeDisconnect = useCallback(async () => {
    try {
      await disconnectReader();
    } catch (err) {
      console.warn('Failed to disconnect reader:', err);
    }
  }, [disconnectReader]);

  const showReadersHandler = useCallback(async () => {
    if (connectedReader) {
      await safeDisconnect();
    }
    showDiscoverReaders();
  }, [connectedReader, safeDisconnect, showDiscoverReaders]);

  return (
    <ScrollView style={styles.container} testID={AppTestIDs.homePageScroll}>
      {uploadsFetched && (
        <View style={styles.section}>
          <View style={styles.sectionContent}>
            <ScannerUploadStatus
              ticketsToUpload={uploadsNeeded}
              refreshUploads={fetchUploadsNeeded}
            />
          </View>
        </View>
      )}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{strings.currentEvent}</Text>
        <View style={styles.sectionContent}>
          <EventSelection event={accessContext.eventInfo} onSelectEvent={showOrganizationEvents} />
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{strings.readerConnection}</Text>
        <View style={styles.sectionContent}>
          <ReaderConnection
            findReaderPressed={showReadersHandler}
            disconnectPressed={safeDisconnect}
          />
        </View>
      </View>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>{strings.contactSupport}</Text>
        <View style={styles.sectionContent}>
          <ContactSupport />
        </View>
      </View>
    </ScrollView>
  );
}
