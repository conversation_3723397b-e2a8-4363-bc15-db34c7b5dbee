import { UniformStyleSheet, UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../utils/ColorUtils';

export const stylesheet = UniformStyleSheet.create((colors) => ({
  container: {
    paddingHorizontal: UniformSpace.one,
  },
  section: {
    paddingVertical: UniformSpace.half,
    display: 'flex',
    flexDirection: 'column',
    gap: UniformSpace.half,
  },
  sectionTitle: {
    color: colors.baseWhite,
    fontWeight: '700',
    fontSize: 16,
    paddingBottom: UniformSpace.threeQuarter,
  },
  sectionContent: {
    backgroundColor: nonUniformColors.containerBG,
    padding: UniformSpace.one,
    borderRadius: 8,
  },
}));
