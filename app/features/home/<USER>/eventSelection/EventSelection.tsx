import React, { useMemo } from 'react';
import { View } from 'react-native';

import moment from 'moment';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, IconCalendar, Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './EventSelection.style';
import { SportFromName } from '../../../../common/Sports';
import { AppTestIDs } from '../../../../components/AppTestIDs';
import IconSport from '../../../../components/icons/IconSport';
import { EventInfo } from '../../../../models/EventInfo';

interface Props {
  event?: EventInfo;
  onSelectEvent: () => void;
}

export default function EventSelection({ event, onSelectEvent }: Props): React.ReactElement {
  const styles = useUniformStyles(stylesheet);

  const strings = useI18n(
    {
      unselectedTitle: 'event-selection.unselected-title',
      unselectedContent: 'event-selection.unselected-content',
      unselectedButton: 'event-selection.unselected-button',
      selectedButton: 'event-selection.selected-button',
    },
    []
  );

  const hasEventSelected = useMemo(() => !!event?.id, [event?.id]);

  const headerText = useMemo(() => {
    return hasEventSelected ? event?.name : strings.unselectedTitle;
  }, [event?.name, hasEventSelected, strings.unselectedTitle]);

  const contentText = useMemo(() => {
    return hasEventSelected
      ? moment(event?.date).format('MMMM D, YYYY [at] h:mmA')
      : strings.unselectedContent;
  }, [event?.date, hasEventSelected, strings.unselectedContent]);

  const buttonText = useMemo(() => {
    return event?.id ? strings.selectedButton : strings.unselectedButton;
  }, [event, strings]);

  const icon = useMemo(() => {
    if (hasEventSelected) {
      const sport = event?.sport ? SportFromName[event.sport] : undefined;
      return <IconSport sport={sport} />;
    }
    return <IconCalendar />;
  }, [event?.sport, hasEventSelected]);

  return (
    <View style={styles.container}>
      <View style={styles.left}>
        <View style={styles.iconWrapper}>{icon}</View>
      </View>
      <View style={styles.right}>
        <View style={styles.header}>
          <Text style={styles.headerText} testID={AppTestIDs.homePageEventTitle}>
            {headerText}
          </Text>
        </View>
        <View style={styles.content}>
          <Text
            style={event?.id ? styles.selectedContentText : styles.unselectedContentText}
            testID={AppTestIDs.homePageEventDetails}>
            {contentText}
          </Text>
        </View>
        <Button
          onPress={onSelectEvent}
          text={buttonText}
          buttonType={'secondary'}
          style={styles.button}
          size={'small'}
          testID={AppTestIDs.homePageSelectAndSwitchEventButton}
        />
      </View>
    </View>
  );
}
