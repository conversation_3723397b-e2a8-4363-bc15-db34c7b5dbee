import React from 'react';

import { fireEvent, screen } from '@testing-library/react-native';
import moment from 'moment';

import { HudlI18n } from '@hudl/jarvis/i18n';

import EventSelection from './EventSelection';
import { EventInfo } from '../../../../models/EventInfo';
import enUS from '../../../../strings/en-US.json';
import { renderWithOptions } from '../../../../test/renderHelpers';

beforeAll(() => {
  HudlI18n.loadTranslations({
    'en-US': enUS,
  });
});

describe('EventSelection', () => {
  const onSelectEvent = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders unselected state correctly', () => {
    renderWithOptions(<EventSelection onSelectEvent={onSelectEvent} />);

    expect(screen.getByText('Select Your Event')).toBeTruthy();
    expect(
      screen.getByText(
        "To check in fans and process sales, select the event you're helping with today."
      )
    ).toBeTruthy();
    expect(screen.getByText('Select Event')).toBeTruthy();
  });

  it('renders selected event state correctly', () => {
    const event: EventInfo = {
      id: '123',
      name: 'Test Event',
      date: new Date('2025-12-25T19:00:00Z').toISOString(),
      sport: 'Soccer',
    } as EventInfo;

    renderWithOptions(<EventSelection event={event} onSelectEvent={onSelectEvent} />);

    expect(screen.getByText('Test Event')).toBeTruthy();
    expect(screen.getByText(moment(event.date).format('MMMM D, YYYY [at] h:mmA'))).toBeTruthy();
    expect(screen.getByText('Switch Event')).toBeTruthy();
  });

  it('calls onSelectEvent when button is pressed', () => {
    renderWithOptions(<EventSelection onSelectEvent={onSelectEvent} />);
    fireEvent.press(screen.getByText('Select Event'));
    expect(onSelectEvent).toHaveBeenCalled();
  });
});
