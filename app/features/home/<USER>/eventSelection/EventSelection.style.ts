import { UniformStyleSheet, UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const stylesheet = UniformStyleSheet.create((colors) => ({
  container: {
    flexDirection: 'row',
    gap: UniformSpace.one,
  },
  left: {
    flex: 0,
  },
  right: {
    flex: 1,
  },
  iconWrapper: {
    backgroundColor: nonUniformColors.contentBaseBackground,
    borderRadius: 4,
    height: 32,
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    width: '100%',
  },
  headerText: {
    color: colors.baseWhite,
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 20,
  },
  content: {
    paddingVertical: UniformSpace.quarter,
  },
  unselectedContentText: {
    color: nonUniformColors.contentStrongBG,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
  },
  selectedContentText: {
    color: colors.baseWhite,
    fontWeight: '500',
    fontSize: 14,
  },
  button: {
    width: 124,
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
  },
}));
