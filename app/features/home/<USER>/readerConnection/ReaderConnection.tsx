import React, { useMemo, useState } from 'react';
import { View } from 'react-native';

import { useStripeTerminal } from '@stripe/stripe-terminal-react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { Button, Text, useUniformStyles } from '@hudl/rn-uniform';

import { stylesheet } from './ReaderConnection.style';
import { AppTestIDs } from '../../../../components/AppTestIDs';
import IconReader from '../../../../icons/IconReader';
import { getBatteryLevelIcon } from '../../../../utils/BatteryLevelUtils';
import { getReaderDisplayName } from '../../../../utils/ReaderUtils';

interface Props {
  findReaderPressed: () => void;
  disconnectPressed: () => void;
}

export default function ReaderConnection({
  findReaderPressed,
  disconnectPressed,
}: Props): React.ReactElement {
  const styles = useUniformStyles(stylesheet);

  const [readerDisconnected, setReaderDisconnected] = useState(false);

  const { connectedReader } = useStripeTerminal({
    onDidDisconnect: () => {
      setReaderDisconnected(true);
    },
    onDidChangeConnectionStatus(status) {
      if (status === 'connected') {
        setReaderDisconnected(false);
      }
    },
  });

  const strings = useI18n(
    {
      unselectedTitle: 'reader-connection.unselected-title',
      unselectedContent: 'reader-connection.unselected-content',
      unselectedButton: 'reader-connection.unselected-button',
      disconnectButton: 'reader-connection.disconnect-button',
      changeReader: 'reader-connection.change-reader',
      connected: 'reader-connection.connected',
    },
    []
  );

  const hasValidConnection = useMemo(
    () => !readerDisconnected && connectedReader,
    [connectedReader, readerDisconnected]
  );

  const icon = useMemo(() => {
    return getBatteryLevelIcon(
      connectedReader?.batteryLevel ?? 0,
      Boolean(connectedReader?.isCharging),
      'medium',
      'default'
    );
  }, [connectedReader?.batteryLevel, connectedReader?.isCharging]);

  const headerLeftText = useMemo(() => {
    return hasValidConnection ? connectedReader?.serialNumber : strings.unselectedTitle;
  }, [connectedReader?.serialNumber, hasValidConnection, strings.unselectedTitle]);

  const headerRightText = useMemo(() => {
    return hasValidConnection ? strings.connected : undefined;
  }, [hasValidConnection, strings.connected]);

  const contentLeftText = useMemo(() => {
    return hasValidConnection && connectedReader
      ? getReaderDisplayName(connectedReader)
      : strings.unselectedContent;
  }, [connectedReader, hasValidConnection, strings.unselectedContent]);

  const contentRightText = useMemo(() => {
    if (!hasValidConnection) {
      return undefined;
    }
    const level = connectedReader?.batteryLevel ?? 0;
    return Math.round(level * 100);
  }, [connectedReader?.batteryLevel, hasValidConnection]);

  const buttons = useMemo(() => {
    if (connectedReader) {
      return (
        <View style={styles.buttonRow}>
          <Button
            onPress={findReaderPressed}
            text={strings.changeReader}
            buttonType={'secondary'}
            style={styles.button}
            size={'small'}
          />
          <Button
            onPress={disconnectPressed}
            text={strings.disconnectButton}
            buttonType={'secondary'}
            style={styles.button}
            size={'small'}
          />
        </View>
      );
    }
    return (
      <Button
        onPress={findReaderPressed}
        text={strings.unselectedButton}
        buttonType={'secondary'}
        style={styles.button}
        size={'small'}
        testID={AppTestIDs.homePageFindNearbyReadersButton}
      />
    );
  }, [connectedReader, disconnectPressed, findReaderPressed, strings, styles]);

  return (
    <View style={styles.container}>
      <View style={styles.left}>
        <View style={styles.iconWrapper}>
          <IconReader height={18} />
        </View>
      </View>
      <View style={styles.right}>
        <View style={styles.header}>
          <Text style={styles.headerLeftText}>{headerLeftText}</Text>
          {headerRightText && <Text style={styles.headerRightText}>{headerRightText}</Text>}
        </View>
        <View style={styles.content}>
          <Text style={styles.contentLeftText}>{contentLeftText}</Text>
          {contentRightText && (
            <View style={styles.batteryRow}>
              {icon}
              <Text style={styles.contentRightText}>{contentRightText}%</Text>
            </View>
          )}
        </View>
        {buttons}
      </View>
    </View>
  );
}
