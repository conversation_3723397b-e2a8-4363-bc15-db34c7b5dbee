import { UniformStyleSheet, UniformSpace } from '@hudl/rn-uniform';

import { nonUniformColors } from '../../../../utils/ColorUtils';

export const stylesheet = UniformStyleSheet.create((colors) => ({
  container: {
    flexDirection: 'row',
    gap: UniformSpace.one,
  },
  left: {
    flex: 0,
  },
  right: {
    flex: 1,
  },
  iconWrapper: {
    backgroundColor: nonUniformColors.contentBaseBackground,
    borderRadius: 4,
    height: 32,
    width: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  header: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeftText: {
    color: colors.baseWhite,
    fontSize: 14,
    fontWeight: '700',
    lineHeight: 20,
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 0,
  },
  headerRightText: {
    color: colors.contentDefault,
    fontSize: 14,
    fontWeight: '500',
    flexShrink: 0,
    flexGrow: 0,
  },
  content: {
    paddingVertical: UniformSpace.half,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contentLeftText: {
    color: nonUniformColors.contentStrongBG,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    flexShrink: 1,
    flexGrow: 1,
    flexBasis: 0,
  },
  contentRightText: {
    color: colors.contentDefault,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    flexShrink: 0,
    flexGrow: 0,
  },
  button: {
    alignSelf: 'flex-start',
    backgroundColor: nonUniformColors.contentBaseBackgroundContrast,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: UniformSpace.half,
  },
  batteryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: UniformSpace.eighth,
  },
}));
