import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { StyleProp, View, ViewStyle } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { UniformSpace, UniformStyleSheet, useUniformStyles } from '@hudl/rn-uniform';

import { AppNav } from '../../Nav';
import { AppTestIDs } from '../../components/AppTestIDs';
import MenuButton from '../../components/Buttons/MenuButton';
import InstructionsSheet from '../../components/InstructionsSheet/InstructionsSheet';
import { useShowOrganizationEvents } from '../../components/OrganizationEvents/OrganizationEventsModal';
import { useAccessContext } from '../../context/AccessContext';
import IconCamera from '../../icons/IconCamera';
import IconOrder from '../../icons/IconOrder';
import { nonUniformColors } from '../../utils/ColorUtils';
import { Navigation } from '../../utils/Enums';

const uniStyles = UniformStyleSheet.create(() => ({
  container: {
    padding: UniformSpace.one,
    justifyContent: 'space-between',
    gap: UniformSpace.one,
  },
  content: {
    height: '100%',
    width: '100%',
  },
  iconStyle: {
    color: nonUniformColors.contentBaseForeground,
  },
}));

export default function CheckInRoot(): React.ReactElement {
  const styles = useUniformStyles(uniStyles);
  const { showOrganizationEvents } = useShowOrganizationEvents();
  const accessContext = useAccessContext();
  const nav = AppNav.checkIn.useNavigation();
  const {
    params: { showNeedHelp, timeStamp },
  } = AppNav.checkIn.useRoute(Navigation.CheckIn);
  const [showScannerInstructions, setShowScannerInstructions] = useState(showNeedHelp ?? false);

  useEffect(() => {
    setShowScannerInstructions(showNeedHelp ?? false);
  }, [showNeedHelp, timeStamp]);

  const strings = useI18n(
    {
      scanTickets: 'check-in.scan-tickets',
      searchForOrder: 'check-in.search-for-order',
      contentHeader: 'scanner-instructions.content-header',
      allowCameraAccess: 'scanner-instructions.allow-camera-access',
      increaseBrightness: 'scanner-instructions.increase-brightness',
      wifiAvailable: 'scanner-instructions.wifi-available',
      alignTicket: 'scanner-instructions.align-ticket',
      confirmTicketIsValid: 'scanner-instructions.confirm-ticket-is-valid',
      uploadStatus: 'scanner-instructions.upload-status',
      closeInstructions: 'scanner-instructions.close-instructions',
    },
    []
  );

  const onScanTicketsPress = useCallback((): void => {
    nav.navigate(Navigation.Scanner, {});
  }, [nav]);

  const onSearchForOrderPress = useCallback((): void => {
    nav.navigate(Navigation.PurchaserList, {});
  }, [nav]);

  const scanPressed = useCallback(() => {
    if (!accessContext.eventInfo) {
      showOrganizationEvents();
      return;
    }
    onScanTicketsPress();
  }, [accessContext.eventInfo, onScanTicketsPress, showOrganizationEvents]);

  const searchPressed = useCallback(() => {
    if (!accessContext.eventInfo) {
      showOrganizationEvents();
      return;
    }
    onSearchForOrderPress();
  }, [accessContext.eventInfo, onSearchForOrderPress, showOrganizationEvents]);

  const instructions = useMemo(
    () => [
      strings.allowCameraAccess,
      strings.increaseBrightness,
      strings.wifiAvailable,
      strings.alignTicket,
      strings.confirmTicketIsValid,
      strings.uploadStatus,
    ],
    [
      strings.alignTicket,
      strings.allowCameraAccess,
      strings.confirmTicketIsValid,
      strings.increaseBrightness,
      strings.uploadStatus,
      strings.wifiAvailable,
    ]
  );

  return (
    <View style={styles.container}>
      <MenuButton
        text={strings.scanTickets}
        icon={<IconCamera style={styles.iconStyle as StyleProp<ViewStyle>} />}
        onPress={scanPressed}
        qaId={AppTestIDs.scanTicketsMenuButton}
      />
      <MenuButton
        text={strings.searchForOrder}
        icon={<IconOrder style={styles.iconStyle as StyleProp<ViewStyle>} />}
        onPress={searchPressed}
        qaId={AppTestIDs.searchForOrderMenuButton}
      />
      <InstructionsSheet
        modalVisible={showScannerInstructions}
        instructions={instructions}
        headerText={strings.contentHeader}
        buttonText={strings.closeInstructions}
        setModalVisible={setShowScannerInstructions}
      />
    </View>
  );
}
