import React, { useCallback } from 'react';
import { TouchableOpacity, View } from 'react-native';

import { useI18n } from '@hudl/jarvis/i18n';
import { IconInformation } from '@hudl/rn-uniform';

import CheckInRoot from './CheckInRoot';
import { AppNav } from '../../Nav';
import { useDefaultStackScreenOptions } from '../../NavOptions';
import { AppTestIDs } from '../../components/AppTestIDs';
import DefaultHeader from '../../components/NavigationHeaders/DefaultHeader';
import { PurchaserList } from '../../components/screens/PurchaserList/PurchaserList';
import ScannerScreen from '../../components/screens/scanner/ScannerScreen/ScannerScreen';
import { Navigation, Screens } from '../../utils/Enums';

const Stack = AppNav.checkIn.createStackNavigator();

export default function CheckInTab(): React.ReactElement {
  const screenOptions = useDefaultStackScreenOptions();
  const nav = AppNav.checkIn.useNavigation();

  const strings = useI18n(
    {
      tabTitle: 'check-in.tab-title',
      orderListHeader: 'purchaser-list.header',
    },
    []
  );

  const openInstructions = useCallback((): void => {
    nav.navigate(Navigation.CheckIn, { showNeedHelp: true, timeStamp: Date.now() });
  }, [nav]);

  const renderHelpIcon = useCallback(() => {
    return (
      <View>
        <TouchableOpacity onPress={openInstructions} testID={AppTestIDs.needHelpButton}>
          {<IconInformation />}
        </TouchableOpacity>
      </View>
    );
  }, [openInstructions]);

  return (
    <Stack.Navigator
      screenOptions={{
        ...screenOptions,
        headerShown: true,
        header: DefaultHeader,
      }}>
      <Stack.Screen
        name={Screens.CheckIn}
        component={CheckInRoot}
        initialParams={{ hideLeftIcon: true }}
        options={{
          title: strings.tabTitle,
          headerRight: () => renderHelpIcon(),
        }}
      />
      <Stack.Screen
        name={Navigation.Scanner}
        component={ScannerScreen}
        options={{
          headerRight: () => renderHelpIcon(),
        }}
      />
      <Stack.Screen
        name={Navigation.PurchaserList}
        component={PurchaserList}
        options={{
          title: strings.orderListHeader,
        }}
      />
    </Stack.Navigator>
  );
}
