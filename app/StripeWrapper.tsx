import React, { ReactElement, useCallback } from 'react';

import { StripeTerminalProvider } from '@stripe/stripe-terminal-react-native';

import useCreateTerminalConnectionToken from './gql/hooks/useTerminalConnectionToken';

export function StripeWrapper({ children }: { children: ReactElement }): ReactElement {
  const { createStripeConnectionToken } = useCreateTerminalConnectionToken();

  const fetchTokenProvider = useCallback(async (): Promise<string> => {
    const result = await createStripeConnectionToken();
    const clientSecret = result?.data?.createTerminalConnectionToken?.clientSecret;
    if (!clientSecret) {
      throw new Error('Failed to fetch terminal connection token');
    }
    return clientSecret;
  }, [createStripeConnectionToken]);

  return (
    <StripeTerminalProvider logLevel="verbose" tokenProvider={fetchTokenProvider}>
      {children}
    </StripeTerminalProvider>
  );
}
