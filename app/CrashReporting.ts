import * as Sentry from '@sentry/react-native';

import { getSentrySDKConfig } from '@hudl/analytics';

import packageInfo from '../package.json';

export default class CrashReporting {
  private static isInitialized = false;

  public static init(): void {
    if (CrashReporting.isInitialized) {
      return;
    }

    const sentryConfig = getSentrySDKConfig('ticketing', packageInfo.version);

    Sentry.init(sentryConfig);

    CrashReporting.isInitialized = true;
  }

  public static setUserID(id?: string): void {
    if (CrashReporting.isInitialized) {
      Sentry.setUser({ id });
    }
  }

  public static nativeCrash(): void {
    if (CrashReporting.isInitialized) {
      Sentry.nativeCrash();
    }
  }
}
