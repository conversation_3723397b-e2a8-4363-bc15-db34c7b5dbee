{"landing": {"get-started": "Get Started", "continue": "Continue"}, "volunteer-info": {"header": "Your Information", "sub-header": "Fill out your information to start scanning tickets or accepting payments for an event.", "continue-as-header": "Continue As", "continue-as-sub-header": "Is the previous information below correct?", "first-name": "First Name", "first-name-placeholder": "Enter first name", "last-name": "Last Name", "last-name-placeholder": "Enter last name", "access-code-placeholder": "Enter access code", "access-code": "Access Code", "email": "Email Address", "email-placeholder": "Enter email address"}, "device-setup": {"header": "Get you Scanner ready", "sub-header": "To Scan tickets from this app, you will need to allow camera access.", "allow-camera-access": "Allow Camera Access"}, "organization-events": {"header": "Select Event", "empty-state": "No upcoming ticketed events.", "error-state": "There was a problem loading data."}, "scanner-instructions": {"content-header": "Ticket Scanning Tips", "allow-camera-access": "Allow Camera Access by tapping on 'Scan Tickets' Button.", "increase-brightness": "Tell fans to increase device brightness.", "wifi-available": "If WiFi is available, connect before scanning.", "align-ticket": "Align ticket QR codes with scanner lines.", "confirm-ticket-is-valid": "Confirm tickets are valid.", "upload-status": "Refer to the home page for scanned tickets upload status.", "close-instructions": "Close Instructions"}, "event-action-selection": {"scan-tickets": "Scan Tickets", "sell-tickets": "Sell Tickets", "check-in-fans": "Check In Fans", "back-to-all-events": "Back to All Events", "continue": "Continue", "your-event-tools": "Your Event Tools", "contact-support": "Contact Support", "custom-sales": "Custom Sales"}, "connect-reader": {"connected": "Connected", "disconnect": "Disconnect", "software-update-installing": "Software update installing... please stay on this page to connect."}, "pos": {"item-selection": {"header": "Wait to show fans your screen until it's time to review their order.", "cash-button": "Cash", "card-button": "Card", "free": "Free", "sold-out": "SOLD OUT", "fee-message": "Additional fees will be applied at checkout.", "non-refundable": "All sales are non-refundable.", "ticket-limit-note": "You've reached the max number of tickets allowed per order ({{limit}} tickets).", "error": {"header": "Unable to Load Tickets", "message": ["We're having trouble loading tickets for this event. Please try again. If the problem persists, ", "contact support", "."], "try-again": "Try Again"}, "pricing-summary-error": {"header": "Unable to Calculate Order Total", "message": ["There was a problem calculating the order total. Please try again. If the problem persists, ", "contact support", "."], "try-again": "Try Again"}}, "review-order": {"header": "You may show fans this screen to review their order before payment.", "review-order": "Review Order", "paid-sub-header": "Confirm the order is correct before payment.", "free-sub-header": "Confirm the order is correct before checkout.", "free": "Free", "items-header": {"zero": "Items", "one": "<PERSON><PERSON>", "other": "Items"}, "reviewable-item-name-quantity": "{{quantity}} x {{name}}", "fee-message": "Total includes all fees.", "process-card-button": "Process Card Payment", "process-cash-button": "Process Cash Payment", "edit-button": "Edit Order", "error": {"header": "Unable to Load Tickets", "message": ["We're having trouble loading the ticket order. Please try again. If the problem persists, ", "contact support", "."], "try-again": "Try Again"}, "order-transaction-fee": "Order Transaction Fee"}, "order-total": {"order-total": "Order Total: {{count}}", "tickets": {"zero": "tickets", "one": "ticket", "other": "tickets"}, "non-refundable": "All sales are non-refundable.", "additional-fees": "Additional fees may be applied at checkout. All sales are non-refundable."}, "receipt-selection": {"header": "Need a Receipt?", "sub-header": "Receipts are sent via email.", "yes-button": "Yes, Send Receipt", "no-button": "No Thanks", "info-input": {"header": "Email Receipt", "first-name": "First Name", "last-name": "Last Name", "email": "Email Address", "email-placeholder": "<EMAIL>", "error": {"first": "Please provide your first name.", "last": "Please provide your last name.", "email": "Please enter a valid email address."}, "required": "*"}, "accept-payment-button": "Accept Payment", "place-order-button": "Place Order"}, "order-complete": {"header": "Order Complete", "done-selling": "<PERSON>", "sell-more-tickets": "<PERSON><PERSON> Tickets", "sell-more-items": "<PERSON><PERSON> Items", "custom-item": "Custom Item", "tickets-sold": {"one": "{{ticketCount}} Ticket Sold", "other": "{{ticketCount}} Tickets Sold"}, "order-number": "Order Number", "payment-method": "Payment Method", "total-amount": "Total Amount", "cash": "Cash", "card": "Card"}}, "selectable-item": {"tickets-remaining": {"one": "{{count}} ticket left", "other": "{{count}} tickets left"}}, "accept-payments": {"reader-input-options": ["{{readerInputOptions}}", " to purchase tickets."], "loading-header": "Use Reader for Payment", "loading-help-text": "Tap to pay via phone or physical card. When tapping, make sure the payment method is held close to the reader.", "accepting-payment": "Accepting Payment...", "declined-by-stripe-payment-header": "There was an issue with your payment.", "declined-by-stripe-payment-help-text": "Please try again or use a different payment method.", "payment-error-header": "Unable to Collect Payment", "cash-payment-error-header": "Unable to Process Cash Transaction", "payment-error-help-text": ["We're having trouble communicating with our payments provider. Please try again.", "If the issue persists, ", "contact support", ", or direct fans to Hudl - Fan (fan.hudl.com) to purchase tickets."], "cash-payment-error-help-text": ["We’re having trouble processing cash transactions. Please try again.", "If the issue persists, ", "contact support", ", or manually record cash sales after the event has concluded."], "try-again": "Try Again", "card-declined": ["The card entered was declined. To finish placing the order,", " try again or use a different card."], "location-services-disabled": "Location Services Disabled", "location-services-disabled-help-text": ["Enable location services in device settings to accept payments. Once enabled, please try again."], "cancel-payment": "Cancel Payment"}, "reader-display-message": {"remove-card": ["Please", " remove the card"], "retry-card": ["Unable to read card. ", "Try again."], "insert-card": ["Insert a card", " to purchase tickets"], "insert-or-swipe-card": ["Insert or swipe a card", " to purchase tickets"], "multiple-contactless-cards-detected": ["We detected multiple cards.", "Try again with one card"], "try-another-card": ["Unable to accept payment. ", "Try using a different card."], "try-another-read-method": ["Unable to read card. ", "Swipe, tap, or insert the card instead."], "card-removed-too-early": ["The card was removed too quickly. Try again."], "check-mobile-device": "Unable to accept payment. Make sure your bluetooth is on and you are connected to the reader."}, "schedule": {"recent": "Recent", "next": "Up Next", "live": "LIVE", "schedule": "Schedule", "at": "AT", "vs": "VS", "now": "Now", "now-all-capitalized": "NOW", "today": "Today", "flag": {"view": "View", "tickets": "Tickets", "streaming": "Streaming"}}, "scanner-status": {"header": {"ticket-not-recognized": "Not Recognized", "ticket-already-scanned": "Already Scanned", "incorrect-event": "Incorrect Event", "error-scanning-ticket": "<PERSON><PERSON>", "event-not-included": "Event not included", "invalid-pass-dates": "Invalid Pass Dates"}, "message": {"ticket-not-recognized": "Double check this QR code is for a Hudl ticketed event.", "ticket-already-scanned-start": "This QR code was already scanned for this event on ", "ticket-already-scanned-end": "It is no longer valid for entry", "incorrect-event-start": "This ticket is not valid for", "error-scanning-ticket": "Something went wrong while scanning. Please try again or contact support.", "align-qr-code": "Align QR code with lines", "scan-complete": "Scan complete!", "event-not-included": "This pass doesn't include the event: ", "invalid-pass-dates": "This pass is only valid for events between "}, "button": {"finish-scanning": "Finish Scanning", "need-help": "Need Help?"}}, "upload-tickets": {"header": {"finished-scanning": "Finished Scanning?", "tickets-found-to-upload": "Upload Scanned Tickets & Passes"}, "message": {"finished-scanning-start": "You recently scanned tickets for ", "finished-scanning-end": "Let us know you have completed scanning for this event by tapping the button below.", "tickets-found-to-upload": {"zero": "You have {{count}} scans that need to be uploaded for tracking purposes.", "one": "You have {{count}} scan that needs to be uploaded for tracking purposes.", "other": "You have {{count}} scans that need to be uploaded for tracking purposes."}}, "button": {"needs-uploading": "Upload Data", "error-uploading": "Upload Failed", "completed-uploading": "Upload Complete", "uploading": "Uploading", "finish-scanning": "Finish Scanning"}}, "purchaser-list": {"header": "Order List", "sub-header": "Search for fans who ordered tickets and manually check them in if needed.", "no-purchasers": "Fans have not purchased tickets for this event yet. Check back later for updates.", "error-message": ["We're having trouble loading this content. Try reloading or ", "contact support."], "search-name": "Search name", "reload": "Reload", "no-results-found": "No results found."}, "purchaser-list-item": {"tickets": {"zero": "{{count}} tickets", "one": "{{count}} ticket", "other": "{{count}} tickets"}, "passes": {"zero": "{{count}} passes", "one": "{{count}} pass", "other": "{{count}} passes"}}, "contact-support": {"support-tutorials": ["Have questions? Check out our ", "support tutorials", "."], "having-trouble": ["If you're still having trouble, contact us by ", "phone", " or ", "email", "."]}, "custom-sales": {"calculator-input-price-header": "Input Custom Price", "minimum-sales-price": "Sale price must be at least $0.50.", "maximum-sales-price": "Sale price must be less than $250.00.", "checkout": "Checkout", "cancel-order": "Cancel Order", "fee-message": "Additional fees will be applied at checkout."}, "custom-sales-selection": {"concessions": "Concessions", "apparel": "<PERSON><PERSON><PERSON>", "other": "Other", "select-item-sell-header": "Select what type of item you are selling", "continue": "Continue"}, "tab": {"home": "Home", "sell": "<PERSON>ll", "check-in": "Check In"}, "sell": {"tab-title": "What Are You Selling?", "tickets": "Tickets", "passes": "Passes", "custom-sales": "Custom Sales"}, "check-in": {"tab-title": "Check In Fans", "scan-tickets": "Scan Tickets", "search-for-order": "Search for Order"}, "sign-out": {"title": "Sign Out", "subtext": "Are you sure you want to sign out?", "confirmation-button": "Yes, Sign Out", "cancel": "Cancel"}, "home-root": {"current-event": "Current Event", "reader-connection": "Reader Connection", "contact-support": "Contact Support"}, "event-selection": {"unselected-title": "Select Your Event", "unselected-content": "To check in fans and process sales, select the event you're helping with today.", "unselected-button": "Select Event", "selected-button": "Switch Event"}, "scanner-upload-status": {"upload-needed-header": "Upload Scanned Tickets & Passes", "upload-needed-helper": {"zero": "You have {{count}} scans that need to be uploaded for tracking purposes.", "one": "You have {{count}} scan that needs to be uploaded for tracking purposes.", "other": "You have {{count}} scans that need to be uploaded for tracking purposes."}, "upload-not-needed-header": "All Scans Have Been Uploaded", "upload-not-needed-helper": "Your scanned tickets & passes have successfully been uploaded. No further action needed.", "upload-data-button": "Upload Data"}, "reader-connection": {"unselected-title": "Connect to a Reader", "unselected-content": "In order to take digital payments, you must be connected to a nearby reader.", "unselected-button": "Find Nearby Readers", "disconnect-button": "Disconnect", "change-reader": "Change Reader", "connected": "Connected"}, "reader-connection-wrapper": {"finding-title": "Finding Readers Nearby", "connect-title": "Connect to a Reader", "finding-details": "Hang tight! We're working on it. Please stay on this screen.", "connect-details": "Select the reader you'll be using to accept payments.", "continue-button": "Continue", "cancel-button": "Cancel", "try-again": "Try Again", "still-looking-title": "We're Still Looking...", "still-looking-details": "It's taking a bit longer than usual—make sure your readers are powered on and within range. Continue to stay on this page."}, "discover-readers-content": {"finding-readers": "Finding Readers..."}, "discover-readers-errors": {"discover-readers-error": ["We're having trouble discovering readers. Please try again or if this problem persists, ", "contact support", "."], "system-connection-error-title": "System Connection Error", "system-connection-error-subheader": "We're having trouble communicating with our payments provider. Please try again.", "error-contact-support": ["If the issue persists, ", "contact support,", " or direct fans to Hudl - Fan (fan.hudl.com) to purchase tickets."], "generic-error": ["We're having trouble discovering readers. Please try again or if this problem persists, ", "contact support."], "unable-to-discover": "Unable to Discover Readers", "unable-to-connect": "Unable to Connect to Readers", "no-readers-found-title": "No Readers Found", "no-readers-found-subtitle": ["Looks like we could not find any readers nearby. Please try again or if this problem persists, ", "contact support."], "low-battery-title": "Critically Low Battery", "low-battery-details": ["The reader's battery is critically low. Please charge the reader and try again. If this problem persists, ", "contact support."]}, "reader-connection-init": {"android-permission-title": "Location Permission", "android-permission-message": "Terminal needs access to your location", "android-permission-button": "Accept"}, "ticketing-instructions": {"title": "How to Accept Payments for Tickets", "step-1": "Select the desired ticket type and quantity.", "step-2": "Review the order with the customer: Only show the “Review Order” screen to customers for this step when asked.", "step-3": "Card Payment: Use the Stripe Terminal Reader to accept payment (tap to pay or physical card).", "step-4": "Cash Payment: After accepting cash payment, process order for payment confirmation.", "close-button": "Close Instructions"}, "camera-permission-requester": {"header": "Allow Camera Access", "subtext": "Open settings to enable camera permissions to access the scanner.", "open-settings-button": "Open Settings", "cancel-button": "Cancel"}}