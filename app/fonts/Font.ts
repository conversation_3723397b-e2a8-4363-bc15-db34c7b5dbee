import { TextStyle } from 'react-native';

import { useFonts } from '@hudl/jarvis/device';

export const FontWeight: { [key in string]: TextStyle['fontWeight'] } = {
  bold: '700',
};

const Font = {
  teko: {
    light: 'Teko-Light',
    medium: 'Teko-Medium',
    regular: 'Teko-Regular',
    semiBold: 'Teko-SemiBold',
    bold: 'Teko-Bold',
  },
};

export function useAppFonts(): boolean {
  return useFonts({
    [Font.teko.light]: require('./Teko-Light.ttf'),
    [Font.teko.medium]: require('./Teko-Medium.ttf'),
    [Font.teko.regular]: require('./Teko-Regular.ttf'),
    [Font.teko.semiBold]: require('./Teko-SemiBold.ttf'),
    [Font.teko.bold]: require('./Teko-Bold.ttf'),
  });
}

export default Font;
