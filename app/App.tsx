import React from 'react';
import { Platform } from 'react-native';

import { ApolloProvider } from '@apollo/client';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { NavigationContainer } from '@react-navigation/native';
import { withDetoxContext } from 'react-native-detox-context';
import { DetoxContext } from 'react-native-detox-context';
import DeviceInfo from 'react-native-device-info';
import { gestureHandlerRootHOC } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { JarvisConstants } from '@hudl/jarvis/client';
import { useI18n } from '@hudl/jarvis/i18n';
import { ToastMessenger, withUniformTheme } from '@hudl/rn-uniform';

import CrashReporting from './CrashReporting';
import { AppNav } from './Nav';
import { useBottomTabsScreenOptions, useDefaultStackScreenOptions } from './NavOptions';
import { StripeWrapper } from './StripeWrapper';
import { useConfigureHudlLogger } from './common/Logging';
import DefaultHeader from './components/NavigationHeaders/DefaultHeader';
import { OrganizationEventsModal } from './components/OrganizationEvents/OrganizationEventsModal';
import { FanModal } from './components/modal/Modal';
import { withFanModalProvider } from './components/modal/ModalProvider';
import { Landing } from './components/screens/Landing';
import { LoadingOrg } from './components/screens/LoadingOrg';
import { VolunteerInfo } from './components/screens/VolunteerInfo';
import { DiscoverReadersModal } from './components/screens/pointOfSale/DiscoverReaders/DiscoverReadersModal';
import { AccessProvider } from './context/AccessContext';
import CheckInTab from './features/checkIn/CheckInTab';
import HomeTab from './features/home/<USER>';
import SellTab from './features/sell/SellTab';
import { useAppFonts } from './fonts/Font';
import { useAppSession, withAppSessionProvider } from './session/AppSession';
import { UploadRemainingTicketsModal } from './ticket-uploading/UploadRemainingTicketsModal';
import { Navigation } from './utils/Enums';

const Stack = AppNav.root.createStackNavigator();
const BottomTabs = AppNav.bottomTabs.createBottomTabNavigator();

function App(): React.ReactElement | null {
  // Disable Sentry builds for dev builds, automated tests, and while running on the Jarvis Client.
  if (!__DEV__ && !DetoxContext.isAutomatedTest && !JarvisConstants.isRunningOnJarvisClient()) {
    CrashReporting.init();
  }
  const screenOptions = useDefaultStackScreenOptions();

  const fontsLoaded = useAppFonts();

  const session = useAppSession();

  if (!fontsLoaded) {
    // Current session still loading
    return null;
  }

  function HudlLoggerConfigurator(): null {
    useConfigureHudlLogger();
    return null;
  }

  const allowLandscape = DeviceInfo.isTablet();

  return (
    <ApolloProvider client={session.gqlClients.public}>
      <NavigationContainer>
        <AccessProvider>
          <HudlLoggerConfigurator />
          <StripeWrapper>
            <SafeAreaProvider>
              <BottomSheetModalProvider>
                <Stack.Navigator
                  screenOptions={{
                    ...screenOptions,
                    headerShown: false,
                    orientation: allowLandscape ? 'all' : 'portrait',
                  }}>
                  <Stack.Group screenOptions={{ header: DefaultHeader }}>
                    <Stack.Screen name="landing" component={Landing} />
                  </Stack.Group>
                  <Stack.Screen name={'root'} component={RootTabs} />
                  <Stack.Screen name="volunteerInfo" component={VolunteerInfo} />
                  <Stack.Screen
                    name="loadingOrg"
                    component={LoadingOrg}
                    options={{
                      headerShown: false,
                    }}
                  />
                  <Stack.Screen
                    name="organizationEvents"
                    component={OrganizationEventsModal}
                    options={{
                      presentation: Platform.OS === 'android' ? 'modal' : 'card',
                      gestureDirection: 'vertical',
                      gestureEnabled: true,
                      animation: 'slide_from_bottom',
                    }}
                  />
                  <Stack.Screen
                    name={Navigation.UploadTickets}
                    component={UploadRemainingTicketsModal}
                    options={{
                      presentation: 'transparentModal',
                      contentStyle: { backgroundColor: '#40404040' },
                      animation: 'fade',
                    }}
                  />
                  <Stack.Screen
                    name="discoverReaders"
                    component={DiscoverReadersModal}
                    options={{
                      presentation: Platform.OS === 'android' ? 'modal' : 'card',
                      gestureDirection: 'vertical',
                      gestureEnabled: true,
                      animation: 'slide_from_bottom',
                    }}
                  />
                </Stack.Navigator>
              </BottomSheetModalProvider>
              <FanModal />
            </SafeAreaProvider>
          </StripeWrapper>
          <ToastMessenger />
        </AccessProvider>
      </NavigationContainer>
    </ApolloProvider>
  );
}

function RootTabs(): React.ReactElement {
  const screenOptions = useBottomTabsScreenOptions();

  const strings = useI18n(
    {
      home: 'tab.home',
      sell: 'tab.sell',
      checkIn: 'tab.check-in',
    },
    []
  );

  return (
    <BottomTabs.Navigator {...{ screenOptions }}>
      <BottomTabs.Screen
        name={Navigation.HomeTab}
        component={HomeTab}
        options={{
          title: strings.home,
          tabBarTestID: AppNav.bottomTabs.testID(Navigation.HomeTab),
        }}
      />
      <BottomTabs.Screen
        name={Navigation.SellTab}
        component={SellTab}
        options={{
          title: strings.sell,
          tabBarTestID: AppNav.bottomTabs.testID(Navigation.SellTab),
        }}
      />
      <BottomTabs.Screen
        name={Navigation.CheckInTab}
        component={CheckInTab}
        options={{
          title: strings.checkIn,
          tabBarTestID: AppNav.bottomTabs.testID(Navigation.CheckInTab),
        }}
      />
    </BottomTabs.Navigator>
  );
}

export default withDetoxContext(
  withUniformTheme(withAppSessionProvider(withFanModalProvider(gestureHandlerRootHOC(App))), 'dark')
);
