import { ReactElement } from 'react';

import { LineItemType, TooltipType } from '../enums/shared';

export interface LineItemSelection {
  quantityRemaining?: number;
  lineItemCategory?: string;
  name: string;
  quantitySelected: number;
  lineItemType: LineItemType;
  unitPriceInCents: number;
  lineItemId?: string;
  shouldShowFee?: boolean;
  hudlFeeInCents?: number;
}

export interface ReviewableItem {
  id: string;
  name: string;
  totalPriceInCents: number;
  quantity?: number;
  shouldShowFee: boolean;
  hudlFeeInCents: number;
  subtotalInCents: number;
}

export interface ReaderMessage {
  message: ReactElement;
  icon: ReactElement;
  background?: boolean;
}

export type DeveloperOptionKeys = 'simulateReader' | 'simulatedCardNumber' | 'simulateReaderUpdate';

export type DeveloperOptionData = {
  enabled: boolean;
  value?: string;
};

export interface EventAction {
  label: string;
  icon: ReactElement;
  onPress: () => void;
  qaId: string;
  isDisabled?: boolean;
}

export interface CalculatorKey {
  label: string;
  value: string;
}

export interface TooltipElement {
  tooltipType: TooltipType;
  tooltipText: string;
  tooltipHeader: string;
}

export interface BarCodeReadEvent {
  value: string;
}
