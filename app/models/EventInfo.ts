// CO: This only exists because we cant just pass the TicketedEvent model through the navigation stack as some of

import { TicketTypePricingSummary } from '../gql/public/__generated__/graphql';

// its properties are not serializable :/
export interface EventInfo {
  id: string;
  name: string;
  description: string;
  date: string;
  timezoneIdentifier: string;
  organizationId: string;
  participatingTeamIds: string[];
  venueConfigurationId?: string;
  associatedPassConfigIds: string[];
  ticketTypePricingSummaries: TicketTypePricingSummary[];
  sport?: string;
}
