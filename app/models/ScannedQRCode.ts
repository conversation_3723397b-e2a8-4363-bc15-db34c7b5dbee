export type ScannedQRCode = Ticket | Pass;

export interface Ticket {
  TicketId: string;
  TicketedEventId: string;
  TicketedEventName: string;
  QRCodeVersion: string;
  Checksum: string;
}

export interface Pass {
  PassId: string;
  OrganizationId: string;
  Teams?: string[];
  StartDate?: string;
  EndDate?: string;
  QRCodeVersion: string;
  Checksum: string;
  PassConfigurationId?: string;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isTicket(o: any): o is Ticket {
  return (
    'TicketId' in o &&
    'TicketedEventId' in o &&
    'QRCodeVersion' in o &&
    'Checksum' in o &&
    'TicketedEventName' in o
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isPass(o: any): o is Pass {
  return 'PassId' in o && 'OrganizationId' in o && 'QRCodeVersion' in o && 'Checksum' in o;
}
