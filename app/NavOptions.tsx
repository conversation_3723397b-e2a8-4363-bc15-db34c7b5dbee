import React, { useCallback, useMemo } from 'react';
import { Platform } from 'react-native';

import { BottomTabNavigationOptions, BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationOptions } from '@react-navigation/native-stack';

import { UniformSpace, useColors, useUniformTheme } from '@hudl/rn-uniform';

import { BottomTabsParamList } from './Nav';
import IconCheckIn from './icons/IconCheckIn';
import IconHome from './icons/IconHome';
import IconSell from './icons/IconSell';
import { Navigation } from './utils/Enums';

export function useDefaultStackScreenOptions(): NativeStackNavigationOptions {
  const { colors } = useUniformTheme();

  return useMemo(
    () => ({
      contentStyle: {
        backgroundColor: colors.bgLevel1,
      },
      headerStyle: {
        backgroundColor: colors.bgLevel1,
      },
    }),
    [colors]
  );
}

type IconProps = Parameters<typeof IconHome>[0];

const TAB_ICONS: { [key in keyof BottomTabsParamList]: React.ComponentType<IconProps> } = {
  [Navigation.HomeTab]: IconHome,
  [Navigation.SellTab]: IconSell,
  [Navigation.CheckInTab]: IconCheckIn,
};

type TabScreenOptions = (props: {
  route: RouteProp<BottomTabsParamList, keyof BottomTabsParamList>;
  navigation: BottomTabNavigationProp<BottomTabsParamList>;
}) => BottomTabNavigationOptions;

export function useBottomTabsScreenOptions(): TabScreenOptions {
  const colors = useColors();

  return useCallback(
    ({ route }) => {
      return {
        tabBarStyle: {
          backgroundColor: colors.bgLevel0,
          borderTopWidth: 0,
          paddingHorizontal: UniformSpace.one,
          paddingBottom: Platform.OS === 'android' ? UniformSpace.one : UniformSpace.two,
          paddingTop: UniformSpace.threeQuarter,
          height: Platform.OS === 'android' ? 84 : 96,
        },
        headerShadowVisible: false,
        tabBarHideOnKeyboard: Platform.OS === 'android',
        headerShown: false,
        tabBarActiveTintColor: colors.contentContrast,
        tabBarInactiveTintColor: colors.contentSubtle,
        tabBarLabelStyle: {
          fontWeight: '700',
          fontSize: 14,
        },
        tabBarIcon: ({ focused }: { focused: boolean }) => {
          const Icon = TAB_ICONS[route.name];
          return <Icon color={focused ? 'contrast' : 'subtle'} />;
        },
        lazy: false,
      };
    },
    [colors]
  );
}
