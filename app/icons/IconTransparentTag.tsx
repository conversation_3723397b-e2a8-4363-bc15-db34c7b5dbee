import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconTransparentTag({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 21 21" size={size} color={color} style={style} {...props}>
      <Path
        d="M14.3911 4.49264C14.9769 3.90685 15.9266 3.90685 16.5124 4.49264C17.0982 5.07843 17.0982 6.02817 16.5124 6.61396C15.9266 7.19975 14.9769 7.19975 14.3911 6.61396C13.8053 6.02817 13.8053 5.07843 14.3911 4.49264Z"
        fill="#FEFEFE"
      />
      <Path
        d="M12.0502 0.46959C12.1908 0.328987 12.3816 0.25 12.5805 0.25H16.2016C16.4005 0.25 16.5913 0.328988 16.732 0.46959L20.5354 4.27305C20.676 4.4137 20.755 4.60451 20.755 4.80338V8.42454C20.755 8.62342 20.676 8.81423 20.5354 8.95487L9.35293 20.1374C9.20648 20.2838 8.96904 20.2838 8.8226 20.1374L0.867647 12.1824C0.721201 12.036 0.721201 11.7985 0.867647 11.6521L12.0502 0.46959ZM9.08776 18.2812L19.2552 8.1138V5.11412L15.8909 1.74984H12.8912L2.7238 11.9173L9.08776 18.2812Z"
        fill="#FEFEFE"
      />
    </Icon>
  );
}

export default IconTransparentTag;
