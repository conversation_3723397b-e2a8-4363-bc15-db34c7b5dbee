import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconCamera({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 24 24" size={size} color={color} style={style} {...props}>
      <Path
        d="M21.75 19.5H2.25C2.05109 19.5 1.86032 19.421 1.71967 19.2803C1.57902 19.1397 1.5 18.9489 1.5 18.75V6C1.5 5.80109 1.57902 5.61032 1.71967 5.46967C1.86032 5.32902 2.05109 5.25 2.25 5.25H7.095L8.3775 3.3375C8.44544 3.23433 8.53781 3.14954 8.64641 3.09066C8.75501 3.03178 8.87647 3.00064 9 3H15C15.1235 3.00064 15.245 3.03178 15.3536 3.09066C15.4622 3.14954 15.5546 3.23433 15.6225 3.3375L16.905 5.25H21.75C21.9489 5.25 22.1397 5.32902 22.2803 5.46967C22.421 5.61032 22.5 5.80109 22.5 6V18.75C22.5 18.9489 22.421 19.1397 22.2803 19.2803C22.1397 19.421 21.9489 19.5 21.75 19.5ZM3 18H21V6.75H16.5C16.3765 6.74936 16.255 6.71822 16.1464 6.65934C16.0378 6.60046 15.9454 6.51567 15.8775 6.4125L14.595 4.5H9.405L8.1225 6.4125C8.05456 6.51567 7.96219 6.60046 7.85359 6.65934C7.74499 6.71822 7.62353 6.74936 7.5 6.75H3V18Z"
        fill="#FEFEFE"
      />
      <Path
        d="M12 16.5C11.11 16.5 10.24 16.2361 9.49994 15.7416C8.75991 15.2471 8.18314 14.5443 7.84254 13.7221C7.50195 12.8998 7.41283 11.995 7.58647 11.1221C7.7601 10.2492 8.18869 9.44736 8.81802 8.81802C9.44736 8.18869 10.2492 7.7601 11.1221 7.58647C11.995 7.41283 12.8998 7.50195 13.7221 7.84254C14.5443 8.18314 15.2471 8.75991 15.7416 9.49994C16.2361 10.24 16.5 11.11 16.5 12C16.5 13.1935 16.0259 14.3381 15.182 15.182C14.3381 16.0259 13.1935 16.5 12 16.5ZM12 9C11.4067 9 10.8266 9.17595 10.3333 9.50559C9.83994 9.83524 9.45543 10.3038 9.22836 10.852C9.0013 11.4001 8.94189 12.0033 9.05765 12.5853C9.1734 13.1672 9.45912 13.7018 9.87868 14.1213C10.2982 14.5409 10.8328 14.8266 11.4147 14.9424C11.9967 15.0581 12.5999 14.9987 13.1481 14.7716C13.6962 14.5446 14.1648 14.1601 14.4944 13.6667C14.8241 13.1734 15 12.5933 15 12C15 11.2044 14.6839 10.4413 14.1213 9.87868C13.5587 9.31607 12.7957 9 12 9Z"
        fill="#FEFEFE"
      />
    </Icon>
  );
}

export default IconCamera;
