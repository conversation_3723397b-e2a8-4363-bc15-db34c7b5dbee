import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconReader({ size = 'medium', color, title, style, ...props }: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 8 12"
      size={size}
      color={color}
      title={title || 'IconReader'}
      style={style}
      {...props}>
      <Path
        fill="#FEFEFE"
        d="M3 1.5C2.72385 1.5 2.5 1.72388 2.5 2C2.5 2.27612 2.72385 2.5 3 2.5H5C5.27615 2.5 5.5 2.27612 5.5 2C5.5 1.72388 5.27615 1.5 5 1.5H3Z"
      />
      <Path
        fill="#FEFEFE"
        d="M0 2C0 0.895447 0.895416 0 2 0H6C7.10458 0 8 0.895447 8 2V10C8 11.1046 7.10458 12 6 12H2C0.895416 12 0 11.1046 0 10V2ZM2 1H6C6.55228 1 7 1.44769 7 2V10C7 10.5523 6.55228 11 6 11H2C1.44772 11 1 10.5523 1 10V2C1 1.44769 1.44772 1 2 1Z"
      />
    </Icon>
  );
}

export default IconReader;
