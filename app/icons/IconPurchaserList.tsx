import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconPurchaserList({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 33 32"
      size={size}
      color={color}
      title={title || 'IconPurchaserList'}
      style={style}
      {...props}>
      <Path
        fill="#E8EAED"
        d="M11.4167 11.0002C9.95 11.0002 8.69444 10.4779 7.65 9.4335C6.60556 8.38905 6.08333 7.1335 6.08333 5.66683C6.08333 4.20016 6.60556 2.94461 7.65 1.90016C8.69444 0.855718 9.95 0.333496 11.4167 0.333496C12.8833 0.333496 14.1389 0.855718 15.1833 1.90016C16.2278 2.94461 16.75 4.20016 16.75 5.66683C16.75 7.1335 16.2278 8.38905 15.1833 9.4335C14.1389 10.4779 12.8833 11.0002 11.4167 11.0002ZM11.4167 8.3335C12.15 8.3335 12.7778 8.07239 13.3 7.55016C13.8222 7.02794 14.0833 6.40016 14.0833 5.66683C14.0833 4.9335 13.8222 4.30572 13.3 3.7835C12.7778 3.26127 12.15 3.00016 11.4167 3.00016C10.6833 3.00016 10.0556 3.26127 9.53333 3.7835C9.01111 4.30572 8.75 4.9335 8.75 5.66683C8.75 6.40016 9.01111 7.02794 9.53333 7.55016C10.0556 8.07239 10.6833 8.3335 11.4167 8.3335ZM26.2167 26.3335L21.95 22.0668C21.4833 22.3335 20.9833 22.5557 20.45 22.7335C19.9167 22.9113 19.35 23.0002 18.75 23.0002C17.0833 23.0002 15.6667 22.4168 14.5 21.2502C13.3333 20.0835 12.75 18.6668 12.75 17.0002C12.75 15.3335 13.3333 13.9168 14.5 12.7502C15.6667 11.5835 17.0833 11.0002 18.75 11.0002C20.4167 11.0002 21.8333 11.5835 23 12.7502C24.1667 13.9168 24.75 15.3335 24.75 17.0002C24.75 17.6002 24.6611 18.1668 24.4833 18.7002C24.3056 19.2335 24.0833 19.7335 23.8167 20.2002L28.0833 24.4668L26.2167 26.3335ZM18.75 20.3335C19.6833 20.3335 20.4722 20.0113 21.1167 19.3668C21.7611 18.7224 22.0833 17.9335 22.0833 17.0002C22.0833 16.0668 21.7611 15.2779 21.1167 14.6335C20.4722 13.9891 19.6833 13.6668 18.75 13.6668C17.8167 13.6668 17.0278 13.9891 16.3833 14.6335C15.7389 15.2779 15.4167 16.0668 15.4167 17.0002C15.4167 17.9335 15.7389 18.7224 16.3833 19.3668C17.0278 20.0113 17.8167 20.3335 18.75 20.3335ZM0.75 21.6668V17.9668C0.75 17.2113 0.938889 16.5113 1.31667 15.8668C1.69444 15.2224 2.21667 14.7335 2.88333 14.4002C4.01667 13.8224 5.29444 13.3335 6.71667 12.9335C8.13889 12.5335 9.71667 12.3335 11.45 12.3335C11.1833 12.7335 10.9556 13.1613 10.7667 13.6168C10.5778 14.0724 10.4278 14.5446 10.3167 15.0335C8.98333 15.1446 7.79445 15.3724 6.75 15.7168C5.70556 16.0613 4.82778 16.4224 4.11667 16.8002C3.89444 16.9113 3.72222 17.0724 3.6 17.2835C3.47778 17.4946 3.41667 17.7224 3.41667 17.9668V19.0002H10.3167C10.4278 19.4891 10.5778 19.9557 10.7667 20.4002C10.9556 20.8446 11.1833 21.2668 11.45 21.6668H0.75Z"
      />
    </Icon>
  );
}

export default IconPurchaserList;
