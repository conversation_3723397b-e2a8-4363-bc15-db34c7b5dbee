import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconHome({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 24 20" size={size} color={color} style={style} {...props}>
      <Path d="M12.4583 0.660354C12.3251 0.556469 12.1611 0.500046 11.9923 0.500046C11.8234 0.500046 11.6594 0.556469 11.5262 0.660354L1.04676 8.8324C0.882662 8.96036 0.854138 9.19746 0.983201 9.3607L1.45006 9.95116C1.5779 10.1128 1.81226 10.141 1.97481 10.0143L2.99905 9.21568V19.625C2.99905 19.8321 3.16694 20 3.37405 20C9.12397 19.9999 14.8739 19.9963 20.6238 19.9998C20.831 19.9999 20.999 19.832 20.999 19.6248V9.2225L22.0233 10.021C22.1858 10.1477 22.4202 10.1195 22.548 9.95784L23.0149 9.36736C23.1439 9.2041 23.1154 8.96696 22.9512 8.83902L12.4583 0.660354ZM13.499 18.5H10.499V12.5H13.499V18.5ZM14.999 18.5V11.375C14.999 11.1679 14.8312 11 14.624 11H9.37405C9.16694 11 8.99905 11.1679 8.99905 11.375V18.5H4.49905V8.04613L11.999 2.20363L19.499 8.054V18.5H14.999Z" />
    </Icon>
  );
}

export default IconHome;
