import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconBluetoothDisabled({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 128 128"
      size={size}
      color={color}
      title={title || 'IconBluetoothDisabled'}
      style={style}
      {...props}>
      <Path
        fill="#E6F2FF"
        d="M105.6 120.533L83.1999 98.1332L63.9999 117.333H58.6666V76.7998L34.1332 101.333L26.6666 93.8665L52.7999 67.7332L7.46655 22.3998L14.9332 14.9332L113.067 113.067L105.6 120.533ZM69.3332 96.7998L75.4666 90.6665L69.3332 84.5332V96.7998ZM75.1999 60.2665L67.7332 52.7998L79.4666 41.0665L69.3332 31.1998V54.3998L58.6666 43.7332V10.6665H63.9999L94.3999 41.0665L75.1999 60.2665Z"
      />
    </Icon>
  );
}

export default IconBluetoothDisabled;
