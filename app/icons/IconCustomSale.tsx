import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconCustomSale({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 28 24"
      size={size}
      color={color}
      title={title || 'IconCustomSale'}
      style={style}
      {...props}>
      <Path
        d="M26.2834 10.7333V21.3333C26.2834 22.0667 26.0223 22.6944 25.5 23.2167C24.9778 23.7389 24.35 24 23.6167 24H4.95004C4.2167 24 3.58893 23.7389 3.0667 23.2167C2.54448 22.6944 2.28337 22.0667 2.28337 21.3333V10.7333C1.77226 10.2667 1.37781 9.66667 1.10004 8.93333C0.822258 8.2 0.816703 7.4 1.08337 6.53333L2.48337 2C2.66115 1.42222 2.97781 0.944444 3.43337 0.566667C3.88893 0.188889 4.4167 0 5.0167 0H23.55C24.15 0 24.6723 0.183333 25.1167 0.55C25.5611 0.916667 25.8834 1.4 26.0834 2L27.4834 6.53333C27.75 7.4 27.7445 8.18889 27.4667 8.9C27.1889 9.61111 26.7945 10.2222 26.2834 10.7333ZM17.2167 9.33333C17.8167 9.33333 18.2723 9.12778 18.5834 8.71667C18.8945 8.30556 19.0167 7.84444 18.95 7.33333L18.2167 2.66667H15.6167V7.6C15.6167 8.06667 15.7723 8.47222 16.0834 8.81667C16.3945 9.16111 16.7723 9.33333 17.2167 9.33333ZM11.2167 9.33333C11.7278 9.33333 12.1445 9.16111 12.4667 8.81667C12.7889 8.47222 12.95 8.06667 12.95 7.6V2.66667H10.35L9.6167 7.33333C9.52781 7.86667 9.64448 8.33333 9.9667 8.73333C10.2889 9.13333 10.7056 9.33333 11.2167 9.33333ZM5.28337 9.33333C5.68337 9.33333 6.03337 9.18889 6.33337 8.9C6.63337 8.61111 6.8167 8.24444 6.88337 7.8L7.6167 2.66667H5.0167L3.68337 7.13333C3.55004 7.57778 3.62226 8.05556 3.90004 8.56667C4.17781 9.07778 4.63893 9.33333 5.28337 9.33333ZM23.2834 9.33333C23.9278 9.33333 24.3945 9.07778 24.6834 8.56667C24.9723 8.05556 25.0389 7.57778 24.8834 7.13333L23.4834 2.66667H20.95L21.6834 7.8C21.75 8.24444 21.9334 8.61111 22.2334 8.9C22.5334 9.18889 22.8834 9.33333 23.2834 9.33333ZM4.95004 21.3333H23.6167V11.9333C23.5056 11.9778 23.4334 12 23.4 12H23.2834C22.6834 12 22.1556 11.9 21.7 11.7C21.2445 11.5 20.7945 11.1778 20.35 10.7333C19.95 11.1333 19.4945 11.4444 18.9834 11.6667C18.4723 11.8889 17.9278 12 17.35 12C16.75 12 16.1889 11.8889 15.6667 11.6667C15.1445 11.4444 14.6834 11.1333 14.2834 10.7333C13.9056 11.1333 13.4667 11.4444 12.9667 11.6667C12.4667 11.8889 11.9278 12 11.35 12C10.7056 12 10.1223 11.8889 9.60004 11.6667C9.07782 11.4444 8.6167 11.1333 8.2167 10.7333C7.75004 11.2 7.28893 11.5278 6.83337 11.7167C6.37781 11.9056 5.86115 12 5.28337 12H5.13337C5.07781 12 5.0167 11.9778 4.95004 11.9333V21.3333Z"
        fill="#E8EAED"
      />
    </Icon>
  );
}

export default IconCustomSale;
