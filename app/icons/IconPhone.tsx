import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconPhone({ size = 'medium', color, title, style, ...props }: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 24 24"
      size={size}
      color={color}
      title={title || 'IconPhone'}
      style={style}
      {...props}>
      <Path
        d="M22.6 24C19.8222 24 17.0778 23.3944 14.3667 22.1833C11.6556 20.9722 9.18889 19.2556 6.96667 17.0333C4.74444 14.8111 3.02778 12.3444 1.81667 9.63333C0.605556 6.92222 0 4.17778 0 1.4C0 1 0.133333 0.666667 0.4 0.4C0.666667 0.133333 1 0 1.4 0H6.8C7.11111 0 7.38889 0.105556 7.63333 0.316667C7.87778 0.527778 8.02222 0.777778 8.06667 1.06667L8.93333 5.73333C8.97778 6.08889 8.96667 6.38889 8.9 6.63333C8.83333 6.87778 8.71111 7.08889 8.53333 7.26667L5.3 10.5333C5.74444 11.3556 6.27222 12.15 6.88333 12.9167C7.49444 13.6833 8.16667 14.4222 8.9 15.1333C9.58889 15.8222 10.3111 16.4611 11.0667 17.05C11.8222 17.6389 12.6222 18.1778 13.4667 18.6667L16.6 15.5333C16.8 15.3333 17.0611 15.1833 17.3833 15.0833C17.7056 14.9833 18.0222 14.9556 18.3333 15L22.9333 15.9333C23.2444 16.0222 23.5 16.1833 23.7 16.4167C23.9 16.65 24 16.9111 24 17.2V22.6C24 23 23.8667 23.3333 23.6 23.6C23.3333 23.8667 23 24 22.6 24ZM4.03333 8L6.23333 5.8L5.66667 2.66667H2.7C2.81111 3.57778 2.96667 4.47778 3.16667 5.36667C3.36667 6.25556 3.65556 7.13333 4.03333 8ZM15.9667 19.9333C16.8333 20.3111 17.7167 20.6111 18.6167 20.8333C19.5167 21.0556 20.4222 21.2 21.3333 21.2667V18.3333L18.2 17.7L15.9667 19.9333Z"
        fill="#E8EAED"
      />
    </Icon>
  );
}

export default IconPhone;
