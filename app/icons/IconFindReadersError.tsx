import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconFindReadersError({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 108 102"
      size={size}
      color={color}
      title={title || 'IconFindReadersError'}
      style={style}
      {...props}>
      <Path
        fill="#C3CEDB"
        d="M27.3327 101.333C19.9549 101.333 13.666 98.7333 8.46602 93.5333C3.26602 88.3333 0.666016 82.0444 0.666016 74.6667C0.666016 67.2889 3.26602 61 8.46602 55.8C13.666 50.6 19.9549 48 27.3327 48C34.7105 48 40.9993 50.6 46.1993 55.8C51.3993 61 53.9993 67.2889 53.9993 74.6667C53.9993 82.0444 51.3993 88.3333 46.1993 93.5333C40.9993 98.7333 34.7105 101.333 27.3327 101.333ZM99.866 96L65.7327 61.8667C64.666 60.7111 63.5327 59.5333 62.3327 58.3333C61.1327 57.1333 59.9549 56 58.7993 54.9333C62.1771 52.8 64.8882 49.9556 66.9327 46.4C68.9771 42.8444 69.9993 38.9333 69.9993 34.6667C69.9993 28 67.666 22.3333 62.9993 17.6667C58.3327 13 52.666 10.6667 45.9993 10.6667C39.3327 10.6667 33.666 13 28.9993 17.6667C24.3327 22.3333 21.9993 28 21.9993 34.6667C21.9993 35.2 22.0216 35.7111 22.066 36.2C22.1105 36.6889 22.1771 37.2 22.266 37.7333C20.666 37.9111 18.9105 38.2667 16.9993 38.8C15.0882 39.3333 13.3771 39.9556 11.866 40.6667C11.6882 39.6889 11.5549 38.7111 11.466 37.7333C11.3771 36.7556 11.3327 35.7333 11.3327 34.6667C11.3327 24.9778 14.6882 16.7778 21.3993 10.0667C28.1105 3.35556 36.3105 0 45.9993 0C55.6882 0 63.8882 3.35556 70.5993 10.0667C77.3105 16.7778 80.666 24.9778 80.666 34.6667C80.666 38.4889 80.066 42.1111 78.866 45.5333C77.666 48.9556 75.9993 52.0889 73.866 54.9333L107.333 88.5333L99.866 96ZM17.866 87.8667L27.3327 78.4L36.666 87.8667L40.5327 84.1333L31.066 74.6667L40.5327 65.2L36.7993 61.4667L27.3327 70.9333L17.866 61.4667L14.1327 65.2L23.5993 74.6667L14.1327 84.1333L17.866 87.8667Z"
      />
    </Icon>
  );
}

export default IconFindReadersError;
