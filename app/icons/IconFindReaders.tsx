import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconFindReaders({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 192 96"
      size={size}
      color={color}
      title={title || 'IconFindReaders'}
      style={style}
      {...props}>
      <Path
        fill="#E6F2FF"
        d="M73.4 59.4L67.6 53.6C68.4 52.8667 69.0167 52.0167 69.45 51.05C69.8833 50.0833 70.1 49.0667 70.1 48C70.1 46.9333 69.8833 45.9167 69.45 44.95C69.0167 43.9833 68.4 43.1333 67.6 42.4L73.4 36.6C74.9333 38.1333 76.1 39.8833 76.9 41.85C77.7 43.8167 78.1 45.8667 78.1 48C78.1 50.1333 77.7 52.1833 76.9 54.15C76.1 56.1167 74.9333 57.8667 73.4 59.4ZM83.2 69.2L77.6 63.6C79.6667 61.5333 81.2667 59.1667 82.4 56.5C83.5333 53.8333 84.1 51 84.1 48C84.1 45 83.5333 42.1667 82.4 39.5C81.2667 36.8333 79.6667 34.4667 77.6 32.4L83.2 26.8C86.0667 29.6 88.2667 32.8333 89.8 36.5C91.3333 40.1667 92.1 44 92.1 48C92.1 52 91.3333 55.8333 89.8 59.5C88.2667 63.1667 86.0667 66.4 83.2 69.2ZM28 92C25.8 92 23.9167 91.2167 22.35 89.65C20.7833 88.0833 20 86.2 20 84V12C20 9.8 20.7833 7.91667 22.35 6.35C23.9167 4.78333 25.8 4 28 4H68C70.2 4 72.0833 4.78333 73.65 6.35C75.2167 7.91667 76 9.8 76 12V28H68V24H28V72H68V68H76V84C76 86.2 75.2167 88.0833 73.65 89.65C72.0833 91.2167 70.2 92 68 92H28ZM28 80V84H68V80H28ZM28 16H68V12H28V16Z"
      />
      <Path
        fill="#E6F2FF"
        d="M124 92C121.8 92 119.917 91.2167 118.35 89.65C116.783 88.0833 116 86.2 116 84V12C116 9.8 116.783 7.91667 118.35 6.35C119.917 4.78333 121.8 4 124 4H164C166.2 4 168.083 4.78333 169.65 6.35C171.217 7.91667 172 9.8 172 12V84C172 86.2 171.217 88.0833 169.65 89.65C168.083 91.2167 166.2 92 164 92H124ZM124 80V84H164V80H124ZM124 72H164V24H124V72ZM124 16H164V12H124V16Z"
      />
    </Icon>
  );
}

export default IconFindReaders;
