import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconCreditCard({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 96 96"
      size={size}
      color={color}
      title={title || 'IconCreditCard'}
      style={style}
      {...props}>
      <Path
        d="M88 24V72C88 74.2 87.2167 76.0833 85.65 77.65C84.0833 79.2167 82.2 80 80 80H16C13.8 80 11.9167 79.2167 10.35 77.65C8.78333 76.0833 8 74.2 8 72V24C8 21.8 8.78333 19.9167 10.35 18.35C11.9167 16.7833 13.8 16 16 16H80C82.2 16 84.0833 16.7833 85.65 18.35C87.2167 19.9167 88 21.8 88 24ZM16 32H80V24H16V32ZM16 48V72H80V48H16Z"
        fill="#E6F2FF"
      />
    </Icon>
  );
}

export default IconCreditCard;
