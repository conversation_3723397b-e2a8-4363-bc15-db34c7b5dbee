import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconWhiteBgCheck({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 40 40" size={size} color={color} style={style} {...props}>
      <Path
        d="M0 20C0 8.97143 8.97286 0 20 0C31.0286 0 40 8.97143 40 20C40 31.0286 31.0286 40 20 40C8.97286 40 0 31.0286 0 20ZM27.2354 11.9415L16.2183 25.0729L11.8354 21.3972C11.2411 20.8972 10.334 20.9715 9.82686 21.5744L8.90828 22.6715C8.39686 23.2815 8.47828 24.1744 9.08114 24.6801L15.654 30.1958C15.9511 30.4444 16.3269 30.5515 16.6926 30.5201L16.6954 30.5229C17.0597 30.4901 17.4111 30.3172 17.664 30.0158L30.5183 14.6958C31.0297 14.0887 30.9483 13.1901 30.344 12.6844L29.2483 11.7629C28.6383 11.2529 27.7426 11.3372 27.2354 11.9415Z"
        fill="#538306"
      />
      <Path
        d="M27.2363 11.9414L16.2191 25.0728L11.8363 21.3971C11.242 20.8971 10.3348 20.9714 9.82769 21.5743L8.90912 22.6714C8.39769 23.2814 8.47912 24.1743 9.08198 24.68L15.6548 30.1957C15.952 30.4443 16.3277 30.5514 16.6934 30.52L16.6963 30.5228C17.0605 30.49 17.412 30.3171 17.6648 30.0157L30.5191 14.6957C31.0306 14.0886 30.9491 13.19 30.3448 12.6843L29.2491 11.7628C28.6391 11.2528 27.7434 11.3371 27.2363 11.9414Z"
        fill="white"
      />
    </Icon>
  );
}

export default IconWhiteBgCheck;
