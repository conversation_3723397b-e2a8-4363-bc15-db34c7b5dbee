import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconCustomBattery({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 13 8"
      size={size}
      color={color}
      title={title || 'IconCustomBattery'}
      style={style}
      {...props}>
      <Path
        fill="#C0C6CD"
        d="M1.75 1.5C1.61192 1.5 1.5 1.61194 1.5 1.75V6.25C1.5 6.38806 1.61192 6.5 1.75 6.5H7.25C7.38806 6.5 7.5 6.38806 7.5 6.25V1.75C7.5 1.61194 7.38806 1.5 7.25 1.5H1.75Z"
      />
      <Path
        fill="#C0C6CD"
        d="M10.75 8C10.8881 8 11 7.88806 11 7.75V6.5H12.25C12.3881 6.5 12.5 6.38806 12.5 6.25V1.75C12.5 1.61194 12.3881 1.5 12.25 1.5H11V0.25C11 0.111938 10.8881 0 10.75 0H0.25C0.111923 0 0 0.111938 0 0.25V7.75C0 7.88806 0.111923 8 0.25 8H10.75ZM10.25 5.5C10.1119 5.5 10 5.61194 10 5.75V7H1V1H10V2.25C10 2.38806 10.1119 2.5 10.25 2.5H11.5V5.5H10.25Z"
      />
    </Icon>
  );
}

export default IconCustomBattery;
