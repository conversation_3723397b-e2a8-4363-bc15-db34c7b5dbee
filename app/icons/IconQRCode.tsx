import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconQRCode({ size = 'medium', color, title, style, ...props }: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 33 32"
      size={size}
      color={color}
      title={title || 'IconQRCode'}
      style={style}
      {...props}>
      <Path
        fill="#E8EAED"
        d="M0.416748 7.33341V0.666748H7.08341V3.33341H3.08341V7.33341H0.416748ZM0.416748 27.3334V20.6667H3.08341V24.6667H7.08341V27.3334H0.416748ZM20.4167 27.3334V24.6667H24.4167V20.6667H27.0834V27.3334H20.4167ZM24.4167 7.33341V3.33341H20.4167V0.666748H27.0834V7.33341H24.4167ZM21.0834 21.3334H23.0834V23.3334H21.0834V21.3334ZM21.0834 17.3334H23.0834V19.3334H21.0834V17.3334ZM19.0834 19.3334H21.0834V21.3334H19.0834V19.3334ZM17.0834 21.3334H19.0834V23.3334H17.0834V21.3334ZM15.0834 19.3334H17.0834V21.3334H15.0834V19.3334ZM19.0834 15.3334H21.0834V17.3334H19.0834V15.3334ZM17.0834 17.3334H19.0834V19.3334H17.0834V17.3334ZM15.0834 15.3334H17.0834V17.3334H15.0834V15.3334ZM23.0834 4.66675V12.6667H15.0834V4.66675H23.0834ZM12.4167 15.3334V23.3334H4.41675V15.3334H12.4167ZM12.4167 4.66675V12.6667H4.41675V4.66675H12.4167ZM10.4167 21.3334V17.3334H6.41675V21.3334H10.4167ZM10.4167 10.6667V6.66675H6.41675V10.6667H10.4167ZM21.0834 10.6667V6.66675H17.0834V10.6667H21.0834Z"
      />
    </Icon>
  );
}

export default IconQRCode;
