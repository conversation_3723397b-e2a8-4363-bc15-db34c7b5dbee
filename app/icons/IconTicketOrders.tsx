import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconTicketOrder({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 33 32"
      size={size}
      color={color}
      title={title || 'IconTicketOrder'}
      style={style}
      {...props}>
      <Path
        fill="#E8EAED"
        d="M12.2501 21.3333C11.8723 21.3333 11.5556 21.2055 11.3001 20.9499C11.0445 20.6944 10.9167 20.3777 10.9167 19.9999V17.3333C11.2945 17.3333 11.6112 17.2055 11.8667 16.9499C12.1223 16.6944 12.2501 16.3777 12.2501 15.9999C12.2501 15.6221 12.1223 15.3055 11.8667 15.0499C11.6112 14.7944 11.2945 14.6666 10.9167 14.6666V11.9999C10.9167 11.6221 11.0445 11.3055 11.3001 11.0499C11.5556 10.7944 11.8723 10.6666 12.2501 10.6666H20.2501C20.6279 10.6666 20.9445 10.7944 21.2001 11.0499C21.4556 11.3055 21.5834 11.6221 21.5834 11.9999V14.6666C21.2056 14.6666 20.889 14.7944 20.6334 15.0499C20.3779 15.3055 20.2501 15.6221 20.2501 15.9999C20.2501 16.3777 20.3779 16.6944 20.6334 16.9499C20.889 17.2055 21.2056 17.3333 21.5834 17.3333V19.9999C21.5834 20.3777 21.4556 20.6944 21.2001 20.9499C20.9445 21.2055 20.6279 21.3333 20.2501 21.3333H12.2501ZM16.2501 19.3333C16.4279 19.3333 16.5834 19.2666 16.7167 19.1333C16.8501 18.9999 16.9167 18.8444 16.9167 18.6666C16.9167 18.4888 16.8501 18.3333 16.7167 18.1999C16.5834 18.0666 16.4279 17.9999 16.2501 17.9999C16.0723 17.9999 15.9167 18.0666 15.7834 18.1999C15.6501 18.3333 15.5834 18.4888 15.5834 18.6666C15.5834 18.8444 15.6501 18.9999 15.7834 19.1333C15.9167 19.2666 16.0723 19.3333 16.2501 19.3333ZM16.2501 16.6666C16.4279 16.6666 16.5834 16.5999 16.7167 16.4666C16.8501 16.3333 16.9167 16.1777 16.9167 15.9999C16.9167 15.8221 16.8501 15.6666 16.7167 15.5333C16.5834 15.3999 16.4279 15.3333 16.2501 15.3333C16.0723 15.3333 15.9167 15.3999 15.7834 15.5333C15.6501 15.6666 15.5834 15.8221 15.5834 15.9999C15.5834 16.1777 15.6501 16.3333 15.7834 16.4666C15.9167 16.5999 16.0723 16.6666 16.2501 16.6666ZM16.2501 13.9999C16.4279 13.9999 16.5834 13.9333 16.7167 13.7999C16.8501 13.6666 16.9167 13.511 16.9167 13.3333C16.9167 13.1555 16.8501 12.9999 16.7167 12.8666C16.5834 12.7333 16.4279 12.6666 16.2501 12.6666C16.0723 12.6666 15.9167 12.7333 15.7834 12.8666C15.6501 12.9999 15.5834 13.1555 15.5834 13.3333C15.5834 13.511 15.6501 13.6666 15.7834 13.7999C15.9167 13.9333 16.0723 13.9999 16.2501 13.9999ZM9.58341 30.6666C8.85008 30.6666 8.2223 30.4055 7.70008 29.8833C7.17786 29.361 6.91675 28.7333 6.91675 27.9999V3.99992C6.91675 3.26659 7.17786 2.63881 7.70008 2.11659C8.2223 1.59436 8.85008 1.33325 9.58341 1.33325H22.9167C23.6501 1.33325 24.2779 1.59436 24.8001 2.11659C25.3223 2.63881 25.5834 3.26659 25.5834 3.99992V27.9999C25.5834 28.7333 25.3223 29.361 24.8001 29.8833C24.2779 30.4055 23.6501 30.6666 22.9167 30.6666H9.58341ZM9.58341 26.6666V27.9999H22.9167V26.6666H9.58341ZM9.58341 23.9999H22.9167V7.99992H9.58341V23.9999ZM9.58341 5.33325H22.9167V3.99992H9.58341V5.33325Z"
      />
    </Icon>
  );
}

export default IconTicketOrder;
