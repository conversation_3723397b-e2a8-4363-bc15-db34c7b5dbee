import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconCheckIn({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 24 24" size={size} color={color} style={style} {...props}>
      <Path d="M18 19.5V21H19.5V19.5H18Z" />
      <Path d="M13.5 16.5V18H15V16.5H13.5Z" />
      <Path d="M13.5 22.5H16.5V21H15V19.5H13.5V22.5Z" />
      <Path d="M19.5 16.5V19.5H21V16.5H19.5Z" />
      <Path d="M21 19.5H22.5V22.5H19.5V21H21V19.5Z" />
      <Path d="M19.5 15V13.5H22.5V16.5H21V15H19.5Z" />
      <Path d="M18 15H16.5V18H15V19.5H18V15Z" />
      <Path d="M13.5 13.5V15H16.5V13.5H13.5Z" />
      <Path d="M7.5 16.5H4.5V19.5H7.5V16.5Z" />
      <Path d="M10.5 22.5H1.5V13.5H10.5V22.5ZM3 21H9V15H3V21Z" />
      <Path d="M19.5 4.5H16.5V7.5H19.5V4.5Z" />
      <Path d="M22.5 10.5H13.5V1.5H22.5V10.5ZM15 9H21V3H15V9Z" />
      <Path d="M7.5 4.5H4.5V7.5H7.5V4.5Z" />
      <Path d="M10.5 10.5H1.5V1.5H10.5V10.5ZM3 9H9V3H3V9Z" />
    </Icon>
  );
}

export default IconCheckIn;
