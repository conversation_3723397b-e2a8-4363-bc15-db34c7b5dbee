import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconOrder({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 18 22" size={size} color={color} style={style} {...props}>
      <Path
        d="M5.625 9.5H4.875C4.66789 9.5 4.5 9.66789 4.5 9.875V16.625C4.5 16.8321 4.66789 17 4.875 17H5.625C5.83211 17 6 16.8321 6 16.625V9.875C6 9.66789 5.83211 9.5 5.625 9.5Z"
        fill="#FEFEFE"
      />
      <Path
        d="M12.375 12.5H13.125C13.3321 12.5 13.5 12.6679 13.5 12.875V16.625C13.5 16.8321 13.3321 17 13.125 17H12.375C12.1679 17 12 16.8321 12 16.625V12.875C12 12.6679 12.1679 12.5 12.375 12.5Z"
        fill="#FEFEFE"
      />
      <Path
        d="M8.625 14H9.375C9.58211 14 9.75 14.1679 9.75 14.375V16.625C9.75 16.8321 9.58211 17 9.375 17H8.625C8.41789 17 8.25 16.8321 8.25 16.625V14.375C8.25 14.1679 8.41789 14 8.625 14Z"
        fill="#FEFEFE"
      />
      <Path
        d="M6.75 0.5C5.50736 0.5 4.5 1.50736 4.5 2.75H1.125C0.917893 2.75 0.75 2.91789 0.75 3.125V21.125C0.75 21.3321 0.917893 21.5 1.125 21.5H16.875C17.0821 21.5 17.25 21.3321 17.25 21.125V3.125C17.25 2.91789 17.0821 2.75 16.875 2.75H13.5C13.5 1.50736 12.4926 0.5 11.25 0.5H6.75ZM11.25 2H6.75C6.33579 2 6 2.33579 6 2.75V5H12V2.75C12 2.33579 11.6642 2 11.25 2ZM2.25 4.25H4.5V6.125C4.5 6.33211 4.66789 6.5 4.875 6.5H13.125C13.3321 6.5 13.5 6.33211 13.5 6.125V4.25H15.75V20H2.25V4.25Z"
        fill="#FEFEFE"
      />
    </Icon>
  );
}

export default IconOrder;
