import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconSell({ size = 'medium', color, style, ...props }: IconProps): ReactElement {
  return (
    <Icon viewBox="0 0 12 20" size={size} color={color} style={style} {...props}>
      <Path d="M11.2515 13.3863C11.2515 9.92492 8.41654 9.5305 6.13872 9.21408C3.65622 8.8684 2.25154 8.56907 2.25154 6.43157C2.25154 4.63683 4.13187 4 5.74189 4C6.54436 3.97404 7.34151 4.13922 8.06759 4.4819C8.79368 4.82458 9.4279 5.33495 9.91797 5.97093L11.0851 5.02907C10.5603 4.35423 9.90723 3.78977 9.16353 3.36812C8.41983 2.94648 7.60013 2.67597 6.75154 2.57215V0.25H5.25154V2.5165C2.54014 2.6809 0.751544 4.2115 0.751544 6.4315C0.751544 9.979 3.62404 10.3785 5.93217 10.699C8.37154 11.0386 9.75154 11.3308 9.75154 13.3863C9.75154 15.6606 7.40209 16 6.00154 16C3.42949 16 2.34289 15.2771 1.33512 14.0291L0.167969 14.9709C0.762563 15.7544 1.53159 16.3885 2.41402 16.823C3.29645 17.2574 4.268 17.4802 5.25154 17.4736V19.75H6.75154V17.4663C9.54574 17.2382 11.2515 15.7209 11.2515 13.3863Z" />
    </Icon>
  );
}

export default IconSell;
