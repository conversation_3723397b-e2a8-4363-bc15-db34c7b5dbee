import React, { ReactElement } from 'react';

import { Path } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';
import { IconProps } from '@hudl/rn-uniform/dist/components/icons/Icon.component';

function IconRemoveCard({
  size = 'medium',
  color,
  title,
  style,
  ...props
}: IconProps): ReactElement {
  return (
    <Icon
      viewBox="0 0 136 96"
      size={size}
      color={color}
      title={title || 'IconRemoveCard'}
      style={style}
      {...props}>
      <Path
        d="M78.15 77.65C79.7167 76.0833 80.5 74.2 80.5 72V24C80.5 21.8 79.7167 19.9167 78.15 18.35C76.5833 16.7833 74.7 16 72.5 16H8.5C6.3 16 4.41667 16.7833 2.85 18.35C1.28333 19.9167 0.5 21.8 0.5 24V72C0.5 74.2 1.28333 76.0833 2.85 77.65C4.41667 79.2167 6.3 80 8.5 80H72.5C74.7 80 76.5833 79.2167 78.15 77.65ZM72.5 32H8.5V24H72.5V32ZM8.5 72V48H72.5V72H8.5ZM112.379 69.1213C111.207 67.9497 111.207 66.0503 112.379 64.8787L125.257 52H93.5C91.8431 52 90.5 50.6569 90.5 49C90.5 47.3431 91.8431 46 93.5 46H125.257L112.379 33.1213C111.207 31.9497 111.207 30.0502 112.379 28.8787C113.55 27.7071 115.45 27.7071 116.621 28.8787L134.621 46.8787C135.793 48.0503 135.793 49.9497 134.621 51.1213L116.621 69.1213C115.45 70.2929 113.55 70.2929 112.379 69.1213Z"
        fill="#E6F2FF"
      />
    </Icon>
  );
}

export default IconRemoveCard;
