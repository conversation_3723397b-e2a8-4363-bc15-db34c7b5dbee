/*
Jest setup for applications that consume Jarvis
*/

export {};

// #region Third party module mocks that Jarvis APIs and/or Components use

jest.mock('@react-native-async-storage/async-storage', () => {
  return require('@hudl/jarvis/__mocks__/@react-native-async-storage/async-storage');
});

jest.mock('@react-native-community/netinfo', () => {
  return require('@hudl/jarvis/__mocks__/@react-native-community/netinfo');
});

jest.mock('react-native-device-info', () => {
  return require('@hudl/jarvis/__mocks__/react-native-device-info');
});

jest.mock('react-native-localize', () => {
  return { ...require('@hudl/jarvis/__mocks__/react-native-localize') };
});

jest.mock('react-native-snackbar', () => {
  return require('@hudl/jarvis/__mocks__/react-native-snackbar');
});

// #endregion
