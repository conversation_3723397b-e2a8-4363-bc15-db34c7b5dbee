import { BottomTabScreenProps, createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import {
  NavigationProp,
  NavigatorScreenParams,
  ParamListBase,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import { HeaderLeftIcon } from './components/NavigationHeaders/DefaultHeader';
import { LineItemType } from './enums/shared';
import { School } from './gql/public/__generated__/graphql';
import { EventInfo } from './models/EventInfo';
import { Volunteer } from './models/Volunteer';
import { Navigation } from './utils/Enums';

export interface DefaultHeaderParams {
  hideLeftIcon?: boolean;
  leftIcon?: HeaderLeftIcon;
  navigateToTop?: boolean;
}

export interface EventHeaderParams {
  title: string;
  startTime: string;
}

export interface DeviceSetupParamParams {
  volunteerInfo: Volunteer;
  organization: School;
}

export type RootStackParamList = {
  home: undefined;
  root: {
    screen: keyof BottomTabsParamList;
    params?: BottomTabsParamList[keyof BottomTabsParamList];
  };
  landing: undefined;
  volunteerInfo: { currentUser?: Volunteer } & DefaultHeaderParams;

  loadingOrg: { volunteerInfo: Volunteer } & DefaultHeaderParams;

  organizationEvents: DefaultHeaderParams;
  eventConfirmation: {
    volunteerInfo: Volunteer;
    organization: School;
    event: EventInfo;
  } & DefaultHeaderParams;

  discoverReaders: undefined;

  posOnboarding: {
    volunteerInfo: Volunteer;
    organization: School;
    event: EventInfo;
  } & DefaultHeaderParams;

  itemSelection: undefined;

  reviewOrder: {
    isCustomSale?: boolean;
    isCashSale?: boolean;
  };

  receiptSelection: {
    totalPrice: number;
    isCashSale?: boolean;
  };

  acceptPayment: {
    totalPrice: number;
    purchaserEmail: string;
  };
  acceptCashPayment: {
    totalPrice: number;
    purchaserEmail: string;
  };

  orderComplete: {
    lineItemType: LineItemType;
    totalAmount: number;
    usedCard: boolean;
    ticketCount?: number;
    ticketGroupReference?: string;
  };

  uploadTickets: undefined;
  customSalesSelection: undefined;
  customSales: undefined;
};

export type HomeStackParamList = {
  home: {
    volunteerInfo: Volunteer;
    organization: School;
    event: EventInfo;
  } & DefaultHeaderParams;
};

export type SellStackParamList = {
  sell: DefaultHeaderParams;
  itemSelection: undefined;
  reviewOrder: {
    isCustomSale?: boolean;
    isCashSale?: boolean;
  };
  receiptSelection: {
    totalPrice: number;
    isCashSale?: boolean;
  };
  customSalesSelection: undefined;
  customSales: undefined;
  acceptPayment: {
    totalPrice: number;
    purchaserEmail: string;
  };
  acceptCashPayment: {
    totalPrice: number;
    purchaserEmail: string;
  };
  orderComplete: {
    lineItemType: LineItemType;
    totalAmount: number;
    usedCard: boolean;
    ticketCount?: number;
    ticketGroupReference?: string;
  };
};

export type CheckInStackParamList = {
  checkIn: { showNeedHelp?: boolean; timeStamp?: number } & DefaultHeaderParams;
  purchaserList: DefaultHeaderParams;
  scanner: DefaultHeaderParams;
};

export type BottomTabsParamList = {
  [Navigation.HomeTab]: NavigatorScreenParams<HomeStackParamList>;
  [Navigation.SellTab]: NavigatorScreenParams<SellStackParamList>;
  [Navigation.CheckInTab]: NavigatorScreenParams<CheckInStackParamList>;
};

export type BottomTabsScreenProps<T extends keyof BottomTabsParamList> = BottomTabScreenProps<
  BottomTabsParamList,
  T
>;

// Best to just infer the return type here on account of all the generics
// eslint-disable-next-line @typescript-eslint/explicit-function-return-type
function navAPI<T extends ParamListBase>() {
  return {
    createStackNavigator: createNativeStackNavigator<T>,
    createBottomTabNavigator: createBottomTabNavigator<T>,
    useNavigation: useNavigation<NavigationProp<T>>,
    useRoute: <R extends keyof T>(_r?: R) => useRoute<RouteProp<T, R>>(),
    testID: <K extends keyof T>(k: K): K => k,
  };
}

export const AppNav = {
  root: navAPI<RootStackParamList>(),
  home: navAPI<HomeStackParamList>(),
  sell: navAPI<SellStackParamList>(),
  checkIn: navAPI<CheckInStackParamList>(),
  bottomTabs: navAPI<BottomTabsParamList>(),
};
