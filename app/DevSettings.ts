export interface DevSettingsConfig {
  useLocalApolloServer: boolean;
  localApolloServerURL: string;
  gqlSchemaBaseURL: string;
}

// Fail-safe just incase a particular dev setting is misused in production code
const prodSettings: DevSettingsConfig = {
  useLocalApolloServer: false,
  localApolloServerURL: '',
  gqlSchemaBaseURL: '',
};

// eslint-disable-next-line @typescript-eslint/no-var-requires
export default __DEV__ ? (require('../.dev.json') as DevSettingsConfig) : prodSettings;
