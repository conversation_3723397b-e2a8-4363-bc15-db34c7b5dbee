import { faker } from '@faker-js/faker';

import { ScannedQRCodeResult } from '../../components/screens/scanner/ScannerUtils';
import { ScannedQRCodeResultStatus, TicketingEntityType } from '../../utils/Enums';
import { StoredResultGroup } from '../ResultGroupStorage';

export interface MockStoredResultGroup {
  ticketedEventId?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  accessCode?: string;
  appVersion?: string;
  results?: ScannedQRCodeResult[];
  uploadedResults?: ScannedQRCodeResult[];
  lastScanTime?: string;
}

export function mockStoredResultGroup(mock?: MockStoredResultGroup): StoredResultGroup {
  return {
    ticketedEventId: mock?.ticketedEventId ?? faker.datatype.uuid(),
    firstName: mock?.firstName ?? faker.name.firstName(),
    lastName: mock?.lastName ?? faker.name.lastName(),
    email: mock?.email ?? faker.internet.email(),
    accessCode: mock?.accessCode ?? faker.word.interjection(),
    appVersion:
      mock?.appVersion ??
      `${faker.random.numeric()}.${faker.random.numeric()}.${faker.random.numeric()}`,
    results: mock?.results ?? [],
    uploadedResults: mock?.uploadedResults ?? [],
    lastScanTime: mock?.lastScanTime ?? faker.date.past().toISOString(),
  };
}

export interface MockScannedQRCodeResult {
  id?: string;
  qrCodeVersion?: string;
  scannedTime?: string;
  status?: ScannedQRCodeResultStatus;
  type?: TicketingEntityType;
}

export function mockScannedQRCodeResult(mock?: MockScannedQRCodeResult): ScannedQRCodeResult {
  return {
    id: mock?.id ?? faker.datatype.uuid(),
    qrCodeVersion:
      mock?.qrCodeVersion ??
      `${faker.random.numeric()}.${faker.random.numeric()}.${faker.random.numeric()}`,
    scannedAt: mock?.scannedTime ?? faker.date.past().toISOString(),
    type:
      mock?.type ??
      faker.helpers.arrayElement(
        Object.values([TicketingEntityType.Ticket, TicketingEntityType.Pass])
      ),
    status:
      mock?.status ??
      faker.helpers.arrayElement(Object.values([ScannedQRCodeResultStatus.Redeemed])),
  };
}
