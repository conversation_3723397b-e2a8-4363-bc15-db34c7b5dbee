import AsyncStorage from '@react-native-async-storage/async-storage';
import { KeyValuePair } from '@react-native-async-storage/async-storage/lib/typescript/types';

import { ScannedQRCodeResult } from '../components/screens/scanner/ScannerUtils';
import { EventInfo } from '../models/EventInfo';
import { Volunteer } from '../models/Volunteer';
import { ParseResult, safeJsonParse } from '../utils/safe-parse';

const RESULT_GROUP_PREFIX = 'RESULT_GROUP';

export interface StoredResultGroup {
  ticketedEventId: string;
  firstName: string;
  lastName: string;
  email: string;
  accessCode: string;
  appVersion: string;
  lastScanTime: string;
  results: ScannedQRCodeResult[];
  uploadedResults: ScannedQRCodeResult[];
}

export async function getResultGroup(
  currentEvent: EventInfo,
  volunteerInfo: Volunteer,
  appVersion: string
): Promise<StoredResultGroup | undefined> {
  const key = generateResultGroupId(
    currentEvent.id,
    volunteerInfo.firstName,
    volunteerInfo.lastName,
    volunteerInfo.email,
    volunteerInfo.accessCode,
    appVersion
  );

  const savedGroupData = await AsyncStorage.getItem(key);
  const result = safeJsonParse(isStoredResultGroup, savedGroupData ?? '');

  return result.parsed;
}

export async function getResultGroupsFor(
  ticketedEventId: string
): Promise<{ groups: StoredResultGroup[]; errors: unknown[] }> {
  const allKeys = await AsyncStorage.getAllKeys();
  const groupingPrefix = generateResultGroupPrefix(ticketedEventId);
  const keysForEvents = allKeys.filter((k) => k.startsWith(groupingPrefix));

  const groupsKeyValuePair = await AsyncStorage.multiGet(keysForEvents);
  const parsedResults = groupsKeyValuePair.map(toStoredResultGroup);
  const resultsWithError = parsedResults.filter((r) => r.error);
  const results = parsedResults.filter((r) => r.parsed).map((r) => r.parsed) as StoredResultGroup[];

  return {
    groups: results,
    errors: resultsWithError,
  };
}

export async function getAllRemainingScanResultGroups(): Promise<{
  groups: StoredResultGroup[];
  numberOfResults: number;
  errors: unknown[];
}> {
  const allKeys = await AsyncStorage.getAllKeys();

  const keysForEvents = allKeys.filter((k) => k.startsWith(RESULT_GROUP_PREFIX));

  const groupsKeyValuePair = await AsyncStorage.multiGet(keysForEvents);
  const parsedResults = groupsKeyValuePair.map(toStoredResultGroup);
  const resultsWithError = parsedResults.filter((r) => r.error);
  const resultGroups = parsedResults
    .filter((r) => r.parsed)
    .map((r) => r.parsed) as StoredResultGroup[];

  return {
    groups: resultGroups,
    numberOfResults: resultGroups.map((result) => result.results.length).reduce((a, b) => a + b, 0),
    errors: resultsWithError,
  };
}

/**
 * Saves StoredResultGroup. If one already exists instead of creating a new one we will simply
 * add the results from the given group to the results of the saved group
 * @param group
 * @returns true if save was successful. False if not.
 */
export async function saveResultGroup(group: StoredResultGroup): Promise<boolean> {
  try {
    const groupKey = generateResultGroupId(
      group.ticketedEventId,
      group.firstName,
      group.lastName,
      group.email,
      group.accessCode,
      group.appVersion
    );

    await AsyncStorage.setItem(groupKey, JSON.stringify(group));

    return true;
  } catch (error) {
    return false;
  }
}

export async function removeResultGroup(group: StoredResultGroup): Promise<boolean> {
  try {
    const groupKey = generateResultGroupId(
      group.ticketedEventId,
      group.firstName,
      group.lastName,
      group.email,
      group.accessCode,
      group.appVersion
    );
    await AsyncStorage.removeItem(groupKey);

    return true;
  } catch (error) {
    return false;
  }
}

function generateResultGroupId(
  ticketedEventId: string,
  firstName: string,
  lastName: string,
  email: string,
  accessCode: string,
  appVersion: string
): string {
  return `${generateResultGroupPrefix(
    ticketedEventId
  )}-${firstName}-${lastName}-${email}-${accessCode}-${appVersion}`;
}

function generateResultGroupPrefix(ticketedEventId: string): string {
  return `${RESULT_GROUP_PREFIX}-${ticketedEventId}`;
}

function toStoredResultGroup(kvp: KeyValuePair): ParseResult<StoredResultGroup> {
  return safeJsonParse(isStoredResultGroup, kvp[1] ?? '');
}
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isStoredResultGroup(o: any): o is StoredResultGroup {
  return (
    'ticketedEventId' in o &&
    'firstName' in o &&
    'lastName' in o &&
    'email' in o &&
    'accessCode' in o &&
    'appVersion' in o &&
    'results' in o
  );
}
export function byId(a: ScannedQRCodeResult, b: ScannedQRCodeResult): number {
  return a.id?.localeCompare(b.id) ?? 0;
}
