import AsyncStorage from '@react-native-async-storage/async-storage';

import { Volunteer, isVolunteer } from '../models/Volunteer';
import { safeJsonParse } from '../utils/safe-parse';

export class CouldNotSaveVolunteer extends Error {}
export class CouldNotGetVolunteer extends Error {}

const CURRENT_VOLUNTEER_ID = 'CURRENT_VOLUNTEER_ID';

export async function getCurrentVolunteer(): Promise<Volunteer | undefined> {
  try {
    const data = await AsyncStorage.getItem(CURRENT_VOLUNTEER_ID);
    const result = safeJsonParse(isVolunteer, data ?? '');

    if (result.hasError) {
      return undefined;
    }

    return result.parsed;
  } catch (error) {
    throw new CouldNotGetVolunteer();
  }
}

export async function saveCurrentVolunteer(volunteer: Volunteer): Promise<void> {
  try {
    await AsyncStorage.setItem(CURRENT_VOLUNTEER_ID, JSON.stringify(volunteer));
  } catch (error) {
    throw new CouldNotSaveVolunteer();
  }
}

export async function removeCurrentVolunteer(): Promise<void> {
  await AsyncStorage.removeItem(CURRENT_VOLUNTEER_ID);
}
