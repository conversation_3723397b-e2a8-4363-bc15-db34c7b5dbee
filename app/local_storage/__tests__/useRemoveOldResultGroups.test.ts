import { faker } from '@faker-js/faker';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { attemptDeleteGroups } from '../LocalStorageHooks';
import { saveResultGroup } from '../ResultGroupStorage';
import {
  mockScannedQRCodeResult,
  mockStoredResultGroup,
} from '../__mocks__/ResultGroupStorage.mocks';

describe('useRemoveOldResultGroups', (): void => {
  beforeEach(async (): Promise<void> => {
    AsyncStorage.clear();
  });

  it('empty groups - no upload required - none removed', async (): Promise<void> => {
    const result = await attemptDeleteGroups();

    expect(result.requireUpload).toBe(false);
    expect(result.numberRemoved).toBe(0);
  });

  it('new groups - upload required - none removed', async (): Promise<void> => {
    await saveResultGroup(
      mockStoredResultGroup({
        results: [mockScannedQRCodeResult()],
        lastScanTime: faker.date.past(3).toISOString(),
      })
    );

    const result = await attemptDeleteGroups();

    expect(result.requireUpload).toBe(true);
    expect(result.numberRemoved).toBe(0);
  });

  it('old groups - removed old group - none uploaded', async (): Promise<void> => {
    await saveResultGroup(
      mockStoredResultGroup({
        uploadedResults: [mockScannedQRCodeResult()],
        lastScanTime: faker.date.past(3).toISOString(),
      })
    );

    const result = await attemptDeleteGroups();

    expect(result.requireUpload).toBe(false);
    expect(result.numberRemoved).toBeDefined();
    expect(result.numberRemoved).toBe(1);
  });
});
