import { faker } from '@faker-js/faker';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { testVolunteerInfo } from '../../components/screens/scanner/__mocks__/Constants';
import { EventInfo } from '../../models/EventInfo';
import {
  getAllRemainingScanResultGroups,
  getResultGroup,
  getResultGroupsFor,
  saveResultGroup,
} from '../ResultGroupStorage';
import { mockStoredResultGroup } from '../__mocks__/ResultGroupStorage.mocks';

describe('ResultGroupStorage', (): void => {
  beforeEach(async (): Promise<void> => {
    AsyncStorage.clear();
  });

  it('can save result group', async (): Promise<void> => {
    const resultGroup = mockStoredResultGroup();
    await saveResultGroup(resultGroup);
  });

  it('can get result groups for eventId', async (): Promise<void> => {
    const resultGroup = mockStoredResultGroup();
    await saveResultGroup(resultGroup);

    const savedResultGroup = await getResultGroupsFor(resultGroup.ticketedEventId);
    expect(savedResultGroup).toBeDefined();
    expect(savedResultGroup.groups).toBeDefined();
    expect(savedResultGroup.groups.length).toBeGreaterThan(0);
    expect(savedResultGroup.errors).toBeDefined();
    expect(savedResultGroup.errors.length).toEqual(0);
  });

  it('can get specific result group', async (): Promise<void> => {
    const eventInfo: EventInfo = {
      id: faker.datatype.uuid(),
      description: faker.word.verb(),
      name: faker.company.name(),
      date: faker.date.past().toISOString(),
      organizationId: faker.datatype.uuid(),
      participatingTeamIds: [faker.datatype.uuid(), faker.datatype.uuid()],
      timezoneIdentifier: 'Etc/UTC',
      associatedPassConfigIds: [],
      ticketTypePricingSummaries: [],
    };

    const resultGroup = mockStoredResultGroup({
      firstName: testVolunteerInfo.firstName,
      lastName: testVolunteerInfo.lastName,
      ticketedEventId: eventInfo.id,
      accessCode: testVolunteerInfo.accessCode,
      email: testVolunteerInfo.email,
    });

    await saveResultGroup(resultGroup);

    const savedResultGroup = await getResultGroup(
      eventInfo,
      testVolunteerInfo,
      resultGroup.appVersion
    );

    expect(savedResultGroup).toBeDefined();
  });

  it('can get all remaining result groups', async (): Promise<void> => {
    const groups = [mockStoredResultGroup(), mockStoredResultGroup(), mockStoredResultGroup()];

    for (const group of groups) {
      await saveResultGroup(group);
    }

    const allGroups = await getAllRemainingScanResultGroups();

    expect(allGroups).toBeDefined();
    expect(allGroups.groups).toBeDefined();
    expect(allGroups.groups.length).toEqual(groups.length);
    expect(allGroups.errors).toBeDefined();
    expect(allGroups.errors.length).toEqual(0);
  });
});
