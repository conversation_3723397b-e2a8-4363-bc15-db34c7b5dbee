import { faker } from '@faker-js/faker';
import AsyncStorage from '@react-native-async-storage/async-storage';
import moment from 'moment';

import { byGroupsForDeletion } from '../LocalStorageHooks';
import {
  mockScannedQRCodeResult,
  mockStoredResultGroup,
} from '../__mocks__/ResultGroupStorage.mocks';

describe('LocalStorageHooks', (): void => {
  beforeEach(async (): Promise<void> => {
    AsyncStorage.clear();
  });

  it('Filter group for deletion', async (): Promise<void> => {
    const now = moment().toISOString();
    const withinLastDay = faker.date.recent(1).toISOString();
    const betweenPast2And10Days = faker.date
      .between(moment().subtract(10, 'd').toISOString(), moment().subtract(2, 'd').toISOString())
      .toISOString();

    const allGroups = [
      mockStoredResultGroup({ lastScanTime: now }),
      mockStoredResultGroup({ lastScanTime: now }),

      mockStoredResultGroup({ lastScanTime: withinLastDay }),
      mockStoredResultGroup({
        lastScanTime: betweenPast2And10Days,
        uploadedResults: [mockScannedQRCodeResult()],
      }), // between 2 and 10 days old - all results already uploaded

      mockStoredResultGroup({
        lastScanTime: betweenPast2And10Days,
        results: [mockScannedQRCodeResult()],
        uploadedResults: [mockScannedQRCodeResult()],
      }), // between 2 and 10 days old but with results
    ];

    const groupsForDeletion = allGroups.filter(byGroupsForDeletion);

    expect(groupsForDeletion.length).toBe(1);
  });
});
