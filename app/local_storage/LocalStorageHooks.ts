import moment from 'moment';

import {
  StoredResultGroup,
  getAllRemainingScanResultGroups,
  removeResultGroup,
} from './ResultGroupStorage';
import PersistedItem from '../common/PersistedItem';

const lastTimeUploadWasRequested = new PersistedItem('lastTimeUploadWasRequested', '');

/**
 * Goes through all of the remaining result groups and remove any that are over a day old AND have all their results uploaded.
 * @returns requireUpload is true if there are still groups that need to be uploaded
 */
export async function attemptDeleteGroups(
  isDevEnv: boolean = false
): Promise<{ requireUpload: boolean; numberRemoved: number }> {
  const allGroups = await getAllRemainingScanResultGroups();
  const groupsToDelete = allGroups.groups.filter(byGroupsForDeletion);

  let removedCount = 0;
  for (const group of groupsToDelete) {
    // CO: not handling when we have an error for some reason
    const result = await removeResultGroup(group);
    if (result) {
      removedCount = removedCount + 1;
      console.log(`Removed group ${group}`);
    } else {
      console.log(`Failed to remove group ${group}`);
    }
  }

  const lastRequest = await lastTimeUploadWasRequested.get();
  let ageCutoff;

  console.log('isDevEnv', isDevEnv);
  if (isDevEnv) {
    ageCutoff = moment(lastRequest).add(30, 'seconds');
  } else {
    ageCutoff = moment(lastRequest).add(1, 'hour');
  }

  const isOld = moment().isAfter(ageCutoff) || lastRequest === '';

  if (isOld) {
    console.log('lastTimeUploadWasRequested is old. Requesting upload.');
    const remainingGroup = await getAllRemainingScanResultGroups();
    const groupsToUpload = remainingGroup.groups.filter(byGroupsForUpload);

    await lastTimeUploadWasRequested.set(moment().toISOString());
    return {
      requireUpload: groupsToUpload.length > 0,
      numberRemoved: removedCount,
    };
  } else {
    const timeUntilNextUploadRequest = moment.duration(ageCutoff.diff(moment()));

    console.log(
      `timeUntilNextUploadRequest: ${timeUntilNextUploadRequest.minutes()}:${timeUntilNextUploadRequest.seconds()}`
    );

    return {
      requireUpload: false,
      numberRemoved: removedCount,
    };
  }
}

export function byGroupsForDeletion(group: StoredResultGroup): boolean {
  const ageCutoff = moment(group.lastScanTime).add(1, 'day');
  const isOld = moment().isAfter(ageCutoff);
  const allResultsUploaded = group.results.length === 0;

  return isOld && allResultsUploaded;
}

export function byGroupsForUpload(group: StoredResultGroup): boolean {
  return group.results.length > 0;
}
