import { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import React from 'react';

import { DetoxContext } from 'react-native-detox-context';

import { HudlApolloClient } from '@hudl/gql-toolkit';
import { DEFAULT_API_URL } from '@hudl/jarvis/networking';

import DevSettings from '../DevSettings';
import PersistedItem from '../common/PersistedItem';
import { removeCurrentVolunteer } from '../local_storage/VolunteerStorage';
import { ThorMasterUrl } from '../utils/Constants';

// #region Types

export type Environment = 'development' | 'production';

interface AppSession {
  baseURL: string;
  setBaseURL: (baseURL: string) => void;
  gqlClients: { public: HudlApolloClient; payments: HudlApolloClient };
  environment: Environment;
  setEnvironment: (environment: Environment) => void;
  isDevelopmentEnvironment: boolean;
  toggleDevelopmentEnvironment: () => void;
}

interface InternalAppSessionProviderProps {
  baseURL: string;
  setBaseURL: (baseURL: string) => void;
  environment: Environment;
  setEnvironment: (environment: Environment) => void;
}

// #endregion

// #region Const

const persistedBaseURL = new PersistedItem('baseURL', DEFAULT_API_URL);
const persistedEnvironment = new PersistedItem('environment', 'production');

// #endregion

// #region Context

const AppSessionContext = React.createContext<AppSession | undefined>(undefined);

export function AppSessionProvider({
  children,
}: React.PropsWithChildren<unknown>): React.ReactElement | null {
  const [baseURL, setBaseURL] = useBaseURL();
  const [environment, setEnvironment] = useEnvironment();

  if (!baseURL) {
    // Do not set anything else up until we've loaded the baseURL
    return null;
  }

  return (
    <AppSessionProviderInternals {...{ baseURL, setBaseURL, environment, setEnvironment }}>
      {children}
    </AppSessionProviderInternals>
  );
}

function AppSessionProviderInternals({
  children,
  baseURL,
  setBaseURL,
  environment,
  setEnvironment,
}: React.PropsWithChildren<InternalAppSessionProviderProps>): React.ReactElement {
  const isDevelopmentEnvironment = environment === 'development';

  const toggleDevelopmentEnvironment = useCallback(() => {
    setEnvironment(isDevelopmentEnvironment ? 'production' : 'development');
    setBaseURL(isDevelopmentEnvironment ? DEFAULT_API_URL : ThorMasterUrl);
    removeCurrentVolunteer();
  }, [isDevelopmentEnvironment, setBaseURL, setEnvironment]);

  let apolloBaseURL = baseURL;

  if (__DEV__ && DevSettings.useLocalApolloServer) {
    apolloBaseURL = DevSettings.localApolloServerURL;
  }

  // Point at thor master if running automated tests
  if (DetoxContext.isAutomatedTest) {
    apolloBaseURL = ThorMasterUrl;
  }

  const appSession = useMemo<AppSession>(() => {
    return {
      baseURL,
      setBaseURL,
      environment,
      setEnvironment,
      isDevelopmentEnvironment,
      toggleDevelopmentEnvironment: toggleDevelopmentEnvironment,
      gqlClients: {
        public: new HudlApolloClient({
          baseURL: apolloBaseURL,
          endpointID: 'public',
          authToken: '',
        }),
        payments: new HudlApolloClient({
          baseURL: apolloBaseURL,
          endpointID: 'payments',
          authToken: '',
        }),
      },
    };
  }, [
    baseURL,
    setBaseURL,
    environment,
    setEnvironment,
    isDevelopmentEnvironment,
    toggleDevelopmentEnvironment,
    apolloBaseURL,
  ]);

  return <AppSessionContext.Provider value={appSession}>{children}</AppSessionContext.Provider>;
}

// #endregion

// #region Hooks and HOC

export function withAppSessionProvider<P extends object>(Component: React.ComponentType<P>) {
  return function EnhancedComponent(props: P) {
    return (
      <AppSessionProvider>
        <Component {...props} />
      </AppSessionProvider>
    );
  };
}

export function useAppSession(): AppSession {
  const appSession = useContext(AppSessionContext);

  if (!appSession) {
    throw new Error('Please wrap root component in an AppSessionProvider');
  }

  return appSession;
}

function useBaseURL(): [string | undefined, (baseURL: string) => void] {
  const [baseURL, setInMemoryBaseURL] = useState<string>();

  const setBaseURL = useCallback((updatedBaseURL: string) => {
    updatedBaseURL = updatedBaseURL.replace(/\/+$/g, '') + '/';
    persistedBaseURL.set(updatedBaseURL);
    setInMemoryBaseURL(updatedBaseURL);
  }, []);

  useEffect(() => {
    persistedBaseURL.get().then(setInMemoryBaseURL);
  }, []);

  return [baseURL, setBaseURL];
}

function useEnvironment(): [Environment, (environment: Environment) => void] {
  const [environment, setInMemoryEnvironment] = useState<Environment>(
    __DEV__ ? 'development' : 'production'
  );

  const setEnvironment = useCallback((updatedEnvironment: Environment) => {
    persistedEnvironment.set(updatedEnvironment);
    setInMemoryEnvironment(updatedEnvironment);
  }, []);

  useEffect(() => {
    persistedEnvironment.get().then((env) => {
      const isValidEnvironment = env === 'development' || env === 'production';
      setInMemoryEnvironment(isValidEnvironment ? env : 'production');
    });
  }, []);

  return [environment, setEnvironment];
}
// #endregion
