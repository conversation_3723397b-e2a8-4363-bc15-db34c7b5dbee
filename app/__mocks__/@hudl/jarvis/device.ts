export default class ScreenOrientation {
  public static lockToPortrait(): void {}
  public static lockToLandscape(): void {}
  public static lockToLandscapeLeft(): void {}
  public static lockToLandscapeRight(): void {}
  public static unlock(): void {}
}

export function useBundleID(): string {
  return '';
}

export function useFonts(): boolean {
  return true;
}

export function usePortraitLockForPhones(): void {}

export const WifiUtility = {};
