import { faker } from '@faker-js/faker';

import {
  Gender,
  LinkedEntry,
  Sport,
  TicketType,
  TicketTypeReference,
  TicketedEvent,
} from '../../gql/public/__generated__/graphql';

export interface MockTicketedEvent {
  createdAt?: string;
  date?: string;
  deletedAt?: string;
  description?: string;
  eventStatus?: string;
  gender?: string;
  id?: string;
  linkedEntries?: LinkedEntry[];
  name?: string;
  organizationId?: string;
  participatingTeamIds?: string[];
  refundable?: boolean;
  sport?: string;
  ticketTypeReferences?: TicketTypeReference[];
  ticketTypes?: TicketType[];
  updatedAt?: string;
  venueId?: string;
  timeZoneIdentifier?: string;
}

export function mockedTicketedEvent(mock?: MockTicketedEvent): TicketedEvent {
  return {
    createdAt: mock?.createdAt ?? faker.date.soon(),
    date: mock?.date ?? faker.date.soon(),
    deletedAt: mock?.deletedAt,
    description: mock?.description,
    eventStatus: mock?.eventStatus,
    gender: mock?.gender ?? faker.helpers.arrayElement(Object.values(Gender)),
    id: mock?.id ?? faker.datatype.uuid(),
    linkedEntries: mock?.linkedEntries,
    name: mock?.name ?? faker.company.name(),
    organizationId: mock?.organizationId ?? faker.datatype.uuid(),
    participatingTeamIds: mock?.participatingTeamIds,
    refundable: mock?.refundable,
    sport: mock?.sport ?? faker.helpers.arrayElement(Object.values(Sport)),
    ticketTypeReferences: mock?.ticketTypeReferences,
    updatedAt: mock?.createdAt ?? faker.date.past(),
    venueId: mock?.venueId,
    timeZoneIdentifier: mock?.timeZoneIdentifier ?? faker.address.timeZone(),
  };
}
