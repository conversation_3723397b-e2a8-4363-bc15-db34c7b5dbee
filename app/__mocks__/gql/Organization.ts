import { ApolloError, FetchResult } from '@apollo/client';
import { MockedResponse } from '@apollo/client/testing';
import { GraphQLError } from 'graphql';

import { GetTicketedEventAccessCodeInput } from '../../gql/public/__generated__/graphql';
import GetTicketedEventsByOrganizationId from '../../gql/public/queries/GetTicketedEventsByOrganizationId';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function mockOrganizationResult(): FetchResult<Record<string, any>> {
  return {
    data: {
      ticketedEventAccessCode: {
        accessCode: 'c9jkt',
        organizationId: 'U2Nob29sMTc0MTMy',
        organization: {
          id: 'U2Nob29sMTc0MTMy',
          internalId: '174132',
          abbreviation: 'HTFS',
          fullName: 'HUDL TEST - FAN SQUAD 6',
          shortName: 'HUDL TEST - FAN SQUA',
          mascot: '',
          primaryColor: '#000000',
          secondaryColor: '#000000',
        },
      },
    },
  };
}

// Mocking example: https://www.dolthub.com/blog/2021-02-05-guide-to-react-unit-testing/
export function mockGetOrganizationQuery({
  inputs,
  responseDelay = 500,
}: {
  inputs: GetTicketedEventAccessCodeInput;

  responseDelay?: number;
}): MockedResponse {
  return {
    request: {
      query: GetTicketedEventsByOrganizationId,
      variables: {
        input: inputs,
      },
    },
    result: mockOrganizationResult(),
    delay: responseDelay,
  };
}

export function mockTicketedEventQueryError(
  inputs: GetTicketedEventAccessCodeInput
): MockedResponse {
  return {
    request: {
      query: GetTicketedEventsByOrganizationId,
      variables: {
        input: inputs,
      },
    },
    error: new ApolloError({
      graphQLErrors: [
        new GraphQLError('Unable to decode identifier xxx', {
          extensions: { code: 'BAD_USER_INPUT', path: ['ticketed-events'] },
        }),
      ],
    }),
  };
}
