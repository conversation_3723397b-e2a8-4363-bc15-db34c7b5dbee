import { ApolloError, FetchR<PERSON>ult } from '@apollo/client';
import { MockedResponse } from '@apollo/client/testing';
import { GraphQLError } from 'graphql';

import {
  GetTicketedEventsByOrganizationIdInput,
  TicketedEvent,
} from '../../gql/public/__generated__/graphql';
import GetTicketedEventsByOrganizationId from '../../gql/public/queries/GetTicketedEventsByOrganizationId';
import { mockedTicketedEvent } from '../TicketedEvent/TicketedEvent';

function mockTicketedEventResult(
  mocks: TicketedEvent[],
  totalCount: number,
  cursor: string,
  pageInfo?: {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor?: string;
    endCursor?: string;
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): FetchResult<Record<string, any>> {
  return {
    data: {
      ticketedEventsByOrganizationId: {
        totalCount: totalCount,
        edges: [
          {
            cursor: cursor,
          },
        ],
        pageInfo: {
          hasNextPage: pageInfo?.hasNextPage ?? false,
          hasPreviousPage: pageInfo?.hasPreviousPage ?? false,
          startCursor: pageInfo?.startCursor ?? '',
          endCursor: pageInfo?.endCursor ?? '',
        },
        items: mocks,
      },
    },
  };
}

// Mocking example: https://www.dolthub.com/blog/2021-02-05-guide-to-react-unit-testing/
export function mockTicketedEventQuery({
  inputs,
  totalCount,
  cursor,
  mocks,
  pageInfo,
  responseDelay = 500,
}: {
  inputs: GetTicketedEventsByOrganizationIdInput;
  totalCount: number;
  cursor: string;
  mocks?: TicketedEvent[];
  responseDelay?: number;
  pageInfo?: {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor?: string;
    endCursor?: string;
  };
}): MockedResponse {
  return {
    request: {
      query: GetTicketedEventsByOrganizationId,
      variables: {
        input: inputs,
      },
    },
    result: mockTicketedEventResult(mocks ?? [mockedTicketedEvent()], totalCount, cursor, pageInfo),
    delay: responseDelay,
  };
}

export function mockTicketedEventQueryError(
  inputs: GetTicketedEventsByOrganizationIdInput
): MockedResponse {
  return {
    request: {
      query: GetTicketedEventsByOrganizationId,
      variables: {
        input: inputs,
      },
    },
    error: new ApolloError({
      graphQLErrors: [
        new GraphQLError('Unable to decode identifier xxx', {
          extensions: { code: 'BAD_USER_INPUT', path: ['ticketed-events'] },
        }),
      ],
    }),
  };
}
