{"jarvisVersion": "17.0.0", "notes": ["This file is automatically generated and maintained by the @hudl/jarvis package. Please do not update it directly.", "", "The @hudl/jarvis package contains a number of third party React Native libraries (e.g. react-native-reanimated),", "that get transitively linked to projects that include @hudl/jarvis as a dependency. This allows us to maintain a", "certain level of consistency across our React Native projects, as well as allowing projects to run on the Jarvis", "Client app during development (https://sync.hudlnet.com/display/Mobile/Jarvis).", "", "By design, VSCode (and TypeScript) will never suggest importing from transitive dependencies. This package.json", "file acts as a workaround, and ensures that VSCode auto-import and auto-complete functionality works correctly for", "the third party React Native Libraries that make up the Jarvis Native Runtime.", "", "See https://github.com/microsoft/TypeScript/issues/39911#issuecomment-806342648 for more info."], "peerDependencies": {"@bam.tech/react-native-image-resizer": "*", "@react-native-async-storage/async-storage": "*", "@react-native-camera-roll/camera-roll": "*", "@react-native-clipboard/clipboard": "*", "@react-native-community/datetimepicker": "*", "@react-native-community/netinfo": "*", "@react-native-documents/picker": "*", "@react-native-google-signin/google-signin": "*", "@react-native-masked-view/masked-view": "*", "@react-native-picker/picker": "*", "@sentry/react-native": "*", "expo": "*", "react-native-audio-recorder-player": "*", "react-native-auth0": "*", "react-native-blob-util": "*", "react-native-detox-context": "*", "react-native-device-info": "*", "react-native-fs": "*", "react-native-gesture-handler": "*", "react-native-get-random-values": "*", "react-native-haptic-feedback": "*", "react-native-image-crop-picker": "*", "react-native-keychain": "*", "react-native-linear-gradient": "*", "react-native-localize": "*", "react-native-pager-view": "*", "react-native-pdf": "*", "react-native-permissions": "*", "react-native-reanimated": "*", "react-native-safe-area-context": "*", "react-native-screens": "*", "react-native-share": "*", "react-native-snackbar": "*", "react-native-svg": "*", "react-native-system-bars": "*", "react-native-video": "*", "react-native-webrtc": "*", "react-native-webview": "*", "react-native-zip-archive": "*", "react": "*", "react-native": "*", "@hudl/rn-uniform": "*"}}