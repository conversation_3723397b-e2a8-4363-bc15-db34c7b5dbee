GIT
  remote: **************:hudl/fastlane-plugins.git
  revision: be007e6b214988cb68841d1534fb72e0994a1b7f
  specs:
    fastlane-plugin-aws_actions (0.2.0)
      aws-sdk-s3 (~> 1.87)
      aws-sdk-secretsmanager (~> 1.0.0)
    fastlane-plugin-xcodeproj_build_methods (0.1.0)
      xcodeproj

GEM
  remote: https://rubygems.org/
  specs:
    CFPropertyList (3.0.6)
      rexml
    activesupport (7.0.8)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.6)
      public_suffix (>= 2.0.2, < 6.0)
    algoliasearch (1.27.5)
      httpclient (~> 2.8, >= 2.8.3)
      json (>= 1.5.1)
    artifactory (3.0.17)
    atomos (0.1.3)
    aws-eventstream (1.3.0)
    aws-partitions (1.883.0)
    aws-sdk-core (3.190.3)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.8)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.76.0)
      aws-sdk-core (~> 3, >= 3.188.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.142.0)
      aws-sdk-core (~> 3, >= 3.189.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.8)
    aws-sdk-secretsmanager (1.0.0)
      aws-sdk-core (~> 3)
      aws-sigv4 (~> 1.0)
    aws-sigv4 (1.8.0)
      aws-eventstream (~> 1, >= 1.0.2)
    babosa (1.0.4)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    claide (1.1.0)
    cocoapods (1.13.0)
      addressable (~> 2.8)
      claide (>= 1.0.2, < 2.0)
      cocoapods-core (= 1.13.0)
      cocoapods-deintegrate (>= 1.0.3, < 2.0)
      cocoapods-downloader (>= 1.6.0, < 2.0)
      cocoapods-plugins (>= 1.0.0, < 2.0)
      cocoapods-search (>= 1.0.0, < 2.0)
      cocoapods-trunk (>= 1.6.0, < 2.0)
      cocoapods-try (>= 1.1.0, < 2.0)
      colored2 (~> 3.1)
      escape (~> 0.0.4)
      fourflusher (>= 2.3.0, < 3.0)
      gh_inspector (~> 1.0)
      molinillo (~> 0.8.0)
      nap (~> 1.0)
      ruby-macho (>= 2.3.0, < 3.0)
      xcodeproj (>= 1.23.0, < 2.0)
    cocoapods-core (1.13.0)
      activesupport (>= 5.0, < 8)
      addressable (~> 2.8)
      algoliasearch (~> 1.0)
      concurrent-ruby (~> 1.1)
      fuzzy_match (~> 2.0.4)
      nap (~> 1.0)
      netrc (~> 0.11)
      public_suffix (~> 4.0)
      typhoeus (~> 1.0)
    cocoapods-deintegrate (1.0.5)
    cocoapods-downloader (1.6.3)
    cocoapods-plugins (1.0.0)
      nap
    cocoapods-search (1.0.1)
    cocoapods-trunk (1.6.0)
      nap (>= 0.8, < 2.0)
      netrc (~> 0.11)
    cocoapods-try (1.2.0)
    colored (1.2)
    colored2 (3.1.2)
    commander (4.6.0)
      highline (~> 2.0.0)
    concurrent-ruby (1.2.2)
    declarative (0.0.20)
    digest-crc (0.6.5)
      rake (>= 12.0.0, < 14.0.0)
    domain_name (0.6.20240107)
    dotenv (2.8.1)
    emoji_regex (3.2.3)
    escape (0.0.4)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    excon (0.112.0)
    faraday (1.10.3)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-cookie_jar (0.0.7)
      faraday (>= 0.8.0)
      http-cookie (~> 1.0.0)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    faraday_middleware (1.2.1)
      faraday (~> 1.0)
    fastimage (2.4.0)
    fastlane (2.226.0)
      CFPropertyList (>= 2.3, < 4.0.0)
      addressable (>= 2.8, < 3.0.0)
      artifactory (~> 3.0)
      aws-sdk-s3 (~> 1.0)
      babosa (>= 1.0.3, < 2.0.0)
      bundler (>= 1.12.0, < 3.0.0)
      colored (~> 1.2)
      commander (~> 4.6)
      dotenv (>= 2.1.1, < 3.0.0)
      emoji_regex (>= 0.1, < 4.0)
      excon (>= 0.71.0, < 1.0.0)
      faraday (~> 1.0)
      faraday-cookie_jar (~> 0.0.6)
      faraday_middleware (~> 1.0)
      fastimage (>= 2.1.0, < 3.0.0)
      fastlane-sirp (>= 1.0.0)
      gh_inspector (>= 1.1.2, < 2.0.0)
      google-apis-androidpublisher_v3 (~> 0.3)
      google-apis-playcustomapp_v1 (~> 0.1)
      google-cloud-env (>= 1.6.0, < 2.0.0)
      google-cloud-storage (~> 1.31)
      highline (~> 2.0)
      http-cookie (~> 1.0.5)
      json (< 3.0.0)
      jwt (>= 2.1.0, < 3)
      mini_magick (>= 4.9.4, < 5.0.0)
      multipart-post (>= 2.0.0, < 3.0.0)
      naturally (~> 2.2)
      optparse (>= 0.1.1, < 1.0.0)
      plist (>= 3.1.0, < 4.0.0)
      rubyzip (>= 2.0.0, < 3.0.0)
      security (= 0.1.5)
      simctl (~> 1.6.3)
      terminal-notifier (>= 2.0.0, < 3.0.0)
      terminal-table (~> 3)
      tty-screen (>= 0.6.3, < 1.0.0)
      tty-spinner (>= 0.8.0, < 1.0.0)
      word_wrap (~> 1.0.0)
      xcodeproj (>= 1.13.0, < 2.0.0)
      xcpretty (~> 0.4.0)
      xcpretty-travis-formatter (>= 0.0.3, < 2.0.0)
    fastlane-plugin-firebase_app_distribution (0.7.4)
      google-apis-firebaseappdistribution_v1 (~> 0.3.0)
    fastlane-plugin-yarn (1.2)
    fastlane-sirp (1.0.0)
      sysrandom (~> 1.0)
    ffi (1.16.3)
    fourflusher (2.3.1)
    fuzzy_match (2.0.4)
    gh_inspector (1.1.3)
    google-apis-androidpublisher_v3 (0.54.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-core (0.11.3)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
    google-apis-firebaseappdistribution_v1 (0.3.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-iamcredentials_v1 (0.17.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-playcustomapp_v1 (0.13.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-storage_v1 (0.31.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-cloud-core (1.7.1)
      google-cloud-env (>= 1.0, < 3.a)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.6.0)
      faraday (>= 0.17.3, < 3.0)
    google-cloud-errors (1.4.0)
    google-cloud-storage (1.47.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-iamcredentials_v1 (~> 0.1)
      google-apis-storage_v1 (~> 0.31.0)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    googleauth (1.8.1)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    highline (2.0.3)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    jmespath (1.6.2)
    json (2.7.1)
    jwt (2.7.1)
    logger (1.7.0)
    mini_magick (4.13.2)
    mini_mime (1.1.5)
    minitest (5.20.0)
    molinillo (0.8.0)
    multi_json (1.15.0)
    multipart-post (2.3.0)
    mutex_m (0.3.0)
    nanaimo (0.3.0)
    nap (1.1.0)
    naturally (2.2.1)
    netrc (0.11.0)
    optparse (0.6.0)
    os (1.1.4)
    plist (3.7.2)
    public_suffix (4.0.7)
    rake (13.2.1)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.2.6)
    rouge (3.28.0)
    ruby-macho (2.5.1)
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    security (0.1.5)
    signet (0.18.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simctl (1.6.10)
      CFPropertyList
      naturally
    sysrandom (1.0.5)
    terminal-notifier (2.0.0)
    terminal-table (3.0.2)
      unicode-display_width (>= 1.1.1, < 3)
    trailblazer-option (0.1.2)
    tty-cursor (0.7.1)
    tty-screen (0.8.2)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    typhoeus (1.4.0)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (2.6.0)
    word_wrap (1.0.0)
    xcodeproj (1.23.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.3.0)
      rexml (~> 3.2.4)
    xcpretty (0.4.0)
      rouge (~> 3.28.0)
    xcpretty-travis-formatter (1.0.1)
      xcpretty (~> 0.2, >= 0.0.7)

PLATFORMS
  ruby

DEPENDENCIES
  activesupport (>= *******, != 7.1.0)
  benchmark
  bigdecimal
  cocoapods (>= 1.13, != 1.15.1, != 1.15.0)
  concurrent-ruby (< 1.3.4)
  fastlane (= 2.226.0)
  fastlane-plugin-aws_actions!
  fastlane-plugin-firebase_app_distribution
  fastlane-plugin-xcodeproj_build_methods!
  fastlane-plugin-yarn
  logger
  mutex_m
  xcodeproj (< 1.26.0)

RUBY VERSION
   ruby 3.2.3p157

BUNDLED WITH
   2.4.19
