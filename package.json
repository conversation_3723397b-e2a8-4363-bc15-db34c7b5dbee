{"name": "rn-ticketing", "version": "1.15.0", "private": true, "scripts": {"android": "yarn react-native run-android", "build:android:release": "yarn gradle app:assembleRelease", "build:androidTest:release": "yarn gradle app:assembleAndroidTest -DtestBuildType=release", "build:ios:release": "RCT_NO_LAUNCH_PACKAGER=true xcodebuild -workspace ./ios/HudlTicketing.xcworkspace -scheme HudlTicketing -destination 'generic/platform=iOS Simulator' -configuration Release -sdk iphonesimulator -derivedDataPath ./ios/build", "bundle:android": "yarn jarvis bundle generate --platform android --entry-file index.js", "bundle:ios": "yarn jarvis bundle generate --platform ios --entry-file index.js", "ci": "yarn lint --max-warnings=0 && yarn compile && yarn test --json --outputFile=output.json", "compile": "yarn compile:app && yarn compile:server", "compile:app": "tsc -p tsconfig.json --pretty", "compile:server": "cd local-apollo-server/ && tsc -p tsconfig.json", "dev-json-codegen": "babel-node -x .ts -- ./scripts/dev-json-codegen.ts", "e2e-build": "jarvis e2e-build", "e2e-run": "jarvis e2e-run", "generate-workflows": "babel-node -x .ts -- ./scripts/generate-workflows.ts", "gradle": "cd ./android && ./gradlew", "gql-codegen": "babel-node -x .ts -- ./scripts/gql-codegen.ts", "gql-download-schemas": "babel-node -x .ts -- ./scripts/gql-download-schemas.ts", "ios": "yarn react-native run-ios", "jarvis-auto-imports": "node -e \"require('@hudl/jarvis/native-runtime-auto-link.js').generateAutoImportPackageJSON('./app')\"", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lc": "yarn lint --fix && yarn compile", "nuke": "jarvis nuke", "postinstall": "husky install && yarn workarounds && yarn dev-json-codegen && node ./scripts/generate-auto-link-lock-file.js", "precommit": "lint-staged && yarn compile", "setup:android": "yarn workarounds", "setup:ios": "yarn workarounds && bundle install && cd ios/ && bundle exec pod install", "setup:native": "yarn setup:android && yarn setup:ios", "start": "jarvis start", "start-apollo-server": "babel-node -x .ts -- ./local-apollo-server", "test": "TZ=UTC jest", "upload-bundle": "yarn install && yarn jarvis bundle upload --entry-file index.js --clean-up", "verify-jarvis-runtime": "babel-node -x .ts -- ./scripts/verify-jarvis-native-runtime.ts", "workarounds": "node ./scripts/workarounds/workarounds.js", "xc": "open ./ios/HudlTicketing.xcworkspace"}, "dependencies": {"@apollo/client": "3.7.17", "@gorhom/bottom-sheet": "5.1.6", "@hudl/analytics": "1.1.0", "@hudl/gql-toolkit": "0.3.0", "@hudl/jarvis": "17.0.0", "@react-navigation/bottom-tabs": "6.6.0", "@react-navigation/elements": "1.3.29", "@react-navigation/native": "6.1.7", "@react-navigation/native-stack": "6.9.13", "@stripe/stripe-terminal-react-native": "0.0.1-beta.25", "date-fns": "2.30.0", "date-fns-tz": "2.0.1", "graphql": "16.7.1", "lodash": "4.17.21", "moment-timezone": "0.5.43", "react-native": "0.79.5", "react-native-uuid": "2.0.3", "react-native-vision-camera": "4.7.0"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/node": "7.26.0", "@babel/preset-env": "7.26.9", "@babel/runtime": "7.27.0", "@faker-js/faker": "^7.6.0", "@hudl/ci-toolkit": "0.22.1", "@hudl/detox-utils": "6.4.0", "@hudl/eslint-config-react-native": "0.13.0", "@hudl/jarvis-cli": "9.3.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.5", "@react-native/metro-config": "0.79.5", "@react-native/typescript-config": "0.79.5", "@testing-library/react-native": "13.2.0", "@types/jest": "29.5.14", "@types/lodash": "4.17.16", "@types/react": "19.1.8", "@types/react-test-renderer": "19.1.0", "babel-jest": "29.6.4", "detox": "20.40.2", "eslint": "8.19.0", "find-in-files": "0.5.0", "github-actions-ctrf": "0.0.50", "husky": "8.0.2", "jest": "29.7.0", "jest-ctrf-json-reporter": "0.0.9", "jest-summary-reporter": "0.0.2", "lint-staged": "13.0.3", "react-test-renderer": "19.0.0", "replace-in-file": "7.0.1", "typescript": "5.0.4"}, "expo": {"autolinking": {"exclude": ["expo-notifications"]}}, "jarvis": {"clientAppName": "ticketing", "clientVersion": "develop"}, "engines": {"node": ">=22"}, "packageManager": "yarn@3.6.4"}