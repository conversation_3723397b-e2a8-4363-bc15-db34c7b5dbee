import { GraphQLResolveInfo, GraphQLScalarType, GraphQLScalarTypeConfig } from 'graphql';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
  /** The `Cursor` scalar type is an ordered key used in paginated Relay connections. */
  Cursor: any;
  /** The `Date` scalar type represents a year, month and day in accordance with the [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) standard. */
  Date: any;
  /** The `DateTime` scalar type represents a date and time. `DateTime` expects timestamps to be formatted in accordance with the [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) standard. */
  DateTime: any;
  /** The `DateTimeOffset` scalar type represents a date, time and offset from UTC. `DateTimeOffset` expects timestamps to be formatted in accordance with the [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) standard. */
  DateTimeOffset: any;
  Decimal: any;
  /** The `Milliseconds` scalar type represents a period of time represented as the total number of milliseconds. */
  Milliseconds: any;
  /** The `Seconds` scalar type represents a period of time represented as the total number of seconds. */
  Seconds: any;
};

export enum AthleteInviteRequestValidationStatus {
  AlreadyOnTeam = 'ALREADY_ON_TEAM',
  CanJoinHudl = 'CAN_JOIN_HUDL',
  HasAccount = 'HAS_ACCOUNT',
  InvalidInviteCode = 'INVALID_INVITE_CODE',
  RequestPendingForTeam = 'REQUEST_PENDING_FOR_TEAM',
  Unknown = 'UNKNOWN'
}

export type Carrier = {
  __typename?: 'Carrier';
  description?: Maybe<Scalars['String']>;
  value?: Maybe<Scalars['String']>;
};

export enum Gender {
  Coed = 'COED',
  Mens = 'MENS',
  Womens = 'WOMENS'
}

export type Group = {
  __typename?: 'Group';
  /** Group date created */
  dateCreated: Scalars['DateTime'];
  /** The graphql Id of the group. */
  id: Scalars['ID'];
  /** The hudl Id of the group. */
  internalId?: Maybe<Scalars['String']>;
  /** Group is system group */
  isSystemGroup: Scalars['Boolean'];
  /** Member ids */
  memberIds?: Maybe<IdConnection>;
  /** Group Name */
  name?: Maybe<Scalars['String']>;
  /** Group Video Activity */
  videoActivity?: Maybe<VideoActivity>;
};


export type GroupMemberIdsArgs = {
  after?: InputMaybe<Scalars['String']>;
  before?: InputMaybe<Scalars['String']>;
  first?: InputMaybe<Scalars['Int']>;
  last?: InputMaybe<Scalars['Int']>;
};

/** Connection to related objects with relevant pagination information. */
export type GroupConnection = {
  __typename?: 'GroupConnection';
  /** Information to aid in pagination. */
  edges?: Maybe<Array<Maybe<GroupEdge>>>;
  /** A list of all of the objects returned in the connection. This is a convenience field provided for quickly exploring the API; rather than querying for `{ edges { node } }` when no edge data is needed, this field can be used instead. Note that when clients like Relay need to fetch the `cursor` field on the edge to enable efficient pagination, this shortcut cannot be used, and the full `{ edges { node } }` version should be used instead. */
  items?: Maybe<Array<Maybe<Group>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** A count of the total number of objects in this connection, ignoring pagination. This allows a client to fetch the first five objects by passing "5" as the argument to first, then fetch the total count so it could display "5 of 83", for example. In cases where we employ infinite scrolling or don't have an exact count of entries, this field will return `null`. */
  totalCount?: Maybe<Scalars['Int']>;
};

/** An edge in a connection between two objects. */
export type GroupEdge = {
  __typename?: 'GroupEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['Cursor'];
  /** The item at the end of the edge. */
  node?: Maybe<Group>;
};

export type GroupInput = {
  /** Group id */
  id?: InputMaybe<Scalars['String']>;
  /** Member ids to remove */
  membersToAdd?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Member ids to remove */
  membersToRemove?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Group Name */
  name?: InputMaybe<Scalars['String']>;
};

/** Connection to related objects with relevant pagination information. */
export type IdConnection = {
  __typename?: 'IDConnection';
  /** Information to aid in pagination. */
  edges?: Maybe<Array<Maybe<IdEdge>>>;
  /** A list of all of the objects returned in the connection. This is a convenience field provided for quickly exploring the API; rather than querying for `{ edges { node } }` when no edge data is needed, this field can be used instead. Note that when clients like Relay need to fetch the `cursor` field on the edge to enable efficient pagination, this shortcut cannot be used, and the full `{ edges { node } }` version should be used instead. */
  items?: Maybe<Array<Scalars['ID']>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** A count of the total number of objects in this connection, ignoring pagination. This allows a client to fetch the first five objects by passing "5" as the argument to first, then fetch the total count so it could display "5 of 83", for example. In cases where we employ infinite scrolling or don't have an exact count of entries, this field will return `null`. */
  totalCount?: Maybe<Scalars['Int']>;
};

/** An edge in a connection between two objects. */
export type IdEdge = {
  __typename?: 'IDEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['Cursor'];
  /** The item at the end of the edge. */
  node: Scalars['ID'];
};

export type INode = {
  id: Scalars['ID'];
};

export type InviteCode = {
  __typename?: 'InviteCode';
  /** Date created */
  dateCreated: Scalars['DateTime'];
  /** Invite code Id */
  id?: Maybe<Scalars['String']>;
  /** Invite requests */
  inviteRequests?: Maybe<Array<Maybe<InviteRequest>>>;
  /** Date expired */
  lastModifiedDate: Scalars['DateTime'];
  /** Team */
  team?: Maybe<TeamHeader>;
  /** Team Id */
  teamId?: Maybe<Scalars['String']>;
};

export type InviteCodeWrapper = {
  __typename?: 'InviteCodeWrapper';
  /** Invite Code */
  inviteCode?: Maybe<InviteCode>;
  /** Team Join Eligibility Status */
  teamJoinEligibilityStatus: AthleteInviteRequestValidationStatus;
};

export type InviteRequest = {
  __typename?: 'InviteRequest';
  /** Cell carrier */
  cellCarrier?: Maybe<Scalars['String']>;
  /** Cell number. */
  cellPhone?: Maybe<Scalars['String']>;
  /** Date created */
  dateCreated: Scalars['DateTime'];
  /** Email */
  email?: Maybe<Scalars['String']>;
  /** First Name */
  firstName?: Maybe<Scalars['String']>;
  /** Graduation Year */
  graduationYear?: Maybe<Scalars['Int']>;
  /** Jersey number */
  jersey?: Maybe<Scalars['String']>;
  /** Last Name */
  lastName?: Maybe<Scalars['String']>;
  /** Position */
  position?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Role */
  role: Role;
  /** User Id */
  userId?: Maybe<Scalars['String']>;
};

export type InviteRequestInput = {
  /** Cell carrier */
  cellCarrier?: InputMaybe<Scalars['String']>;
  /** Cell phone */
  cellPhone?: InputMaybe<Scalars['String']>;
  /** Email */
  email?: InputMaybe<Scalars['String']>;
  /** Existing User */
  existingUser: Scalars['Boolean'];
  /** First name */
  firstName?: InputMaybe<Scalars['String']>;
  /** Graduation Year */
  graduationYear?: InputMaybe<Scalars['Int']>;
  /** Jersey */
  jersey?: InputMaybe<Scalars['String']>;
  /** Last name */
  lastName?: InputMaybe<Scalars['String']>;
  /** Password */
  password?: InputMaybe<Scalars['String']>;
  /** Position */
  position?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Role */
  role: Role;
};

export type MaxPrepsTeam = {
  __typename?: 'MaxPrepsTeam';
  /** MaxPreps has roster information. */
  hasRoster: Scalars['Boolean'];
  /** MaxPreps has schedule information. */
  hasSchedule: Scalars['Boolean'];
  /** Hudl school ID */
  hudlSchoolId?: Maybe<Scalars['String']>;
  /** Descriptive season name */
  hudlSeason?: Maybe<Scalars['String']>;
  /** Descriptive name of Hudl sport */
  hudlSport?: Maybe<Scalars['String']>;
  /** MaxPreps School Name */
  maxPrepsName?: Maybe<Scalars['String']>;
  /** MaxPreps School ID */
  maxPrepsSchoolId?: Maybe<Scalars['String']>;
  /** Longform name of the school with location */
  name?: Maybe<Scalars['String']>;
  /** Season ID */
  seasonId?: Maybe<Scalars['String']>;
  /** Season year */
  seasonYear: Scalars['Int'];
  /** Sport enum ID */
  sportId: Scalars['Int'];
  /** Hudl Team ID */
  teamId?: Maybe<Scalars['String']>;
};

export type Member = INode & {
  __typename?: 'Member';
  /** Member's phone number. */
  cell?: Maybe<Scalars['String']>;
  /** Member's phone carrier. */
  cellCarrier?: Maybe<Scalars['String']>;
  /** Member's email. */
  email?: Maybe<Scalars['String']>;
  /** Member's first name. */
  firstName?: Maybe<Scalars['String']>;
  /** Member's full name. */
  fullName?: Maybe<Scalars['String']>;
  /** Member Graduation Year */
  graduationYear?: Maybe<Scalars['Int']>;
  /** Member groups */
  groups?: Maybe<IdConnection>;
  /** Height */
  height?: Maybe<Scalars['String']>;
  /** The graphql Id of the member. */
  id: Scalars['ID'];
  /** The hudl Id of the member. */
  internalId?: Maybe<Scalars['String']>;
  /** IsDisabled */
  isDisabled: Scalars['Boolean'];
  /** Is member opted in to Recruit */
  isOptedInToRecruit: Scalars['Boolean'];
  /** Jersey */
  jersey?: Maybe<Scalars['String']>;
  /** Last Login Date */
  lastLoginDate?: Maybe<Scalars['DateTime']>;
  /** Member's last name. */
  lastName?: Maybe<Scalars['String']>;
  /** Picture */
  picture?: Maybe<Scalars['String']>;
  /** PictureUrl */
  pictureUrl?: Maybe<Scalars['String']>;
  /** Position */
  position?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Member's role. */
  role: Role;
  /** Season Ids */
  seasonIds?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Member's status */
  status: UserStatus;
  /** Member Video Activity */
  videoActivity?: Maybe<VideoActivity>;
  /** Weight */
  weight?: Maybe<Scalars['Int']>;
};


export type MemberGroupsArgs = {
  after?: InputMaybe<Scalars['String']>;
  before?: InputMaybe<Scalars['String']>;
  first?: InputMaybe<Scalars['Int']>;
  includeFamilyMembers?: InputMaybe<Scalars['Boolean']>;
  last?: InputMaybe<Scalars['Int']>;
};

/** Connection to related objects with relevant pagination information. */
export type MemberConnection = {
  __typename?: 'MemberConnection';
  /** Information to aid in pagination. */
  edges?: Maybe<Array<Maybe<MemberEdge>>>;
  /** A list of all of the objects returned in the connection. This is a convenience field provided for quickly exploring the API; rather than querying for `{ edges { node } }` when no edge data is needed, this field can be used instead. Note that when clients like Relay need to fetch the `cursor` field on the edge to enable efficient pagination, this shortcut cannot be used, and the full `{ edges { node } }` version should be used instead. */
  items?: Maybe<Array<Maybe<Member>>>;
  /** Information to aid in pagination. */
  pageInfo: PageInfo;
  /** A count of the total number of objects in this connection, ignoring pagination. This allows a client to fetch the first five objects by passing "5" as the argument to first, then fetch the total count so it could display "5 of 83", for example. In cases where we employ infinite scrolling or don't have an exact count of entries, this field will return `null`. */
  totalCount?: Maybe<Scalars['Int']>;
};

/** An edge in a connection between two objects. */
export type MemberEdge = {
  __typename?: 'MemberEdge';
  /** A cursor for use in pagination. */
  cursor: Scalars['Cursor'];
  /** The item at the end of the edge. */
  node?: Maybe<Member>;
};

export type MemberInput = {
  /** Phone number */
  cell?: InputMaybe<Scalars['String']>;
  /** Phone carrier */
  cellCarrier?: InputMaybe<Scalars['String']>;
  /** Member's email */
  email?: InputMaybe<Scalars['String']>;
  /** Member's first name */
  firstName?: InputMaybe<Scalars['String']>;
  /** Graduation Year */
  graduationYear?: InputMaybe<Scalars['Int']>;
  /** Group Membership */
  groupMembership?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Height */
  height?: InputMaybe<Scalars['String']>;
  /** Member's id */
  id?: InputMaybe<Scalars['String']>;
  /** Jersey */
  jersey?: InputMaybe<Scalars['String']>;
  /** Member's last name */
  lastName?: InputMaybe<Scalars['String']>;
  /** Position */
  position?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Member's role */
  role: Role;
  /** SeasonIds */
  seasonIds?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  /** Weight */
  weight?: InputMaybe<Scalars['Int']>;
};

export type MoveAthletesResult = {
  __typename?: 'MoveAthletesResult';
  copied: Scalars['Int'];
  existing: Scalars['Int'];
};

export type Mutation = {
  __typename?: 'Mutation';
  acceptInviteRequests?: Maybe<InviteCode>;
  addAthletesToSeason: Scalars['Boolean'];
  addGroup?: Maybe<Group>;
  addInviteRequest?: Maybe<Scalars['String']>;
  addMembers?: Maybe<Array<Maybe<Member>>>;
  deleteGroup: Scalars['Boolean'];
  deleteInviteCode: Scalars['Boolean'];
  deleteInviteRequests?: Maybe<InviteCode>;
  editGroup?: Maybe<Group>;
  editMember?: Maybe<Member>;
  generateInviteCode?: Maybe<InviteCode>;
  moveAthletesToTeam?: Maybe<MoveAthletesResult>;
  removeMembers: Scalars['Boolean'];
  removeSeasonMembers: Scalars['Boolean'];
  updateInviteRequest?: Maybe<InviteCode>;
};


export type MutationAcceptInviteRequestsArgs = {
  memberIds: Array<InputMaybe<Scalars['String']>>;
  teamId: Scalars['String'];
};


export type MutationAddAthletesToSeasonArgs = {
  athleteIds: Array<InputMaybe<Scalars['String']>>;
  seasonId?: InputMaybe<Scalars['String']>;
  teamId: Scalars['String'];
};


export type MutationAddGroupArgs = {
  group: GroupInput;
  teamId: Scalars['String'];
};


export type MutationAddInviteRequestArgs = {
  inviteRequest: InviteRequestInput;
  teamCode: Scalars['String'];
};


export type MutationAddMembersArgs = {
  newMembers: Array<InputMaybe<MemberInput>>;
  source?: InputMaybe<Scalars['String']>;
  teamId: Scalars['String'];
};


export type MutationDeleteGroupArgs = {
  groupId: Scalars['String'];
  teamId: Scalars['String'];
};


export type MutationDeleteInviteCodeArgs = {
  teamId: Scalars['String'];
};


export type MutationDeleteInviteRequestsArgs = {
  memberIds: Array<InputMaybe<Scalars['String']>>;
  teamId: Scalars['String'];
};


export type MutationEditGroupArgs = {
  group: GroupInput;
  teamId: Scalars['String'];
};


export type MutationEditMemberArgs = {
  member: MemberInput;
  teamId: Scalars['String'];
};


export type MutationGenerateInviteCodeArgs = {
  teamId: Scalars['String'];
};


export type MutationMoveAthletesToTeamArgs = {
  destinationTeamId?: InputMaybe<Scalars['String']>;
  memberIds: Array<InputMaybe<Scalars['String']>>;
  sourceTeamId?: InputMaybe<Scalars['String']>;
};


export type MutationRemoveMembersArgs = {
  memberIds: Array<InputMaybe<Scalars['String']>>;
  teamId: Scalars['String'];
};


export type MutationRemoveSeasonMembersArgs = {
  memberIds: Array<InputMaybe<Scalars['String']>>;
  seasonId?: InputMaybe<Scalars['String']>;
  teamId: Scalars['String'];
};


export type MutationUpdateInviteRequestArgs = {
  inviteRequest: InviteRequestInput;
  teamId: Scalars['String'];
};

export type Organization = {
  __typename?: 'Organization';
  /** Classification ID of organization */
  classificationId: Scalars['Int'];
  /** Full name of organization */
  fullName?: Maybe<Scalars['String']>;
  /** The hudl Id of organization */
  internalId?: Maybe<Scalars['String']>;
  /** Type of organization */
  organizationType: OrganizationType;
};

export enum OrganizationType {
  Club = 'CLUB',
  College = 'COLLEGE',
  HighSchool = 'HIGH_SCHOOL',
  Juco = 'JUCO',
  MiddleSchool = 'MIDDLE_SCHOOL',
  Other = 'OTHER',
  Pro = 'PRO',
  Youth = 'YOUTH'
}

/** Information about pagination in a connection. */
export type PageInfo = {
  __typename?: 'PageInfo';
  /** When paginating forwards, the cursor to continue. */
  endCursor: Scalars['Cursor'];
  /** When paginating forwards, are there more items? */
  hasNextPage: Scalars['Boolean'];
  /** When paginating backwards, are there more items? */
  hasPreviousPage: Scalars['Boolean'];
  /** When paginating backwards, the cursor to continue. */
  startCursor: Scalars['Cursor'];
};

export type Query = {
  __typename?: 'Query';
  /** Get invite code by id */
  inviteCode?: Maybe<InviteCode>;
  /** Get a list of my teams */
  myTeams?: Maybe<Array<Maybe<Team>>>;
  /** Get team by id */
  team?: Maybe<Team>;
  /** Determine whether user can join team */
  validateInviteCodeForUser?: Maybe<InviteCodeWrapper>;
};


export type QueryInviteCodeArgs = {
  inviteCodeId: Scalars['String'];
};


export type QueryMyTeamsArgs = {
  membershipRoles?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
  requiredFeatures?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type QueryTeamArgs = {
  id?: InputMaybe<Scalars['String']>;
};


export type QueryValidateInviteCodeForUserArgs = {
  email: Scalars['String'];
  inviteCodeId: Scalars['String'];
};

export enum Role {
  Administrator = 'ADMINISTRATOR',
  Coach = 'COACH',
  Fan = 'FAN',
  Insider = 'INSIDER',
  None = 'NONE',
  Participant = 'PARTICIPANT',
  Recruiter = 'RECRUITER',
  Technique = 'TECHNIQUE'
}

export type Season = {
  __typename?: 'Season';
  description?: Maybe<Scalars['String']>;
  isMaxPrepsImported?: Maybe<Scalars['Boolean']>;
  value?: Maybe<Scalars['String']>;
  year: Scalars['Int'];
};

export enum Sport {
  AustralianRulesFootball = 'AUSTRALIAN_RULES_FOOTBALL',
  AustralianRulesFootballRecruiting = 'AUSTRALIAN_RULES_FOOTBALL_RECRUITING',
  Badminton = 'BADMINTON',
  BadmintonRecruiting = 'BADMINTON_RECRUITING',
  Baseball = 'BASEBALL',
  BaseballRecruiting = 'BASEBALL_RECRUITING',
  Basketball = 'BASKETBALL',
  BasketballRecruiting = 'BASKETBALL_RECRUITING',
  CheerAndSpirit = 'CHEER_AND_SPIRIT',
  CheerAndSpiritRecruiting = 'CHEER_AND_SPIRIT_RECRUITING',
  Cricket = 'CRICKET',
  CricketRecruiting = 'CRICKET_RECRUITING',
  CrossCountry = 'CROSS_COUNTRY',
  CrossCountryRecruiting = 'CROSS_COUNTRY_RECRUITING',
  Cycling = 'CYCLING',
  CyclingRecruiting = 'CYCLING_RECRUITING',
  DanceAndDrill = 'DANCE_AND_DRILL',
  DanceAndDrillRecruiting = 'DANCE_AND_DRILL_RECRUITING',
  Fencing = 'FENCING',
  FencingRecruiting = 'FENCING_RECRUITING',
  FieldHockey = 'FIELD_HOCKEY',
  FieldHockeyRecruiting = 'FIELD_HOCKEY_RECRUITING',
  Football = 'FOOTBALL',
  FootballRecruiting = 'FOOTBALL_RECRUITING',
  Golf = 'GOLF',
  GolfRecruiting = 'GOLF_RECRUITING',
  Gymnastics = 'GYMNASTICS',
  GymnasticsRecruiting = 'GYMNASTICS_RECRUITING',
  Handball = 'HANDBALL',
  HandballRecruiting = 'HANDBALL_RECRUITING',
  IceHockey = 'ICE_HOCKEY',
  IceHockeyRecruiting = 'ICE_HOCKEY_RECRUITING',
  Lacrosse = 'LACROSSE',
  LacrosseRecruiting = 'LACROSSE_RECRUITING',
  Netball = 'NETBALL',
  NetballRecruiting = 'NETBALL_RECRUITING',
  NoSport = 'NO_SPORT',
  Other = 'OTHER',
  PerformingArts = 'PERFORMING_ARTS',
  PerformingArtsRecruiting = 'PERFORMING_ARTS_RECRUITING',
  Rugby = 'RUGBY',
  RugbyLeague = 'RUGBY_LEAGUE',
  RugbyLeagueRecruiting = 'RUGBY_LEAGUE_RECRUITING',
  RugbyRecruiting = 'RUGBY_RECRUITING',
  RugbyUnion = 'RUGBY_UNION',
  RugbyUnionRecruiting = 'RUGBY_UNION_RECRUITING',
  SailingAndYachting = 'SAILING_AND_YACHTING',
  SailingAndYachtingRecruiting = 'SAILING_AND_YACHTING_RECRUITING',
  Soccer = 'SOCCER',
  SoccerRecruiting = 'SOCCER_RECRUITING',
  Softball = 'SOFTBALL',
  SoftballRecruiting = 'SOFTBALL_RECRUITING',
  Squash = 'SQUASH',
  SquashRecruiting = 'SQUASH_RECRUITING',
  Surfing = 'SURFING',
  SurfingRecruiting = 'SURFING_RECRUITING',
  SwimmingAndDiving = 'SWIMMING_AND_DIVING',
  SwimmingAndDivingRecruiting = 'SWIMMING_AND_DIVING_RECRUITING',
  Tennis = 'TENNIS',
  TennisRecruiting = 'TENNIS_RECRUITING',
  TenpinBowling = 'TENPIN_BOWLING',
  TenpinBowlingRecruiting = 'TENPIN_BOWLING_RECRUITING',
  Track = 'TRACK',
  TrackRecruiting = 'TRACK_RECRUITING',
  Volleyball = 'VOLLEYBALL',
  VolleyballRecruiting = 'VOLLEYBALL_RECRUITING',
  WaterPolo = 'WATER_POLO',
  WaterPoloRecruiting = 'WATER_POLO_RECRUITING',
  Wrestling = 'WRESTLING',
  WrestlingRecruiting = 'WRESTLING_RECRUITING'
}

export type Team = {
  __typename?: 'Team';
  /** Is the team opted out of Recruit */
  areAthletesRecruitable: Scalars['Boolean'];
  /** Cell Carriers */
  cellCarriers?: Maybe<Array<Maybe<Carrier>>>;
  /** The year of the current season. */
  currentSeasonYear?: Maybe<Scalars['Int']>;
  /** The gender of the team. */
  gender: Gender;
  /** One Group */
  group?: Maybe<Group>;
  /** Team groups */
  groups?: Maybe<GroupConnection>;
  /** Has max preps */
  hasMaxPreps: Scalars['Boolean'];
  /** The graphql Id of the team. */
  id: Scalars['ID'];
  /** The hudl Id of the team. */
  internalId?: Maybe<Scalars['String']>;
  /** Invite Code */
  inviteCode?: Maybe<InviteCode>;
  /** The team level */
  level: TeamLevel;
  /** The team logo URL. */
  logoUrl?: Maybe<Scalars['String']>;
  /** Get MaxPreps team */
  maxPrepsTeam?: Maybe<MaxPrepsTeam>;
  /** One member, use member id or internal member id */
  member?: Maybe<Member>;
  /** Team members */
  members?: Maybe<MemberConnection>;
  /** Team Members Additional Fields */
  membersAdditionalFields?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Suggested Team members to remove */
  membersToRemove?: Maybe<MemberConnection>;
  /** The name of the team. */
  name?: Maybe<Scalars['String']>;
  /** Organization */
  organization?: Maybe<Organization>;
  /** Team positions */
  positions?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** The User's roles on the team */
  role: Role;
  /** Seasons */
  seasons?: Maybe<Array<Maybe<Season>>>;
  /** The sport that the team plays. */
  sport: Sport;
};


export type TeamGroupArgs = {
  groupId?: InputMaybe<Scalars['String']>;
};


export type TeamGroupsArgs = {
  after?: InputMaybe<Scalars['String']>;
  before?: InputMaybe<Scalars['String']>;
  first?: InputMaybe<Scalars['Int']>;
  includeFamilyMembers?: InputMaybe<Scalars['Boolean']>;
  last?: InputMaybe<Scalars['Int']>;
};


export type TeamMaxPrepsTeamArgs = {
  seasonYear?: InputMaybe<Scalars['Int']>;
};


export type TeamMemberArgs = {
  memberId?: InputMaybe<Scalars['String']>;
  memberInternalId?: InputMaybe<Scalars['String']>;
};


export type TeamMembersArgs = {
  after?: InputMaybe<Scalars['String']>;
  before?: InputMaybe<Scalars['String']>;
  disabledOnly?: InputMaybe<Scalars['Boolean']>;
  first?: InputMaybe<Scalars['Int']>;
  groupId?: InputMaybe<Scalars['String']>;
  last?: InputMaybe<Scalars['Int']>;
  roles?: InputMaybe<Array<InputMaybe<Scalars['String']>>>;
};


export type TeamMembersToRemoveArgs = {
  after?: InputMaybe<Scalars['String']>;
  before?: InputMaybe<Scalars['String']>;
  first?: InputMaybe<Scalars['Int']>;
  last?: InputMaybe<Scalars['Int']>;
};

export type TeamHeader = {
  __typename?: 'TeamHeader';
  /** Team additional Fields */
  additionalFields?: Maybe<Array<Maybe<Scalars['String']>>>;
  /** Cell Carriers */
  cellCarriers?: Maybe<Array<Maybe<Carrier>>>;
  /** Current season year */
  currentSeasonYear?: Maybe<Scalars['Int']>;
  /** Team id */
  id?: Maybe<Scalars['String']>;
  /** Team logo */
  logo?: Maybe<Scalars['String']>;
  /** Team name */
  name?: Maybe<Scalars['String']>;
  /** Team Organization */
  organizationName?: Maybe<Scalars['String']>;
  /** Positions */
  positions?: Maybe<Array<Maybe<Scalars['String']>>>;
};

export enum TeamLevel {
  Freshman = 'FRESHMAN',
  JuniorVarsity = 'JUNIOR_VARSITY',
  Other = 'OTHER',
  OtherNonHs = 'OTHER_NON_HS',
  Sophomore = 'SOPHOMORE',
  Varsity = 'VARSITY'
}

export enum UserStatus {
  Active = 'ACTIVE',
  CreatedWithoutAnEmailOrUsername = 'CREATED_WITHOUT_AN_EMAIL_OR_USERNAME',
  Disabled = 'DISABLED',
  HasNeverLoggedIn = 'HAS_NEVER_LOGGED_IN'
}

export type VideoActivity = {
  __typename?: 'VideoActivity';
  /** Video watched this week, formatted and with an empty indicator */
  formattedVideoWatchedThisWeek?: Maybe<Scalars['String']>;
  /** Video Watched This Week */
  videoWatchedThisWeekInSeconds: Scalars['Int'];
  /** Video Watched Today */
  videoWatchedTodayInSeconds: Scalars['Int'];
  /** Video Watched Yesterday */
  videoWatchedYesterdayInSeconds: Scalars['Int'];
};

export type WithIndex<TObject> = TObject & Record<string, any>;
export type ResolversObject<TObject> = WithIndex<TObject>;

export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = ResolversObject<{
  AthleteInviteRequestValidationStatus: AthleteInviteRequestValidationStatus;
  Boolean: ResolverTypeWrapper<Scalars['Boolean']>;
  Carrier: ResolverTypeWrapper<Carrier>;
  Cursor: ResolverTypeWrapper<Scalars['Cursor']>;
  Date: ResolverTypeWrapper<Scalars['Date']>;
  DateTime: ResolverTypeWrapper<Scalars['DateTime']>;
  DateTimeOffset: ResolverTypeWrapper<Scalars['DateTimeOffset']>;
  Decimal: ResolverTypeWrapper<Scalars['Decimal']>;
  Gender: Gender;
  Group: ResolverTypeWrapper<Group>;
  GroupConnection: ResolverTypeWrapper<GroupConnection>;
  GroupEdge: ResolverTypeWrapper<GroupEdge>;
  GroupInput: GroupInput;
  ID: ResolverTypeWrapper<Scalars['ID']>;
  IDConnection: ResolverTypeWrapper<IdConnection>;
  IDEdge: ResolverTypeWrapper<IdEdge>;
  INode: ResolversTypes['Member'];
  Int: ResolverTypeWrapper<Scalars['Int']>;
  InviteCode: ResolverTypeWrapper<InviteCode>;
  InviteCodeWrapper: ResolverTypeWrapper<InviteCodeWrapper>;
  InviteRequest: ResolverTypeWrapper<InviteRequest>;
  InviteRequestInput: InviteRequestInput;
  MaxPrepsTeam: ResolverTypeWrapper<MaxPrepsTeam>;
  Member: ResolverTypeWrapper<Member>;
  MemberConnection: ResolverTypeWrapper<MemberConnection>;
  MemberEdge: ResolverTypeWrapper<MemberEdge>;
  MemberInput: MemberInput;
  Milliseconds: ResolverTypeWrapper<Scalars['Milliseconds']>;
  MoveAthletesResult: ResolverTypeWrapper<MoveAthletesResult>;
  Mutation: ResolverTypeWrapper<{}>;
  Organization: ResolverTypeWrapper<Organization>;
  OrganizationType: OrganizationType;
  PageInfo: ResolverTypeWrapper<PageInfo>;
  Query: ResolverTypeWrapper<{}>;
  Role: Role;
  Season: ResolverTypeWrapper<Season>;
  Seconds: ResolverTypeWrapper<Scalars['Seconds']>;
  Sport: Sport;
  String: ResolverTypeWrapper<Scalars['String']>;
  Team: ResolverTypeWrapper<Team>;
  TeamHeader: ResolverTypeWrapper<TeamHeader>;
  TeamLevel: TeamLevel;
  UserStatus: UserStatus;
  VideoActivity: ResolverTypeWrapper<VideoActivity>;
}>;

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = ResolversObject<{
  Boolean: Scalars['Boolean'];
  Carrier: Carrier;
  Cursor: Scalars['Cursor'];
  Date: Scalars['Date'];
  DateTime: Scalars['DateTime'];
  DateTimeOffset: Scalars['DateTimeOffset'];
  Decimal: Scalars['Decimal'];
  Group: Group;
  GroupConnection: GroupConnection;
  GroupEdge: GroupEdge;
  GroupInput: GroupInput;
  ID: Scalars['ID'];
  IDConnection: IdConnection;
  IDEdge: IdEdge;
  INode: ResolversParentTypes['Member'];
  Int: Scalars['Int'];
  InviteCode: InviteCode;
  InviteCodeWrapper: InviteCodeWrapper;
  InviteRequest: InviteRequest;
  InviteRequestInput: InviteRequestInput;
  MaxPrepsTeam: MaxPrepsTeam;
  Member: Member;
  MemberConnection: MemberConnection;
  MemberEdge: MemberEdge;
  MemberInput: MemberInput;
  Milliseconds: Scalars['Milliseconds'];
  MoveAthletesResult: MoveAthletesResult;
  Mutation: {};
  Organization: Organization;
  PageInfo: PageInfo;
  Query: {};
  Season: Season;
  Seconds: Scalars['Seconds'];
  String: Scalars['String'];
  Team: Team;
  TeamHeader: TeamHeader;
  VideoActivity: VideoActivity;
}>;

export type CarrierResolvers<ContextType = any, ParentType extends ResolversParentTypes['Carrier'] = ResolversParentTypes['Carrier']> = ResolversObject<{
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface CursorScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Cursor'], any> {
  name: 'Cursor';
}

export interface DateScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Date'], any> {
  name: 'Date';
}

export interface DateTimeScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['DateTime'], any> {
  name: 'DateTime';
}

export interface DateTimeOffsetScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['DateTimeOffset'], any> {
  name: 'DateTimeOffset';
}

export interface DecimalScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Decimal'], any> {
  name: 'Decimal';
}

export type GroupResolvers<ContextType = any, ParentType extends ResolversParentTypes['Group'] = ResolversParentTypes['Group']> = ResolversObject<{
  dateCreated?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  internalId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  isSystemGroup?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  memberIds?: Resolver<Maybe<ResolversTypes['IDConnection']>, ParentType, ContextType, RequireFields<GroupMemberIdsArgs, 'after' | 'before' | 'first' | 'last'>>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  videoActivity?: Resolver<Maybe<ResolversTypes['VideoActivity']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GroupConnectionResolvers<ContextType = any, ParentType extends ResolversParentTypes['GroupConnection'] = ResolversParentTypes['GroupConnection']> = ResolversObject<{
  edges?: Resolver<Maybe<Array<Maybe<ResolversTypes['GroupEdge']>>>, ParentType, ContextType>;
  items?: Resolver<Maybe<Array<Maybe<ResolversTypes['Group']>>>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type GroupEdgeResolvers<ContextType = any, ParentType extends ResolversParentTypes['GroupEdge'] = ResolversParentTypes['GroupEdge']> = ResolversObject<{
  cursor?: Resolver<ResolversTypes['Cursor'], ParentType, ContextType>;
  node?: Resolver<Maybe<ResolversTypes['Group']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type IdConnectionResolvers<ContextType = any, ParentType extends ResolversParentTypes['IDConnection'] = ResolversParentTypes['IDConnection']> = ResolversObject<{
  edges?: Resolver<Maybe<Array<Maybe<ResolversTypes['IDEdge']>>>, ParentType, ContextType>;
  items?: Resolver<Maybe<Array<ResolversTypes['ID']>>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type IdEdgeResolvers<ContextType = any, ParentType extends ResolversParentTypes['IDEdge'] = ResolversParentTypes['IDEdge']> = ResolversObject<{
  cursor?: Resolver<ResolversTypes['Cursor'], ParentType, ContextType>;
  node?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type INodeResolvers<ContextType = any, ParentType extends ResolversParentTypes['INode'] = ResolversParentTypes['INode']> = ResolversObject<{
  __resolveType: TypeResolveFn<'Member', ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
}>;

export type InviteCodeResolvers<ContextType = any, ParentType extends ResolversParentTypes['InviteCode'] = ResolversParentTypes['InviteCode']> = ResolversObject<{
  dateCreated?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  inviteRequests?: Resolver<Maybe<Array<Maybe<ResolversTypes['InviteRequest']>>>, ParentType, ContextType>;
  lastModifiedDate?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  team?: Resolver<Maybe<ResolversTypes['TeamHeader']>, ParentType, ContextType>;
  teamId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type InviteCodeWrapperResolvers<ContextType = any, ParentType extends ResolversParentTypes['InviteCodeWrapper'] = ResolversParentTypes['InviteCodeWrapper']> = ResolversObject<{
  inviteCode?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType>;
  teamJoinEligibilityStatus?: Resolver<ResolversTypes['AthleteInviteRequestValidationStatus'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type InviteRequestResolvers<ContextType = any, ParentType extends ResolversParentTypes['InviteRequest'] = ResolversParentTypes['InviteRequest']> = ResolversObject<{
  cellCarrier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  cellPhone?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  dateCreated?: Resolver<ResolversTypes['DateTime'], ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  graduationYear?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  jersey?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  position?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  role?: Resolver<ResolversTypes['Role'], ParentType, ContextType>;
  userId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MaxPrepsTeamResolvers<ContextType = any, ParentType extends ResolversParentTypes['MaxPrepsTeam'] = ResolversParentTypes['MaxPrepsTeam']> = ResolversObject<{
  hasRoster?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hasSchedule?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hudlSchoolId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  hudlSeason?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  hudlSport?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  maxPrepsName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  maxPrepsSchoolId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  seasonId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  seasonYear?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  sportId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  teamId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MemberResolvers<ContextType = any, ParentType extends ResolversParentTypes['Member'] = ResolversParentTypes['Member']> = ResolversObject<{
  cell?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  cellCarrier?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  email?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  firstName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  fullName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  graduationYear?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  groups?: Resolver<Maybe<ResolversTypes['IDConnection']>, ParentType, ContextType, RequireFields<MemberGroupsArgs, 'after' | 'before' | 'first' | 'includeFamilyMembers' | 'last'>>;
  height?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  internalId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  isDisabled?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  isOptedInToRecruit?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  jersey?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  lastLoginDate?: Resolver<Maybe<ResolversTypes['DateTime']>, ParentType, ContextType>;
  lastName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  picture?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  pictureUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  position?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  role?: Resolver<ResolversTypes['Role'], ParentType, ContextType>;
  seasonIds?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  status?: Resolver<ResolversTypes['UserStatus'], ParentType, ContextType>;
  videoActivity?: Resolver<Maybe<ResolversTypes['VideoActivity']>, ParentType, ContextType>;
  weight?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MemberConnectionResolvers<ContextType = any, ParentType extends ResolversParentTypes['MemberConnection'] = ResolversParentTypes['MemberConnection']> = ResolversObject<{
  edges?: Resolver<Maybe<Array<Maybe<ResolversTypes['MemberEdge']>>>, ParentType, ContextType>;
  items?: Resolver<Maybe<Array<Maybe<ResolversTypes['Member']>>>, ParentType, ContextType>;
  pageInfo?: Resolver<ResolversTypes['PageInfo'], ParentType, ContextType>;
  totalCount?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MemberEdgeResolvers<ContextType = any, ParentType extends ResolversParentTypes['MemberEdge'] = ResolversParentTypes['MemberEdge']> = ResolversObject<{
  cursor?: Resolver<ResolversTypes['Cursor'], ParentType, ContextType>;
  node?: Resolver<Maybe<ResolversTypes['Member']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface MillisecondsScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Milliseconds'], any> {
  name: 'Milliseconds';
}

export type MoveAthletesResultResolvers<ContextType = any, ParentType extends ResolversParentTypes['MoveAthletesResult'] = ResolversParentTypes['MoveAthletesResult']> = ResolversObject<{
  copied?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  existing?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type MutationResolvers<ContextType = any, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = ResolversObject<{
  acceptInviteRequests?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType, RequireFields<MutationAcceptInviteRequestsArgs, 'memberIds' | 'teamId'>>;
  addAthletesToSeason?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationAddAthletesToSeasonArgs, 'athleteIds' | 'seasonId' | 'teamId'>>;
  addGroup?: Resolver<Maybe<ResolversTypes['Group']>, ParentType, ContextType, RequireFields<MutationAddGroupArgs, 'group' | 'teamId'>>;
  addInviteRequest?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType, RequireFields<MutationAddInviteRequestArgs, 'inviteRequest' | 'teamCode'>>;
  addMembers?: Resolver<Maybe<Array<Maybe<ResolversTypes['Member']>>>, ParentType, ContextType, RequireFields<MutationAddMembersArgs, 'newMembers' | 'source' | 'teamId'>>;
  deleteGroup?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationDeleteGroupArgs, 'groupId' | 'teamId'>>;
  deleteInviteCode?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationDeleteInviteCodeArgs, 'teamId'>>;
  deleteInviteRequests?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType, RequireFields<MutationDeleteInviteRequestsArgs, 'memberIds' | 'teamId'>>;
  editGroup?: Resolver<Maybe<ResolversTypes['Group']>, ParentType, ContextType, RequireFields<MutationEditGroupArgs, 'group' | 'teamId'>>;
  editMember?: Resolver<Maybe<ResolversTypes['Member']>, ParentType, ContextType, RequireFields<MutationEditMemberArgs, 'member' | 'teamId'>>;
  generateInviteCode?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType, RequireFields<MutationGenerateInviteCodeArgs, 'teamId'>>;
  moveAthletesToTeam?: Resolver<Maybe<ResolversTypes['MoveAthletesResult']>, ParentType, ContextType, RequireFields<MutationMoveAthletesToTeamArgs, 'destinationTeamId' | 'memberIds' | 'sourceTeamId'>>;
  removeMembers?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationRemoveMembersArgs, 'memberIds' | 'teamId'>>;
  removeSeasonMembers?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationRemoveSeasonMembersArgs, 'memberIds' | 'seasonId' | 'teamId'>>;
  updateInviteRequest?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType, RequireFields<MutationUpdateInviteRequestArgs, 'inviteRequest' | 'teamId'>>;
}>;

export type OrganizationResolvers<ContextType = any, ParentType extends ResolversParentTypes['Organization'] = ResolversParentTypes['Organization']> = ResolversObject<{
  classificationId?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  fullName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  internalId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  organizationType?: Resolver<ResolversTypes['OrganizationType'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type PageInfoResolvers<ContextType = any, ParentType extends ResolversParentTypes['PageInfo'] = ResolversParentTypes['PageInfo']> = ResolversObject<{
  endCursor?: Resolver<ResolversTypes['Cursor'], ParentType, ContextType>;
  hasNextPage?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  hasPreviousPage?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  startCursor?: Resolver<ResolversTypes['Cursor'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type QueryResolvers<ContextType = any, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = ResolversObject<{
  inviteCode?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType, RequireFields<QueryInviteCodeArgs, 'inviteCodeId'>>;
  myTeams?: Resolver<Maybe<Array<Maybe<ResolversTypes['Team']>>>, ParentType, ContextType, RequireFields<QueryMyTeamsArgs, 'membershipRoles' | 'requiredFeatures'>>;
  team?: Resolver<Maybe<ResolversTypes['Team']>, ParentType, ContextType, RequireFields<QueryTeamArgs, 'id'>>;
  validateInviteCodeForUser?: Resolver<Maybe<ResolversTypes['InviteCodeWrapper']>, ParentType, ContextType, RequireFields<QueryValidateInviteCodeForUserArgs, 'email' | 'inviteCodeId'>>;
}>;

export type SeasonResolvers<ContextType = any, ParentType extends ResolversParentTypes['Season'] = ResolversParentTypes['Season']> = ResolversObject<{
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  isMaxPrepsImported?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  value?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  year?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export interface SecondsScalarConfig extends GraphQLScalarTypeConfig<ResolversTypes['Seconds'], any> {
  name: 'Seconds';
}

export type TeamResolvers<ContextType = any, ParentType extends ResolversParentTypes['Team'] = ResolversParentTypes['Team']> = ResolversObject<{
  areAthletesRecruitable?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  cellCarriers?: Resolver<Maybe<Array<Maybe<ResolversTypes['Carrier']>>>, ParentType, ContextType>;
  currentSeasonYear?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  gender?: Resolver<ResolversTypes['Gender'], ParentType, ContextType>;
  group?: Resolver<Maybe<ResolversTypes['Group']>, ParentType, ContextType, RequireFields<TeamGroupArgs, 'groupId'>>;
  groups?: Resolver<Maybe<ResolversTypes['GroupConnection']>, ParentType, ContextType, RequireFields<TeamGroupsArgs, 'after' | 'before' | 'first' | 'includeFamilyMembers' | 'last'>>;
  hasMaxPreps?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  internalId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  inviteCode?: Resolver<Maybe<ResolversTypes['InviteCode']>, ParentType, ContextType>;
  level?: Resolver<ResolversTypes['TeamLevel'], ParentType, ContextType>;
  logoUrl?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  maxPrepsTeam?: Resolver<Maybe<ResolversTypes['MaxPrepsTeam']>, ParentType, ContextType, RequireFields<TeamMaxPrepsTeamArgs, 'seasonYear'>>;
  member?: Resolver<Maybe<ResolversTypes['Member']>, ParentType, ContextType, RequireFields<TeamMemberArgs, 'memberId' | 'memberInternalId'>>;
  members?: Resolver<Maybe<ResolversTypes['MemberConnection']>, ParentType, ContextType, RequireFields<TeamMembersArgs, 'after' | 'before' | 'disabledOnly' | 'first' | 'groupId' | 'last' | 'roles'>>;
  membersAdditionalFields?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  membersToRemove?: Resolver<Maybe<ResolversTypes['MemberConnection']>, ParentType, ContextType, RequireFields<TeamMembersToRemoveArgs, 'after' | 'before' | 'first' | 'last'>>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  organization?: Resolver<Maybe<ResolversTypes['Organization']>, ParentType, ContextType>;
  positions?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  role?: Resolver<ResolversTypes['Role'], ParentType, ContextType>;
  seasons?: Resolver<Maybe<Array<Maybe<ResolversTypes['Season']>>>, ParentType, ContextType>;
  sport?: Resolver<ResolversTypes['Sport'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type TeamHeaderResolvers<ContextType = any, ParentType extends ResolversParentTypes['TeamHeader'] = ResolversParentTypes['TeamHeader']> = ResolversObject<{
  additionalFields?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  cellCarriers?: Resolver<Maybe<Array<Maybe<ResolversTypes['Carrier']>>>, ParentType, ContextType>;
  currentSeasonYear?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  id?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  logo?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  organizationName?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  positions?: Resolver<Maybe<Array<Maybe<ResolversTypes['String']>>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type VideoActivityResolvers<ContextType = any, ParentType extends ResolversParentTypes['VideoActivity'] = ResolversParentTypes['VideoActivity']> = ResolversObject<{
  formattedVideoWatchedThisWeek?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  videoWatchedThisWeekInSeconds?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  videoWatchedTodayInSeconds?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  videoWatchedYesterdayInSeconds?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
}>;

export type Resolvers<ContextType = any> = ResolversObject<{
  Carrier?: CarrierResolvers<ContextType>;
  Cursor?: GraphQLScalarType;
  Date?: GraphQLScalarType;
  DateTime?: GraphQLScalarType;
  DateTimeOffset?: GraphQLScalarType;
  Decimal?: GraphQLScalarType;
  Group?: GroupResolvers<ContextType>;
  GroupConnection?: GroupConnectionResolvers<ContextType>;
  GroupEdge?: GroupEdgeResolvers<ContextType>;
  IDConnection?: IdConnectionResolvers<ContextType>;
  IDEdge?: IdEdgeResolvers<ContextType>;
  INode?: INodeResolvers<ContextType>;
  InviteCode?: InviteCodeResolvers<ContextType>;
  InviteCodeWrapper?: InviteCodeWrapperResolvers<ContextType>;
  InviteRequest?: InviteRequestResolvers<ContextType>;
  MaxPrepsTeam?: MaxPrepsTeamResolvers<ContextType>;
  Member?: MemberResolvers<ContextType>;
  MemberConnection?: MemberConnectionResolvers<ContextType>;
  MemberEdge?: MemberEdgeResolvers<ContextType>;
  Milliseconds?: GraphQLScalarType;
  MoveAthletesResult?: MoveAthletesResultResolvers<ContextType>;
  Mutation?: MutationResolvers<ContextType>;
  Organization?: OrganizationResolvers<ContextType>;
  PageInfo?: PageInfoResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  Season?: SeasonResolvers<ContextType>;
  Seconds?: GraphQLScalarType;
  Team?: TeamResolvers<ContextType>;
  TeamHeader?: TeamHeaderResolvers<ContextType>;
  VideoActivity?: VideoActivityResolvers<ContextType>;
}>;

