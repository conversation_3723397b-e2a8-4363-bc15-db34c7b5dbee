module.exports = {
  preset: 'react-native',
  setupFiles: [
    './app/__fixtures__/JestSetup.ts',
    './node_modules/react-native-gesture-handler/jestSetup.js',
  ],
  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?react-native|@react-native|@react-navigation|crypto-es|@hudl)',
  ],
  collectCoverage: true,
  collectCoverageFrom: ['app/**/*.{ts,tsx}'],
  moduleNameMapper: {
    '\\.ttf': '<rootDir>/node_modules/react-native/jest/assetFileTransformer.js',
  },
};
