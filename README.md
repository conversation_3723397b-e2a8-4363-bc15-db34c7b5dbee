# Hudl Ticketing Mobile

* [Setup](#setup)
  * [Quickstart](#quickstart)
  * [Building the native apps (advanced)](#building-the-native-apps-advanced)
* [GraphQL](#graphql)
* [Unit testing](#unit-testing)
* [Installing builds](#installing-builds)
* [Releasing a New Version of Ticket Reader](#releasing-a-new-version-of-ticket-reader)

## Setup

> If you need help getting set up and running, please reach out to [#ask-mobile-platform](https://hudl.slack.com/archives/CBNKRH6BY) on Slack.

### Quickstart

The `rn-ticketing` project is fully powered by [<PERSON>](https://apps.hudltools.com/mobile/guides-and-concepts/jarvis#overview), meaning you only need a few things to get up and running.

All you need is NodeJS, Yarn, VS Code, and the [Jarvis Client mobile app](https://apps.hudltools.com/mobile/guides-and-concepts/firebase-app-distribution#jarvis-client) installed on your iOS or Android device. To setup these tools follow the [installation](https://apps.hudltools.com/mobile) guide from our Hudl React Native documentation.

Once you have the required tooling you can checkout and run the rn-ticketing app on the Jarvis Client:

**Clone the repo**

```bash
<NAME_EMAIL>:hudl/rn-ticketing.git
cd rn-ticketing
````

**Ensure you're using the right [NodeJS version](https://github.com/hudl/rn-ticketing/blob/main/.nvmrc)**

```bash
nvm use
```

**Install Node dependencies**

```bash
yarn install
```

**Run the project on the Jarvis Client app**

```bash
yarn start -t

**Scan QR code in Jarvis mobile app**
```

> _You can also install the Jarvis Client on an Android emulator or iOS Simulator. Details [here](https://apps.hudltools.com/mobile/guides-and-concepts/developing/local-development?device-type=emulator-simulator#launch-the-jarvis-client-app)._

## Developer Mode
Developer mode allows you to access a handful of toggles and features that are useful for development, such as pointing the app at a specific thor branch, simulating various stripe readers and cards, and more. To enable developer mode, click `Get Started` on the landing screen, fill out any first name, last name, and email, then enter `hud1d3v` as the access code. Hit `Continue`. You should be taken back to the landing screen, now with a `Developer Mode` button at the top. Click that to access the developer mode settings. You can now access the developer panel by clicking the dropper icon on the right side of the header on any screen.

## Building the native apps (advanced)

The [quickstart](#quickstart) guide above is the recommended way to develop for this project, but if you'd prefer, you can also build and run the full Android and/or iOS app locally using native tools like Android Studio and Xcode.

### Setup

This guide assumes you are have already completed the [installation](https://apps.hudltools.com/mobile) section of Hudl's React Native docs. Once you have done that you can then follow the [Advanced Developer Setup guide](https://apps.hudltools.com/mobile/getting-started/developer-setup) for installing and setting up Android Studio and Xcode.

### Running

To build and run the iOS app:

```bash
yarn setup:ios
yarn ios
```

To build and run the Hudl Android app:

```bash
yarn setup:android
yarn android
```

> _Note: You can use Android Studio and Xcode to run the native projects, but using the React Native CLI via the commands above is recommended (especially for Android). For Android, the React Native CLI will automatically start the React Native packager, and run an "adb reverse" command in order for your Android device or emulator to communicate with the packager. If you run straight from Android Studio you'll have to manually start the React Native packager and run `yarn jarvis adb rc`._

## GraphQL

This project pulls data from Hudl's [GraphQL](https://graphql.org/) endpoints using [Apollo Client](https://www.apollographql.com/docs/react/). TypeScript types are generated for GQL queries and mutations using the [GraphQL Code Generator CLI](https://the-guild.dev/graphql/codegen).    

### Downloading updated schemas

There will be occasions when one or more of the GQL schemas for hudl.com GQL endpoints has changed, meaning the local copies of `schema.graphql` within the `gql-schemas/` directory are out of date. The local schemas can be updated with the following command:

> Note: This script requires access to thorhudl.com, so make sure you are connected to the Hudl VPN or have allow listed your machines IP using [Chronos](https://chronos.hudltools.com/).

```
yarn gql-download-schemas
```

#### Downloading schemas from custom thor branch

By default the Hudl GQL schemas are downloaded from https://master.thorhudl.com. This can be changed in your local environment by updating the `gqlSchemaBaseURL` property within your `.dev.json` file, and then re-running `yarn gql-download-schemas`.

> Note: This should only be used for local development purposes and any resulting changes to the schema files under `gql-schemas/`, should NOT be merged.

### Generating TypeScript types for GQL Queries and Mutations

When one of the local `schema.graphql` files has been updated, or when you have updated a GQL query/mutation file, you can generate the latest TypeScript types with the following command:

```
yarn gql-codegen
```

### Writing Queries and Mutations

GraphQL Queries and Mutations used by the Hudl Ticketing app are located in `app/gql/*`. Take a look at the example below:

```typescript
// app/gql/hudl/MyQuery.ts
import { graphql } from '../__generated__/gql';

export default graphql(`
  query RN_Ticketing_MyQuery_r1 {
    me {
      firstName
      lastName
    }
  }
`);
```

> You'll notice the query name above follows a convention like `RN_Ticketing_<query name>_<revision>`. This is a naming convention that we follow at Hudl. For more info check out [GraphQL+Operation+Name+Convention](https://sync.hudlnet.com/display/DEV/GraphQL+Operation+Name+Convention) on Sync.

After writing the query above and running `yarn gql-codegen`, you'll be able to make a fully typed GQL query in your React Function Components like so:

```typescript
import { useQuery } from '@apollo/client';

// Fully typed 'data' object
const { data } = useQuery(MyQuery);
```

#### Local Apollo Server

To assist in writing GQL queries and mutations you can use the local apollo server that is set up in the project. Take a look at the [README](./local-apollo-server/README.md).

## Unit testing

Unit tests can be written for rn-ticketing using the Jest testing framework, along with [React Native Testing Library](https://testing-library.com/docs/react-native-testing-library/intro/). 

To run the unit tests, use the following command:

```bash
yarn test
```

## Installing builds 

### From Firebase

Android and iOS builds for the Hudl Ticketing App are published to Firebase for all commits, and are available to anyone with an `@hudl.com` email address.

Sign up for Firebase App Distribution using the following links depending on your mobile device:

* [iOS (Enterprise)](https://appdistribution.firebase.dev/i/2e29d27c8ef52956)
* [Android](https://appdistribution.firebase.dev/i/a54712b56136241a)

> Head over to the [Firebase App Distribution](https://apps.hudltools.com/mobile/guides-and-concepts/firebase-app-distribution) page for step-by-step instructions on installing an app onto your device.

## Releasing a New Version of Ticket Reader
See [RELEASE_INSTRUCTIONS.md](./RELEASE_INSTRUCTIONS.md) 
