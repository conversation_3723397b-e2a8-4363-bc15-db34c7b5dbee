// #region Ignore direct RN dependencies added to package.json that contain native code

/*
This project is fully powered by <PERSON> meaning it gets all of its native capabilities from the @hudl/jarvis package.

So by default, any React Native library added directly to the project's package.json that contains native
code, will be ignored by the React Native native autolinking scripts. Note: you can still add RN libs that 
don't contain native code, or any other JavaScript based lib. 

Please take a look at https://github.com/hudl/jarvis#usage for all the native capabilities <PERSON> currently
provides.

If you require a native platform capability that the RN core framework or Jarvis doesn't provide, please
consult with Mobile Platform Team (#ask-mobile-platform) on the best way forward. New APIs can either be 
added directly to the @hudl/jarvis lib, or the Jarvis native runtime can be updated with a reputable (and well 
maintained) third party lib that suits your needs.

If you wish to test out a new RN library that contains native code (or Mobile Platform Team have advised
that an exception is allowed for this new library), then you can add it to the 'allowedDirectDependencies' 
array below.
*/

// Add any exceptions to the above rule here. e.g. 'react-native-some-cool-lib'.
const allowedDirectDependencies = ['react-native-vision-camera', '@stripe/stripe-terminal-react-native'];

const projectDependencies = require(`${__dirname}/package.json`).dependencies;
const ignoredDependencies = {};

for (const [key] of Object.entries(projectDependencies)) {
  if (allowedDirectDependencies.includes(key)) {
    continue;
  }

  ignoredDependencies[key] = {
    platforms: {
      ios: null,
      android: null,
    },
  };
}
// #endregion

const { configFor } = require(`${__dirname}/node_modules/@hudl/jarvis/native-runtime-auto-link`);

// Build native configuration for the React Native project.
module.exports = {
  dependencies: {
    // Ignore any direct native dependency that gets added to the project
    // since every native capability should come from Jarvis.
    ...ignoredDependencies,

    // Auto link Jarvis native runtime
    ...configFor({
      thirdPartyInclude: [
        '@sentry/react-native',
        'react-native-detox-context',
        'react-native-linear-gradient',
        '@stripe/stripe-terminal-react-native',
        'react-native-permissions',
        'react-native-zip-archive',
        'react-native-snackbar',
        'react-native-get-random-values',
      ]
    }).dependencies,

    // Ticketing doesn't use the auth APIs from Jarvis, and the version of react-native-auth0
    // current used in Jarvis is not compatible with the new RN architecture. So we can safetly
    // unlink it for now.
    'react-native-auth0': {
      platforms: {
        android: null
      }
    }
  },
};
