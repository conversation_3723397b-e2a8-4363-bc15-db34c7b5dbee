.DS_Store

node_modules/
npm-debug.log
yarn-error.log
package-lock.json

.vscode/.react
.vscode/launchReactNative.js
.vscode/typings/

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Jarvis
.bundles
.jarvis

# Ruby / CocoaPods
**/Pods/
/vendor/bundle/

# Fastlane
fastlane/README.md
fastlane/logs/
fastlane/report.xml
ios-output/
android-output/
ios/export-output

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
**/.xcode.env.local
ios/HudlTicketing.xcarchive/
ios/HudlTicketing.xcworkspace/xcshareddata/

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore
.kotlin/

# Jest
output.json
coverage/

# Local Dev
local-apollo-server/dist
.dev.json

# Bundle artifact
*.jsbundle
ios/main.jsbundle.map

# testing
/coverage

# Expo
.expo/
dist/
web-build/
expo-env.d.ts