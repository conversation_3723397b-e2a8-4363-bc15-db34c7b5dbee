import './app/strings/Locales';

import { AppRegistry } from 'react-native';

import { JarvisConstants } from '@hudl/jarvis/client';

import 'react-native-get-random-values';

import App from './app/App';

if (JarvisConstants.isRunningOnCustomJarvisClient()) {
  AppRegistry.registerComponent(
    'JarvisClient',
    // Lazy load Jarvis Client as it's only applicable for development
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    () => require('@hudl/jarvis/client/internal/JarvisClientHome.component').default
  );
}

AppRegistry.registerComponent('main', () => App);
