name: Setup Android Environment

inputs:
  alyx-ssh-key:
    required: true
    description: Alyx SSH key for installing private Ruby Gems.

runs:
  using: "composite"
  steps:
    - name: Setup Ruby
      uses: hudl/mobile-github-workflows/setup-ruby@main

    - name: Setup Java and Gradle
      uses: hudl/mobile-github-workflows/setup-java-and-gradle@main
      with:
        java-version: 17

    - name: Setup Android SDK
      uses: android-actions/setup-android@7c5672355aaa8fde5f97a91aa9a99616d1ace6bc

    - name: Setup Node Environment
      uses: hudl/mobile-github-workflows/setup-node-and-yarn@main

    - name: Yarn 2 Cache
      uses: hudl/mobile-github-workflows/setup-yarn2-cache@main

    - name: Bundle Cache
      uses: actions/cache@v4
      with:
        path: ./vendor/bundle
        key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}

    - name: Install Node Dependencies
      run: |
        yarn install --immutable
        yarn setup:android
      shell: bash

    - name: Install Ruby Dependencies
      run: |
        eval `ssh-agent -s`
        echo "${{ inputs.alyx-ssh-key }}" | ssh-add -
        bundle install
      shell: bash