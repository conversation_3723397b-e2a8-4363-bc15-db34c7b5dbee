name: Setup iOS Environment

inputs:
  alyx-ssh-key:
    required: true
    description: Alyx SSH key for installing private Ruby Gems.

runs:
  using: "composite"
  steps:
    - name: Setup Xcode
      uses: hudl/mobile-github-workflows/setup-xcode@main

    - name: Setup Ruby
      uses: hudl/mobile-github-workflows/setup-ruby@main
  
    - name: Setup Node Environment
      uses: hudl/mobile-github-workflows/setup-node-and-yarn@main

    - name: Yarn 2 Cache
      uses: hudl/mobile-github-workflows/setup-yarn2-cache@main

    - name: Bundle Cache
      uses: actions/cache@v4
      with:
        path: ./vendor/bundle
        key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}

    - name: Pods Cache
      uses: actions/cache@v4
      with:
        path: ios/Pods
        key: ${{ runner.os }}-pods-v1-${{ hashFiles('ios/Podfile.lock') }}

    - name: Setup iOS
      continue-on-error: true
      id: setup-ios
      run: |
        eval `ssh-agent -s`
        echo "${{ inputs.alyx-ssh-key }}" | ssh-add -
        yarn install --immutable
        yarn setup:ios
      shell: bash

    - name: Update CocoaPods If Necessary
      if: steps.setup-ios.outcome == 'failure'
      run: |
        eval `ssh-agent -s`
        echo "${{ inputs.alyx-ssh-key }}" | ssh-add -
        yarn setup:ios --repo-update
      shell: bash