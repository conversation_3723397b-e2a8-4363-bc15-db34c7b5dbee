name: Remove old JS Bundles

on: delete

env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

jobs:
  remove-old-bundles:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node Environment
        uses: hudl/mobile-github-workflows/setup-node-and-yarn@main

      - name: Install Dependencies
        run: yarn install --immutable

      - name: Remove public bundles
        run: yarn jarvis bundle delete --ci --branch-name ${{ github.event.ref }}