name: Required Labels

on:
  pull_request:
    types: [labeled, unlabeled, opened, synchronize, reopened]


jobs:
  label-check:
    runs-on: ubuntu-latest
    steps:
      - name: Check Type label
        uses: mheap/github-action-required-labels@388fd6af37b34cdfe5a23b37060e763217e58b03
        with:
          mode: exactly
          count: 1
          labels: "Type: Enhancement, Type: Fix, Type: Foundational, Type: Hotfix"
      - name: Check Team label
        uses: mheap/github-action-required-labels@388fd6af37b34cdfe5a23b37060e763217e58b03
        with:
          mode: exactly
          count: 1
          labels: "Team: Web Stack, Team: Business Operations, Team: Core Video, Team: Elite, Team: Elite NAS, Team: Elite ESP, Team: Focus, Team: Elite GF&R, Team: Elite HP, Team: Fan, Team: AML, Team: Data Generation, Team: Club, Team: HS, Team: Level Up, Team: Mobile Platform"
