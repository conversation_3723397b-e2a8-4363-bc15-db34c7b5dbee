name: 'Lint, compile, test'

on:
  pull_request:

env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}

jobs:
  verify_js:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        
      - name: Setup Node Environment
        uses: hudl/mobile-github-workflows/setup-node-and-yarn@main

      - name: Verify JS
        run: |
          yarn install --immutable
          yarn ci
    
      - name: Generate Test Report
        if: always() 
        uses: hudl/mobile-github-workflows/test-result-summary@main
