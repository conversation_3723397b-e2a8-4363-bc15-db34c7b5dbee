# This workflow was automatically generated. Do not modify it directly.

name: Build and Distribute Native Apps to Firebase and S3
on:
  pull_request:
    branches: "*"
  push:
    branches:
      - develop
env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_REGION: us-east-1
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  build_type:
    name: Determine Build Type
    runs-on: ubuntu-latest
    outputs:
      build-type: ${{ steps.determine-build-type.outputs.build-type }}
      download-branch: ${{ steps.determine-build-type.outputs.download-branch }}
      version-code: ${{ steps.vars.outputs.version-code }}
      commit-message: ${{ steps.vars.outputs.commit-message }}
      short-sha: ${{ steps.vars.outputs.short-sha }}
      sha: ${{ steps.vars.outputs.sha }}
      version: ${{ steps.vars.outputs.version }}
      branch: ${{ steps.vars.outputs.branch }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set Branch Info
        run: |-
          echo "version=$(node -e "console.log(require('./package.json').version);")" >> $GITHUB_OUTPUT
          echo "version-code=$((10000 + ${{ github.run_number }}))" >> $GITHUB_OUTPUT
          if [[ "${{ github.event_name }}" == "push" ]]; then
            echo "branch=$GITHUB_REF_NAME" >> $GITHUB_OUTPUT
            sha=${{ github.sha }}
          else
            echo "branch=$GITHUB_HEAD_REF" >> $GITHUB_OUTPUT
            sha=${{ github.event.pull_request.head.sha }}
          fi
          echo "sha=$sha" >> $GITHUB_OUTPUT
          echo "short-sha=$(git rev-parse --short $sha)" >> $GITHUB_OUTPUT
          echo "commit-message=$(git show -s --format=%s $sha)" >> $GITHUB_OUTPUT
        id: vars
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Determine Changed Files
        run: |-
          build_type_info=$(yarn ci-toolkit gh get-variable --repo '${{ github.event.repository.name }}' --pr-number '${{github.event.pull_request.number}}' --gh-token '${{ secrets.GH_REPO_FULL }}')
          buildType=$(echo "$build_type_info" | jq -r ".buildType")
          buildBranch=$(echo "$build_type_info" | jq -r ".buildBranch")
          changesetVersion=$(echo "$build_type_info" | jq -r ".changesetVersion")
          echo "build-type=$buildType" >> $GITHUB_OUTPUT
          echo "download-branch=$buildBranch" >> $GITHUB_OUTPUT
        id: determine-build-type
        if: ${{ github.event_name == 'pull_request' }}
  build_native_android:
    name: Build and Distribute Native Android
    runs-on:
      - self-hosted
      - macOS
      - ARM64
      - m1
    needs:
      - build_type
    if: needs.build_type.outputs.build-type == 'full-native-build' || github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Java And Gradle
        uses: hudl/mobile-github-workflows/setup-java-and-gradle@main
      - name: Setup Android SDK
        uses: android-actions/setup-android@7c5672355aaa8fde5f97a91aa9a99616d1ace6bc
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Install Ruby Dependencies
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          bundle install
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Run Setup Android Script
        run: yarn setup:android
      - name: Setup Android Signing
        id: android-signing
        uses: hudl/mobile-github-workflows/setup-android-signing@main
        with:
          keystore: ${{ secrets.HUDL_KEYSTORE }}
          keystore-passwords: ${{ secrets.HUDL_KEYSTORE_PASSWORDS }}
          keystore-filename: hudl.keystore
          keystore-alias: hudl-upload
          password-filename: passwords.properties
      - name: Build Android Release APK
        run: >-
          yarn jarvis native-build --platform android --platform-dir android --mode release --target physical-device --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{
          needs.build_type.outputs.version-code }} --app-name "Ticket Reader (Test)" --includeTestBinary
      - name: Create Sentry Release
        run: >-
          yarn ci-toolkit create-sentry-release -an ticketing -av ${{ needs.build_type.outputs.version }} -bn ${{ needs.build_type.outputs.version-code }}  -nbd android  -sp
          android/app/build/generated/assets/createBundleReleaseJsAndAssets/index.android.bundle,android/app/build/generated/sourcemaps/react/release/index.android.bundle.map
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:android:d9d1e70346420251a3a391 --service-account-secret pst/mobile/ticketing-firebase-app-distribution-a12d2 --app-path android/app/build/outputs/apk/release/app-release.apk --release-notes "Branch: ${{
          needs.build_type.outputs.branch }}\nCommit: ${{ needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload release-android to S3
        run: yarn jarvis app-upload s3 --app-path android/app/build/outputs/apk/release/app-release.apk --app-variant release-android --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Upload release-androidTest to S3
        run: yarn jarvis app-upload s3 --app-path android/app/build/outputs/apk/androidTest/release/app-release-androidTest.apk --app-variant release-androidTest --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Build Android Dev Client APK
        run: >-
          yarn jarvis native-build --platform android --platform-dir android --mode debug --target physical-device --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{
          needs.build_type.outputs.version-code }} --app-name "Ticket Reader (Dev Client)"
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:android:2573bd6a197ac898f10059 --service-account-secret pst/mobile/jarvisClient-firebase-app-distribution --app-path android/app/build/outputs/apk/debug/app-debug.apk --release-notes "Branch: ${{ needs.build_type.outputs.branch
          }}\nCommit: ${{ needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload debug-android to S3
        run: yarn jarvis app-upload s3 --app-path android/app/build/outputs/apk/debug/app-debug.apk --app-variant debug-android --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Build Android Release AAB
        run: >-
          yarn jarvis native-build --platform android --platform-dir android --mode release --target app-store --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{
          needs.build_type.outputs.version-code }} --app-name "Ticket Reader (Test)"
      - name: Upload release-aab to S3
        run: yarn jarvis app-upload s3 --app-path android/app/build/outputs/bundle/release/app-release.aab --app-variant release-aab --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Build Android Dev Client AAB
        run: >-
          yarn jarvis native-build --platform android --platform-dir android --mode debug --target app-store --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{
          needs.build_type.outputs.version-code }} --app-name "Ticket Reader (Dev Client)"
      - name: Upload debug-aab to S3
        run: yarn jarvis app-upload s3 --app-path android/app/build/outputs/bundle/debug/app-debug.aab --app-variant debug-aab --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
  modify_native_android:
    name: Build and Distribute Modified Android
    runs-on:
      - self-hosted
      - macOS
      - ARM64
      - m1
    needs:
      - build_type
    if: needs.build_type.outputs.build-type == 'modify-existing-binary' && github.ref != 'refs/heads/develop'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Java And Gradle
        uses: hudl/mobile-github-workflows/setup-java-and-gradle@main
      - name: Setup Android SDK
        uses: android-actions/setup-android@7c5672355aaa8fde5f97a91aa9a99616d1ace6bc
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Install Ruby Dependencies
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          bundle install
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Run Setup Android Script
        run: yarn setup:android
      - name: Setup Android Signing
        id: android-signing
        uses: hudl/mobile-github-workflows/setup-android-signing@main
        with:
          keystore: ${{ secrets.HUDL_KEYSTORE }}
          keystore-passwords: ${{ secrets.HUDL_KEYSTORE_PASSWORDS }}
          keystore-filename: hudl.keystore
          keystore-alias: hudl-upload
          password-filename: passwords.properties
      - name: Generate Javascript Bundle
        run: yarn jarvis bundle generate --entry-file index.js -p android
      - name: Download and Extract release-aab
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/release-aab.zip ./release-aab.zip
          unzip -o ./release-aab.zip -d ./.jarvis
      - name: Modify Release Android AAB With New Bundle
        run: >-
          rm -rf android-output && yarn jarvis modify-binary --outputPath android-output/app-release.aab --jsBundlePath ./.bundles/android/main.jsbundle.hbc --keystorePath ~/.android/hudl.keystore --keystorePassword ${{ steps.android-signing.outputs.keystore-password }} --keystoreAlias hudl-upload
          --buildNumber ${{ needs.build_type.outputs.version-code }} --versionName ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --extractRunnableBinary ./.jarvis/app-release.aab
      - name: Create Sentry Release
        run: yarn ci-toolkit create-sentry-release -an ticketing -av ${{ needs.build_type.outputs.version }} -bn ${{ needs.build_type.outputs.version-code }}  -sp ./.bundles/android/main.jsbundle,./.bundles/android/main.jsbundle.map
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:android:d9d1e70346420251a3a391 --service-account-secret pst/mobile/ticketing-firebase-app-distribution-a12d2 --app-path android-output/app-release.apk --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload release-android to S3
        run: yarn jarvis app-upload s3 --app-path android-output/app-release.apk --app-variant release-android --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Upload release-aab to S3
        run: yarn jarvis app-upload s3 --app-path android-output/app-release.aab --app-variant release-aab --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Download and Extract release-androidTest
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/release-androidTest.zip ./release-androidTest.zip
          unzip -o ./release-androidTest.zip -d ./.jarvis
      - name: Upload release-androidTest to S3
        run: yarn jarvis app-upload s3 --app-path ./.jarvis/app-release-androidTest.apk --app-variant release-androidTest --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Generate Debug JavaScript Bundle
        run: ./gradlew clean createBundleDebugJsAndAssets
        working-directory: android
      - name: Download and Extract debug-aab
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/debug-aab.zip ./debug-aab.zip
          unzip -o ./debug-aab.zip -d ./.jarvis
      - name: Modify Dev Client APK With New Bundle
        run: >-
          rm -rf android-output && yarn jarvis modify-binary --outputPath android-output/app-debug.aab --jsBundlePath android/app/build/generated/assets/createBundleDebugJsAndAssets/index.android.bundle --keystorePath android/app/debug.keystore --keystorePassword android --keystoreAlias
          androiddebugkey --buildNumber ${{ needs.build_type.outputs.version-code }} --versionName ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --extractRunnableBinary ./.jarvis/app-debug.aab
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:android:2573bd6a197ac898f10059 --service-account-secret pst/mobile/jarvisClient-firebase-app-distribution --app-path android-output/app-debug.apk --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload debug-android to S3
        run: yarn jarvis app-upload s3 --app-path android-output/app-debug.apk --app-variant debug-android --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Upload debug-aab to S3
        run: yarn jarvis app-upload s3 --app-path android-output/app-debug.aab --app-variant debug-aab --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
  android_ui_tests:
    name: Android Detox UI Tests
    runs-on:
      - self-hosted
      - macOS
      - ARM64
      - m1
    if: always() && !failure() && !cancelled()
    needs:
      - build_native_android
      - modify_native_android
      - build_type
    timeout-minutes: 45
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Java And Gradle
        uses: hudl/mobile-github-workflows/setup-java-and-gradle@main
      - name: Setup Android SDK
        uses: android-actions/setup-android@7c5672355aaa8fde5f97a91aa9a99616d1ace6bc
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Install Ruby Dependencies
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          bundle install
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Run Setup Android Script
        run: yarn setup:android
      - name: Download Android app for Testing
        run: yarn jarvis install -p android -cv ${{ needs.build_type.outputs.branch }} -a ticketing --local --downloadOnly --includeTestAPK -m release
      - name: Run Android Phone UI Tests
        uses: hudl/mobile-github-workflows/create-android-emulator@main
        with:
          arch: arm64-v8a
          target: default
          detoxrc: ./.detoxrc.js
          emulator-options: "-no-snapshot-save -gpu host -noaudio -no-boot-anim"
          script: yarn e2e-run android --no-verify --take-screenshots failing --forceExit --json --outputFile=output.json
        env:
          OWNING_BUSINESS_UNIT: competitive
          APPLICATION_PLATFORM: android
      - name: Upload Failing Artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: detox-artifacts-android
          path: ./artifacts
      - name: Cleanup
        if: always()
        run: |-
          yarn jarvis stop-packager
          killall Terminal || : && rm -rf ~/Library/Saved Application State/com.apple.Terminal.savedState
          yarn jarvis emulator --killAll
          rm -rf $HOME/.emulator_console_auth_token
      - name: Publish Test Reports on Workflow Summary
        run: |-
          yarn github-actions-ctrf summary ./ctrf/ctrf-report.json --title "Test Summary for Job ${{ github.job }}"
          yarn github-actions-ctrf failed ./ctrf/ctrf-report.json
          yarn github-actions-ctrf flaky ./ctrf/ctrf-report.json
          yarn github-actions-ctrf suite-folded ./ctrf/ctrf-report.json
        if: always()
      - name: Upload Test Results Artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ctrf-report-${{ github.job}}
          path: ./ctrf/ctrf-report.json
      - name: Publish Flaky Test Summary
        run: yarn github-actions-ctrf flaky-rate ./ctrf/ctrf-report.json --artifact-name ctrf-report-${{ github.job}}
        if: always()
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  build_native_ios:
    name: Build and Distribute Native iOS
    runs-on:
      - self-hosted
      - macOS
      - ARM64
      - m1
    needs:
      - build_type
    if: needs.build_type.outputs.build-type == 'full-native-build' || github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Xcode
        uses: hudl/mobile-github-workflows/setup-xcode@main
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Setup iOS Pods Cache
        uses: actions/cache@v4
        with:
          path: ios/Pods
          key: ${{ runner.os }}-pods-v1-${{ hashFiles('ios/Podfile.lock') }}
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Setup iOS Project
        id: setup-ios
        continue-on-error: true
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          yarn setup:ios
      - name: Update CocoaPods If Necessary
        if: steps.setup-ios.outcome == 'failure'
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          yarn setup:ios --repo-update
      - name: Setup Apple Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_APPSTORE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_ADHOC_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password: ${{ secrets.IOS_CERT_PASSWORD }}
      - name: Build Release Adhoc iOS IPA
        run: >-
          yarn jarvis native-build --platform ios --platform-dir ios --mode release --target physical-device --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{
          needs.build_type.outputs.version-code }} --app-name "Ticket Reader (AdHoc)" --signing-type ad-hoc
      - name: Create Sentry Release
        run: yarn ci-toolkit create-sentry-release -an ticketing -av ${{ needs.build_type.outputs.version }} -bn ${{ needs.build_type.outputs.version-code }}  -nbd ios  -sp ios/HudlTicketing.xcarchive/Products/Applications/HudlTicketing.app/main.jsbundle,./ios/main.jsbundle.map
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:ios:e3b5b1fc62942627a3a391 --service-account-secret pst/mobile/ticketing-firebase-app-distribution-a12d2 --app-path ios/export-output/HudlTicketing.ipa --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload release-adhoc-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios/export-output/HudlTicketing.ipa --app-variant release-adhoc-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Setup Apple Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_ENTERPRISE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_ENTERPRISE_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password: ${{ secrets.IOS_CERT_PASSWORD }}
      - name: Resigning IPA
        run: >-
          yarn ci-toolkit resign --ipa-path ios/export-output/HudlTicketing.ipa --bundle-id com.hudl.ticketing.enterprise --display-name "Ticket Reader (Test)" --cert-name "iPhone Distribution: Agile Sports Technologies, Inc." --provisioning-profile "$HOME/Library/MobileDevice/Provisioning
          Profiles/hudl.mobileprovision"
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:ios:70b690b21a2c26fca3a391 --service-account-secret pst/mobile/ticketing-firebase-app-distribution-a12d2 --app-path ios/export-output/HudlTicketing.ipa --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload release-inhouse-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios/export-output/HudlTicketing.ipa --app-variant release-inhouse-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Setup Apple Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_APPSTORE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_ADHOC_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password: ${{ secrets.IOS_CERT_PASSWORD }}
      - name: Build Dev Client iOS IPA
        run: >-
          yarn jarvis native-build --platform ios --platform-dir ios --mode debug --target physical-device --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{ needs.build_type.outputs.version-code
          }} --app-name "Ticket Reader (Dev Client)" --signing-type ad-hoc
      - name: Setup Apple Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_ENTERPRISE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_ENTERPRISE_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password: ${{ secrets.IOS_CERT_PASSWORD }}
      - name: Resigning IPA
        run: >-
          yarn ci-toolkit resign --ipa-path ios/export-output/HudlTicketing.ipa --bundle-id com.hudl.ticketing.enterprise --display-name "Ticket Reader (Dev Client)" --cert-name "iPhone Distribution: Agile Sports Technologies, Inc." --provisioning-profile "$HOME/Library/MobileDevice/Provisioning
          Profiles/hudl.mobileprovision"
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:ios:43390ef924d51794f10059 --service-account-secret pst/mobile/jarvisClient-firebase-app-distribution --app-path ios/export-output/HudlTicketing.ipa --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload debug-inhouse-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios/export-output/HudlTicketing.ipa --app-variant debug-inhouse-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Build Release iOS Simulator
        run: >-
          yarn jarvis native-build --platform ios --platform-dir ios --mode release --target simulator --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{ needs.build_type.outputs.version-code }}
          --app-name "Ticket Reader (Test)"
      - name: Upload release-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios/build/Build/Products/Release-iphonesimulator/HudlTicketing.app --app-variant release-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Build Dev Client iOS Simulator
        run: >-
          yarn jarvis native-build --platform ios --platform-dir ios --mode debug --target simulator --version-name ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --build-number ${{ needs.build_type.outputs.version-code }}
          --app-name "Ticket Reader (Dev Client)"
      - name: Upload debug-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios/build/Build/Products/Debug-iphonesimulator/HudlTicketing.app --app-variant debug-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
  modify_native_ios:
    name: Build and Distribute Modified iOS
    runs-on:
      - self-hosted
      - macOS
      - ARM64
      - m1
    needs:
      - build_type
    if: needs.build_type.outputs.build-type == 'modify-existing-binary' && github.ref != 'refs/heads/develop'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Xcode
        uses: hudl/mobile-github-workflows/setup-xcode@main
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Setup iOS Pods Cache
        uses: actions/cache@v4
        with:
          path: ios/Pods
          key: ${{ runner.os }}-pods-v1-${{ hashFiles('ios/Podfile.lock') }}
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Setup iOS Project
        id: setup-ios
        continue-on-error: true
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          yarn setup:ios
      - name: Update CocoaPods If Necessary
        if: steps.setup-ios.outcome == 'failure'
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          yarn setup:ios --repo-update
      - name: Setup Apple Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_APPSTORE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_ADHOC_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password: ${{ secrets.IOS_CERT_PASSWORD }}
      - name: Generate Javascript Bundle
        run: yarn jarvis bundle generate --entry-file index.js -p ios
      - name: Download and Extract release-adhoc-ios
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/release-adhoc-ios.zip ./release-adhoc-ios.zip
          unzip -o ./release-adhoc-ios.zip -d ./.jarvis
      - name: Modify Release Adhoc IPA With New Bundle
        run: >-
          rm -rf ios-output && yarn jarvis modify-binary --outputPath ios-output/HudlTicketing.ipa --jsBundlePath ./.bundles/ios/main.jsbundle.hbc --assetsPath ./.bundles/ios/assets/ --bundleID com.hudl.ticketing --certificateName "iPhone Distribution: Agile Sports Technologies"
          --provisioningProfilePath "$HOME/Library/MobileDevice/Provisioning Profiles/hudl.mobileprovision" --buildNumber ${{ needs.build_type.outputs.version-code }} --versionName ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha
          }} ./.jarvis/HudlTicketing.ipa
      - name: Create Sentry Release
        run: yarn ci-toolkit create-sentry-release -an ticketing -av ${{ needs.build_type.outputs.version }} -bn ${{ needs.build_type.outputs.version-code }}  -sp ./.bundles/ios/main.jsbundle.map,./.bundles/ios/main.jsbundle
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:ios:e3b5b1fc62942627a3a391 --service-account-secret pst/mobile/ticketing-firebase-app-distribution-a12d2 --app-path ios-output/HudlTicketing.ipa --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload release-adhoc-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios-output/HudlTicketing.ipa --app-variant release-adhoc-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Setup Apple Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_ENTERPRISE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_ENTERPRISE_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password: ${{ secrets.IOS_CERT_PASSWORD }}
      - name: Resigning IPA
        run: >-
          yarn ci-toolkit resign --ipa-path ios-output/HudlTicketing.ipa --bundle-id com.hudl.ticketing.enterprise --display-name "Ticket Reader (Test)" --cert-name "iPhone Distribution: Agile Sports Technologies, Inc." --provisioning-profile "$HOME/Library/MobileDevice/Provisioning
          Profiles/hudl.mobileprovision"
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:ios:70b690b21a2c26fca3a391 --service-account-secret pst/mobile/ticketing-firebase-app-distribution-a12d2 --app-path ios-output/HudlTicketing.ipa --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload release-inhouse-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios-output/HudlTicketing.ipa --app-variant release-inhouse-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Download and Extract release-ios
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/release-ios.zip ./release-ios.zip
          unzip -o ./release-ios.zip -d ./.jarvis
      - name: Modify Release Simulator App With New Bundle
        run: rm -rf ios-output && yarn jarvis modify-binary ./.jarvis/HudlTicketing.app --outputPath ios-output/HudlTicketing.app --jsBundlePath ./.bundles/ios/main.jsbundle --assetsPath ./.bundles/ios/assets/ --buildNumber ${{ needs.build_type.outputs.version-code }}
      - name: Upload release-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios-output/HudlTicketing.app --app-variant release-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Generate Javascript Bundle
        run: yarn jarvis bundle generate --entry-file index.js -p ios --dev
      - name: Download and Extract debug-inhouse-ios
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/debug-inhouse-ios.zip ./debug-inhouse-ios.zip
          unzip -o ./debug-inhouse-ios.zip -d ./.jarvis
      - name: Modify Dev Client IPA With New Bundle
        run: >-
          rm -rf ios-output && yarn jarvis modify-binary --outputPath ios-output/HudlTicketing.ipa --assetsPath "./.bundles/ios/assets/" --bundleID com.hudl.ticketing.enterprise --certificateName "iPhone Distribution: Agile Sports Technologies, Inc." --provisioningProfilePath
          "$HOME/Library/MobileDevice/Provisioning Profiles/hudl.mobileprovision" --buildNumber ${{ needs.build_type.outputs.version-code }} --versionName ${{ needs.build_type.outputs.version }}-${{ needs.build_type.outputs.branch }}-${{ needs.build_type.outputs.short-sha }} --jsBundlePath
          ./.bundles/ios/main.jsbundle.hbc ./.jarvis/HudlTicketing.ipa
      - name: Upload App To Firebase
        run: >-
          yarn jarvis app-upload firebase --firebase-id 1:************:ios:43390ef924d51794f10059 --service-account-secret pst/mobile/jarvisClient-firebase-app-distribution --app-path ios-output/HudlTicketing.ipa --release-notes "Branch: ${{ needs.build_type.outputs.branch }}\nCommit: ${{
          needs.build_type.outputs.sha }}\nCommit Message: ${{ needs.build_type.outputs.commit-message }}" --groups developers
      - name: Upload debug-inhouse-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios-output/HudlTicketing.ipa --app-variant debug-inhouse-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
      - name: Download and Extract debug-ios
        run: |-
          aws s3 cp s3://hudl-mobile-artifacts/jarvis_releases/ticketing/${{ needs.build_type.outputs.download-branch }}/debug-ios.zip ./debug-ios.zip
          unzip -o ./debug-ios.zip -d ./.jarvis
      - name: Modify Dev Client iOS Simulator With New Bundle
        run: rm -rf ios-output && yarn jarvis modify-binary --outputPath ios-output/HudlTicketing.app --jsBundlePath ./.bundles/ios/main.jsbundle.hbc ./.jarvis/HudlTicketing.app
      - name: Upload debug-ios to S3
        run: yarn jarvis app-upload s3 --app-path ios-output/HudlTicketing.app --app-variant debug-ios --branch ${{ needs.build_type.outputs.branch }} --app-name "ticketing"
  ios_ui_tests:
    name: iOS Detox UI Tests
    if: always() && !failure() && !cancelled()
    runs-on:
      - self-hosted
      - macOS
      - ARM64
      - m1
    needs:
      - build_native_ios
      - modify_native_ios
      - build_type
    timeout-minutes: 45
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Xcode
        uses: hudl/mobile-github-workflows/setup-xcode@main
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Download iOS app for Testing
        run: yarn jarvis install -p ios -cv ${{ needs.build_type.outputs.branch }} -a ticketing --local --downloadOnly -m release
      - name: Run iOS UI Tests
        run: yarn e2e-run ios --no-verify --take-screenshots failing --forceExit --json --outputFile=output.json
        env:
          OWNING_BUSINESS_UNIT: competitive
          APPLICATION_PLATFORM: ios
      - name: Upload Failing Artifacts
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: detox-artifacts-ios
          path: ./artifacts
      - name: Cleanup
        if: always()
        run: |-
          yarn jarvis stop-packager
          killall Terminal || : && rm -rf ~/Library/Saved Application State/com.apple.Terminal.savedState
          xcrun simctl shutdown all
          xcrun simctl erase all
      - name: Publish Test Reports on Workflow Summary
        run: |-
          yarn github-actions-ctrf summary ./ctrf/ctrf-report.json --title "Test Summary for Job ${{ github.job }}"
          yarn github-actions-ctrf failed ./ctrf/ctrf-report.json
          yarn github-actions-ctrf flaky ./ctrf/ctrf-report.json
          yarn github-actions-ctrf suite-folded ./ctrf/ctrf-report.json
        if: always()
      - name: Upload Test Results Artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ctrf-report-${{ github.job}}
          path: ./ctrf/ctrf-report.json
      - name: Publish Flaky Test Summary
        run: yarn github-actions-ctrf flaky-rate ./ctrf/ctrf-report.json --artifact-name ctrf-report-${{ github.job}}
        if: always()
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  set_changeset_key:
    name: Set Changeset Key for Current Build
    if: always() && !cancelled() && !failure() && github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    needs:
      - build_type
      - build_native_android
      - modify_native_android
      - build_native_ios
      - modify_native_ios
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Set Github Variable
        run: |-
          yarn ci-toolkit gh set-variable \
          --repo ${{ github.event.repository.name }} \
          --pr-number ${{ github.event.pull_request.number }} \
          --gh-token ${{ secrets.GH_REPO_FULL }}
