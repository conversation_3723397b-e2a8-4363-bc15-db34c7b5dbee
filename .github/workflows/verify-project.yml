name: Verify project is configured correctly

on:
  pull_request:

env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}

jobs:
  verify:
    runs-on: ubuntu-latest
      
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node Environment
        uses: hudl/mobile-github-workflows/setup-node-and-yarn@main

      - name: Install Node Modules
        run: yarn install --immutable

      - name: Verify Native Runtime
        run: yarn verify-jarvis-runtime

      - name: Verify app/package.json
        uses: hudl/mobile-github-workflows/verify-auto-generated-file@main
        with:
          file: app/package.json
          script: jarvis-auto-imports

      - name: Verify React Native Version
        uses: hudl/mobile-github-workflows/verify-rn-version@main
