# This workflow is manually triggered from the "Create RC" workflow. 
# For details on manually triggering a workflow please see the official Github docs
# https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#workflow_dispatch
name: Create RC

on:
  workflow_dispatch:
    inputs:
      version-type:
        description: "Semantic Versioning type: major, minor or patch"
        required: false
        default: minor
        type: choice
        options:
          - major
          - minor
          - patch
      auto-merge-version:
        description: Automatically merge version bump PR
        required: false
        default: true
        type: boolean
      create-rc:
        description: Create Release Candidate PR
        required: false
        default: true
        type: boolean

concurrency:
  group: ${{ github.head_ref }}-rc
  cancel-in-progress: true

jobs:
  create-rc:
    uses: hudl/mobile-github-workflows/.github/workflows/create-rc.yml@main
    with:
      update-changelog: false
      version-type: ${{ github.event.inputs.version-type }}
      labels: "Type: Enhancement,Release Candidate,Team: Mobile Platform"
      auto-merge-version: ${{ inputs.auto-merge-version }}
      create-rc: ${{ inputs.create-rc }}
    secrets:
      merge-token: ${{ secrets.GH_REPO_FULL }}