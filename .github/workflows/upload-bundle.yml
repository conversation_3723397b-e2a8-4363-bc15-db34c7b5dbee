name: Generate JS Bundle and Upload

on:
  push:
    branches-ignore:
      - main
      - MOB-BumpVersion-*
  pull_request:
    types:
      - opened
      - synchronize


concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

jobs:
  upload-bundle:
    runs-on: ubuntu-latest
    # We only want to upload the bundle once per push if an PR is open
    # Without this, the job runs for both the push and pull_request events
    if: ${{ github.event_name == 'push' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node Environment
        uses: hudl/mobile-github-workflows/setup-node-and-yarn@main

      - name: Create Bundle and upload
        run: |
          yarn install --immutable
          yarn jarvis bundle upload --entry-file index.js --clean-up

  qr-code-comment:
    runs-on: ubuntu-latest
    # We want to always run this job but wait until the bundle is uploaded and only if a PR is open
    # This is because the unload-bundle job only runs on push events and skipped on pull_request events
    if: ${{ always() && github.event.pull_request.state == 'open' }}
    needs: upload-bundle

    steps:
      - name: QR Comment
        uses:  hudl/mobile-github-workflows/qr-comment@main
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}