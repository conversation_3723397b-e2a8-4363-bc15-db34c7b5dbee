name: Release Apps

on:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  BUNDLE_ID: 'com.hudl.ticketing'
  LANG: en_US.UTF-8
  FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT: "30"
  FASTLANE_HIDE_TIMESTAMP: 1

jobs:
  build_version:
    name: Determine Build Version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.vars.outputs.version }}
      ios-version-code: ${{ steps.vars.outputs.ios-version-code }}
      android-version-code: ${{ steps.vars.outputs.android-version-code }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Install Ruby Dependencies
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          bundle install
      - name: Set Version Info
        run: |-
          version=$(node -e "console.log(require('./package.json').version);")
          echo "version=$version" >> $GITHUB_OUTPUT
          echo "ios-version-code=$(bundle exec fastlane determine_ios_build_number package_version:$version bundle_id:$BUNDLE_ID | grep 'BUILD_NUMBER=' | sed -E 's/BUILD_NUMBER=//')" >> $GITHUB_OUTPUT
          echo "android-version-code=${{ github.run_number }}${{ github.run_attempt }}" >> $GITHUB_OUTPUT
        id: vars
  release-android:
    runs-on: [self-hosted, macOS, ARM64, m1]
    needs:
      - build_version

    env:
      BUILD_NUMBER: ${{ needs.build_version.outputs.android-version-code }}
      PACKAGE_VERSION: ${{ needs.build_version.outputs.version }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Android Environment
        uses: ./.github/composite-actions/setup-android-environment
        with:
          alyx-ssh-key: ${{ secrets.ALYX_SSH_KEY }}

      - name: Setup Android Signing
        id: android-signing
        uses: hudl/mobile-github-workflows/setup-android-signing@main
        with:
          keystore: ${{ secrets.HUDL_KEYSTORE }}
          keystore-passwords: ${{ secrets.HUDL_KEYSTORE_PASSWORDS }}
          keystore-filename: hudl.keystore
          keystore-alias: hudl-upload
          password-filename: passwords.properties

      - name: Clean Android app
        working-directory: android
        run: ./gradlew clean

      - name: Build Android app
        working-directory: android
        run: ./gradlew bundleRelease -PversionCode=$BUILD_NUMBER -PversionName="$PACKAGE_VERSION"

      - name: Create Sentry Release
        run: >-
          yarn ci-toolkit create-sentry-release -an ticketing -av $PACKAGE_VERSION -bn $BUILD_NUMBER  -nbd android  -sp
          android/app/build/generated/assets/createBundleReleaseJsAndAssets/index.android.bundle,android/app/build/generated/sourcemaps/react/release/index.android.bundle.map

      - name: Distribute to Play Store
        run: bundle exec fastlane android distribute_to_playstore

      - name: Upload App as artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: android-bundle
          path: android/app/build/outputs/bundle/release/app-release.aab


  release-ios:
    runs-on: [self-hosted, macOS, ARM64, m1]
    needs:
      - build_version

    env:
      BUILD_NUMBER: ${{ needs.build_version.outputs.ios-version-code }}
      PACKAGE_VERSION: ${{ needs.build_version.outputs.version }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup iOS Environment
        uses: ./.github/composite-actions/setup-ios-environment
        with:
          alyx-ssh-key: ${{ secrets.ALYX_SSH_KEY }}

      - name: Setup Enterprise Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_APPSTORE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_APPSTORE_PROFILE}}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password:  ${{ secrets.IOS_CERT_PASSWORD }}

      - name: Build and distribute to App Store
        run: bundle exec fastlane ios build_and_distribute_to_appstore

      - name: Create Sentry Release
        run: >-
          yarn ci-toolkit create-sentry-release -an ticketing -av $PACKAGE_VERSION -bn $BUILD_NUMBER  -nbd ios  -sp
          ios/HudlTicketing.xcarchive/Products/Applications/HudlTicketing.app/main.jsbundle,ios/main.jsbundle.map

      - name: Upload App as artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ios-ipa
          path: |
            ios-output/HudlTicketing.ipa
            ios-output/HudlTicketing.app.dSYM.zip
  
      - name: Upload Xcodebuild log on failure
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: xcodebuild-log
          path: fastlane/logs
