# This workflow was automatically generated. Do not modify it directly.

name: Delete Changeset Key
on:
  pull_request:
    types:
      - closed
env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
jobs:
  cleanup_builds:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Delete Changeset Key
        run: yarn ci-toolkit gh remove-changeset-variable --repo ${{ github.event.repository.name }} --pr-number ${{ github.event.pull_request.number }} --gh-token ${{ secrets.GH_REPO_FULL }}
