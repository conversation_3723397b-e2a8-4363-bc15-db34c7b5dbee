# This workflow was automatically generated. Do not modify it directly.

name: Delete feature branch binaries from S3
on:
  - delete
env:
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
jobs:
  cleanup_builds:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Setup Node Environment
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
      - name: Setup Yarn
        run: npm install -g yarn
      - name: Setup Node Cache
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
          cache: yarn
      - name: Install Dependencies
        run: yarn install --immutable
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@ececac1a45f3b08a01d2dd070d28d111c5fe6722
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Cleanup Feature Builds
        if: github.event.ref_type == 'branch'
        run: yarn ci-toolkit cleanup-s3-apps --branch ${{ github.event.ref }} --app-alias ticketing
