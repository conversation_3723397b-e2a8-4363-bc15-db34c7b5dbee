name: Build and Distribute for External Testing

on:
  workflow_dispatch:
    inputs:
      what_to_test:
        description: "What to Test"
        type: string
        default: "Welcome to Hudl Ticket Reader"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

env:
  FIREBASE_APP_DIST_SERVICE_ACCOUNT: ${{ secrets.FIREBASE_APP_DIST_SERVICE_ACCOUNT }}
  SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
  GH_PACKAGES_TOKEN: ${{ secrets.GH_PACKAGES_READ_TOKEN }}
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_REGION: us-east-1
  LANG: en_US.UTF-8
  BUNDLE_ID: 'com.hudl.ticketing'
  FASTLANE_HIDE_TIMESTAMP: 1

jobs:
  build_version:
    name: Determine Build Version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.vars.outputs.version }}
      ios-version-code: ${{ steps.vars.outputs.ios-version-code }}
      android-version-code: ${{ steps.vars.outputs.android-version-code }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Ruby
        uses: hudl/mobile-github-workflows/setup-ruby@main
      - name: Setup Bundle Cache
        uses: actions/cache@v4
        with:
          path: ./vendor/bundle
          key: ${{ runner.os }}-bundle-${{ hashFiles('Gemfile.lock') }}
      - name: Install Ruby Dependencies
        run: |-
          eval `ssh-agent -s`
          echo "${{ secrets.ALYX_SSH_KEY }}" | ssh-add -
          bundle install
      - name: Set Version Info
        run: |-
          version=$(node -e "console.log(require('./package.json').version);")
          echo "version=$version" >> $GITHUB_OUTPUT
          echo "ios-version-code=$(bundle exec fastlane determine_ios_build_number package_version:$version bundle_id:$BUNDLE_ID | grep 'BUILD_NUMBER=' | sed -E 's/BUILD_NUMBER=//')" >> $GITHUB_OUTPUT
          echo "android-version-code=$(${{ github.run_number }}${{ github.run_attempt }})" >> $GITHUB_OUTPUT
        id: vars

  build_and_distribute_ios_to_testflight:
    name: Build and Distribute iOS to TestFlight
    needs:
      - build_version
    runs-on: [self-hosted, macOS, ARM64, m1]
    if: github.ref == 'refs/heads/develop'
    env:
      BUILD_NUMBER: ${{ needs.build_version.outputs.ios-version-code }}
      PACKAGE_VERSION: ${{ needs.build_version.outputs.version }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup iOS Environment
        uses: ./.github/composite-actions/setup-ios-environment
        with:
          alyx-ssh-key: ${{ secrets.ALYX_SSH_KEY }}

      - name: Setup Enterprise Code Signing
        uses: hudl/mobile-github-workflows/setup-keychain-and-profile@main
        with:
          certificate: ${{ secrets.IOS_APPSTORE_CERT }}
          provisioning-profile: ${{ secrets.HUDL_TICKETING_APPSTORE_PROFILE }}
          keychain-password: ${{ secrets.IOS_KEYCHAIN_PASSWORD }}
          cert-password:  ${{ secrets.IOS_CERT_PASSWORD }}

      - name: Build and distribute to TestFlight
        run: bundle exec fastlane ios build_and_distribute_to_testflight testing_notes:"${{ github.event.inputs.what_to_test }}"

      - name: Create Sentry Release
        run: >-
          yarn ci-toolkit create-sentry-release -an ticketing -av $PACKAGE_VERSION -bn $BUILD_NUMBER  -nbd ios  -sp
          ios/HudlTicketing.xcarchive/Products/Applications/HudlTicketing.app/main.jsbundle,ios/main.jsbundle.map

      - name: Upload Xcodebuild log on failure
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: xcodebuild-log
          path: fastlane/logs

  build_and_distribute_android_to_firebase:
    name: Build and Distribute Android to External Testers
    needs:
      - build_version
    runs-on: [self-hosted, macOS, ARM64, m1]
    if: github.ref == 'refs/heads/develop'
    env:
      BUILD_NUMBER: ${{ needs.build_version.outputs.android-version-code }}
      PACKAGE_VERSION: ${{ needs.build_version.outputs.version }}

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Android Environment
        uses: ./.github/composite-actions/setup-android-environment
        with:
          alyx-ssh-key: ${{ secrets.ALYX_SSH_KEY }}

      - name: Setup Android Signing
        id: android-signing
        uses: hudl/mobile-github-workflows/setup-android-signing@main
        with:
          keystore: ${{ secrets.HUDL_KEYSTORE }}
          keystore-passwords: ${{ secrets.HUDL_KEYSTORE_PASSWORDS }}
          keystore-filename: hudl.keystore
          keystore-alias: hudl-upload
          password-filename: passwords.properties

      - name: Clean Android build
        working-directory: android
        run: ./gradlew clean

      - name: Build and distribute to Firebase
        working-directory: android
        run: |
          ./gradlew bundleRelease \
          -PversionCode=$BUILD_NUMBER \
          -PversionName="${{ needs.build_version.outputs.version }}"

      - name: Extract APK
        run: |
          yarn jarvis modify-binary -o android-output/app-release.aab \
          -j "android/app/build/ASSETS/createBundleReleaseJsAndAssets/index.android.bundle" \
          -e --keystorePath '~/.android/hudl.keystore' \
          --keystorePassword "$KEYSTORE_PASSWORD" \
          --keystoreAlias 'hudl-upload' android/app/build/outputs/bundle/release/app-release.aab
        env:
            KEYSTORE_PASSWORD: ${{ steps.android-signing.outputs.keystore-password }}

      - name: Create Sentry Release
        run: >-
          yarn ci-toolkit create-sentry-release -an ticketing -av $PACKAGE_VERSION -bn $BUILD_NUMBER  -nbd android  -sp
          android/app/build/generated/assets/createBundleReleaseJsAndAssets/index.android.bundle,android/app/build/generated/sourcemaps/react/release/index.android.bundle.map

      - name: Distribute to Firebase
        run: |
          bundle exec fastlane android distribute_to_firebase \
          apk_path:android-output/app-release.apk \
          groups:external_testers testing_notes:"${{ github.event.inputs.what_to_test }}"