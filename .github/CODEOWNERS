# See https://help.github.com/articles/about-code-owners/

/app/                   @hudl/fan-team-6
/e2e/                   @hudl/fan-team-6
/storybook/             @hudl/fan-team-6
/gql-schemas/           @hudl/fan-team-6   

# The Fan Team from Competitive BU owns and maintains the features that
# make up the Ticketing App, but the following project directories and files
# are maintained by Mobile Platform Team
.bundle/                @hudl/vision
.github/                @hudl/vision
.vscode/                @hudl/vision
.yarn/                  @hudl/vision
fastlane/               @hudl/vision
local-apollo-server     @hudl/vision @hudl/fan-team-6
scripts/                @hudl/vision
.detoxrc.json           @hudl/vision
.editorconfig           @hudl/vision
.eslintignore           @hudl/vision
.eslintrc.json          @hudl/vision
.gitattributes          @hudl/vision
.gitignore              @hudl/vision
.lintstagedrc.js        @hudl/vision
.nvmrc                  @hudl/vision
.ruby-version           @hudl/vision
.watchmanconfig         @hudl/vision
.xcode-version          @hudl/vision
.yarnrc.yml             @hudl/vision
auto-link.lock          @hudl/vision
babel.config.js         @hudl/vision
Gemfile                 @hudl/vision
Gemfile.lock            @hudl/vision
jest.config.js          @hudl/vision
metro.config.js         @hudl/vision
package.json            @hudl/vision
react-native-config.js  @hudl/vision
tsconfig.json           @hudl/vision
yarn.lock               @hudl/vision

# Note: Mobile Platform Team aren't owners of code inside the ios/
# and android/ directories, but still want to keep track of changes
# that might negatively impact the ability for Ticketing to run on a 
# pre-built Jarvis Client App.
index.js                @hudl/vision @hudl/fan-team-6
/android/               @hudl/vision @hudl/fan-team-6
/ios/                   @hudl/vision @hudl/fan-team-6
