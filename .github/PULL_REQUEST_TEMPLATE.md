## [Overview](#overview)
_<The description of what you've changed goes here ...>_

## [Tickets](#tickets):
- [FAN-XXXX](https://hudl-jira.atlassian.net/browse/FAN-XXXX)

## [Illustrations](#illustrations):
_<before screenshot (if applicable)>_
_<after screenshot (if applicable)>_

## [Acceptance Criteria](#acceptance-criteria):
- [ ] Test 1 - description

### Must Be Completed Before Testing:
- [ ] Develop has recently been merged
- [ ] Any necessary code reviews are completed
- [ ] Any UI changes have been reviewed by designer
- [ ] Testability needs have been met as agreed upon by Engineering and QA (i.e. element ids, accessibility labels, etc.)

### Must Be Completed Before Deploy:
- [ ] All Major, Critical, and Blocker defects have been fixed
- [ ] All automation passes
- [ ] Manual Regression testing identified and completed
- [ ] Test case automation has been implemented at the appropriate level (Unit, API/integration, or UI)
- [ ] Logging tested
- [ ] All internationalized key-value strings have been tested

**What gif best describes this PR or how it makes you feel?**
<img src="<INSERT_GIPHY_LINK_HERE"/>
