/* eslint-disable */
//prettier-ignore
module.exports = {
name: "@yarnpkg/plugin-hudl-packages",
factory: function (require) {
var plugin=(()=>{var w=Object.create,u=Object.defineProperty;var f=Object.getOwnPropertyDescriptor;var b=Object.getOwnPropertyNames;var S=Object.getPrototypeOf,P=Object.prototype.hasOwnProperty;var A=e=>u(e,"__esModule",{value:!0});var r=e=>{if(typeof require!="undefined")return require(e);throw new Error('Dynamic require of "'+e+'" is not supported')};var _=(e,t)=>{for(var o in t)u(e,o,{get:t[o],enumerable:!0})},v=(e,t,o)=>{if(t&&typeof t=="object"||typeof t=="function")for(let s of b(t))!P.call(e,s)&&s!=="default"&&u(e,s,{get:()=>t[s],enumerable:!(o=f(t,s))||o.enumerable});return e},g=e=>v(A(u(e!=null?w(S(e)):{},"default",e&&e.__esModule&&"default"in e?{get:()=>e.default,enumerable:!0}:{value:e,enumerable:!0})),e);var E={};_(E,{default:()=>R,plugin:()=>m});var d=g(r("https")),n=g(r("fs")),p=r("readline"),i=`${r("os").homedir()}/.hudl-packages`,T="[33m%s[0m",k="[31m",m={hooks:{async validateProject(e){let t=e.configuration.values.get("npmScopes").get("hudl");if(!t.get("npmAuthToken"))if(n.default.existsSync(i)){let o=n.default.readFileSync(i).toString();try{await y(o)}catch(s){console.log(k,`
${s.message}
`),process.exit(1)}t.set("npmAuthToken",o)}else{console.log(T,`This project uses private Hudl NPM packages.
Follow these instructions to setup or update your Github Personal Access Token.`),console.log(`
1. Head over to https://github.com/settings/tokens.`),console.log("2. Select 'Generate new token'."),console.log("3. Give your token a Note (e.g. 'Hudl Packages Read Only') and check 'read:packages' (don't check anything else)."),console.log("4. Scroll to bottom of page and select 'Generate token'."),console.log("5. Copy the token and paste it into the terminal below."),console.log(`6. Back on Github select 'Enable SSO' beside your new token and follow the instructions.
`);let o=await H();try{await y(o)}catch(s){console.log(k,`
${s.message}
`),process.exit(1)}t.set("npmAuthToken",o)}}}};async function H(){let e=p.createInterface({input:process.stdin,output:process.stdout});e.input.on("keypress",function(){let o=e.line.length;p.moveCursor(e.output,-o,0),p.clearLine(e.output,1);for(let s=0;s<o;s++)e.output.write("*")});let t=await new Promise(o=>{e.question("Whats your Github Personal Access Token?:",o)});return e.close(),t}async function y(e){if(console.log("\u{1F6C2} Validating your Github Personal Access Token..."),!await a.isGitHubReachable())throw new Error("\u{1F6A8} Unable to validate token as github.com is not reachable. Please check your internet connection.");if(!await a.authenticate(e))throw n.default.existsSync(i)&&n.default.unlinkSync(i),new Error('\u{1F6A8} Your personal access token is invalid. Please re-run "yarn install" for advice on generating a new one.');n.default.existsSync(i)||n.default.writeFileSync(i,e);let t=await a.getScopesListForToken(e);if(t.length>1)throw new Error(`\u{1F6A8} Your personal access token has too many permissions.

Head over to https://github.com/settings/tokens and update your token to only have the 'read:packages' permission.`);if(!t.includes("read:packages"))throw new Error("\u{1F6A8} Your personal access token does not have the 'read:packages' scope. Head to https://github.com/settings/tokens and update it.");if(!await a.hudlSSOHasBeenAuthorized(e))throw new Error(`\u{1F6A8} It looks like your token does not have Hudl SSO authorization...

Navigate to https://github.com/settings/tokens, select "Configure SSO" for your token, and ensure it has been authorized.`);console.log("\u2728 Token is valid!")}var a=class{static async isGitHubReachable(){return new Promise(t=>{r("dns").lookup("github.com",function(o){o&&o.code=="ENOTFOUND"?t(!1):t(!0)})})}static async authenticate(t){return(await this.makeRequest(this.BASE_GITHUB_API_URL,t)).statusCode===200}static async hudlSSOHasBeenAuthorized(t){return(await this.makeRequest(this.HUDL_NPM_PACKAGES_URL,t)).statusCode===200}static async getScopesListForToken(t){var c;let o=await this.makeRequest(this.BASE_GITHUB_API_URL,t),s=(c=o.headers["x-oauth-scopes"])!=null?c:"";if(o.statusCode===200)return typeof s=="string"?s.split(", "):s;throw new Error(`\u{1F6A8} Unable to get scopes for token. Status Code ${o.statusCode}. Raw Scopes ${s}.`)}static async makeRequest(t,o){return new Promise((s,c)=>{let h=d.default.request(t,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`token ${o.trim()}`,"user-agent":"node.js"}},l=>{s(l)});h.on("error",l=>{c(l)}),h.end()})}};a.BASE_GITHUB_API_URL="https://api.github.com",a.HUDL_NPM_PACKAGES_URL="https://api.github.com/orgs/hudl/packages?package_type=npm";var R=m;return E;})();
return plugin;
}
};
