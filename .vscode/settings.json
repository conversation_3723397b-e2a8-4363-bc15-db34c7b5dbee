{
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        { "language": "typescript", "autoFix": true },
        { "language": "typescriptreact", "autoFix": true }
    ],
    "eslint.autoFixOnSave": true,
    "editor.formatOnSave": false,
    "javascript.format.enable": false,
    "javascript.validate.enable": false,
    "typescript.validate.enable": true,
    "files.autoSave": "off",
    "files.eol": "\n",
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },
    "typescript.tsdk": "node_modules/typescript/lib",
    "cSpell.words": [
        "Hudl"
    ],
}