buildscript {
    apply from: '../node_modules/@hudl/jarvis/native/jarvis_gradle_util.gradle'
    ext.kotlin_version = rnGradlePropGet('kotlin')
    ext.kotlinVersion = ext.kotlin_version // Some libraries expect a 'kotlinVersion' prop and some expect 'kotlin_version'.
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 29
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "27.1.12297006"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
    }
}

allprojects {
    repositories {
        maven {
            // All of Detox's artifacts are provided via the npm module
            url "$rootDir/../node_modules/detox/Detox-android"
        }
        addReposForJarvis(repositories)
    }
}

project.ext.set("versionCode", project.hasProperty('versionCode') ? versionCode.toInteger() : 1)
project.ext.set("versionName", project.hasProperty('versionName') ? versionName : "1.0")
project.ext.set("appName", project.hasProperty('appName') ? appName : 'Ticket Reader')

apply plugin: "com.facebook.react.rootproject"