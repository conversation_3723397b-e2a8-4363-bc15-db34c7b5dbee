package com.hudl.ticketing

import android.content.res.Configuration
import expo.modules.ApplicationLifecycleDispatcher
import expo.modules.ReactNativeHostWrapper
import android.app.Application
import com.facebook.hermes.reactexecutor.HermesExecutorFactory
import com.facebook.react.PackageList
import com.facebook.react.ReactApplication
import com.facebook.react.ReactHost
import com.facebook.react.ReactNativeHost
import com.facebook.react.ReactPackage
import com.facebook.react.bridge.JavaScriptExecutorFactory
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.load
import com.facebook.react.defaults.DefaultReactHost.getDefaultReactHost
import com.facebook.react.defaults.DefaultReactNativeHost
import com.facebook.react.soloader.OpenSourceMergedSoMapping
import com.facebook.soloader.SoLoader
import com.hudl.jarvis.client.JarvisClientUtil
import com.hudl.jarvis.client.JarvisConstants

class MainApplication : Application(), ReactApplication {

  init {
    JarvisConstants.isRunningOnJarvisClient = BuildConfig.DEBUG
  }

  override val reactNativeHost: ReactNativeHost =
    ReactNativeHostWrapper(this, object : DefaultReactNativeHost(this) {
      override fun getPackages(): List<ReactPackage> =
          PackageList(this).packages.apply {
            // Packages that cannot be autolinked yet can be added manually here, for example:
            // add(MyReactNativePackage())
          }

      override fun getJSMainModuleName(): String = "index"

      override fun getJSBundleFile(): String? {
        return if (BuildConfig.DEBUG) JarvisClientUtil.getJSBundleFile() else super.getJSBundleFile()
      }

      override fun getBundleAssetName(): String? {
        if (BuildConfig.DEBUG) {
          val assetName = JarvisClientUtil.getBundleAssetName()
          return assetName ?: super.getBundleAssetName()
        }

        return super.getBundleAssetName()
      }

      override fun getUseDeveloperSupport(): Boolean = BuildConfig.DEBUG && JarvisClientUtil.getUseDeveloperSupport()

      override fun getJavaScriptExecutorFactory(): JavaScriptExecutorFactory? {
        return HermesExecutorFactory()
      }

      override val isNewArchEnabled: Boolean = BuildConfig.IS_NEW_ARCHITECTURE_ENABLED
      override val isHermesEnabled: Boolean = BuildConfig.IS_HERMES_ENABLED
    })

  override val reactHost: ReactHost
    get() = getDefaultReactHost(applicationContext, reactNativeHost)

  override fun onCreate() {
    super.onCreate()
    if (BuildConfig.DEBUG) {
      JarvisClientUtil.reset(this);
    }

    /* React Native */
    SoLoader.init(this, OpenSourceMergedSoMapping)
    if (BuildConfig.IS_NEW_ARCHITECTURE_ENABLED) {
      // If you opted-in for the New Architecture, we load the native entry point for this app.
      load()
    }

    ApplicationLifecycleDispatcher.onApplicationCreate(this);
  }

  override fun onConfigurationChanged(newConfig: Configuration) {
    super.onConfigurationChanged(newConfig);
    ApplicationLifecycleDispatcher.onConfigurationChanged(this, newConfig);
  }
}