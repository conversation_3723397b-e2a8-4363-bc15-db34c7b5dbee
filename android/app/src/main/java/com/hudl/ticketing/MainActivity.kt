package com.hudl.ticketing

import android.os.Bundle

import expo.modules.ReactActivityDelegateWrapper
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.hudl.rn.detoxcontext.DetoxContext
import com.hudl.jarvis.client.JarvisClientUtil

class MainActivity : ReactActivity() {

  override fun onCreate(savedInstanceState: Bundle?) {
    // react-native-screens setup
    // https://github.com/software-mansion/react-native-screens#android
    super.onCreate(null)
  }

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = if (BuildConfig.DEBUG) JarvisClientUtil.getMainComponentName() else "main"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
   */
  override fun createReactActivityDelegate() = ReactActivityDelegateWrapper(this, BuildConfig.IS_NEW_ARCHITECTURE_ENABLED, object : DefaultReactActivityDelegate(
      this,
      mainComponentName,
      fabricEnabled) {
      override fun getLaunchOptions() = DetoxContext.processLaunchArgs(intent)
  })
}