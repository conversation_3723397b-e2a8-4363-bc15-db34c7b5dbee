"""
The purpose of the `cost` directive is to define a `weight` for GraphQL types, fields, and arguments. Static analysis can use these weights when calculating the overall cost of a query or response.
"""
directive @cost(
  """
  The `weight` argument defines what value to add to the overall cost for every appearance, or possible appearance, of a type, field, argument, etc.
  """
  weight: String!
) on ARGUMENT_DEFINITION | ENUM | FIELD_DEFINITION | INPUT_FIELD_DEFINITION | OBJECT | SCALAR

"""
The purpose of the `@listSize` directive is to either inform the static analysis about the size of returned lists (if that information is statically available), or to point the analysis to where to find that information.
"""
directive @listSize(
  """
  The `assumedSize` argument can be used to statically define the maximum length of a list returned by a field.
  """
  assumedSize: Int

  """
  The `requireOneSlicingArgument` argument can be used to inform the static analysis that it should expect that exactly one of the defined slicing arguments is present in a query. If that is not the case (i.e., if none or multiple slicing arguments are present), the static analysis may throw an error.
  """
  requireOneSlicingArgument: Boolean! = true

  """
  The `sizedFields` argument can be used to define that the value of the `assumedSize` argument or of a slicing argument does not affect the size of a list returned by a field itself, but that of a list returned by one of its sub-fields.
  """
  sizedFields: [String!]

  """
  The `slicingArgumentDefaultValue` argument can be used to define a default value for a slicing argument, which is used if the argument is not present in a query.
  """
  slicingArgumentDefaultValue: Int

  """
  The `slicingArguments` argument can be used to define which of the field's arguments with numeric type are slicing arguments, so that their value determines the size of the list returned by that field. It may specify a list of multiple slicing arguments.
  """
  slicingArguments: [String!]
) on FIELD_DEFINITION

"""Ad targeting model."""
type AdTargeting {
  """The targeting context for the ad."""
  adContext: String

  """Toggle to opt out of targeted ads."""
  canReceivePersonalizedAds: Boolean!
}

"""Representation of a key-value pair input for additional properties."""
input AdditionalPropertiesKeyValuePairInput {
  """Represents the additonal property to set."""
  key: String

  """Represents the value of the additional property."""
  value: String
}

"""Representation of an athlete and guardian relationship."""
type AthleteAndGuardianRelationship {
  """GraphQL encoded user ID for the athlete."""
  athleteUserId: ID!

  """Athlete's birth date."""
  birthDate: Date

  """Athlete's last name."""
  familyName: String!

  """Athlete's gender."""
  gender: Gender

  """Athlete's first name."""
  givenName: String!

  """Athlete's graduation year."""
  graduationYear: Int

  """The guardian relationship."""
  guardianRelationship: GuardianRelationship!
}

type AthleteProfile {
  """The athlete's team details."""
  athleteTeamDetails: [AthleteProfileTeamDetails]

  """The athlete's banner images"""
  bannerImages: AthleteProfileImages

  """The athlete's bio."""
  bio: String

  """The athlete's first name."""
  firstName: String

  """The athlete's follower count."""
  followerCount: Int!

  """The athlete's graduation year."""
  graduationYear: Int!

  """The athlete's height."""
  height: Long!

  """The athlete's highlight view count."""
  highlightViews: Int!

  """The GraphQL id of the athlete."""
  id: ID!

  """The internal id of the athlete."""
  internalId: String

  """The athlete's internal school ids."""
  internalSchoolIds: [String]

  """The athlete's internal team ids."""
  internalTeamIds: [String]

  """The athlete's last name."""
  lastName: String

  """The athlete's lastest GraphQL TeamIds for each sport they played."""
  latestTeamIdForEachSport: [ID]

  """The athlete's latest internal teamIds for each sport they played."""
  latestTeamInternalIdForEachSport: [String]

  """The athlete's primary graphql teamId."""
  primaryTeamId: ID!

  """The athlete's primary internal teamId."""
  primaryTeamInternalId: String

  """The athlete's profile images"""
  profileImages: AthleteProfileImages

  """The athlete's school ids."""
  schoolIds: [ID]

  """The schools for the athlete"""
  schools: [School]

  """The athlete's team ids."""
  teamIds: [ID]

  """The athlete's Twitter Handle"""
  twitterHandle: String

  """The athlete's vitals."""
  vitals: AthleteProfileVitals

  """The athlete's weight."""
  weight: Long!
}

type AthleteProfileImages {
  """Full sized image URL"""
  full: String

  """Normal sized image URL"""
  normal: String

  """Retina sized image URL"""
  retina: String

  """Thumbnail sized image URL"""
  thumbnail: String
}

type AthleteProfileTeamDetails {
  """The gender of the team"""
  gender: Gender!

  """The athlete's graduation year"""
  graduationYear: Short

  """The internal Id of the team"""
  internalTeamId: String

  """Whether the athlete is disabled"""
  isAthleteDisabled: Boolean!

  """The athlete's jersey number"""
  jersey: Byte

  """The level of the team"""
  level: TeamLevel!

  """The full name of the organization"""
  orgFullName: String

  """The short name of the organization"""
  orgShortName: String

  """The athlete's positions on the team"""
  positions: [String]

  """The GraphQL Id of the school"""
  schoolId: ID!

  """The sport of the team"""
  sport: Sport!

  """The GraphQL Id of the team"""
  teamId: ID!

  """The name of the team"""
  teamName: String
}

type AthleteProfileVitals {
  """The athlete's Achievements."""
  achievements: [String]

  """The athlete's Approaching Jump Touch with One Arm."""
  approachJumpTouchOneArm: String

  """The athlete's Bench."""
  bench: String

  """The athlete's Bench Press Reps."""
  benchPressReps: String

  """Whether the athlete's Bench Press Reps is verified."""
  benchPressRepsVerified: Boolean!

  """Whether the athlete's Bench is verified."""
  benchVerified: Boolean!

  """The athlete's Clean."""
  clean: String

  """The athlete's Dead Lift."""
  deadLift: String

  """The athlete's Forty."""
  forty: String

  """Whether the athlete's Forty is verified."""
  fortyVerified: Boolean!

  """The athlete's 100 Meter Time."""
  meter100: String

  """The athlete's 400 Meter Time."""
  meter400: String

  """The athlete's 1600 Meter Time."""
  meter1600: String

  """The athlete's 3200 Meter Time."""
  meter3200: String

  """The athlete's Nike Football rating"""
  nikeFootballRating: Float

  """Whether the athlete's Nike Football rating is verified"""
  nikeFootballRatingVerified: Boolean!

  """The athlete's Powerball."""
  powerball: String

  """Whether the athlete's Powerball is verified."""
  powerballVerified: Boolean!

  """The athlete's Pro Agility."""
  proAgility: String

  """The athlete's Shuttle."""
  shuttle: String

  """Whether the athlete's Shuttle is verified."""
  shuttleVerified: Boolean!

  """The athlete's Six Touches Sideline to Sideline."""
  sixTouchesSidelineToSideline: String

  """The athlete's Squat."""
  squat: String

  """The athlete's Standing Blocking Reach."""
  standingBlockingReach: String

  """The athlete's Standing Reach."""
  standingReach: String

  """The athlete's Vertical."""
  vertical: String

  """The athlete's Vertical Jump with One Arm."""
  verticalJumpOneArm: String

  """The athlete's Vertical Jumping Block with Two Arms."""
  verticalJumpingBlockTwoArms: String

  """Whether the athlete's Vertical is verified."""
  verticalVerified: Boolean!
}

type AuthenticationPayload {
  endDate: DateTime
  licenseType: LicenseType
  machine: String
  publicHash: String
  registration: String
  schoolName: String
  signature: String
  special: String
  startDate: DateTime
  subscriptionEndDate: DateTime
  subscriptionStartDate: DateTime
  token: String
}

type BackgroundImage {
  """The image content server id"""
  contentServerId: String

  """The image height"""
  height: Int

  """Returns true if contentServerId or path is defined"""
  isValid: Boolean!

  """The image path"""
  path: String

  """The image secure url"""
  secureUrl: String

  """The image size"""
  size: ImageSize!

  """The image url"""
  url: String

  """The image width"""
  width: Int
}

type Broadcast {
  """List of access passes for the broadcast."""
  accessPassIds: [String]

  """Broadcast availability."""
  available: Boolean!

  """The time the broadcast will begin in the UTC timezone."""
  broadcastDateUtc: DateTime!

  """The internal broadcastId of the broadcast."""
  broadcastId: String

  """
  The Id of the user or device that created the broadcast. This value could represent a userId for a mobile broadcast, an installationId for a focus broadcast, etc
  """
  broadcastOriginatorId: String

  """
  The name of the user or device that created the broadcast. This value could represent a user's name for a mobile broadcast, a device name for a focus broadcast, etc
  """
  broadcastOriginatorName: String

  """
  The internal BsonId of the broadcast. Refers to the Id originating from hudl-broadcasts.
  """
  bsonId: String

  """The date the broadcast was last modified."""
  dateModified: DateTime!

  """The description of the broadcast."""
  description: String

  """Indicates if the broadcast has domain blocking rules."""
  domainBlocking: Boolean!

  """String url to start the download process for the broadcast."""
  downloadUrl: String

  """The current duration of the broadcast."""
  duration: Float!

  """
  String HTML code used to embed the broadcast into a webpage using an iFrame.
  """
  embedCode: String

  """The src string from the embed code."""
  embedCodeSrc: String

  """Determines if the broadcast should be hidden from the user."""
  hidden: Boolean!

  """
  The Id of the broadcast. Refers to the encoded BroadcastId originating from vCloud.
  """
  id: ID!

  """
  The internal Id of the broadcast. Refers to the BroadcastId originating from vCloud.
  """
  internalId: String

  """String url to poster image uploaded to broadcast. Large version."""
  largeThumbnail: String

  """The duration of the broadcast when it was live."""
  liveDuration: Float!

  """Related media stream for a broadcast."""
  mediaStreamId: String

  """String url to poster image uploaded to broadcast. Medium version."""
  mediumThumbnail: String

  """The number of syndication endpoints for the broadcast."""
  numSyndicationEndpoints: Int!

  """Indicates if the broadcast has geoblocking rules."""
  regionBlocking: Boolean!

  """Indicates whether the broadcast requires login for pay-per-view."""
  requireLogin: String

  """The identifier of the schedule entry associated with this broadcast."""
  scheduleEntryId: ID

  """The public schedule entry summary for the broadcast"""
  scheduleEntrySummary: ScheduleEntryPublicSummary

  """The identifier of the school that owns this broadcast."""
  schoolId: ID

  """The Scoring Session for the broadcast."""
  scoreboardSession: ScoreboardSession

  """The internalId of the season associated with this broadcast."""
  seasonId: ID

  """
  The sectionId that classifies this broadcast. Sections correlate to Hudl genders and sports.
  """
  sectionId: Int!

  """
  The title of the section that classifies this broadcasts. Sections correlate to Hudl genders and sports.
  """
  sectionTitle: String

  """Indicates that the broadcast was shared to a site that doesn't own it."""
  shared: Boolean!

  """List of site slugs that this broadcast is shared with."""
  sharedSites: [String]

  """The siteId of the site that owns the broadcast."""
  siteId: String

  """The site slug of the site that owns the broadcast."""
  siteSlug: String

  """The title of the site that owns this broadcast."""
  siteTitle: String

  """String url to poster image uploaded to broadcast. Small version."""
  smallThumbnail: String

  """
  If set, the GraphQL encoded broadcastId that this broadcast uses video data from. This broadcast would be considered a simulcast of the source broadcast.
  """
  sourceBroadcastId: ID

  """The status of the broadcast."""
  status: String

  """The identifier of the team that owns this broadcast."""
  teamId: ID

  """
  The timezone the broadcast will be recorded in. Used to convert BroadcastDateUtc.
  """
  timezone: String

  """The title of the broadcast."""
  title: String

  """The upload source of the livestream"""
  uploadSource: PublishSessionUploadSource!
}

"""A connection to a list of items."""
type BroadcastConnection {
  """A list of edges."""
  edges: [BroadcastEdge!]
  items: [Broadcast!]

  """A flattened list of the nodes."""
  nodes: [Broadcast]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type BroadcastEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: Broadcast
}

enum BroadcastFilter {
  ALL
  ARCHIVED
  LIVE
  SCHEDULED
  STREAMING
  UPCOMING
  UPCOMING_OR_STREAMING
}

enum BroadcastSortType {
  BROADCAST_DATE
}

"""
The `Byte` scalar type represents non-fractional whole numeric values. Byte can represent values between 0 and 255.
"""
scalar Byte

type Carrier {
  """Description for the phone carrier"""
  description: String

  """Carrier value of the enum"""
  value: String
}

enum CommunityContentContainerPlayer {
  EMBEDDABLE_PLAYER
  IMMERSIVE
  NATIVE_SOCIAL
  PARTNER
  TIMELINE_CARD
  UNKNOWN
  VIDEO_MANAGEMENT_PLAYER
  VIDEO_PAGE
}

enum CommunityContentContainerType {
  EMBEDDABLE_PLAYER
  EXPLORE
  HOME
  OWNED_SOCIAL
  PARTNER
  PERFORMANCE_CENTER
  PROFILE
  UNKNOWN
  VIDEO_MANAGEMENT
  VIDEO_PAGE
}

"""Used to link metadata to community content."""
input CommunityContentIdInput {
  """
  Specifies the unique ID of this content, e.g. the reelId for a highlight.
  """
  relatedId: String

  """
  Specifies a secondary ID that may be necessary when querying for a piece of content, e.g. the ownerId for a highlight.
  """
  secondaryRelatedId: String

  """Specifies the type of content referenced."""
  type: CommunityContentType!
}

enum CommunityContentRequestingApp {
  ANDROID
  IOS
  UNKNOWN
  WEB
}

enum CommunityContentType {
  FEED_CONTENT
  HUDL_HIGHLIGHT
  MESSAGE
  PERFORMANCE_CENTER
  TEAM_HIGHLIGHT
  UNKNOWN
  USER_HIGHLIGHT
}

type CompetitionHeader {
  """The competition periods for the competition."""
  competitionPeriods: [CompetitionPeriodHeader]

  """
  The ISO 3166-1 alpha-3 code representing the country of the Competition
  """
  countryIso: String

  """The current competition period for the competition"""
  currentCompetitionPeriod: CompetitionPeriodHeader

  """The gender of the Competition."""
  gender: Gender!

  """The Id of the Governing Body the Competition belongs to"""
  governingBodyId: String

  """The Id of the Competition"""
  id: ID!

  """The internal Id of the Competition"""
  internalId: String

  """The name of the Competition"""
  name: String

  """The sport of the Competition."""
  sport: Sport!

  """The Id of the Subdivision the Competition belongs to"""
  subdivisionId: String

  """
  The ISO 3166-2 code representing the state or province of the Competition
  """
  subdivisionIso: String
}

type CompetitionPeriodHeader {
  """The Id of the Comeptition the period belongs to"""
  competitionId: String

  """The UTC time that the Competition Period ends"""
  endDateUtc: DateTime

  """The graphQL Id of the Competition Period"""
  id: ID!

  """The internal Id of the Competition Period"""
  internalId: String

  """The name of the Competition Period"""
  name: String

  """The UTC time that the Competition Period starts"""
  startDateUtc: DateTime

  """The list of team Ids associated with the Competition Period"""
  teamIds: [String]
}

"""
Representation of an input to create a new provisional athlete and guardian relationship.
"""
input CreateProvisionalAthleteAndGuardianRelationshipInput {
  """Provisional athlete's birth date."""
  birthDate: Date!

  """User ID of the person creating the provisional athlete."""
  creatingUserId: String!

  """Provisional athlete's last name."""
  familyName: String!

  """Provisional athlete's gender."""
  gender: Gender!

  """Provisional athlete's first name."""
  givenName: String!

  """Provisional athlete's graduation year."""
  graduationYear: Int!
}

"""Representation of an input to create a terminal connection token."""
input CreateTerminalConnectionTokenInput {
  """The access code for the org that is using terminal payments."""
  accessCode: String!

  """The ID of the org that is using terminal payments"""
  organizationId: ID!
}

"""Input to create a payment token for a cart of ticketing items."""
input CreateTicketingPaymentTokenInput {
  """The expected total amount in cents to charge for the token."""
  expectedTotalInCents: Int!

  """A list of ticketing items to include in the total for the token."""
  lineItems: [TicketingPricingLineItemInput]!

  """The organization ID to use for the token."""
  organizationId: ID!

  """The source of the cart of ticketing items."""
  source: String
}

"""Represents a cursor"""
scalar Cursor

"""The `Date` scalar represents an ISO-8601 compliant date type."""
scalar Date

"""
The `DateTimeType` scalar represents an ISO-8601 compliant date time type
"""
scalar DateTime

"""Input parameters for a deregistration operation."""
input DeregistrationInput {
  """The client mutation identifier."""
  clientMutationId: String

  """The machine identifier."""
  machineIdentifier: String!

  """The machine name."""
  machineName: String

  """The registration code."""
  registrationCode: String!

  """The License security token."""
  token: String!

  """The installed version of Sportscode."""
  userVersion: String!
}

"""The result of a deregistration operation."""
type DeregistrationPayload {
  """The client mutation identifier."""
  clientMutationId: String

  """Indication of whether the operation was successful or not."""
  isSuccessful: Boolean
}

"""A connection to a list of items."""
type DiscoverablePassConfigsConnection {
  """A list of edges."""
  edges: [DiscoverablePassConfigsEdge!]

  """A flattened list of the nodes."""
  nodes: [PassConfig]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type DiscoverablePassConfigsEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: PassConfig
}

"""A connection to a list of items."""
type DiscoverableTicketedEventsConnection {
  """A list of edges."""
  edges: [DiscoverableTicketedEventsEdge!]

  """A flattened list of the nodes."""
  nodes: [TicketedEvent]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type DiscoverableTicketedEventsEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: TicketedEvent
}

"""Representation of an embedded organization program type."""
type EmbeddedOrganizationProgramType {
  """The Id of the Organization Program Type."""
  id: ID!

  """The name of the program type."""
  name: String!
}

enum EntityType {
  ATHLETE
  ORGANIZATION
  TEAM
  USER
}

type FanBroadcast {
  """Broadcast availability."""
  available: Boolean!

  """The time the broadcast will begin in the UTC timezone."""
  broadcastDateUtc: DateTime!

  """The internal broadcastId of the broadcast."""
  broadcastId: String

  """The description of the broadcast."""
  description: String

  """The current duration of the broadcast."""
  duration: Float!

  """
  String HTML code used to embed the broadcast into a webpage using an iFrame.
  """
  embedCode: String

  """The src string from the embed code."""
  embedCodeSrc: String

  """Determines if the broadcast should be hidden from the user."""
  hidden: Boolean!

  """The broadcast id."""
  id: ID!

  """Determines if the broadcasts corresponding team profile is hidden"""
  isTeamProfileHidden: Boolean!

  """String url to poster image uploaded to broadcast. Large version."""
  largeThumbnail: String

  """String url to poster image uploaded to broadcast. Medium version."""
  mediumThumbnail: String

  """The number of syndication endpoints for the broadcast."""
  numSyndicationEndpoints: Int!

  """The opponent organization of the broadcast."""
  opponentOrganization: FanContentOrganization

  """The organization of the broadcast."""
  organization: FanContentOrganization

  """Indicates whether the broadcast requires login for pay-per-view."""
  requireLogin: String

  """The identifier of the schedule entry associated with this broadcast."""
  scheduleEntryId: ID

  """The identifier of the school that owns this broadcast."""
  schoolId: ID

  """List of site slugs that this broadcast is shared with."""
  sharedSites: [String]

  """The siteId of the site that owns the broadcast."""
  siteId: String

  """String url to poster image uploaded to broadcast. Small version."""
  smallThumbnail: String

  """The status of the broadcast."""
  status: String

  """The team header of the broadcast."""
  teamHeader: FanContentTeamHeader

  """The identifier of the team that owns this broadcast."""
  teamId: ID

  """The title of the broadcast."""
  title: String
}

"""A connection to a list of items."""
type FanBroadcastConnection {
  """A list of edges."""
  edges: [FanBroadcastEdge!]
  items: [FanBroadcast!]

  """A flattened list of the nodes."""
  nodes: [FanBroadcast]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type FanBroadcastEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: FanBroadcast
}

enum FanBroadcastFilter {
  ALL
  ARCHIVED
  LIVE
  SCHEDULED
  STREAMING
  UPCOMING
  UPCOMING_OR_STREAMING
}

enum FanBroadcastSortType {
  BROADCAST_DATE
}

type FanContentOrganization {
  """The abbreviation of the organization."""
  abbreviation: String

  """First address line of the organization."""
  addressLine1: String

  """Second address line of the organization."""
  addressLine2: String

  """The banner image uri of the organization."""
  bannerImageUri: String

  """The city where the organization is located."""
  city: String

  """The country where the organization is located."""
  country: String

  """The full name of the organization."""
  fullName: String

  """The internal id of the organization."""
  internalOrganizationId: String

  """The mascot of the organization."""
  mascot: String

  """Maps to how an organization is classified"""
  orgClassificationId: Int!

  """The fan x profile url of the organization."""
  organizationProfileUrl: String

  """The primary color of the organization."""
  primaryColor: String

  """The uri to the profile image of the organization."""
  profileImageUri: String

  """The secondary color of the organization."""
  secondaryColor: String

  """The short name of the organization."""
  shortName: String

  """The state where the organization is located."""
  state: String

  """The zip code where the organization is located."""
  zipCode: String
}

type FanContentTeamHeader {
  """Current season year"""
  currentSeasonYear: Int

  """The gender of the team"""
  gender: Gender!

  """Internal Id of the team"""
  internalTeamId: String

  """The logo of the team"""
  logo: String

  """The name of the team"""
  name: String

  """The primary color of the team"""
  primaryColor: String

  """The sport of the team"""
  sport: Sport!

  """The level of the team"""
  teamLevel: TeamLevel!

  """The fan x profile url of the team."""
  teamProfileUrl: String
}

type FanHighlightReel {
  """The creation time of the highlight reel."""
  createdAt: DateTime!

  """The description of the highlight reel."""
  description: String

  """The duration of the highlight reel."""
  duration: String

  """
  The duration of the highlight reel in milliseconds, expressed as a number.
  """
  durationInMs: Float!

  """The GraphQL encoded highlight reel Id."""
  id: ID!

  """The internal highlight reel Id."""
  internalId: String

  """Indicates if any of the corresponding teams have their profile hidden."""
  isAnyTeamProfileHidden: Boolean!

  """Indicates if the highlight reel was auto generated"""
  isAutoGen: Boolean!

  """The internal Id of the highlight reel owner."""
  ownerId: String

  """The type of owner of the highlight reel."""
  ownerType: HighlightReelOwnerType!

  """The list of teams related to the highlight reel."""
  teamInfo: [FanHighlightReelTeamInfo]

  """The thumbnail for the highlight reel."""
  thumbnail: String

  """The title of the highlight reel."""
  title: String

  """The video url for the highlight reel."""
  videoUrl: String

  """The number of views on the highlight reel."""
  views: Long!
}

"""A connection to a list of items."""
type FanHighlightReelConnection {
  """A list of edges."""
  edges: [FanHighlightReelEdge!]
  items: [FanHighlightReel!]

  """A flattened list of the nodes."""
  nodes: [FanHighlightReel]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type FanHighlightReelEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: FanHighlightReel
}

type FanHighlightReelTeamInfo {
  """The organization of the highlight reel."""
  organization: FanContentOrganization

  """
  The GraphQL encoded scheduleEntryIds of the games referenced by the highlight reel.
  """
  scheduleEntryIds: [ID]

  """The team header of the team."""
  teamHeader: FanContentTeamHeader
}

enum FanHighlightSortType {
  CREATION_DATE
  VIEW_COUNT
}

enum FanLeagueEntityType {
  COMPETITION
  COMPETITION_PERIOD
  GOVERNING_BODY
  MUNICIPALITY
  REGIONAL_ALIGNMENT
  SUBDIVISION
  UNKNOWN
}

enum FanLeagueMemberSortType {
  ORGANIZATION_NAME
}

enum FanLeagueMemberType {
  ORGANIZATION
  UNKNOWN
}

type FanScheduleEntry {
  """The status of the schedule entries' corresponding broadcast."""
  broadcastStatus: String

  """The numeric value correlating to the GameType enum from hudl-schedules"""
  gameType: Int!

  """The numeric value correlating to the Gender enum"""
  genderId: Int!

  """The GraphQL encoded fan schedule entry id."""
  id: ID!

  """The internal schedule entry id."""
  internalScheduleEntryId: String

  """Determines if the team that owns the schedule entry is hidden"""
  isTeamProfileHidden: Boolean!

  """
  The location of the schedule entry. Corresponds to the ScheduleEntryLocation enum.
  """
  location: Int!

  """The opponent name."""
  opponentName: String

  """The opponent organization."""
  opponentOrganization: FanContentOrganization

  """The GraphQL encoded opponent school id."""
  opponentSchoolId: ID

  """The opponent team header."""
  opponentTeamHeader: FanContentTeamHeader

  """The GraphQL encoded opponent team id."""
  opponentTeamId: ID

  """The owning organization of the schedule entry."""
  organization: FanContentOrganization

  """
  The outcome of the schedule entry. Corresponds to the ScheduleEntryOutcome enum.
  """
  outcome: Int!

  """The projected end time of the schedule entry."""
  projectedEndTime: DateTime

  """The GraphQL encoded schedule entry id."""
  scheduleEntryId: ID!

  """The GraphQL encoded id of the school that owns the schedule entry"""
  schoolId: ID!

  """
  One of the team scores for the schedule entry. Typically correlates to the owning team score.
  """
  score1: Short

  """
  One of the team scores for the schedule entry. Typically correlates to the opponent team score.
  """
  score2: Short

  """The GraphQL encoded season id of the schedule entry."""
  seasonId: ID!

  """The sport id of the schedule entry."""
  sportId: Int!

  """The team header of the team that owns the schedule entry."""
  teamHeader: FanContentTeamHeader

  """The GraphQL encoded team id of the team that owns the schedule entry."""
  teamId: ID!

  """The GraphQL encoded id of the ticketed event"""
  ticketedEventId: ID

  """The event status of the ticketed event"""
  ticketedEventStatus: String

  """The time in UTC that the schedule entry starts."""
  timeUtc: DateTime!
}

"""A connection to a list of items."""
type FanScheduleEntryConnection {
  """A list of edges."""
  edges: [FanScheduleEntryEdge!]
  items: [FanScheduleEntry!]

  """A flattened list of the nodes."""
  nodes: [FanScheduleEntry]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type FanScheduleEntryEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: FanScheduleEntry
}

enum FanScheduleEntrySortType {
  SCHEDULE_ENTRY_DATE
}

type FanSearchAthlete {
  """The abbreviated name of the athlete."""
  abbreviation: String

  """The city of their primary team."""
  city: String

  """The country of their primary team."""
  country: String

  """The profile image uri of the athlete."""
  iconUri: String

  """The GraphQL Id of the athlete."""
  id: ID!

  """The internal Id of the athlete."""
  internalId: String

  """The jersey number on their primary team."""
  jersey: Int

  """The full name of the athlete."""
  name: String

  """
  A list of the org types associated with the team that the athlete belongs to.
  """
  organizationClassificationIds: [Int]

  """The main position on their primary team."""
  position: String

  """The primary color of their primary team."""
  primaryColor: String

  """The full name of the athletes primary organization."""
  primaryOrganizationName: String

  """The Id of the athletes primary team."""
  primaryTeamId: ID!

  """The secondary color of their primary team."""
  secondaryColor: String

  """The Id of the sport of the primary team of the athlete."""
  sportId: Int!

  """The state of their primary team."""
  state: String
}

type FanSearchLeague {
  """The abbreviated name of the league."""
  abbreviation: String

  """The city of the league."""
  city: String

  """The country of the league."""
  country: String

  """The profile picture of the league."""
  iconUri: String

  """The GraphQL Id of the league."""
  id: ID!

  """The internal Id of the league."""
  internalId: String

  """The name of the league."""
  name: String

  """The primary color of the league."""
  primaryColor: String

  """The secondary color of the league."""
  secondaryColor: String

  """The subdivision of the league."""
  subdivision: String
}

type FanSearchOrganization {
  """The abbreviated name of the organization."""
  abbreviation: String

  """The city of the organization."""
  city: String

  """The country of the organization."""
  country: String

  """The profile picture of the organization."""
  iconUri: String

  """The GraphQL Id of the organization."""
  id: ID!

  """The internal Id of the organization."""
  internalId: String

  """The name of the organization."""
  name: String

  """The list of classifcation Ids of the organization."""
  organizationClassificationId: Int!

  """The primary color of the organization."""
  primaryColor: String

  """The secondary color of the organization."""
  secondaryColor: String

  """The state of the organization."""
  state: String
}

input FanSearchRequestInput {
  """The OrganizationClassificationIds to filter by."""
  organizationClassificationIds: [Int!]!

  """The number of results to return for each type. Used for pagination."""
  resultSize: Int!

  """
  The 0-based index to start from when getting search results. Used for pagination.
  """
  resultStartIndex: Int!

  """The term or terms to search by."""
  searchTerm: String!

  """The entity types you want to return."""
  types: [SearchItemType!]!
}

type FanSearchResult {
  """
  The result for an athlete. Will be populated when the Type equals 'Athlete'
  """
  athlete: FanSearchAthlete

  """
  The result for a club. Will be populated when the Type equals 'Club'. You can also retrieve clubs from the organization type
  """
  club: FanSearchOrganization

  """
  The result for a league. Will be populated when the Type equals 'Municipality', 'RegionalAlignment' or 'GoverningBody'
  """
  league: FanSearchLeague

  """
  The result for an organization. Will be populated when the Type equals 'Organization'
  """
  organization: FanSearchOrganization

  """The entity type that will be returned."""
  type: SearchItemResultType!
}

enum FeeResponsibility {
  CUSTOMER
  ORGANIZATION
  UNSET
}

enum FeedUserType {
  HUDL
  TEAM
  UNKNOWN
  USER
}

"""A field on an event or pass to be filled out by the user."""
type FormField {
  """The time the Form Field was created."""
  createdAt: DateTime!

  """The time the Form Field was deleted, if it was deleted."""
  deletedAt: DateTime

  """The type of the Form Field."""
  fieldType: String!

  """The help text for the Form Field."""
  helpText: String

  """The ID of the Form Field."""
  id: ID!

  """If the Form Field is a required field."""
  isRequired: Boolean!

  """The label for the Form Field."""
  label: String!

  """
  The unique identifier for the Organization that the Form Field belongs to.
  """
  organizationId: ID!

  """The time the Form Field was last updated."""
  updatedAt: DateTime!
}

"""A field on an event or pass to be filled out by the user."""
type FormFieldResponse {
  """The unique identifier for the Form Field this response is for."""
  formFieldId: ID!

  """The response to the Form Field."""
  response: [String!]!
}

input FormFieldResponseInput {
  """The ID of the form field."""
  formFieldId: ID!

  """Response to the form field."""
  response: [String]
}

"""Representation of a form reference."""
type FormRef {
  """The display order of the form reference."""
  displayOrder: Int!

  """The form ID."""
  formId: ID!

  """Whether the form reference is enabled."""
  isEnabled: Boolean!
}

enum Gender {
  COED
  MENS
  WOMENS
}

"""Representation of a generic Scoreboard Entry."""
type GenericScoreboardEntry {
  """The current game clock in seconds in the generic scoreboard entry."""
  gameClock: Int!

  """The GraphQL id of the generic scoreboard entry."""
  id: ID!

  """The current period in the generic scoreboard entry."""
  period: String

  """
  The id of the scoreboard session the generic scoreboard entry belongs to.
  """
  scoreboardSessionId: String!

  """Team 1's current score in the generic scoreboard entry."""
  team1Score: Int!

  """Team 2's current score in the generic scoreboard entry."""
  team2Score: Int!

  """The UTC timestamp of the generic scoreboard entry."""
  timeUtc: DateTime!
}

"""Representation of a generic Scoreboard Settings object."""
type GenericScoreboardSettings {
  """Whether or not the game clock is displayed on the scoreboard overlay."""
  gameClockOn: Boolean!

  """Whether or not the team names are displayed on the scoreboard overlay."""
  namesOn: Boolean!

  """
  Whether or not the scoreboard overlay should be displayed on the video.
  """
  overlayOn: Boolean!

  """Whether or not the period is displayed on the scoreboard overlay."""
  periodOn: Boolean!

  """Whether or not the scores are displayed on the scoreboard overlay."""
  scoresOn: Boolean!

  """Team 1's name that is displayed on the scoreboard overlay."""
  team1Name: String!

  """Team 1's primary color that is displayed on the scoreboard overlay."""
  team1PrimaryColor: String!

  """Team 2's name that is displayed on the scoreboard overlay."""
  team2Name: String!

  """Team 2's primary color that is displayed on the scoreboard overlay."""
  team2PrimaryColor: String!

  """The UTC timestamp of the scoreboard settings."""
  timeUtc: DateTime!
}

"""Input to retrieve ad context information."""
input GetAdTargetingInput {
  """IP Address for user to get ad context for."""
  ip: String!
}

"""Input to retrieve all ticket groups associated with a ticketed event"""
input GetAssociatedTicketGroupsWithAccessCodeInput {
  """The access code associated with the organization"""
  accessCode: String!

  """Value used to start a page of ticket groups."""
  after: Cursor

  """Value used to end a page of ticket groups."""
  before: Cursor

  """
  Number of ticket groups requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """
  Number of ticket groups requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """GraphQL encoded Id of the organization the access code associated with"""
  organizationId: ID!

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """The sort type to use for the ticket groups."""
  sortType: TicketGroupSortType!

  """GraphQL encoded Id of the ticketed event to retrieve ticket groups for"""
  ticketedEventId: ID!

  """A list of source types to filter the ticket groups by."""
  validSources: [String!]
}

"""Input to get guardian relationships and athlete info."""
input GetAthleteAndGuardianRelationshipsInput {
  """The user ID used to retrieve guardian relationships and athlete info."""
  userId: ID!
}

"""Representation of an input to fetch broadcasts."""
input GetBroadcastsPaginatedInput {
  """Value used to start a page of broadcasts."""
  after: Cursor

  """Value used to end a page of broadcasts."""
  before: Cursor

  """Used to further filter broadcasts by status."""
  broadcastStatusFilter: BroadcastFilter!

  """
  If set, will return broadcasts that happened before the requested date. Can be used with StartDate to create a date range.
  """
  endDate: DateTime

  """
  Number of broadcasts requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """If true, will include broadcasts that are hidden"""
  includeHiddenBroadcasts: Boolean

  """If true, will include broadcasts from hidden teams"""
  includeHiddenTeams: Boolean

  """If true, will include broadcasts that are unavailable"""
  includeUnavailableBroadcasts: Boolean

  """
  Number of broadcasts requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """Filters broadcasts by GraphQL encoded scheduleEntryIds."""
  scheduleEntryIds: [ID!]

  """Filters broadcasts by GraphQL encoded schoolIds."""
  schoolIds: [ID!]

  """Filters broadcasts by vCloud siteIds."""
  siteIds: [String]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting broadcasts."""
  sortType: BroadcastSortType!

  """
  If set, will return broadcasts that happened after the requested date. Can be used with EndDate to create a date range.
  """
  startDate: DateTime

  """Filters broadcasts by GraphQL encoded teamIds."""
  teamIds: [ID!]
}

input GetCanonicalUrlForVanityUrlInput {
  """
  The slug of the vanity url. The slug is the last, unique tail segment of the url. Optional if vanityUrl is provided.
  """
  vanitySlug: String

  """
  The vanity url. The slug is assumed to be the last unique tail segment of the url. Optional if vanitySlug is provided.
  """
  vanityUrl: String
}

"""
Representation of an input to fetch discoverable league pass configs linked to a league.
"""
input GetDiscoverableLeaguePassConfigsByLeagueInput {
  """The ID of the league associated with the pass configs."""
  leagueEntityId: ID!

  """The type of the league associated with the pass configs."""
  leagueEntityType: String!
}

"""Input to fetch paginated broadcasts."""
input GetFanBroadcastsPaginatedInput {
  """Value used to start a page of broadcasts."""
  after: Cursor

  """Value used to end a page of broadcasts."""
  before: Cursor

  """Used to further filter broadcasts by status."""
  broadcastStatusFilter: FanBroadcastFilter!

  """Filters broadcasts by GraphQL encoded competitionIds."""
  competitionIds: [ID!]

  """Filters broadcasts by GraphQL encoded competitionPeriodIds."""
  competitionPeriodIds: [ID!]

  """
  Return broadcasts that occur before specified date. Can be used in tandem with StartDate to create a date range.
  """
  endDate: DateTime

  """
  Number of broadcasts requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """Filters broadcasts by GraphQL encoded governingBodyIds."""
  governingBodyIds: [ID!]

  """If true, will include hidden broadcasts."""
  includeHiddenBroadcasts: Boolean

  """If true, will include broadcast's from hidden teams."""
  includeHiddenTeams: Boolean

  """If true, will include unavailable broadcasts."""
  includeUnavailableBroadcasts: Boolean

  """
  Number of broadcasts requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """Filters broadcasts by GraphQL encoded municipalityIds."""
  municipalityIds: [ID!]

  """Filters broadcasts by GraphQL encoded opponentOrganizationIds."""
  opponentOrganizationIds: [ID!]

  """Filters broadcasts by GraphQL encoded regionalAlignmentIds."""
  regionalAlignmentIds: [ID!]

  """Filters broadcasts by GraphQL encoded scheduleEntryIds."""
  scheduleEntryIds: [ID!]

  """Filters broadcasts by GraphQL encoded schoolIds."""
  schoolIds: [ID!]

  """Filters broadcasts by vCloud siteIds."""
  siteIds: [String]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting broadcasts."""
  sortType: FanBroadcastSortType!

  """
  Return broadcasts that occur after specified date. Can be used in tandem with EndDate to create a date range.
  """
  startDate: DateTime

  """Filters broadcasts by GraphQL encoded subdivisionIds."""
  subdivisionIds: [ID!]

  """Filters broadcasts by GraphQL encoded teamIds."""
  teamIds: [ID!]
}

"""Input to fetch paginated highlight reels."""
input GetFanHighlightReelsPaginatedInput {
  """Value used to start a page of highlight reels."""
  after: Cursor

  """
  Filters highlight reels by GraphQL encoded athleteUserIds mapped to OwnerId with OwnerType = User.
  """
  athleteUserIds: [ID!]

  """Value used to end a page of highlight reels."""
  before: Cursor

  """Filters highlight reels by GraphQL encoded competitionIds."""
  competitionIds: [ID!]

  """Filters highlight reels by GraphQL encoded competitionPeriodIds."""
  competitionPeriodIds: [ID!]

  """
  Return highlight reels that occur before specified date. Can be used in tandem with StartDate to create a date range.
  """
  endDate: DateTime

  """
  Number of highlight reels requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """Filters highlight reels by GraphQL encoded governingBodyIds."""
  governingBodyIds: [ID!]

  """
  If true, will include highlight reels associated with any teams that have their profiles hidden.
  """
  includeHiddenTeams: Boolean!

  """
  Number of highlight reels requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """Filters highlight reels by GraphQL encoded municipalityIds."""
  municipalityIds: [ID!]

  """Filters highlight reels by GraphQL encoded regionalAlignmentIds."""
  regionalAlignmentIds: [ID!]

  """Filters highlight reels by GraphQL encoded scheduleEntryIds."""
  scheduleEntryIds: [ID!]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting highlight reels."""
  sortType: FanHighlightSortType!

  """
  Return highlight reels that occur after specified date. Can be used in tandem with EndDate to create a date range.
  """
  startDate: DateTime

  """Filters highlight reels by GraphQL encoded subdivisionIds."""
  subdivisionIds: [ID!]

  """
  Filters highlight reels by GraphQL encoded teamIds mapped to OwnerId with OwnerType = Team.
  """
  teamIds: [ID!]
}

"""Input to fetch paginated schedule entries."""
input GetFanScheduleEntriesPaginatedInput {
  """Value used to start a page of schedule entries."""
  after: Cursor

  """Value used to end a page of schedule entries."""
  before: Cursor

  """Filters schedule entries by GraphQL encoded competitionIds."""
  competitionIds: [ID!]

  """Filters schedule entries by GraphQL encoded competitionPeriodIds."""
  competitionPeriodIds: [ID!]

  """
  Return schedule entries that occur before specified date. Can be used in tandem with FilterStartDate to create a date range.
  """
  filterEndDate: DateTime

  """
  Return schedule entries that occur after specified date. Can be used in tandem with FilterEndDate to create a date range.
  """
  filterStartDate: DateTime

  """
  Number of schedule entries requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """Filters schedule entries by GenderId."""
  genderId: Int

  """Filters schedule entries by GraphQL encoded governingBodyIds."""
  governingBodyIds: [ID!]

  """If true, will include schedule entries belonging to hidden teams."""
  includeHiddenTeams: Boolean!

  """If true, will include in progress schedule entries."""
  includeInProgressScheduleEntries: Boolean!

  """If true, will include schedule entries without reported scores."""
  includeUnreportedScores: Boolean

  """
  Number of schedule entries requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """Filters schedule entries by ScheduleEntryLocation."""
  location: Int

  """Filters schedule entries by GraphQL encoded municipalityIds."""
  municipalityIds: [ID!]

  """Filters schedule entries by ScheduleEntryOutcome."""
  outcome: Int

  """Filters schedule entries by GraphQL encoded regionalAlignmentIds."""
  regionalAlignmentIds: [ID!]

  """Filters schedule entries by GraphQL encoded scheduleEntryIds."""
  scheduleEntryIds: [ID!]

  """Filters schedule entries by GraphQL encoded schoolIds."""
  schoolIds: [ID!]

  """Filters schedule entries by GraphQL encoded seasonIds."""
  seasonIds: [ID!]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting schedule entries."""
  sortType: FanScheduleEntrySortType!

  """Filters schedule entries by SportId."""
  sportId: Int

  """Filters schedule entries by GraphQL encoded subdivisionIds."""
  subdivisionIds: [ID!]

  """Filters schedule entries by GraphQL encoded teamIds."""
  teamIds: [ID!]
}

"""Representation of an input to fetch highlight reel summaries."""
input GetHighlightReelSummariesInput {
  """Cursor used to start a page of summaries."""
  after: Cursor

  """Filters highlights by athleteUserIds."""
  athleteUserIds: [ID!]

  """Cursor used to end a page of summaries."""
  before: Cursor

  """If true, will exclude highlights that are private and owned by a team."""
  excludePrivateTeamHighlights: Boolean

  """
  Number of summaries requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """Filters highlights by deletion status."""
  includeDeleted: Boolean!

  """Filters highlights by publish status."""
  includeDrafts: Boolean!

  """If true, will include highlight's from hidden teams"""
  includeHiddenTeams: Boolean

  """
  Number of summaries requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """If set, this will bypass all privacy checks."""
  overridePrivacy: Boolean!

  """Filters highlights by scheduleEntryIds. Maps to gameId property."""
  scheduleEntryIds: [ID!]

  """Filters highlights by schoolIds."""
  schoolIds: [ID!]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting highlights."""
  sortType: HighlightSortType!

  """Filters highlights by teamIds."""
  teamIds: [ID!]

  """
  Controls use of the primary database node. This should only be set on operations that critically require the most up to date data.
  """
  usePrimary: Boolean!
}

"""Representation of an input to fetch highlight reel summaries."""
input GetHighlightReelSummaryInput {
  """Filters highlights by deletion status."""
  includeDeleted: Boolean!

  """Filters highlights by publish status."""
  includeDrafts: Boolean!

  """The legacy ReelId"""
  legacyReelId: String

  """If set, this will bypass all privacy checks."""
  overridePrivacy: Boolean!

  """
  Filters highlights by the owner's internal id. Should be used in tandem with OwnerType.
  """
  ownerId: String

  """Filters highlights by the type of owner, usually team or athlete."""
  ownerType: HighlightOwnerType!

  """The GraphQL encoded Id ReelId"""
  reelId: ID!

  """
  Controls use of the primary database node. This should only be set on operations that critically require the most up to date data.
  """
  usePrimaryReadPreference: Boolean!
}

"""
Representation of an input to fetch league pass configs linked to a league.
"""
input GetLeaguePassConfigsByLeagueInput {
  """Value used to start a page of league pass configs."""
  after: Cursor

  """Value used to end a page of league pass configs."""
  before: Cursor

  """Filters for pass configs with a EndDate property before this date."""
  endDateMax: DateTime

  """Filters for pass configs with a EndDate property after this date."""
  endDateMin: DateTime

  """
  Number of league pass configs requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """
  Number of league pass configs requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """The ID of the league associated with the pass configs."""
  leagueEntityId: ID!

  """The type of the league associated with the pass configs."""
  leagueEntityType: String!

  """Pass config statuses to filter by."""
  leaguePassConfigStatuses: [String]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting summaries."""
  sortType: PassConfigSortType!

  """Filters for pass configs with a StartDate property before this date."""
  startDateMax: DateTime

  """Filters for pass configs with a StartDate property after this date."""
  startDateMin: DateTime

  """
  Pass config visibilities to filter by, such as public or private passes.
  """
  visibilities: [String]
}

input GetMembersForLeagueInput {
  """Value used to start a page of members."""
  after: Cursor

  """Value used to end a page of members."""
  before: Cursor

  """The internal id of the entity to get members for."""
  entityId: String!

  """The type of entity to get members for."""
  entityType: FanLeagueEntityType!

  """
  Number of members requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """
  Number of members requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """The type of member to get. E.g. Organization"""
  memberType: FanLeagueMemberType!

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting members."""
  sortType: FanLeagueMemberSortType!
}

"""
Representation of an input to fetch pass configs linked to an organization ID.
"""
input GetPassConfigsByOrganizationIdInput {
  """Value used to start a page of pass configs."""
  after: Cursor

  """Value used to end a page of pass configs."""
  before: Cursor

  """Filters for pass configs with a EndDate property before this date."""
  endDateMax: DateTime

  """Filters for pass configs with a EndDate property after this date."""
  endDateMin: DateTime

  """
  Pass config exclusions to filter by, such as filtering out passes associated with a league pass.
  """
  exclusions: [String]

  """
  Number of pass configs requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """
  Number of pass configs requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """The ID of the organization associated with the pass configs."""
  organizationId: ID!

  """Pass config statuses to filter by."""
  passConfigStatuses: [String]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting summaries."""
  sortType: PassConfigSortType!

  """Filters for pass configs with a StartDate property before this date."""
  startDateMax: DateTime

  """Filters for pass configs with a StartDate property after this date."""
  startDateMin: DateTime

  """
  Pass config visibilities to filter by, such as public or private passes.
  """
  visibilities: [String]
}

input GetRosterForTeamInput {
  """Filters Roster by GraphQL encoded seasonId."""
  seasonId: ID!

  """Filters Roster by GraphQL encoded teamId."""
  teamId: ID!
}

"""Representation of an input to fetch schedule entry summaries."""
input GetScheduleEntryPublicSummariesInput {
  """Value used to start a page of summaries."""
  after: Cursor

  """Value used to end a page of summaries."""
  before: Cursor

  """Date used to end a filter time span."""
  filterEndDate: DateTime

  """Date used to start a filter time span."""
  filterStartDate: DateTime

  """
  Number of summaries requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """Filters schedule entry summaries by gender."""
  genderId: Int

  """If true, will include schedule entry summaries of hidden teams"""
  includeHiddenTeams: Boolean

  """If true, will include in progress schedule entry summaries"""
  includeInProgressScheduleEntrySummaries: Boolean

  """
  Filters schedule entry summaries by internal teamIds. This will take precedence over TeamIds.
  """
  internalTeamIds: [String!]

  """
  Number of summaries requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """Filters schedule entry summaries by location."""
  location: Int

  """Filters schedule entry summaries by outcome."""
  outcome: Int

  """Filters schedule entry summaries by GraphQL encoded schedule entry Id."""
  scheduleEntryIds: [ID!]

  """Filters schedule entry summaries by GraphQL encoded schoolId."""
  schoolIds: [ID!]

  """Filters schedule entry summaries by GraphQL encoded seasonIds."""
  seasonIds: [ID!]

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting summaries."""
  sortType: ScheduleEntrySortType!

  """Filters schedule entry summaries by sport."""
  sportId: Int

  """Filters schedule entry summaries by GraphQL encoded teamIds."""
  teamIds: [ID!]
}

"""Representation of an input to fetch schools for a competition period."""
input GetSchoolsForCompetitionPeriodByTeamIdsPaginatedInput {
  """Team Id value used to start a page of schools."""
  after: Cursor

  """GraphQL encoded competition period Id to filter schools by."""
  competitionPeriodId: ID!

  """
  Number of teams to fetch schools for. Note that the number of schools returned may be less than this value if there are duplicates. Maximum is 100
  """
  first: Int!
}

"""Representation of an input to fetch schools for a municipality."""
input GetSchoolsForMunicipalityPaginatedInput {
  """Value used to start a page of schools."""
  after: Cursor

  """Number of schools requested in a single page. Maximum is 100"""
  first: Int!

  """GraphQL encoded municipalityId to filter schools by."""
  municipalityId: ID!
}

"""Representation of an input to fetch schools for a Regional Alignment."""
input GetSchoolsForRegionalAlignmentByTeamIdsPaginatedInput {
  """Team Id value used to start a page of schools."""
  after: Cursor

  """
  Number of teams to fetch schools for. Note that the number of schools returned may be less than this value if there are duplicates. Maximum is 100
  """
  first: Int!

  """GraphQL encoded regional alignment Id to filter schools by."""
  regionalAlignmentId: ID!
}

"""Representation of an input to fetch team headers."""
input GetTeamHeadersByIdsInput {
  """Filters teams by teamIds"""
  teamIds: [String]!
}

"""Representation of an input to fetch teams for a competition period."""
input GetTeamHeadersForCompetitionPeriodPaginatedInput {
  """Value used to start a page of schools."""
  after: Cursor

  """GraphQL encoded competition period Id to filter teams by."""
  competitionPeriodId: ID!

  """Number of teams requested in a single page. Maximum is 150."""
  first: Int!
}

"""Representation of an input to fetch team headers."""
input GetTeamHeadersForSchoolInput {
  """If true, will include hidden teams"""
  includeHiddenTeams: Boolean

  """Filters teams by schoolId."""
  schoolId: ID!

  """Filters teams by sport."""
  sports: [Sport!]

  """Filters teams by team level."""
  teamLevels: [TeamLevel!]
}

"""
Representation of an input to fetch an access code linked to an organization.
"""
input GetTicketedEventAccessCodeInput {
  """Access Code associated with an organization"""
  accessCode: String
}

"""
Representation of an input to fetch ticketed events linked to an organization ID.
"""
input GetTicketedEventsByOrganizationIdInput {
  """Value used to start a page of ticketed events."""
  after: Cursor

  """Value used to end a page of ticketed events."""
  before: Cursor

  """Filters for Ticketed Events with a Date property before this date."""
  endDate: DateTime

  """
  Number of ticketed events requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """If true, will return results that have been deleted."""
  includeDeleted: Boolean

  """
  Number of ticketed events requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """The ID of the organization associated with the ticketed event."""
  organizationId: ID!

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting summaries."""
  sortType: TicketedEventSortType!

  """Filters for Ticketed Events with a Date property after this date."""
  startDate: DateTime

  """Event statuses to filter by."""
  ticketedEventStatuses: [String]

  """
  Event visibilities to filter by, such as public, private, or not for sale events.
  """
  visibilities: [String]
}

"""
Representation of an input to fetch ticketed events linked to a Pass Config ID.
"""
input GetTicketedEventsByPassConfigIdInput {
  """Value used to start a page of ticketed events."""
  after: Cursor

  """Value used to end a page of ticketed events."""
  before: Cursor

  """
  Number of ticketed events requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """If true, will return results that have been deleted."""
  includeDeleted: Boolean

  """
  Number of ticketed events requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """The ID of the pass config associated with the ticketed event."""
  passConfigId: ID!

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting summaries."""
  sortType: TicketedEventSortType!
}

"""Representation of an input to fetch upcoming ticketed events."""
input GetTicketedEventsForTeamInput {
  """Value used to start a page of ticketedEvents."""
  after: Cursor

  """Value used to start a page of ticketedEvents."""
  before: Cursor

  """Number of ticketed events requested in a single page."""
  first: Int

  """Number of ticketed events requested in a single page."""
  last: Int

  """Id of the team"""
  teamId: String!
}

type GoverningBodyHeader {
  """The abbreviation of the Governing Body"""
  abbreviation: String

  """True if the user can edit the governing body."""
  canUserEditLeagueEntity: Boolean!

  """
  All competitions for a Governing Body, including those that aren't connected to subdivisions.
  """
  competitions: [CompetitionHeader]

  """The graphQL Id of the Governing Body"""
  id: ID!

  """The internal Id of the Governing Body"""
  internalId: String

  """The name of the Governing Body"""
  name: String

  """The organization branding for the public governing body."""
  organizationBranding: OrganizationBranding

  """The list of a regions for the Governing Body"""
  regions: [Region]
}

enum Grade {
  EIGHTH
  ELEVENTH
  FIFTH
  FIRST
  FOURTH
  KINDERGARTEN
  NINTH
  PRE_K
  SECOND
  SEVENTH
  SIXTH
  TENTH
  THIRD
  TWELFTH
}

"""Representation of a relationship between two entities."""
type GuardianMetadata {
  """The id of the user that updated the status of this metadata."""
  approverUserId: String

  """The id of the organization reference."""
  organizationId: String!

  """The status the approving user set this metadata to."""
  status: GuardianStatusType!

  """
  The list of teams Ids that the guardian has access to via their athlete.
  """
  teamIds: [String]

  """The date and time this metadata was last updated."""
  updatedAt: DateTime
}

"""Representation of a guardian relationship between two users."""
type GuardianRelationship {
  """The date and time this relationship was created."""
  createdAt: DateTime

  """The date and time this relationship was deleted."""
  deletedAt: DateTime

  """Indicates the level of guardian access to the athlete and their teams."""
  guardianshipLevel: GuardianshipLevel

  """The GraphQL ID of the relationship."""
  id: ID!

  """
  The list of metadata informing which organizations the guardian has access to and the status of that access.
  """
  metadata: [GuardianMetadata]

  """The primary entity of the relationship's Id."""
  primaryEntityId: ID!

  """The type of entity that PrimaryEntityId refers to."""
  primaryEntityType: EntityType!

  """The related entity of the relationship's Id."""
  relatedEntityId: ID!

  """The type of entity that RelatedEntityId refers to."""
  relatedEntityType: EntityType!

  """The type of relationship."""
  relationshipType: RelationshipType!

  """The date and time this relationship was last updated."""
  updatedAt: DateTime
}

enum GuardianStatusType {
  APPROVED
  DENIED
  PENDING
}

enum GuardianshipLevel {
  ATHLETE_GUARDIAN_ACCESS
}

enum HighlightOwnerType {
  HUDL_ACCOUNT
  TEAM
  UNKNOWN
  USER
}

type HighlightReelGameInfo {
  """
  The gameId (scheduleEntryId) of the game referenced by the highlight reel.
  """
  gameId: String

  """
  The GraphQL encoded scheduleEntryId of the game referenced by the highlight reel.
  """
  scheduleEntryId: ID

  """The seasonId of the game referenced by the highlight reel."""
  seasonId: String

  """
  The season label of the game referenced by the highlight reel. E.g. 2020-2021 where the first year is the SeasonYear property.
  """
  seasonLabel: String

  """The season year of the game referenced by the highlight reel."""
  seasonYear: Short!
}

enum HighlightReelOwnerType {
  HUDL_ACCOUNT
  TEAM
  UNKNOWN
  USER
}

type HighlightReelSummary {
  """Time the highlight reel was created."""
  createdAt: DateTime!

  """The description for the highlight reel."""
  description: String

  """Duration of the highlight."""
  duration: TimeSpan

  """
  Convenience property to get Duration expressed as a number instead of a TimeSpan string
  """
  durationInMs: Float!

  """The GraphQL id of the highlight reel summary."""
  id: ID!

  """The internal id of the highlight reel summary."""
  internalId: String

  """Whether or not the highlight was auto generated"""
  isAutoGen: Boolean!

  """The internal primary key of the owner of the highlight."""
  ownerId: String

  """The type of owner of the highlight."""
  ownerType: HighlightOwnerType!

  """List of the teams related the highlight reel."""
  teamInfo: [HighlightReelTeamInfo]

  """The thumbnail for the highlight."""
  thumbnail: String

  """The title of the highlight."""
  title: String

  """User tags for highlight."""
  userTags: [TaggedFeedUser]

  """The video url for the highlight."""
  videoUrl: String

  """The number of views on the highlight."""
  views: Long!
}

"""A connection to a list of items."""
type HighlightReelSummaryConnection {
  """A list of edges."""
  edges: [HighlightReelSummaryEdge!]
  items: [HighlightReelSummary!]

  """A flattened list of the nodes."""
  nodes: [HighlightReelSummary]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type HighlightReelSummaryEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: HighlightReelSummary
}

type HighlightReelTeamInfo {
  """The list of games for the team referenced by the highlight."""
  games: [HighlightReelGameInfo]

  """The internal teamId of the team referenced by the highlight."""
  internalTeamId: String

  """The sportId of the team referenced by the highlight."""
  sportId: Int!

  """The GraphQL encoded teamId of the team referenced by the highlight."""
  teamId: ID!
}

enum HighlightSortType {
  HIGHLIGHT_CREATION_DATE
}

"""A connection to a list of items."""
type IDConnection {
  """A list of edges."""
  edges: [IDEdge!]
  items: [ID!]

  """A flattened list of the nodes."""
  nodes: [ID]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type IDEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: ID
}

enum ImageSize {
  FULL
  NORMAL
  RETINA
  THUMBNAIL
}

"""Representation of an input to log an impression of community content."""
input ImpressionCommunityContentInput {
  """The Turn ID or mobile device ID."""
  adTrackingId: String

  """
  Additional properties to log with the impression. Ex: pageVisibility, sessionId, usePostRollCta
  """
  additionalProperties: [AdditionalPropertiesKeyValuePairInput]

  """The authorization ID of the user who is viewing the content."""
  authUserId: String

  """The packages the user has access to."""
  authUserPackages: [Package!]!

  """The authorization ID of the team the user is viewing the content for."""
  authUserTeamId: String

  """Uniquely identifies a piece of community content."""
  communityContentId: CommunityContentIdInput

  """Describes the location of the content container."""
  container: CommunityContentContainerType!

  """
  A way to segment deeper into the container. Ex: for the explore hub, each timeline would be a different container section.
  """
  containerSection: String

  """
  A way to segment deeper into the continer. Ex: for profiles, this could be User, Team, Author, Region, or Conference.
  """
  containerType: String

  """The IP address of the client making the request."""
  ipAddress: String

  """Whether the content is a recommendation."""
  isRecommendation: Boolean!

  """The locale of the user making the request."""
  locale: String

  """Describes the type of player rendering the community content video."""
  player: CommunityContentContainerPlayer!

  """
  If the content is displayed in a list or timeline, this should be the index in the list.
  """
  position: Int

  """The URL of the page that referred the user to the content."""
  referrerUrl: String

  """The URL of the request."""
  requestUrl: String

  """The application type that is requesting the content."""
  requestingApp: CommunityContentRequestingApp!

  """The referrer url for when the session on hudl.com began."""
  sessionReferrerUrl: String

  """The reason the content is being suggested. Ex: UserBasedHighlights"""
  suggestionReason: String

  """The user agent of the client making the request."""
  userAgent: String
}

"""Representation of a payable installment for a program registration."""
type Installment {
  """The amount of the installment."""
  amountInBaseDenomination: Int!

  """The date the installment was created."""
  createdAt: DateTime!

  """The ID of the user who created the installment."""
  createdBy: ID!

  """The date the installment was deleted, if applicable."""
  deletedAt: DateTime

  """The ID of the user who deleted the installment, if applicable."""
  deletedBy: ID

  """The due date of the installment."""
  dueDate: DateTime!

  """The ID of the installment."""
  id: ID!

  """The number of the installment."""
  installmentNumber: Int!

  """The ID of the installment plan."""
  installmentPlanId: ID

  """The ID of the registrant."""
  registrantId: ID!

  """The status of the installment."""
  status: InstallmentStatus!

  """Gets transactions for an installment."""
  transactions: [Transaction!]!

  """The date the installment was last updated."""
  updatedAt: DateTime!

  """The ID of the user who last updated the installment."""
  updatedBy: ID!
}

type InstallmentAllocationDto {
  amountInBaseDenomination: Int!
  currencyCode: String
  installmentId: String
}

enum InstallmentStatus {
  NONE
  OVERDUE
  PAID
  PENDING
}

type InviteCode {
  """Cell carriers"""
  cellCarriers: [Carrier]

  """Date created"""
  dateCreated: DateTime!

  """Invite code Id"""
  id: String

  """Invite requests"""
  inviteRequests: [InviteRequest]

  """Is Family Members enabled for team"""
  isFamilyMembersEnabled: Boolean!

  """Date expired"""
  lastModifiedDate: DateTime!

  """Team"""
  team: TeamHeader

  """Team Id"""
  teamId: String
}

type InviteRequest {
  """Cell carrier"""
  cellCarrier: String

  """Cell number."""
  cellPhone: String

  """Date created"""
  dateCreated: DateTime!

  """Email"""
  email: String

  """First Name"""
  firstName: String

  """Graduation Year"""
  graduationYear: String

  """Jersey number"""
  jersey: String

  """Last Name"""
  lastName: String

  """Position"""
  position: [String]

  """Role"""
  role: Role!

  """User Id"""
  userId: String
}

type LeagueAggregationInfo {
  """The list of league entities in an aggregation"""
  leagueEntities: [LeagueEntityInfo!]!
}

type LeagueEntityInfo {
  """The abbreviation of the league entity"""
  abbreviation: String

  """The internal id of the league entity"""
  id: String!

  """The name of the league entity"""
  name: String

  """The type of the league entity"""
  type: String!
}

enum LeagueEntityType {
  COMPETITION
  COMPETITION_PERIOD
  GOVERNING_BODY
  MUNICIPALITY
  REGIONAL_ALIGNMENT
  SUBDIVISION
  UNKNOWN
}

type LeagueMember {
  """
  When organization members are requested, this field will be populated with the organization data.
  """
  organization: FanSearchOrganization
}

"""A connection to a list of items."""
type LeagueMemberConnection {
  """A list of edges."""
  edges: [LeagueMemberEdge!]

  """A flattened list of the nodes."""
  nodes: [LeagueMember]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type LeagueMemberEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: LeagueMember
}

"""Representation of a league pass config."""
type LeaguePassConfig {
  """The pass configs associated with this leage pass config"""
  associatedPassConfigIds: [LeaguePassConfigAssociatedPassConfig!]

  """Associated schools that have child pass configs"""
  associatedSchools: [School]

  """Date the league pass config was created."""
  createdAt: DateTime!

  """
  The currency associated with the priceInCents of the league pass config.
  """
  currency: String!

  """Date the league pass config was deleted."""
  deletedAt: DateTime

  """The description of the league pass config."""
  description: String

  """The end date the league pass config is valid for. (inclusive)"""
  endDate: DateTime!

  """The game types that are excluded from the league pass config."""
  excludedGameTypes: [String!]

  """The fee strategy to apply to the league pass config."""
  feeStrategy: String!

  """The GraphQL id of the pass config."""
  id: ID!

  """The league associated with the league pass config."""
  leagueEntityId: ID!

  """The status of the league pass config."""
  leagueEntityType: String!

  """The status of the league pass config."""
  leaguePassConfigStatus: String!

  """The name of the league pass config."""
  name: String!

  """Price of the league pass."""
  priceInCents: Int!

  """The start date the league pass config is valid for. (inclusive)"""
  startDate: DateTime!

  """
  The event filters used to determine which events are valid for a pass config.
  """
  teamFilters: [LeaguePassConfigTeamFilter!]!

  """Date the league pass config was updated."""
  updatedAt: DateTime!

  """The visibility of the league pass config."""
  visibility: String!
}

type LeaguePassConfigAssociatedPassConfig {
  organizationId: ID!
  passConfigId: ID!
}

"""A connection to a list of items."""
type LeaguePassConfigConnection {
  """A list of edges."""
  edges: [LeaguePassConfigEdge!]
  items: [LeaguePassConfig!]

  """A flattened list of the nodes."""
  nodes: [LeaguePassConfig]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type LeaguePassConfigEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: LeaguePassConfig
}

"""
The filter used to determine which teams to include in the leage pass child configs.
"""
type LeaguePassConfigTeamFilter {
  """The team id of the event filter"""
  excludedTeamIds: [String!]!

  """The team filter gender"""
  gender: String!

  """The team filter sport"""
  sport: String!

  """The team filter level"""
  teamLevel: String!
}

type LeagueScaffoldingHierarchy {
  """The Competition within this league's hierarchical structure."""
  competition: CompetitionHeader

  """The Competition Period within this league's hierarchical structure."""
  competitionPeriod: CompetitionPeriodHeader

  """The Governing Body within this league's hierarchical structure."""
  governingBody: GoverningBodyHeader

  """The Municipality within this league's hierarchical structure."""
  municipality: MunicipalityHeader

  """The Regional Alignment within this league's hierarchical structure."""
  regionalAlignment: RegionalAlignmentHeader

  """The Subdivision within this league's hierarchical structure."""
  subdivision: SubdivisionHeader
}

"""The result of a registration operation."""
type LicenseRegistrationPayload {
  """The response authentication string."""
  authentication: AuthenticationPayload

  """The client mutation identifier."""
  clientMutationId: String
}

enum LicenseType {
  ELITE
  ELITE_REVIEW
  GAMEBREAKER_PLUS
  GSL_API_TOKEN
  PLAYER
  PRO
  PRO_REVIEW
  STUDIOCODE
  WIMU_SPRO
  WIMU_SVIVO
}

"""Representation of an entry linked to a ticketed event."""
type LinkedEntry {
  """The ID of the linked entry."""
  id: ID!

  """The type of the linked entry."""
  type: String
}

"""
The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1.
"""
scalar Long

enum MediaQuality {
  HD_H264_3000_KB
  LD_H264_722_KB
  SD_H264_1100_KB
  THD_H264_6500_KB
  UHD_H264_16000_KB
  UNKNOWN
}

type MessagingParticipantsMetadata {
  """If the user is an org admin."""
  isOrgAdmin: Boolean!

  """Maximum role of participant across the entire organization."""
  maxRole: Role

  """The internal ID of the participant."""
  userId: String
}

"""A connection to a list of items."""
type MessagingParticipantsMetadataConnection {
  """A list of edges."""
  edges: [MessagingParticipantsMetadataEdge!]
  items: [MessagingParticipantsMetadata!]

  """A flattened list of the nodes."""
  nodes: [MessagingParticipantsMetadata]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type MessagingParticipantsMetadataEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: MessagingParticipantsMetadata
}

type MunicipalityHeader {
  """The abbreviation of the Municipality"""
  abbreviation: String

  """True if the user can edit the municipality."""
  canUserEditLeagueEntity: Boolean!

  """The city of the Municipality"""
  city: String

  """
  The ISO 3166-1 alpha-3 code representing the country of the Municipality
  """
  countryIso: String

  """The Id of the Municipality"""
  id: ID!

  """The internal Id of the Municipality"""
  internalId: String

  """Flag to indicate if the Municipality is hidden from search"""
  isHiddenFromSearch: Boolean!

  """Flag to indicate if the Municipality profile is hidden"""
  isProfileHidden: Boolean!

  """The name of the Municipality"""
  name: String

  """The organization branding for the public municipality."""
  organizationBranding: OrganizationBranding

  """The list of school Ids associated with the Municipality"""
  schoolIds: [String]

  """
  The ISO 3166-2 code representing the state or province of the Municipality
  """
  subdivisionIso: String
}

type Mutation {
  cashCheckoutWithTicketingLineItems(input: TicketingCashCheckoutInput): TicketingCheckoutResult
  checkoutWithTicketingLineItems(input: TicketingCheckoutInput): TicketingCheckoutResult

  """Logs an impression of community content."""
  communityContentImpression(input: ImpressionCommunityContentInput!): Boolean!

  """Logs a play of community content."""
  communityContentPlay(input: PlayCommunityContentInput!): Boolean!

  """Logs a completed view of community content."""
  communityContentViewComplete(input: ViewQuartileCommunityContentInput!): Boolean!

  """Logs a view for the first quartile of community content."""
  communityContentViewFirstQuartile(input: ViewQuartileCommunityContentInput!): Boolean!

  """Logs a view for the midpoint quartile of community content."""
  communityContentViewMidpointQuartile(input: ViewQuartileCommunityContentInput!): Boolean!

  """Logs a view for the third quartile of community content."""
  communityContentViewThirdQuartile(input: ViewQuartileCommunityContentInput!): Boolean!
  createProvisionalAthleteAndGuardianRelationship(input: CreateProvisionalAthleteAndGuardianRelationshipInput!): AthleteAndGuardianRelationship!

  """Creates a terminal connection token"""
  createTerminalConnectionToken(input: CreateTerminalConnectionTokenInput!): TerminalConnectionToken
  createTicketingPaymentToken(input: CreateTicketingPaymentTokenInput): String

  """Deregister a machine."""
  deregister(input: DeregistrationInput!): DeregistrationPayload!
  id: String

  """Queues scanned Tickets and Passes for redemption"""
  queueBatchRedemption(input: ScanningRedemptionInput!): Boolean!

  """Redeems a ticketing entity via the specified redemption method"""
  redeemTicketingEntity(input: RedemptionInput!): [RedemptionEntity!]!

  """Register a machine for the first time."""
  register(input: RegistrationInput!): RegistrationPayload! @deprecated(reason: "This is for old HSC clients. Use RegisterLicense which includes the signature")

  """Register a machine for the first time."""
  registerLicense(input: RegistrationInput!): LicenseRegistrationPayload!

  """Shares ticketing entities by associating them with a new ticket group."""
  shareTicketingEntities(shareTicketingEntitiesInput: ShareTicketingEntitiesInput!): ShareHistory!
  transferTicketingEntities(transferTicketingEntitiesInput: TransferTicketingEntitiesInput!): ShareHistory!

  """Update the registration for a machine."""
  updateLicenseRegistration(input: UpdateRegistrationInput!): LicenseRegistrationPayload!

  """Update the registration for a machine."""
  updateRegistration(input: UpdateRegistrationInput!): RegistrationPayload! @deprecated(reason: "This is for old HSC clients. Use UpdateLicenseRegistration which includes the signature")

  """Upserts registrants for a program registration."""
  upsertRegistrants(input: [UpsertRegistrantInput!]!): [Registrant!]!
}

"""
Opponent details for a schedule entry. Primarily used for fan and public facing pages.
"""
type OpponentDetails {
  """The abbreviation of the opponent for the schedule entry."""
  abbreviation: String

  """The internal schoolId of the opponent for the schedule entry."""
  internalSchoolId: String

  """The internal teamId of the opponent for the schedule entry."""
  internalTeamId: String

  """The mascot name of the opponent for the schedule entry."""
  mascot: String

  """The name of the opponent for the schedule entry."""
  name: String

  """The primary color of the opponent for the schedule entry."""
  primaryColor: String

  """The profile image uri of the opponent for the schedule entry."""
  profileImageUri: String

  """
  The GraphQL encoded schoolId of the opponent for the schedule entry. Can be null for non-Hudl opponents.
  """
  schoolId: ID

  """The seasonId for the opponent for the schedule entry."""
  seasonId: String

  """The season year for the opponent for the schedule entry."""
  seasonYear: Short

  """The secondary color of the opponent for the schedule entry."""
  secondaryColor: String

  """The short name of the opponent for the schedule entry."""
  shortName: String

  """
  The GraphQL encoded teamId of the opponent for the schedule entry. Can be null for non-Hudl opponents.
  """
  teamId: ID
}

type OrganizationBranding {
  """The banner image uri of the organization """
  bannerImageUri: String

  """The Id of the organization"""
  id: ID!

  """The internal Id of the league entity associated with the branding"""
  internalLeagueId: String

  """The internal Id of the school"""
  internalSchoolId: String

  """
  The type of the league entity associated with the branding. Will be Unknown for schools.
  """
  leagueEntityType: LeagueEntityType!

  """The mascot of the organization"""
  mascot: String

  """School's primary color as RGB hex"""
  primaryColor: String

  """The profile image uri of the organization"""
  profileImageUri: String

  """The graphql Id of the school"""
  schoolId: ID

  """School's secondary color as RBG hex"""
  secondaryColor: String
}

"""A summary of Organization information that is public."""
type OrganizationHeader {
  """GraphQL Encoded Organization Id"""
  id: ID!

  """Organization abbreviation"""
  orgAbbreviation: String

  """Organization classification type"""
  orgClassificationId: Int!

  """Organization name"""
  organizationName: String
}

"""Representation of an organization program."""
type OrganizationProgram {
  """When the program was created."""
  createdAt: DateTime!

  """Who created the program."""
  createdBy: ID!

  """When the program was deleted, if applicable."""
  deletedAt: DateTime

  """Who deleted the program, if applicable."""
  deletedBy: ID

  """The description of the program."""
  description: String

  """The end date of the program."""
  endDate: DateTime

  """The fee responsibility setting of the program."""
  feeResponsibility: FeeResponsibility!

  """List of form references associated with the program."""
  formRefs: [FormRef!]!

  """The Id of the Organization Program."""
  id: ID!

  """The id of the organization reference."""
  orgId: ID!

  """Gets registrations for a program."""
  registrationsForProgram: [ProgramRegistration!]!

  """The start date of the program."""
  startDate: DateTime

  """The current state of the program."""
  state: ProgramState!

  """The timezone identifier for the program."""
  timeZoneIdentifier: String

  """The title of the program."""
  title: String

  """The type of the program."""
  type: EmbeddedOrganizationProgramType

  """When the program was last updated."""
  updatedAt: DateTime!

  """Who last updated the program."""
  updatedBy: ID!

  """The visibility setting of the program."""
  visibility: ProgramVisibility!
}

"""Settings for an organization"""
type OrganizationSettings {
  """Indicates if the organization profile is hidden"""
  isOrgProfileHidden: Boolean

  """Indicates if student data privacy is enabled for the organization"""
  isStudentDataPrivacyOn: Boolean
}

enum Package {
  BASIC
  ELITE
  EXCHANGE_ONLY
  FREE_RECRUITING
  GOLD
  INVALID
  LIGHTNING
  LIMITED
  NO_PACKAGE
  PLATINUM
  RECRUITING
  REGIONAL_RECRUITING
  SILVER
  TEAM_BASED_BRONZE
  TEAM_BASED_ELITE
  TEAM_BASED_GOLD
  TEAM_BASED_LIMITED
  TEAM_BASED_PLATINUM
  TEAM_BASED_SILVER
  TEAM_BASED_STARTER
  TRIAL_BASE
  VARSITY
  YOUTH
  YOUTH_MONTHLY
}

"""Information about pagination in a connection."""
type PageInfo {
  """When paginating forwards, the cursor to continue."""
  endCursor: Cursor

  """
  Indicates whether more edges exist following the set defined by the clients arguments.
  """
  hasNextPage: Boolean!

  """
  Indicates whether more edges exist prior the set defined by the clients arguments.
  """
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: Cursor
}

"""Representation of a pass."""
type Pass {
  """Date the pass was created."""
  createdAt: DateTime!

  """Date the pass was deleted."""
  deletedAt: DateTime

  """The email of the person associated with this pass."""
  email: String

  """The first name of the person associated with this pass."""
  firstName: String

  """The GraphQL id of the pass."""
  id: ID!

  """The last name of the person associated with this pass."""
  lastName: String

  """The identifier for the Pass Config this pass is for."""
  passConfigId: ID!

  """The QR Code data for this pass"""
  qrCodeData: String

  """The QR code linking to this pass."""
  qrCodeUrl: String @deprecated(reason: "This property will no longer return a value. Use the QRCodeData property instead.")

  """The reserved seats associated with the pass."""
  reservedSeats: [ReservedSeat!]

  """Date the pass was shared."""
  sharedAt: DateTime

  """The source where the pass was purchased."""
  source: String

  """The tickets created for this pass"""
  tickets: [Ticket]

  """Date the pass was updated."""
  updatedAt: DateTime!

  """
  The Hudl user id of the person associated with this pass, if one exists.
  """
  userId: String
}

"""Representation of a pass config."""
type PassConfig {
  """Date the ticketed event was created."""
  createdAt: DateTime!

  """The currency associated with the priceInCents of the pass config."""
  currency: String

  """Date the ticketed event was deleted."""
  deletedAt: DateTime

  """The description of the pass config."""
  description: String

  """The end date the pass config is valid for. (inclusive)"""
  endDate: DateTime

  """
  The event filters used to determine which events are valid for a pass config.
  """
  eventFilters: [PassConfigEventFilter!]

  """The game types that are excluded from the pass config."""
  excludedGameTypes: [String!]

  """Events that are excluded from the pass config."""
  excludedTicketedEventIds: [ID!]

  """The fee strategy to apply to the pass config."""
  feeStrategy: String

  """The IDs of the form fields associated with the pass config."""
  formFieldIds: [ID!]

  """The form fields associated with the pass config"""
  formFields: [FormField]

  """The GraphQL id of the pass config."""
  id: ID!

  """
  Events that are included in the pass config outside of the events applied via filters.
  """
  includedTicketedEventIds: [ID!]

  """The ID of the League Pass config associated with this pass config."""
  leaguePassConfigId: ID

  """The name of the pass config."""
  name: String

  """The organization associated with the pass config."""
  organizationId: ID!

  """The pricing summary associated with the pass config"""
  passConfigPricingSummary: PassConfigPricingSummary

  """The status of the pass config."""
  passConfigStatus: String

  """The number of passes sold associated with a pass config"""
  passCount: Int!

  """Price of the pass."""
  priceInCents: Int!

  """
  The date the pass config is no longer available for purchase. (inclusive)
  """
  purchaseExpirationDate: DateTime

  """Number of passes available for purchase."""
  quantityAvailable: Int

  """The quantity of passes remaining for the pass config"""
  quantityRemaining: QuantityRemainingOutput

  """The renewal campaign associated with this pass config"""
  renewalCampaign: PassConfigRenewalCampaign

  """The start date the pass config is valid for. (inclusive)"""
  startDate: DateTime

  """List of the team ids valid for this pass config."""
  teamIds: [ID]

  """Selected teams for the pass config"""
  teams: [TeamHeader]

  """List of the ticketed event ids valid for this pass config."""
  ticketedEventIds: [ID!]

  """The status of the pass config's TicketedEventIds list."""
  ticketedEventIdsCalculationStatus: String

  """The ticketed events associated with a pass config"""
  ticketedEvents: [TicketedEvent]

  """Date the ticketed event was updated."""
  updatedAt: DateTime!

  """The visibility of the pass config."""
  visibility: String
}

"""A connection to a list of items."""
type PassConfigConnection {
  """A list of edges."""
  edges: [PassConfigEdge!]
  items: [PassConfig!]

  """A flattened list of the nodes."""
  nodes: [PassConfig]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type PassConfigEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: PassConfig
}

"""
The event filter used to determine which events are valid for a pass config.
"""
type PassConfigEventFilter {
  """
  List of category ids that should be enabled in context of the venue config.
  """
  seatsDotIoEnabledCategoryIds: [String!]

  """
  The Seats.io partial season id associated with the events calculated by the event filter.
  """
  seatsDotIoPartialSeasonId: String

  """The team id of the event filter"""
  teamId: ID!

  """The venue configuration id of the event filter"""
  venueConfigurationId: ID
}

"""
Representation of a pass config pricing summary including information about the Hudl fees applied.
"""
type PassConfigPricingSummary {
  """The currency associated with the priceInCents of the pass config."""
  currency: String

  """The Hudl Fee applied to the pass purchase in cents"""
  hudlFeeInCents: Int!

  """The ID of the pass config."""
  passConfigId: ID!

  """The price of the pass config in cents"""
  priceInCents: Int!

  """The price of the pass config in cents including the Hudl Fee"""
  priceInCentsWithHudlFee: Int!

  """Whether the Hudl fee should be displayed to the user."""
  shouldShowFee: Boolean!
}

"""Representation of a Renewal Campaign for a Pass Config."""
type PassConfigRenewalCampaign {
  """The date and time when the campaign was created"""
  createdAt: DateTime!

  """The date and time when the campaign was deleted"""
  deletedAt: DateTime

  """The ID of the Pass Config Renewal Campaign"""
  id: ID!

  """The ID of the organization associated with this campaign"""
  organizationId: ID!

  """The pass config associated with this renewal campaign"""
  passConfig: PassConfig

  """The ID of the Pass Config associated with this campaign"""
  passConfigId: ID!

  """The visibility of the Pass Config after the renewal campaign ends"""
  postRenewalVisibility: String!

  """The end date of the renewal campaign"""
  renewalEndDate: DateTime!

  """The start date of the renewal campaign"""
  renewalStartDate: DateTime!

  """The time zone associated with the renewal campaign"""
  timeZoneIdentifier: String!

  """The date and time when the campaign was last updated"""
  updatedAt: DateTime!
}

"""
Representation of a Renewer that is a part of a Pass Config Renewal Campaign.
"""
type PassConfigRenewer {
  """The date and time when the renewer was created."""
  createdAt: DateTime!

  """The date and time when the renewer was deleted."""
  deletedAt: DateTime

  """The email address of the Renewer."""
  email: String!

  """The first name of the Renewer."""
  firstName: String!

  """The ID of the Renewer record."""
  id: ID!

  """The last name of the Renewer."""
  lastName: String!

  """The renewal campaign associated with this renewer"""
  renewalCampaign: PassConfigRenewalCampaign

  """
  The ID of the Pass Config Renewal Campaign this Renewer is associated with.
  """
  renewalCampaignId: ID!

  """The ID of the SeatsDotIo channel that is associated with this Renewer."""
  seatsDotIoChannelId: String

  """The list of seat identifiers that the Renewer can select."""
  selectableSeatIdentifiers: [String!]

  """The date and time when the renewer was last updated."""
  updatedAt: DateTime!

  """The ID of the user associated with this Renewer."""
  userId: String
}

enum PassConfigSortType {
  START_DATE
  UNKNOWN
}

type PayloadAuthentication {
  endDate: DateTime
  licenseType: LicenseType
  machine: String
  publicHash: String
  registration: String
  schoolName: String
  special: String
  startDate: DateTime
  subscriptionEndDate: DateTime
  subscriptionStartDate: DateTime
  token: String
}

enum PaymentMethodType {
  ACH
  CASH
  CHECK
  CREDIT_CARD
  NONE
}

"""Representation of an input to log a play of community content."""
input PlayCommunityContentInput {
  """The Turn ID or mobile device ID."""
  adTrackingId: String

  """
  Additional properties to log with the impression. Ex: pageVisibility, sessionId, usePostRollCta
  """
  additionalProperties: [AdditionalPropertiesKeyValuePairInput]

  """The authorization ID of the user who is viewing the content."""
  authUserId: String

  """The packages the user has access to."""
  authUserPackages: [Package!]

  """The authorization ID of the team the user is viewing the content for."""
  authUserTeamId: String

  """Uniquely identifies a piece of community content."""
  communityContentId: CommunityContentIdInput

  """Describes the location of the content container."""
  container: CommunityContentContainerType!

  """
  A way to segment deeper into the container. Ex: for the explore hub, each timeline would be a different container section.
  """
  containerSection: String

  """
  A way to segment deeper into the continer. Ex: for profiles, this could be User, Team, Author, Region, or Conference.
  """
  containerType: String

  """The IP address of the client making the request."""
  ipAddress: String

  """
  True if Hudl automatically advanced the user to this video and played it as the top suggestion.
  """
  isAutoAdvance: Boolean!

  """Specifies if the video was played automatically."""
  isAutoPlay: Boolean!

  """Specifies if the video is being played inline."""
  isInlinePlay: Boolean!

  """Specifies if the video is muted."""
  isMuted: Boolean!

  """The locale of the user making the request."""
  locale: String

  """Describes the type of player rendering the community content video."""
  player: CommunityContentContainerPlayer!

  """The height of the video player in pixels."""
  playerHeight: Int

  """The width of the video player in pixels."""
  playerWidth: Int

  """The URL of the page that referred the user to the content."""
  referrerUrl: String

  """The URL of the request."""
  requestUrl: String

  """The application type that is requesting the content."""
  requestingApp: CommunityContentRequestingApp!

  """The referrer url for when the session on hudl.com began."""
  sessionReferrerUrl: String

  """The user agent of the client making the request."""
  userAgent: String

  """The number of times the content has been viewed."""
  views: Int!
}

"""Representation of an organization program registration."""
type ProgramRegistration {
  """When the program was created."""
  createdAt: DateTime!

  """Who created the program."""
  createdBy: ID!

  """
  The currency of the registration. This is a 3-letter ISO 4217 currency code that currently defaults to USD.
  """
  currencyCode: String

  """When the program was deleted, if applicable."""
  deletedAt: DateTime

  """Who deleted the program, if applicable."""
  deletedBy: ID

  """The description of the registration."""
  description: String

  """The eligibility settings for the registration."""
  eligibility: RegistrationEligibilityOutput

  """The end date of the program."""
  endDate: DateTime

  """The Id of the Registration."""
  id: ID!

  """Is the waitlist enabled for the registration."""
  isWaitlistEnabled: Boolean

  """The max capacity of the registration."""
  maxCapacity: Int

  """The id of the organization reference."""
  orgId: ID!

  """The price of the registration."""
  priceInBaseDenomination: Int!

  """The id of the program reference."""
  programId: ID!

  """The start date of the program."""
  startDate: DateTime!

  """The status of the registration."""
  status: ProgramRegistrationStatus!

  """The timezone identifier for the program."""
  timeZoneIdentifier: String

  """The title of the registration."""
  title: String

  """When the program was last updated."""
  updatedAt: DateTime!

  """Who last updated the program."""
  updatedBy: ID!
}

enum ProgramRegistrationStatus {
  CLOSED
  OPEN
}

enum ProgramState {
  ARCHIVED
  DRAFT
  PUBLISHED
}

enum ProgramVisibility {
  PRIVATE
  PUBLIC
  UNSET
}

type PublicMember {
  """Member's first name."""
  firstName: String

  """Member's full name."""
  fullName: String

  """Member Graduation Year"""
  graduationYear: Short

  """Member groups"""
  groups(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int
    includeFamilyMembers: Boolean

    """Returns the last _n_ elements from the list."""
    last: Int
  ): IDConnection

  """Height"""
  height: String

  """The graphql Id of the member."""
  id: ID!

  """The hudl Id of the member."""
  internalId: String

  """IsDisabled"""
  isDisabled: Boolean!

  """Jersey"""
  jersey: String

  """Last Login Date"""
  lastLoginDate: DateTime

  """Member's last name."""
  lastName: String

  """Participant Id"""
  participantId: String

  """PictureUrl"""
  pictureUrl: String

  """Position"""
  position: [String]

  """Season Ids"""
  seasonIds: [ID]

  """The teamId that the member belongs to"""
  teamId: ID!

  """Weight"""
  weight: Long
}

type PublishEventStreamSummary {
  """The camera id of the publish event stream summary."""
  cameraId: String

  """The external microphone volume for the publish event stream summary."""
  externalMicVolume: Float

  """If the scoreboard is included in the publish event stream summary."""
  includeScoreboard: Boolean!

  """The Installation Id for the publish event stream summary."""
  installationId: String

  """The internal microphone volume for the publish event stream summary."""
  internalMicVolume: Float

  """The quality of the publish event stream summary."""
  quality: MediaQuality @deprecated(reason: "Use QualityId instead.")

  """The qualityId of the publish event stream summary."""
  qualityId: Int!

  """The stream type of the publish event stream"""
  streamType: StreamType!
}

type PublishEventSummary {
  """The GraphQL Id of the publish event summary"""
  id: ID!

  """The InternalId of the publish event."""
  internalId: String

  """The name of the publish event."""
  name: String

  """The RestreamSummaries for the publish event summary."""
  restreamSummaries: [RestreamSummary]

  """The start time for the publish event."""
  startTime: DateTime!

  """The stop time for the publish event."""
  stopTime: DateTime!

  """The Publish event stream summaries for this publish event."""
  streams: [PublishEventStreamSummary]
}

enum PublishSessionUploadSource {
  ADMIN
  AUTOMATIC_CAPTURE
  CUTUP_CONVERSION
  EXCHANGE
  EXTERNAL_INGEST
  IOS
  KROSSOVER_IMPORT
  LEROY_API
  MERCURY
  SPORTSCODE
  UNKNOWN
  VOLLEYMETRICS
  WEB_UPLOADER
}

"""Representation of quantity remaining for a ticket type or pass config."""
type QuantityRemainingOutput {
  """
  The number of tickets or passes remaining for the ticket type or pass config.
  """
  quantityRemaining: Int!

  """The GraphQL Id of the ticket type or pass config."""
  referenceId: ID!
}

type Query {
  """Gets information about an ad context related to a user."""
  adTargeting(
    """Ad Targeting Input"""
    adTargetingInput: GetAdTargetingInput
  ): AdTargeting!

  """
  Fetch all valid ticket groups containing tickets or passes for a provided event. Must provide an access code.
  """
  associatedTicketGroupsForTicketedEventId(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """
    Input to retrieve ticket groups based on event ID, requires access code
    """
    input: GetAssociatedTicketGroupsWithAccessCodeInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): TicketGroupConnection
  athleteAndGuardianRelationships(input: GetAthleteAndGuardianRelationshipsInput!): [AthleteAndGuardianRelationship!]!

  """Fetch Athlete by MemberId"""
  athleteProfile(
    """GraphQL MemberId"""
    memberId: ID!
  ): AthleteProfile

  """
  Fetch broadcast by GraphQL encoded or internal id. Internal id takes precedence.
  """
  broadcast(
    """GraphQL BroadcastId"""
    broadcastId: ID

    """Internal BroadcastId"""
    internalBroadcastId: String
  ): Broadcast

  """Fetch paginated broadcasts by using filters."""
  broadcasts(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to filter, fetch, and paginate broadcasts."""
    input: GetBroadcastsPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): BroadcastConnection

  """
  Fetch the canonical url a vanity url should redirect to. Returns null if the vanity url does not exist.
  """
  canonicalUrlForSchoolVanityUrl(
    """
    The input to fetch the canonical url a vanity url should redirect to. Can contain either the vanity slug or entire vanity url.
    """
    input: GetCanonicalUrlForVanityUrlInput!
  ): String

  """Fetch the public Competition data by Id"""
  competitionHeader(
    """GraphQL competitionId"""
    competitionId: ID

    """Internal competitionId"""
    internalCompetitionId: String
  ): CompetitionHeader

  """Fetch the public Competitions associated with a Governing Body Id"""
  competitionHeadersForGoverningBody(
    """Internal governingBodyId"""
    internalGoverningBodyId: String
  ): [CompetitionHeader]

  """Fetch the public Competitions associated with a Subdivision Id"""
  competitionHeadersForSubdivision(
    """Internal subdivisionId"""
    internalSubdivisionId: String
  ): [CompetitionHeader]

  """Fetch the public Competition Period data by Id"""
  competitionPeriodHeader(
    """GraphQL competitionPeriodId"""
    competitionPeriodId: ID

    """Internal competitionPeriodId"""
    internalCompetitionPeriodId: String
  ): CompetitionPeriodHeader

  """Fetch the public Competition Periods associated with a Competition Id"""
  competitionPeriodHeadersForCompetition(
    """Internal competitionId"""
    internalCompetitionId: String
  ): [CompetitionPeriodHeader]

  """
  Fetch the Current public competition period associated with a Competition Id
  """
  currentCompetitionPeriodHeaderForCompetition(
    """Internal competitionId"""
    internalCompetitionId: String
  ): CompetitionPeriodHeader

  """Fetch the current terms of service for ticketing and payouts."""
  currentTermsOfService: TermsOfService

  """Fetch discoverable league pass configs for a given league."""
  discoverableLeaguePassConfigsByLeague(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """The league associated with the league pass configs."""
    input: GetDiscoverableLeaguePassConfigsByLeagueInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): LeaguePassConfigConnection

  """Fetch discoverable ticketed events for a team."""
  discoverableTicketedEventsForTeam(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input for fetching ticketed events for a team."""
    input: GetTicketedEventsForTeamInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): TicketedEventConnection

  """Fetch paginated broadcasts using filters."""
  fanBroadcasts(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to filter, fetch, and paginate broadcasts."""
    input: GetFanBroadcastsPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): FanBroadcastConnection

  """Fetch paginated highlight reels using filters."""
  fanHighlightReels(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to filter, fetch, and paginate highlight reels."""
    input: GetFanHighlightReelsPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): FanHighlightReelConnection

  """Fetch paginated schedule entries using filters."""
  fanScheduleEntries(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to filter, fetch, and paginate schedule entries."""
    input: GetFanScheduleEntriesPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): FanScheduleEntryConnection

  """
  Fetch a collection of results by search term, entity type, and organization classification ids.
  """
  fanSearch(
    """The parameters for building a search request."""
    input: FanSearchRequestInput!
  ): [FanSearchResult]

  """Fetch form field by GraphQL ID."""
  formFieldById(
    """A form field's GraphQL ID"""
    formFieldId: ID!
  ): FormField

  """Fetch broadcast by media stream id."""
  getBroadcastByMediaStreamId(
    """Media stream id"""
    mediaStreamId: ID!
  ): Broadcast

  """Fetch broadcast by schedule entry id."""
  getBroadcastByScheduleEntryId(
    """Schedule entry id"""
    scheduleEntryId: ID!
  ): Broadcast

  """Fetch the public Governing Body data by Id"""
  governingBodyHeader(
    """GraphQL governingBodyId"""
    governingBodyId: ID

    """Internal governingBodyId"""
    internalGoverningBodyId: String
  ): GoverningBodyHeader

  """Fetch highlight reel summaries with pagination"""
  highlightReelSummaries(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate highlight reel summaries"""
    input: GetHighlightReelSummariesInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): HighlightReelSummaryConnection

  """Fetch highlight reel summaries with pagination"""
  highlightReelSummary(
    """Input used to fetch and paginate a highlight reel summary"""
    input: GetHighlightReelSummaryInput!
  ): HighlightReelSummary

  """Get invite code by id"""
  inviteCode(
    """Invite code id"""
    inviteCodeId: String!
  ): InviteCode

  """
  Fetches league aggregation information by an organization id or teamId.
  """
  leagueAggregation(
    """Whether to include league aggregations for all teams for the school"""
    includeAllTeamsForSchool: Boolean

    """Internal school id"""
    schoolId: String

    """Internal team id"""
    teamId: String

    """Used to scope certain entities to a specific time."""
    timeUtc: DateTime
  ): LeagueAggregationInfo

  """Fetches a given league's hierarchy by ID and type."""
  leagueHierarchy(
    """GraphQL ID of the League"""
    leagueEntityId: ID!

    """The entity type of the league"""
    leagueEntityType: LeagueEntityType!
  ): LeagueScaffoldingHierarchy

  """Fetches members of leagues using filters."""
  leagueMembers(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to filter, fetch, and paginate league members."""
    input: GetMembersForLeagueInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): LeagueMemberConnection

  """Fetch league pass config by GraphQL ID."""
  leaguePassConfigById(
    """A league pass config's GraphQL ID"""
    leaguePassConfigId: ID!
  ): LeaguePassConfig

  """Fetch league pass configs for a given league."""
  leaguePassConfigsByLeague(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """The league associated with the league pass configs."""
    input: GetLeaguePassConfigsByLeagueInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): LeaguePassConfigConnection

  """Fetch the public municipality data by Id"""
  municipalityHeader(
    """Internal municipalityId"""
    internalMunicipalityId: String

    """GraphQL municipalityId"""
    municipalityId: ID
  ): MunicipalityHeader

  """
  Fetch the organization branding data for a school or league. If the league entity id is provided, the school id is not required, and vice versa.
  """
  organizationBranding(
    """Internal id of the league to fetch"""
    internalLeagueId: String

    """Internal schoolId"""
    internalSchoolId: String

    """The entity type of the league"""
    leagueEntityType: LeagueEntityType! = UNKNOWN
  ): OrganizationBranding

  """
  Checks if an organization has ticketing enabled via the config value in hudl-ticketing
  """
  organizationHasTicketingEnabled(organizationId: String!): Boolean!

  """Fetch pass by GraphQL ID."""
  passById(
    """A pass's GraphQL ID"""
    passId: ID!
  ): Pass

  """Fetch pass config by GraphQL ID."""
  passConfigById(
    """A pass config's GraphQL ID"""
    passConfigId: ID!
  ): PassConfig

  """Fetch pass config renewer by GraphQL ID."""
  passConfigRenewerById(
    """A pass config renewer's GraphQL ID"""
    renewerId: ID!
  ): PassConfigRenewer

  """Fetch pass configs by a list of GraphQL IDs."""
  passConfigsByIds(
    """A list of pass config GraphQL IDs"""
    passConfigIds: [ID!]
  ): [PassConfig]

  """Fetch pass configs for a given Organization ID."""
  passConfigsByOrganizationId(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """The Organization ID associated with the pass configs."""
    input: GetPassConfigsByOrganizationIdInput

    """Returns the last _n_ elements from the list."""
    last: Int
  ): PassConfigConnection

  """Fetch the public Regional Alignment data by Id"""
  regionalAlignmentHeader(
    """Internal regionalAlignmentId"""
    internalRegionalAlignmentId: String

    """GraphQL regionalAlignmentId"""
    regionalAlignmentId: ID
  ): RegionalAlignmentHeader

  """
  Fetch active roster members for the current season, and all members for previous seasons
  """
  roster(
    """Input used to fetch team headers"""
    input: GetRosterForTeamInput!
  ): [PublicMember]

  """
  Fetch schedule entry summaries by using filters. Summaries include only public information.
  """
  scheduleEntryPublicSummaries(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate schedule entry summaries"""
    input: GetScheduleEntryPublicSummariesInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): ScheduleEntryPublicSummaryConnection

  """Fetch a schedule entry summary by using a schedule entry ID."""
  scheduleEntryPublicSummary(
    """Input used to fetch a schedule entry summary"""
    scheduleEntryId: ID!
  ): ScheduleEntryPublicSummary

  """Fetch the school data by Id"""
  school(
    """Internal schoolId"""
    internalSchoolId: String

    """GraphQL schoolId"""
    schoolId: ID
  ): School

  """Fetch the schools data for a list of internal school Ids"""
  schools(
    """List of GraphQL schoolIds"""
    graphQLSchoolIds: [ID!]

    """List of internal schoolIds"""
    schoolIds: [String]
  ): [School]

  """
  Fetch schools by their corresponding teams on a Competition Period given a CompetitionPeriodId, with schools returned according to lexicographically paginated TeamIds.
  """
  schoolsForCompetitionPeriod(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate Schools."""
    input: GetSchoolsForCompetitionPeriodByTeamIdsPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): SchoolConnection

  """Fetch paginated schools by municipality Id."""
  schoolsForMunicipality(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate Schools."""
    input: GetSchoolsForMunicipalityPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): SchoolConnection

  """
  Fetch schools by their corresponding teams on a Regional Alignment given a CompetitionPeriodId, with schools returned according to lexicographically paginated TeamIds.
  """
  schoolsForRegionalAlignment(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate Schools."""
    input: GetSchoolsForRegionalAlignmentByTeamIdsPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): SchoolConnection

  """Fetch season by ID"""
  season(
    """Season Internal Id"""
    internalSeasonId: String

    """Internal Team Id"""
    internalTeamId: String!

    """Season GraphQLId"""
    seasonId: ID
  ): Season

  """Fetch season record by ID"""
  seasonRecord(
    """Season record ID"""
    seasonRecordId: String
  ): SeasonRecord

  """Fetch seasons for team."""
  seasons(
    """GraphQL encoded Team Id"""
    teamId: ID!
  ): [Season]

  """Fetch the public Subdivision data by Id"""
  subdivisionHeader(
    """Internal subdivisionId"""
    internalSubdivisionId: String

    """GraphQL subdivisionId"""
    subdivisionId: ID
  ): SubdivisionHeader

  """Fetch the public Subdivisions associated with a Governing Body Id"""
  subdivisionHeadersForGoverningBody(
    """Internal governingBodyId"""
    internalGoverningBodyId: String
  ): [SubdivisionHeader]

  """Fetch the team header data by id"""
  teamHeader(
    """Internal teamId"""
    internalTeamId: String

    """GraphQL teamId"""
    teamId: ID
  ): TeamHeader

  """Fetch team headers based on a list of Ids. Maximum is 150"""
  teamHeaders(
    """Input used to fetch team headers"""
    input: GetTeamHeadersByIdsInput!
  ): [TeamHeader]

  """Fetch paginated team headers by competition period Id."""
  teamHeadersForCompetitionPeriod(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate team headers for a competition period"""
    input: GetTeamHeadersForCompetitionPeriodPaginatedInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): TeamHeaderConnection

  """Fetch team headers using filters."""
  teamHeadersForSchool(
    """Input used to fetch team headers"""
    input: GetTeamHeadersForSchoolInput!
  ): [TeamHeader]

  """Fetch ticket group by GraphQL ID."""
  ticketGroupById(
    """A ticket group's GraphQL ID"""
    ticketGroupId: ID!
  ): TicketGroup

  """Fetch ticket groups by user id"""
  ticketGroupsForCurrentUser(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input to retrieve ticket groups for a user, output is paginated"""
    input: TicketGroupsByUserIdInput

    """Returns the last _n_ elements from the list."""
    last: Int
  ): TicketGroupConnection

  """Fetch ticket types by GraphQL ID."""
  ticketTypeById(
    """A ticket type's GraphQL ID"""
    ticketTypeId: ID!
  ): TicketType

  """
  Fetch ticketable schedule entry summaries by using filters. These summaries do not have linked ticketed events.
  """
  ticketableScheduleEntrySummaries(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Input used to fetch and paginate ticketable schedule entry summaries"""
    input: GetScheduleEntryPublicSummariesInput!

    """Returns the last _n_ elements from the list."""
    last: Int
  ): ScheduleEntryPublicSummaryConnection

  """Fetch Access Code"""
  ticketedEventAccessCode(
    """Inputs to get an Access Code for an organization"""
    input: GetTicketedEventAccessCodeInput
  ): TicketedEventAccessCode

  """Fetch ticketed event by GraphQL ID."""
  ticketedEventById(
    """A ticketed event's GraphQL ID"""
    ticketedEventId: ID!
  ): TicketedEvent

  """Fetch ticketed events by GraphQL IDs."""
  ticketedEventsByIds(
    """A list of ticketed event GraphQL IDs"""
    ticketedEventIds: [ID!]
  ): [TicketedEvent]

  """Fetch ticketed events for a given Organization ID."""
  ticketedEventsByOrganizationId(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """The Organization ID associated with the ticketed events."""
    input: GetTicketedEventsByOrganizationIdInput

    """Returns the last _n_ elements from the list."""
    last: Int
  ): TicketedEventConnection

  """Fetch ticketed events for a given Pass Config ID."""
  ticketedEventsByPassConfigId(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """The Pass Config ID associated with the ticketed events."""
    input: GetTicketedEventsByPassConfigIdInput

    """Returns the last _n_ elements from the list."""
    last: Int
  ): TicketedEventConnection

  """Fetch a ticketing experiment by name."""
  ticketingExperimentByName(
    """A ticketing experiment's name"""
    experimentName: String
  ): TicketingExperiment

  """
  Query for a pricing summarization for a cart of ticketing items including fees.
  """
  ticketingPricingSummary(
    """A list of ticketing items to include in the pricing summary."""
    lineItems: [TicketingPricingLineItemInput]

    """The source of the cart of ticketing items."""
    source: String
  ): TicketingPricingSummary

  """Fetch the vanity urls for a destination."""
  vanityUrlForDestination(
    """The id of the destination entity"""
    destinationId: String

    """The type of the destination entity"""
    destinationType: VanityDestinationType!
  ): String
}

"""Ticketing entity that has been redeemed"""
type RedemptionEntity {
  """
  The ID of the ticket that was created as a result of redeeming the entity
  """
  associatedTicketId: ID

  """The GraphQL encoded Id for the redeemed entity"""
  entityId: ID!

  """The type of the redeemed entity"""
  ticketingEntityType: String!
}

"""Entity to be redeemed"""
input RedemptionEntityInput {
  """The GraphQL encoded Id for the redeemed entity"""
  entityId: ID!

  """The type of the redeemed entity"""
  ticketingEntityType: String!
}

"""Input to redeem Tickets and Passes"""
input RedemptionInput {
  """The entities being redeemed"""
  redemptionEntities: [RedemptionEntityInput!]!

  """The method of the redemption"""
  redemptionMethod: String!

  """GraphQL encoded Id of the event the entity is redeemed for"""
  ticketedEventId: ID!
}

type Region {
  """The ISO 3166-1 alpha-3 code representing a country"""
  countryIso: String

  """The ISO 3166-2 code representing a state or province"""
  subdivisionIso: String
}

type RegionalAlignmentHeader {
  """The abbreviation of the Regional Alignment"""
  abbreviation: String

  """True if the user can edit the regional alignment."""
  canUserEditLeagueEntity: Boolean!

  """
  The ISO 3166-1 alpha-3 code representing the country of the Regional Alignment
  """
  countryIso: String

  """The Id of the Regional Alignment"""
  id: ID!

  """The internal Id of the Regional Alignment"""
  internalId: String

  """The name of the Regional Alignment"""
  name: String

  """The organization branding for the public regional alignment."""
  organizationBranding: OrganizationBranding

  """
  The ISO 3166-2 code representing the state or province of the Regional Alignment
  """
  subdivisionIso: String

  """The list of team Ids associated with the Regional Alignment"""
  teamIds: [String]
}

"""Representation of an organization program registration."""
type Registrant {
  """The date the registrant was created."""
  createdAt: DateTime!

  """The id of the user who created the registrant."""
  createdBy: ID!

  """The date the registrant was deleted, if applicable."""
  deletedAt: DateTime

  """The id of the user who deleted the registrant, if applicable."""
  deletedBy: ID

  """The email of the registrant."""
  email: String!

  """The first name of the registrant."""
  firstName: String!

  """The id of the guardian reference."""
  guardianIds: [ID!]!

  """The Id of the Registrant."""
  id: ID!

  """Gets installments for a registrant."""
  installments: [Installment!]!

  """The last name of the registrant."""
  lastName: String!

  """The id of the registration reference."""
  registrationId: ID!

  """The date the registrant was last updated."""
  updatedAt: DateTime!

  """The id of the user who last updated the registrant."""
  updatedBy: ID!
}

"""Output the eligibility of a registration."""
type RegistrationEligibilityOutput {
  """The birth date from which the registration is eligible."""
  birthDateFrom: DateTime

  """The birth date to which the registration is eligible."""
  birthDateTo: DateTime

  """The gender to which the registration is eligible."""
  gender: RegistrationGender

  """The grades of which the registration is eligible."""
  grades: [Grade!]
}

enum RegistrationGender {
  ANY
  FEMALE
  MALE
}

"""Input parameters for a registration operation."""
input RegistrationInput {
  """The client mutation identifier."""
  clientMutationId: String

  """The machine identifier."""
  machineIdentifier: String!

  """The machine name."""
  machineName: String

  """The os version."""
  osVersion: String

  """The registration code."""
  registrationCode: String!

  """The installed version of Sportscode."""
  userVersion: String!
}

"""The result of a registration operation."""
type RegistrationPayload {
  """The response authentication string."""
  authentication: PayloadAuthentication

  """The client mutation identifier."""
  clientMutationId: String
}

enum RelationshipType {
  FAVORITE
  GUARDIAN
}

"""Representation of a Reserved Seat."""
type ReservedSeat {
  """
  The identifier of the general admission area associated with the reservation
  """
  generalAdmissionArea: String

  """The row of the reserved seat"""
  row: String

  """The identifier of the reserved seat itself"""
  seat: String

  """The full identifier of the reserved seat"""
  seatIdentifier: String

  """The section of the reserved seat"""
  section: String

  """The identifier of the table associated with the reservation"""
  table: String
}

enum RestreamStatus {
  INITIALIZED
  STARTED
  STOPPED
  UNINITIALIZED
  UNKNOWN
}

type RestreamSummary {
  """The GraphQL Restream Id."""
  id: ID!

  """The internal Id of the restream."""
  internalId: String

  """The last known status of the restream."""
  lastKnownStatus: RestreamStatus!

  """The destination platform of the restream."""
  platformId: Int!

  """The hudl-livestreaming restream Id of the restream."""
  restreamId: String

  """The stream type of the restream."""
  streamType: StreamType!
}

enum Role {
  ADMINISTRATOR
  COACH
  FAN @deprecated(reason: "Try to avoid using this. We don't store 'Fan' roles for user records, so prefer to use a nullable Role if 'no role' is a possibility.")
  INSIDER @deprecated(reason: "caleb - This is going away for now. There are some Insider accounts still, but no more should be getting created.")
  NONE @deprecated(reason: "Try to avoid using this. We don't store 'None' roles for user records, so prefer to use a nullable Role if 'no role' is a possibility.")
  PARTICIPANT
  RECRUITER @deprecated(reason: "jond - This should be going away...at some point.  However, it needs to continue to be accounted for in code/security checks")
  TECHNIQUE
}

"""Input to redeem Tickets and Passes"""
input ScannedQRCodeResultInput {
  """The GQL encoded ID of scanned object"""
  id: String!

  """The version of the QR code when scanned"""
  qrCodeVersion: String!

  """The time the QR code was scanned"""
  scannedAt: DateTime!

  """Status of the scanned QR code"""
  status: String

  """The type of scanned object"""
  type: String
}

"""Input to redeem Tickets and Passes via scanning"""
input ScanningRedemptionInput {
  """Access code that was used for scanning"""
  accessCode: String

  """Version of the app used for redemption"""
  appVersion: String

  """Email of the user scanning QR Codes"""
  email: String

  """First name of the user scanning QR codes"""
  firstName: String

  """Last name of the user scanning QR codes"""
  lastName: String

  """QR Codes that were scanned"""
  results: [ScannedQRCodeResultInput]

  """GraphQL encoded Id of the event being scanned for"""
  ticketedEventId: String
}

"""
A summary for a schedule entry. Primarily used for fan and public facing pages.
"""
type ScheduleEntryPublicSummary {
  """The broadcast status for the public schedule entry summary"""
  broadcastStatus: String

  """The numeric enum value of the game type of the schedule entry."""
  gameType: Int!

  """
  The numeric enum value of the gender of the owning team of the schedule entry.
  """
  genderId: Int!

  """The Id of the schedule entry summary."""
  id: ID!

  """The internal Id of the schedule entry summary."""
  internalId: String

  """The internal seasonId for the schedule entry."""
  internalSeasonId: String

  """The internal teamId of the owning team for the schedule entry."""
  internalTeamId: String!

  """The details of the opponent for the schedule entry."""
  opponentDetails: OpponentDetails

  """The UTC time that the schedule entry is projected to end."""
  projectedEndTime: DateTime

  """
  The GraphQL encoded Id of the schedule entry that this summary describes.
  """
  scheduleEntryId: ID!

  """The numeric enum value of the location of the schedule entry."""
  scheduleEntryLocation: Int!

  """The numeric enum value of the outcome of the schedule entry"""
  scheduleEntryOutcome: Int!

  """
  The GraphQL encoded schoolId of the owning team for the schedule entry.
  """
  schoolId: ID!

  """One of the team scores for a schedule entry."""
  score1: Short

  """One of the team scores for a schedule entry."""
  score2: Short

  """The GraphQL encoded seasonId for the schedule entry."""
  seasonId: ID!

  """The sport of owning team of the schedule entry."""
  sport: Sport!

  """
  The numeric enum value of the sport of owning team of the schedule entry.
  """
  sportId: Int!

  """The GraphQL encoded teamId of the owning team for the schedule entry."""
  teamId: ID!

  """The UTC time that the schedule entry will start."""
  timeUtc: DateTime!

  """
  Data about the tournament this schedule entry belongs to (if applicable).
  """
  tournamentMetadata: TournamentMetadata
}

"""A connection to a list of items."""
type ScheduleEntryPublicSummaryConnection {
  """A list of edges."""
  edges: [ScheduleEntryPublicSummaryEdge!]
  items: [ScheduleEntryPublicSummary!]

  """A flattened list of the nodes."""
  nodes: [ScheduleEntryPublicSummary]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type ScheduleEntryPublicSummaryEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: ScheduleEntryPublicSummary
}

enum ScheduleEntrySortType {
  SCHEDULE_ENTRY_DATE
}

type School {
  """Abbreviation of school name"""
  abbreviation: String

  """School's first address line"""
  addressLine1: String

  """School's second address line"""
  addressLine2: String

  """The banner image uri of the organization"""
  bannerImageUri: String

  """
  A boolean representing if the current user can edit the current school. This will always be false for unregistered users.
  """
  canUserEditSchool: Boolean!

  """City where school is located"""
  city: String

  """Country where school is located"""
  country: String

  """
  The two-character ISO 3166-1 alpha-2 country code corresponding to this organization's country
  """
  countryIsoAlpha2: String

  """
  The three-character ISO 3166-1 alpha-3 country code corresponding to this organization's country
  """
  countryIsoAlpha3: String

  """Full name of school"""
  fullName: String

  """The Id of the school"""
  id: ID!

  """The internal Id of the school"""
  internalId: String

  """If school is active or not"""
  isActive: Boolean!

  """If school is enabled or not"""
  isEnabled: Boolean!

  """
  Allows league name to be seen and edited from edit profile page in hudl-profiles
  """
  leagueName: String

  """The mascot of the organization"""
  mascot: String

  """The messaging participants' metadata for a school."""
  messagingParticipantsMetadata(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the last _n_ elements from the list."""
    last: Int
  ): MessagingParticipantsMetadataConnection

  """Maps to how an organization is classified"""
  orgClassificationId: Int!

  """The url for the school's public organization profile."""
  organizationProfileUrl: String

  """Program for an organization by program Id."""
  organizationProgram(
    """Id for organization program."""
    organizationProgramId: String!
  ): OrganizationProgram

  """Programs for an organization."""
  organizationPrograms: [OrganizationProgram]!

  """The settings of the organization"""
  organizationSettings: OrganizationSettings

  """School's primary color"""
  primaryColor: String

  """The profile image uri of the organization"""
  profileImageUri: String

  """School's secondary color"""
  secondaryColor: String

  """First 20 characters of school name"""
  shortName: String

  """State where school is located"""
  state: String

  """The teams for a school."""
  teamHeaders: [TeamHeader]

  """The time zone of the organization"""
  timeZone: String

  """The vanity url for the school's public organization profile."""
  vanityUrl: String

  """Zip code where school is located"""
  zipCode: String
}

"""A connection to a list of items."""
type SchoolConnection {
  """A list of edges."""
  edges: [SchoolEdge!]
  items: [School!]

  """A flattened list of the nodes."""
  nodes: [School]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type SchoolEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: School
}

"""Representation of a Scoreboard Entry."""
union ScoreboardEntry = GenericScoreboardEntry | VolleyballScoreboardEntry

"""Representation of a Scoreboard Session."""
type ScoreboardSession {
  """The timestamp that this Scoreboard Session was created at."""
  createdAt: DateTime!

  """The timestamp that this Scoreboard Session was deleted at."""
  deletedAt: DateTime

  """The timestamp that this Scoreboard Session was finalized at."""
  finalizedAt: DateTime

  """The GraphQL id of the Scoreboard Session."""
  id: String

  """Most recent Scoreboard Entry for this Scoreboard Session."""
  latestEntry: ScoreboardEntry

  """The id of the associated Media Stream."""
  mediaStreamId: String

  """The id of the associated Schedule Entry Id"""
  scheduleEntryId: String

  """List of all settings updates throughout the Scoreboard Session."""
  settings: [ScoreboardSettings]

  """The sport being played for the Scoreboard Session."""
  sport: Sport!

  """The id of the team that owns Scoreboard Session."""
  teamId: String

  """The timestamp that this Scoreboard Session was last updated at."""
  updatedAt: DateTime
}

"""Representation of a Scoreboard Settings object."""
union ScoreboardSettings = GenericScoreboardSettings | VolleyballScoreboardSettings

enum SearchItemResultType {
  ATHLETE
  CLUB
  GOVERNING_BODY
  MUNICIPALITY
  ORGANIZATION
  REGIONAL_ALIGNMENT
  UNKNOWN
}

enum SearchItemType {
  ATHLETE
  CLUB
  GOVERNING_BODY
  MUNICIPALITY
  ORGANIZATION
  REGIONAL_ALIGNMENT
}

type Season {
  """Season description {SeasonYear} - {SeasonYear + 1}"""
  description: String

  """The internal id of the season."""
  internalId: String

  """The internal team Id of the team who owns this season"""
  internalTeamId: String

  """Flag to verify if max preps was imported"""
  isMaxPrepsImported: Boolean
  roster: [PublicMember]

  """The GraphQL id of the season."""
  seasonId: ID!
  seasonRecord: SeasonRecord

  """Season value maps to internal season id"""
  value: String

  """The year that the season was started."""
  year: Int!
}

type SeasonRecord {
  """Date of most recent modification"""
  dateModified: DateTime!

  """Number of draws for the season"""
  draws: Int!

  """Number of losses for the season"""
  losses: Int!

  """The InternalId for the season"""
  seasonId: String

  """Number of wins for the season"""
  wins: Int!
}

"""Representation of a share history."""
type ShareHistory {
  """Date the share history was created."""
  createdAt: DateTime!

  """Date the share history was deleted."""
  deletedAt: DateTime

  """The GraphQL id of the share history."""
  id: ID!

  """The unique identifier for the new ticket group."""
  newTicketGroupId: ID!

  """The unique identifier for the original ticket group."""
  originalTicketGroupId: ID!

  """The list of entities shared"""
  sharedEntities: [SharedEntity!]!

  """Date the share history was updated."""
  updatedAt: DateTime!
}

"""Representation of an input to share ticketing entities."""
input ShareTicketingEntitiesInput {
  """The id associated with the original ticket group."""
  originalTicketGroupId: ID!

  """List of entities shared."""
  sharedEntities: [SharedEntityInput!]!
}

"""Representation of a shared ticketing entity."""
type SharedEntity {
  """The ID of the shared entity."""
  entityId: ID!

  """The type of the shared entity."""
  entityType: String!
}

"""Representation of the shared entities in a ticket group."""
input SharedEntityInput {
  """The ID of the shared entity."""
  entityId: ID!

  """The type of the shared entity."""
  entityType: String!
}

"""
The `Short` scalar type represents non-fractional signed whole 16-bit numeric values. Short can represent values between -(2^15) and 2^15 - 1.
"""
scalar Short

enum Sport {
  AUSTRALIAN_RULES_FOOTBALL
  AUSTRALIAN_RULES_FOOTBALL_RECRUITING
  BADMINTON
  BADMINTON_RECRUITING
  BASEBALL
  BASEBALL_RECRUITING
  BASKETBALL
  BASKETBALL_RECRUITING
  CHEER_AND_SPIRIT
  CHEER_AND_SPIRIT_RECRUITING
  CRICKET
  CRICKET_RECRUITING
  CROSS_COUNTRY
  CROSS_COUNTRY_RECRUITING
  CYCLING
  CYCLING_RECRUITING
  DANCE_AND_DRILL
  DANCE_AND_DRILL_RECRUITING
  FENCING
  FENCING_RECRUITING
  FIELD_HOCKEY
  FIELD_HOCKEY_RECRUITING
  FOOTBALL
  FOOTBALL_RECRUITING
  GOLF
  GOLF_RECRUITING
  GYMNASTICS
  GYMNASTICS_RECRUITING
  HANDBALL
  HANDBALL_RECRUITING
  ICE_HOCKEY
  ICE_HOCKEY_RECRUITING
  LACROSSE
  LACROSSE_RECRUITING
  NETBALL
  NETBALL_RECRUITING
  NO_SPORT
  OTHER
  PERFORMING_ARTS
  PERFORMING_ARTS_RECRUITING
  RUGBY
  RUGBY_LEAGUE
  RUGBY_LEAGUE_RECRUITING
  RUGBY_RECRUITING
  RUGBY_UNION
  RUGBY_UNION_RECRUITING
  SAILING_AND_YACHTING
  SAILING_AND_YACHTING_RECRUITING
  SOCCER
  SOCCER_RECRUITING
  SOFTBALL
  SOFTBALL_RECRUITING
  SQUASH
  SQUASH_RECRUITING
  SURFING
  SURFING_RECRUITING
  SWIMMING_AND_DIVING
  SWIMMING_AND_DIVING_RECRUITING
  TENNIS
  TENNIS_RECRUITING
  TENPIN_BOWLING
  TENPIN_BOWLING_RECRUITING
  TRACK
  TRACK_RECRUITING
  VOLLEYBALL
  VOLLEYBALL_RECRUITING
  WATER_POLO
  WATER_POLO_RECRUITING
  WRESTLING
  WRESTLING_RECRUITING
}

enum StreamType {
  EDGE_DATA
  EXTRA_WIDE_TACTICAL
  GAME_DATA
  MOSAIC_PLAYER_FEED
  PANORAMIC
  PANORAMIC_DEBUG
  RAW_BALL_DATA
  RAW_HISTOGRAM_DATA
  RAW_VIDEO
  SCOREBOARD_VIDEO
  STATIC_CAMERA
  TACTICAL
  TACTICAL_DEBUG
  UNKNOWN
}

type SubdivisionHeader {
  """The Id of the Governing Body the Subdivision belongs to"""
  governingBodyId: String

  """The graphQL Id of the Subdivision"""
  id: ID!

  """The internal Id of the Subdivision"""
  internalId: String

  """The name of the Subdivision"""
  name: String
}

type Subscription {
  id: String
}

type TaggedFeedUser {
  feedUserId: TaggedFeedUserId
  followersCount: Long!
  friendsCount: Long!
  lowestResImageUrl: String
  name: String
}

type TaggedFeedUserId {
  relatedId: String
  type: FeedUserType!
}

"""A summary of team information that is public."""
type TeamHeader {
  """Team additional Fields"""
  additionalFields: [String]

  """Background images for team."""
  backgroundImages: [BackgroundImage]

  """Current Season for a team"""
  currentSeason: Season

  """Current season year"""
  currentSeasonYear: Int

  """Discoverable Pass Configs"""
  discoverablePassConfigs(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the last _n_ elements from the list."""
    last: Int
  ): DiscoverablePassConfigsConnection

  """Discoverable Ticketed Events"""
  discoverableTicketedEvents(
    """Returns the elements in the list that come after the specified cursor."""
    after: Cursor

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: Cursor

    """Returns the first _n_ elements from the list."""
    first: Int

    """Returns the last _n_ elements from the list."""
    last: Int
  ): DiscoverableTicketedEventsConnection

  """The gender of the team."""
  gender: Gender!

  """GraphQL Encoded Team Id"""
  id: ID!

  """Internal teamId"""
  internalId: String

  """Is the user a coach or admin for the team."""
  isCoachOrAdmin: Boolean!

  """Indicates whether the team's profile is hidden."""
  isTeamProfileHidden: Boolean!

  """Indicates whether the team is a test team."""
  isTestTeam: Boolean!

  """Team logo"""
  logo: String

  """Team name"""
  name: String

  """The summary of the team's organization."""
  organizationHeader: OrganizationHeader
  organizationName: String @deprecated(reason: "Use OrganizationHeader.OrganizationName instead")

  """Positions"""
  positions: [String]

  """The primary color of the team."""
  primaryColor: String

  """Seasons for the team."""
  seasons: [Season]

  """The secondary color of the team."""
  secondaryColor: String

  """The sport of the team."""
  sport: Sport!

  """Team Level"""
  teamLevel: TeamLevel!

  """The url for the teams's public team profile."""
  teamProfileUrl: String
}

"""A connection to a list of items."""
type TeamHeaderConnection {
  """A list of edges."""
  edges: [TeamHeaderEdge!]
  items: [TeamHeader!]

  """A flattened list of the nodes."""
  nodes: [TeamHeader]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type TeamHeaderEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: TeamHeader
}

enum TeamLevel {
  FRESHMAN
  JUNIOR_VARSITY
  OTHER
  OTHER_NON_HS
  SOPHOMORE
  VARSITY
}

"""Representation of a terminal connection token."""
type TerminalConnectionToken {
  """The client secret of the connection token."""
  clientSecret: String
}

"""Representation of a terms of service for ticketing."""
type TermsOfService {
  """The effective date of the terms of service."""
  effectiveDate: DateTime!

  """The legal text of the terms of service."""
  text: String

  """The title of the terms of service."""
  title: String

  """The semantic version (major.minor) of the terms of service."""
  version: String
}

"""Representation of a ticket."""
type Ticket {
  """Date the ticket was created."""
  createdAt: DateTime!

  """Date the ticket was deleted."""
  deletedAt: DateTime

  """The email of the person associated with this ticket."""
  email: String

  """The first name of the person associated with this ticket."""
  firstName: String

  """The GraphQL id of the ticket."""
  id: ID!

  """The last name of the person associated with this ticket."""
  lastName: String

  """The unique identifier for the Pass associated with this ticket."""
  passId: ID

  """The payment type used to purchase this ticket."""
  paymentType: String

  """The QR Code data for this ticket"""
  qrCodeData: String

  """The url of the QR code associated with this ticket."""
  qrCodeUrl: String @deprecated(reason: "This property will no longer return a value. Use the QRCodeData property instead.")

  """
  The timestamp when this ticket was redeemed. Will be null if the ticket has not yet been redeemed.
  """
  redeemedAt: DateTime

  """
  Whether or not the ticket is refundable. Taken from the related Ticketed Event.
  """
  refundable: Boolean!

  """The reserved seat associated with this ticket."""
  reservedSeat: ReservedSeat

  """Date the ticket was shared."""
  sharedAt: DateTime

  """The source where the ticket was purchased."""
  source: String

  """The status of the ticket."""
  ticketStatus: String

  """The unique identifier for the Ticket Type associated with this ticket."""
  ticketTypeId: ID

  """The unique identifier for the Ticketed Event this ticket is for."""
  ticketedEventId: ID!

  """Date the ticket was updated."""
  updatedAt: DateTime!

  """
  The Hudl user id of the person associated with this ticket, if one exists.
  """
  userId: String
}

"""Representation of a ticket group."""
type TicketGroup {
  """The timestamp when this ticket group was created."""
  createdAt: DateTime!

  """
  The currency associated with the OrderTotalInCents of this ticket group.
  """
  currency: String

  """
  The timestamp when this ticket group was deleted. Will be null if the ticket is not deleted.
  """
  deletedAt: DateTime

  """The email of the person associated with this ticket group."""
  email: String

  """The first name of the person associated with this ticket group"""
  firstName: String

  """The responses to the form fields associated with this ticket group."""
  formFieldResponses: [FormFieldResponse]

  """The GraphQL id of the ticket group."""
  id: ID!

  """The item categories associated with the ticket group."""
  itemCategories: [String!]

  """The last name of the person associated with this ticket group"""
  lastName: String

  """The total cost of the ticket group in cents."""
  orderTotalInCents: Int

  """The list of pass ids associated with this ticket group."""
  passIds: [ID!]

  """The passes associated with this ticket group"""
  passes: [Pass]

  """The payment type used for this ticket group."""
  paymentType: String

  """The source where the ticket group was purchased."""
  source: String

  """
  The more readable but not necessarily unique reference/identifier for this ticket group.
  """
  ticketGroupReference: String

  """The list of ticket ids associated with this ticket group."""
  ticketIds: [ID!]

  """The tickets associated with this ticket group"""
  tickets: [Ticket]

  """The timestamp when this ticket group was last updated."""
  updatedAt: DateTime!

  """
  The Hudl user id of the person associated with this ticket group, if one exists
  """
  userId: String
}

"""A connection to a list of items."""
type TicketGroupConnection {
  """A list of edges."""
  edges: [TicketGroupEdge!]
  items: [TicketGroup!]

  """A flattened list of the nodes."""
  nodes: [TicketGroup!]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type TicketGroupEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: TicketGroup!
}

enum TicketGroupItemFilterType {
  PASSES
  TICKETS
}

enum TicketGroupSortType {
  TICKET_GROUP_PURCHASER_LAST_NAME
  TICKET_GROUP_PURCHASE_DATE
}

"""
Representation of an input to fetch ticket groups associated with a user.
"""
input TicketGroupsByUserIdInput {
  """Value used to start a page of ticket groups."""
  after: Cursor

  """Value used to end a page of ticket groups."""
  before: Cursor

  """Filters for Ticket Groups created before this date."""
  endDate: DateTime

  """
  Number of ticket groups requested, taken from the start of the total list. Also used for paging.
  """
  first: Int

  """Filters for ticket groups with certain items in the ticket group."""
  itemFilterType: TicketGroupItemFilterType

  """
  Number of ticket groups requested, taken from the end of the total list. Also used for paging.
  """
  last: Int

  """If true, will sort results ascending instead of descending."""
  sortByAscending: Boolean!

  """Used to determine how to sort the resulting summaries."""
  sortType: TicketGroupSortType!

  """Filters for Ticket Groups created after this date."""
  startDate: DateTime
}

"""Representation of a ticket type."""
type TicketType {
  """Date the ticket type was created."""
  createdAt: DateTime!

  """The currency associated with the priceInCents of the ticket type."""
  currency: String

  """Date the ticket type was deleted."""
  deletedAt: DateTime

  """The GraphQL id of the ticket type."""
  id: ID!

  """The name of the ticket type."""
  name: String

  """The organization associated with the ticket type."""
  organizationId: String

  """The price in cents of the ticket type."""
  priceInCents: Int!

  """The quantity of the ticket type."""
  quantity: Int

  """Date the ticket type was updated."""
  updatedAt: DateTime!
}

"""
Representation of a ticket type pricing summary including information about the Hudl fees applied.
"""
type TicketTypePricingSummary {
  """The currency associated with the priceInCents of the ticket type."""
  currency: String

  """The Hudl Fee applied to the ticket purchase in cents"""
  hudlFeeInCents: Int!

  """
  The price of the ticket type in cents, factoring in price overrides set on the event
  """
  priceInCents: Int!

  """The price of the ticket type in cents including the Hudl Fee"""
  priceInCentsWithHudlFee: Int!

  """Whether the Hudl fee should be displayed to the user."""
  shouldShowFee: Boolean!

  """The ID of the ticket type."""
  ticketTypeId: ID!

  """The ID of the ticketed event."""
  ticketedEventId: ID!
}

"""Representation of a ticket type referenced by a ticketed event."""
type TicketTypeReference {
  """The price override to the referenced ticket type."""
  priceOverride: Int

  """The quantity override to the referenced ticket type."""
  quantityOverride: Int

  """The seating chart category IDs for the referenced ticket type."""
  seatingChartCategoryIds: [String]

  """The GraphQL id of the referenced ticket type."""
  ticketTypeId: ID
}

"""Representation of a ticketable event"""
type TicketedEvent {
  """
  The pass configurations that are associated with a given ticketed event
  """
  associatedPassConfigs: [PassConfig]

  """Date the ticketed event was created."""
  createdAt: DateTime!

  """The date/time the ticketed event takes place."""
  date: DateTime!

  """Date the ticketed event was deleted."""
  deletedAt: DateTime

  """The description of the ticketed event."""
  description: String

  """The status of the ticketed event."""
  eventStatus: String

  """The fee strategy applied to the ticketed event."""
  feeStrategy: String

  """The IDs of the form fields associated with the ticketed event."""
  formFieldIds: [ID!]

  """The form fields associated with the ticketed event"""
  formFields: [FormField]

  """The gender of the teams participating in the ticketed event."""
  gender: String

  """The GraphQL id of the ticketed event."""
  id: ID!

  """List of event entries linked to the ticketed event."""
  linkedEntries: [LinkedEntry]

  """The name of the ticketed event."""
  name: String

  """The organization associated with the ticketed event."""
  organizationId: ID!

  """List of the team ids of teams participating in the ticketed event."""
  participatingTeamIds: [ID]

  """
  The quantity remaining for each ticket type associated with a ticketed event
  """
  quantityRemainingForTicketTypes: [QuantityRemainingOutput]

  """Is the ticketed event refundable."""
  refundable: Boolean

  """The ID of the seating chart event associated with this ticketed event."""
  seatingChartEventId: String

  """The sport being played at the ticketed event."""
  sport: String

  """The pricing summaries of the ticket types associated with the event"""
  ticketTypePricingSummaries: [TicketTypePricingSummary]

  """Referenced ticket types for the event"""
  ticketTypeReferences: [TicketTypeReference]

  """The hydrated ticket types associated with this ticketed event"""
  ticketTypes: [TicketType]

  """
  The time zone identifier for when the ticketed event takes place. Must be a valid IANA time zone identifier.
  """
  timeZoneIdentifier: String

  """Date the ticketed event was updated."""
  updatedAt: DateTime!

  """The ID of the venue configuration the ticketed event is being held at."""
  venueConfigurationId: ID

  """The ID of the venue the ticketed event is being held at."""
  venueId: String @deprecated(reason: "Use VenueConfigurationId instead.")

  """The visibility of the ticketed event."""
  visibility: String
}

"""Representation of an Access Code"""
type TicketedEventAccessCode {
  """The Access Code associated with the organization"""
  accessCode: String

  """Date the Access Code was created."""
  createdAt: DateTime!

  """Date the Access Code was deleted."""
  deletedAt: DateTime

  """The GraphQL id of the access code."""
  id: ID!
  organization: School

  """The organization associated with the Access Code."""
  organizationId: ID!

  """Date the Access Code was updated."""
  updatedAt: DateTime!
}

"""A connection to a list of items."""
type TicketedEventConnection {
  """A list of edges."""
  edges: [TicketedEventEdge!]
  items: [TicketedEvent!]

  """A flattened list of the nodes."""
  nodes: [TicketedEvent]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """Identifies the total count of items in the connection."""
  totalCount: Int!
}

"""An edge in a connection."""
type TicketedEventEdge {
  """A cursor for use in pagination."""
  cursor: String!

  """The item at the end of the edge."""
  node: TicketedEvent
}

enum TicketedEventSortType {
  TICKETED_EVENT_DATE
}

"""Input to create a cash checkout for a cart of ticketing items."""
input TicketingCashCheckoutInput {
  """The access code used to log in to the app."""
  accessCode: String!

  """If true, will redeem the ticket at the time of creation"""
  autoRedeemTickets: Boolean

  """
  A unique identifier for the checkout session that is used to prevent duplicate checkouts.
  """
  checkoutSessionId: String!

  """The checksum for the cash checkout request."""
  checksum: String!

  """The email of the checking out person"""
  email: String

  """If true, will email the order to purchaser email address"""
  emailOrder: Boolean

  """The first name of the checking out person"""
  firstName: String

  """The form field responses for the checkout"""
  formFieldResponses: [FormFieldResponseInput]

  """The last name of the checking out person"""
  lastName: String

  """A list of ticketing items to include in the checkout."""
  lineItems: [TicketingPricingLineItemInput]!

  """
  The seats.io channel ids of the channels that the hold is associated with. Only set for pass renewals.
  """
  seatsDotIoChannelIds: [String!]

  """
  The token identifying the hold in the seats.io system on the seats selected for this checkout
  """
  seatsDotIoHoldToken: String

  """The source where the checkout is happening"""
  source: String

  """
  Indicates terms of service agreement, if true will save agreement with purchaser details
  """
  tosAgreement: Boolean

  """The total amount in cents to charge for the checkout."""
  totalInCents: Int!

  """The Hudl user id of the checking out person, if one exists"""
  userId: String
}

"""Input to create a checkout for a cart of ticketing items."""
input TicketingCheckoutInput {
  """If true, will redeem the ticket at the time of creation"""
  autoRedeemTickets: Boolean

  """The email of the checking out person"""
  email: String

  """If true, will email the order to purchaser email address"""
  emailOrder: Boolean

  """The first name of the checking out person"""
  firstName: String

  """The form field responses for the checkout"""
  formFieldResponses: [FormFieldResponseInput]

  """The Hudl payment token id to use for the checkout."""
  hudlPaymentTokenId: String

  """The last name of the checking out person"""
  lastName: String

  """A list of ticketing items to include in the checkout."""
  lineItems: [TicketingPricingLineItemInput]!

  """
  The seats.io channel ids of the channels that the hold is associated with. Only set for pass renewals.
  """
  seatsDotIoChannelIds: [String!]

  """
  The token identifying the hold in the seats.io system on the seats selected for this checkout
  """
  seatsDotIoHoldToken: String

  """The source where the checkout is happening"""
  source: String

  """
  Indicates terms of service agreement, if true will save agreement with purchaser details
  """
  tosAgreement: Boolean

  """The total amount in cents to charge for the checkout."""
  totalInCents: Int!

  """The Hudl user id of the checking out person, if one exists"""
  userId: String
}

"""A checkout result for a ticketing checkout."""
type TicketingCheckoutResult {
  """A summary of pricing information for the checkout."""
  pricingSummary: TicketingPricingSummary

  """The ticket group created as a result of the checkout."""
  ticketGroup: TicketGroup
}

"""Representation of an experiment conducted in hudl-ticketing."""
type TicketingExperiment {
  """The name of the experiment."""
  experimentName: String

  """The ID of the experiment."""
  id: ID!

  """The organization ids associated with the experiment."""
  organizationIds: [String]
}

"""
A ticketing line item with pricing and quantity information. All amounts in cents.
"""
type TicketingLineItem {
  """The ID of the entity with pricing information for the line item."""
  lineItemId: ID

  """The type of the entity referenced by the line item ID."""
  lineItemType: String

  """The quantity of the line item."""
  quantity: Int!

  """
  A supplementary ID for the line item. e.g. for ticket types, this may be the ID of the relevant ticketed event.
  """
  referenceId: ID

  """The total price of the line item (quantity * unit price)."""
  totalPriceInCents: Int!

  """The unit price of the line item."""
  unitPriceInCents: Int!
}

input TicketingPricingLineItemInput {
  """The custom price in cents for this line item."""
  customPriceInCents: Int

  """The category for the line item."""
  lineItemCategory: String

  """The ID of the entity containing pricing information."""
  lineItemId: ID

  """The type of the item to be priced."""
  lineItemType: String

  """The organization ID for the line item."""
  organizationId: ID

  """The quantity of the item to be priced."""
  quantity: Int!

  """
  A supplementary ID for the line item to be priced. e.g. for ticket types, include the ticketed event ID.
  """
  referenceId: ID

  """The selected seats for this line item."""
  selectedSeats: [String]
}

"""
Representation of a pricing summary for a list of ticketing line items. All amounts listed in cents.
"""
type TicketingPricingSummary {
  """The currency for the pricing summary."""
  currency: String

  """
  The total amount of fees for the pricing summary. This field should no longer be used in the UI. Due to transparent pricing, the fee components should be broken down into their own fields.
  """
  feesInCents: Int!

  """The service fee Hudl applies to the order."""
  hudlFeeInCents: Int!

  """
  The list of line items for the pricing summary, each including detailed price information.
  """
  lineItems: [TicketingLineItem]

  """The processing fee our payment provider applies to the order."""
  paymentProcessingFeeInCents: Int!

  """Determines whether the fee should be displayed in the UI."""
  shouldShowFee: Boolean

  """
  The subtotal of the pricing summary. This is the sum of all line items * their quantity (exclusive of fees). This field should no longer be used in the UI. Due to transparent pricing, the subtotal should include the Hudl fee, so opt to use SubtotalWithHudlFeeInCents.
  """
  subtotalInCents: Int!

  """
  The subtotal of all line items in the pricing summary, including the Hudl fee.
  """
  subtotalWithHudlFeeInCents: Int!

  """The total amount for the pricing summary (inclusive of fees)."""
  totalInCents: Int!
}

"""The `TimeSpan` scalar represents an ISO-8601 compliant duration type."""
scalar TimeSpan

type TournamentMetadata {
  """The OpponentDetailsDto for the Away Team."""
  awayTeam: OpponentDetails

  """The Name of the Away Tournament Team"""
  awayTeamName: String!

  """The OpponentDetailsDto for the Home Team."""
  homeTeam: OpponentDetails

  """The Name of the Home Tournament Team"""
  homeTeamName: String!

  """Tournament ID from hudl-tournaments."""
  tournamentId: ID
}

"""Representation of a transaction for an org program registration."""
type Transaction {
  """The allocations of the transaction."""
  allocations: [InstallmentAllocationDto!]!

  """The amount of the transaction."""
  amountInBaseDenomination: Int!

  """The date the transaction was created."""
  createdAt: DateTime!

  """The id of the user who created the transaction."""
  createdBy: ID!

  """The date the transaction was deleted, if applicable."""
  deletedAt: DateTime

  """The id of the user who deleted the transaction, if applicable."""
  deletedBy: ID

  """The id of the transaction."""
  id: ID!

  """The payment method of the transaction."""
  paymentMethodType: PaymentMethodType!

  """The id of the reference."""
  referenceId: String!

  """The ids of the registrants."""
  registrantIds: [ID!]!

  """The status of the transaction."""
  status: TransactionStatus!

  """The date the transaction was last updated."""
  updatedAt: DateTime!

  """The id of the user who last updated the transaction."""
  updatedBy: ID!
}

enum TransactionStatus {
  COMPLETED
  FAILED
  NONE
  PENDING
}

"""Representation of an input to transfer ticketing entities."""
input TransferTicketingEntitiesInput {
  """List of entities to transfer."""
  entitiesToTransfer: [SharedEntityInput!]!

  """The id associated with the original ticket group."""
  originalTicketGroupId: ID!

  """The email of the user to transfer the entities to"""
  recipientEmail: String!

  """The first name of the user to transfer the entities to."""
  recipientFirstName: String!

  """The last name of the user to transfer the entities to"""
  recipientLastName: String!
}

"""
The UnsignedShort scalar type represents a unsigned 16-bit numeric non-fractional value greater than or equal to 0.
"""
scalar UnsignedShort

"""Input parameters for a registration update operation."""
input UpdateRegistrationInput {
  """The client mutation identifier."""
  clientMutationId: String

  """The machine identifier."""
  machineIdentifier: String!

  """The machine name."""
  machineName: String

  """The os version."""
  osVersion: String

  """The registration code."""
  registrationCode: String!

  """The License security token."""
  token: String!

  """The installed version of Sportscode."""
  userVersion: String!
}

"""Input type for upserting a registrant for a program registration."""
input UpsertRegistrantInput {
  """The email of the registrant."""
  email: String!

  """The first name of the registrant."""
  firstName: String!

  """The IDs of the guardians of the registrant."""
  guardianIds: [ID!]

  """The ID of the registrant. Required for updates, null for creates."""
  id: ID

  """The ID of the installment plan for the registrant."""
  installmentPlanId: ID

  """The last name of the registrant."""
  lastName: String!

  """The ID of the program registration this registrant belongs to."""
  registrationId: ID!
}

enum VanityDestinationType {
  ATHLETE
  GOVERNING_BODY
  MUNICIPALITY
  REGIONAL_ALIGNMENT
  SCHOOL
  TEAM
  UNKNOWN
}

"""
Representation of an input to log a viewed quartile of community content.
"""
input ViewQuartileCommunityContentInput {
  """The Turn ID or mobile device ID."""
  adTrackingId: String

  """
  Additional properties to log with the impression. Ex: pageVisibility, sessionId, usePostRollCta
  """
  additionalProperties: [AdditionalPropertiesKeyValuePairInput]

  """The authorization ID of the user who is viewing the content."""
  authUserId: String

  """The packages the user has access to."""
  authUserPackages: [Package!]

  """The authorization ID of the team the user is viewing the content for."""
  authUserTeamId: String

  """Uniquely identifies a piece of community content."""
  communityContentId: CommunityContentIdInput

  """Describes the location of the content container."""
  container: CommunityContentContainerType!

  """
  A way to segment deeper into the container. Ex: for the explore hub, each timeline would be a different container section.
  """
  containerSection: String

  """
  A way to segment deeper into the continer. Ex: for profiles, this could be User, Team, Author, Region, or Conference.
  """
  containerType: String

  """The IP address of the client making the request."""
  ipAddress: String

  """
  True if Hudl automatically advanced the user to this video and played it as the top suggestion.
  """
  isAutoAdvance: Boolean!

  """Specifies if the video was played automatically."""
  isAutoPlay: Boolean!

  """Specifies if the video is being played inline."""
  isInlinePlay: Boolean!

  """Specifies if the video is muted."""
  isMuted: Boolean!

  """The locale of the user making the request."""
  locale: String

  """Describes the type of player rendering the community content video."""
  player: CommunityContentContainerPlayer!

  """The height of the video player in pixels."""
  playerHeight: Int

  """The width of the video player in pixels."""
  playerWidth: Int

  """The URL of the page that referred the user to the content."""
  referrerUrl: String

  """The URL of the request."""
  requestUrl: String

  """The application type that is requesting the content."""
  requestingApp: CommunityContentRequestingApp!

  """The referrer url for when the session on hudl.com began."""
  sessionReferrerUrl: String

  """The user agent of the client making the request."""
  userAgent: String
}

"""Representation of a volleyball Scoreboard Entry."""
type VolleyballScoreboardEntry {
  """The GraphQL id of the volleyball scoreboard entry."""
  id: ID!

  """
  The id of the scoreboard session the volleyball scoreboard entry belongs to.
  """
  scoreboardSessionId: String!

  """The current set in the volleyball scoreboard entry."""
  set: Int!

  """Team 1's current score in the volleyball scoreboard entry."""
  team1Score: Int!

  """Team 1's current set score in the volleyball scoreboard entry."""
  team1SetScore: Int!

  """Team 2's current score in the volleyball scoreboard entry."""
  team2Score: Int!

  """Team 2's current set score in the volleyball scoreboard entry."""
  team2SetScore: Int!

  """The UTC timestamp of the volleyball scoreboard entry."""
  timeUtc: DateTime!
}

"""Representation of a volleyball Scoreboard Settings object."""
type VolleyballScoreboardSettings {
  """Whether or not the team names are displayed on the scoreboard overlay."""
  namesOn: Boolean!

  """
  Whether or not the scoreboard overlay should be displayed on the video.
  """
  overlayOn: Boolean!

  """Whether or not the scores are displayed on the scoreboard overlay."""
  scoresOn: Boolean!

  """Whether or not the set is displayed on the scoreboard overlay."""
  setOn: Boolean!

  """Whether or not the set scores are displayed on the scoreboard overlay."""
  setScoresOn: Boolean!

  """Team 1's name that is displayed on the scoreboard overlay."""
  team1Name: String!

  """Team 1's primary color that is displayed on the scoreboard overlay."""
  team1PrimaryColor: String!

  """Team 2's name that is displayed on the scoreboard overlay."""
  team2Name: String!

  """Team 2's primary color that is displayed on the scoreboard overlay."""
  team2PrimaryColor: String!

  """The UTC timestamp of the scoreboard settings."""
  timeUtc: DateTime!
}