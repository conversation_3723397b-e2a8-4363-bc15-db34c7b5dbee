enum AthleteInviteRequestValidationStatus {
  ALREADY_ON_TEAM
  CAN_JOIN_HUDL
  HAS_ACCOUNT
  INVALID_INVITE_CODE
  REQUEST_PENDING_FOR_TEAM
  UNKNOWN
}

type Carrier {
  description: String
  value: String
}

"""
The `Cursor` scalar type is an ordered key used in paginated Relay connections.
"""
scalar Cursor

"""
The `Date` scalar type represents a year, month and day in accordance with the [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) standard.
"""
scalar Date

"""
The `DateTime` scalar type represents a date and time. `DateTime` expects timestamps to be formatted in accordance with the [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) standard.
"""
scalar DateTime

"""
The `DateTimeOffset` scalar type represents a date, time and offset from UTC. `DateTimeOffset` expects timestamps to be formatted in accordance with the [ISO-8601](https://en.wikipedia.org/wiki/ISO_8601) standard.
"""
scalar DateTimeOffset

scalar Decimal

enum Gender {
  COED
  MENS
  WOMENS
}

type Group {
  """Group date created"""
  dateCreated: DateTime!

  """The graphql Id of the group."""
  id: ID!

  """The hudl Id of the group."""
  internalId: String

  """Group is system group"""
  isSystemGroup: Boolean!

  """Member ids"""
  memberIds(after: String = null, before: String = null, first: Int = null, last: Int = null): IDConnection

  """Group Name"""
  name: String

  """Group Video Activity"""
  videoActivity: VideoActivity
}

"""Connection to related objects with relevant pagination information."""
type GroupConnection {
  """Information to aid in pagination."""
  edges: [GroupEdge]

  """
  A list of all of the objects returned in the connection. This is a convenience field provided for quickly exploring the API; rather than querying for `{ edges { node } }` when no edge data is needed, this field can be used instead. Note that when clients like Relay need to fetch the `cursor` field on the edge to enable efficient pagination, this shortcut cannot be used, and the full `{ edges { node } }` version should be used instead.
  """
  items: [Group]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """
  A count of the total number of objects in this connection, ignoring pagination. This allows a client to fetch the first five objects by passing "5" as the argument to first, then fetch the total count so it could display "5 of 83", for example. In cases where we employ infinite scrolling or don't have an exact count of entries, this field will return `null`.
  """
  totalCount: Int
}

"""An edge in a connection between two objects."""
type GroupEdge {
  """A cursor for use in pagination."""
  cursor: Cursor!

  """The item at the end of the edge."""
  node: Group
}

input GroupInput {
  """Group id"""
  id: String = null

  """Member ids to remove"""
  membersToAdd: [String] = null

  """Member ids to remove"""
  membersToRemove: [String] = null

  """Group Name"""
  name: String = null
}

"""Connection to related objects with relevant pagination information."""
type IDConnection {
  """Information to aid in pagination."""
  edges: [IDEdge]

  """
  A list of all of the objects returned in the connection. This is a convenience field provided for quickly exploring the API; rather than querying for `{ edges { node } }` when no edge data is needed, this field can be used instead. Note that when clients like Relay need to fetch the `cursor` field on the edge to enable efficient pagination, this shortcut cannot be used, and the full `{ edges { node } }` version should be used instead.
  """
  items: [ID!]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """
  A count of the total number of objects in this connection, ignoring pagination. This allows a client to fetch the first five objects by passing "5" as the argument to first, then fetch the total count so it could display "5 of 83", for example. In cases where we employ infinite scrolling or don't have an exact count of entries, this field will return `null`.
  """
  totalCount: Int
}

"""An edge in a connection between two objects."""
type IDEdge {
  """A cursor for use in pagination."""
  cursor: Cursor!

  """The item at the end of the edge."""
  node: ID!
}

interface INode {
  id: ID!
}

type InviteCode {
  """Date created"""
  dateCreated: DateTime!

  """Invite code Id"""
  id: String

  """Invite requests"""
  inviteRequests: [InviteRequest]

  """Date expired"""
  lastModifiedDate: DateTime!

  """Team"""
  team: TeamHeader

  """Team Id"""
  teamId: String
}

type InviteCodeWrapper {
  """Invite Code"""
  inviteCode: InviteCode

  """Team Join Eligibility Status"""
  teamJoinEligibilityStatus: AthleteInviteRequestValidationStatus!
}

type InviteRequest {
  """Cell carrier"""
  cellCarrier: String

  """Cell number."""
  cellPhone: String

  """Date created"""
  dateCreated: DateTime!

  """Email"""
  email: String

  """First Name"""
  firstName: String

  """Graduation Year"""
  graduationYear: Int

  """Jersey number"""
  jersey: String

  """Last Name"""
  lastName: String

  """Position"""
  position: [String]

  """Role"""
  role: Role!

  """User Id"""
  userId: String
}

input InviteRequestInput {
  """Cell carrier"""
  cellCarrier: String = null

  """Cell phone"""
  cellPhone: String = null

  """Email"""
  email: String = null

  """Existing User"""
  existingUser: Boolean!

  """First name"""
  firstName: String = null

  """Graduation Year"""
  graduationYear: Int = null

  """Jersey"""
  jersey: String = null

  """Last name"""
  lastName: String = null

  """Password"""
  password: String = null

  """Position"""
  position: [String] = null

  """Role"""
  role: Role!
}

type MaxPrepsTeam {
  """MaxPreps has roster information."""
  hasRoster: Boolean!

  """MaxPreps has schedule information."""
  hasSchedule: Boolean!

  """Hudl school ID"""
  hudlSchoolId: String

  """Descriptive season name"""
  hudlSeason: String

  """Descriptive name of Hudl sport"""
  hudlSport: String

  """MaxPreps School Name"""
  maxPrepsName: String

  """MaxPreps School ID"""
  maxPrepsSchoolId: String

  """Longform name of the school with location"""
  name: String

  """Season ID"""
  seasonId: String

  """Season year"""
  seasonYear: Int!

  """Sport enum ID"""
  sportId: Int!

  """Hudl Team ID"""
  teamId: String
}

type Member implements INode {
  """Member's phone number."""
  cell: String

  """Member's phone carrier."""
  cellCarrier: String

  """Member's email."""
  email: String

  """Member's first name."""
  firstName: String

  """Member's full name."""
  fullName: String

  """Member Graduation Year"""
  graduationYear: Int

  """Member groups"""
  groups(after: String = null, before: String = null, first: Int = null, includeFamilyMembers: Boolean = null, last: Int = null): IDConnection

  """Height"""
  height: String

  """The graphql Id of the member."""
  id: ID!

  """The hudl Id of the member."""
  internalId: String

  """IsDisabled"""
  isDisabled: Boolean!

  """Is member opted in to Recruit"""
  isOptedInToRecruit: Boolean!

  """Jersey"""
  jersey: String

  """Last Login Date"""
  lastLoginDate: DateTime

  """Member's last name."""
  lastName: String

  """Picture"""
  picture: String

  """PictureUrl"""
  pictureUrl: String

  """Position"""
  position: [String]

  """Member's role."""
  role: Role!

  """Season Ids"""
  seasonIds: [String]

  """Member's status"""
  status: UserStatus!

  """Member Video Activity"""
  videoActivity: VideoActivity

  """Weight"""
  weight: Int
}

"""Connection to related objects with relevant pagination information."""
type MemberConnection {
  """Information to aid in pagination."""
  edges: [MemberEdge]

  """
  A list of all of the objects returned in the connection. This is a convenience field provided for quickly exploring the API; rather than querying for `{ edges { node } }` when no edge data is needed, this field can be used instead. Note that when clients like Relay need to fetch the `cursor` field on the edge to enable efficient pagination, this shortcut cannot be used, and the full `{ edges { node } }` version should be used instead.
  """
  items: [Member]

  """Information to aid in pagination."""
  pageInfo: PageInfo!

  """
  A count of the total number of objects in this connection, ignoring pagination. This allows a client to fetch the first five objects by passing "5" as the argument to first, then fetch the total count so it could display "5 of 83", for example. In cases where we employ infinite scrolling or don't have an exact count of entries, this field will return `null`.
  """
  totalCount: Int
}

"""An edge in a connection between two objects."""
type MemberEdge {
  """A cursor for use in pagination."""
  cursor: Cursor!

  """The item at the end of the edge."""
  node: Member
}

input MemberInput {
  """Phone number"""
  cell: String = null

  """Phone carrier"""
  cellCarrier: String = null

  """Member's email"""
  email: String = null

  """Member's first name"""
  firstName: String = null

  """Graduation Year"""
  graduationYear: Int = null

  """Group Membership"""
  groupMembership: [String] = null

  """Height"""
  height: String = null

  """Member's id"""
  id: String = null

  """Jersey"""
  jersey: String = null

  """Member's last name"""
  lastName: String = null

  """Position"""
  position: [String] = null

  """Member's role"""
  role: Role!

  """SeasonIds"""
  seasonIds: [String] = null

  """Weight"""
  weight: Int = null
}

"""
The `Milliseconds` scalar type represents a period of time represented as the total number of milliseconds.
"""
scalar Milliseconds

type MoveAthletesResult {
  copied: Int!
  existing: Int!
}

type Mutation {
  acceptInviteRequests(
    """User ids for the invite requests to accept"""
    memberIds: [String]!

    """Team id"""
    teamId: String!
  ): InviteCode
  addAthletesToSeason(
    """athlete ids"""
    athleteIds: [String]!

    """Season id"""
    seasonId: String = null

    """Team id"""
    teamId: String!
  ): Boolean!
  addGroup(
    """Group"""
    group: GroupInput!

    """Team id"""
    teamId: String!
  ): Group
  addInviteRequest(
    """Invite request"""
    inviteRequest: InviteRequestInput!

    """Team code"""
    teamCode: String!
  ): String
  addMembers(
    """New members list"""
    newMembers: [MemberInput]!

    """The flow used for adding members"""
    source: String = "Unknown"

    """Team id"""
    teamId: String!
  ): [Member]
  deleteGroup(
    """Group id"""
    groupId: String!

    """Team id"""
    teamId: String!
  ): Boolean!
  deleteInviteCode(
    """Team id"""
    teamId: String!
  ): Boolean!
  deleteInviteRequests(
    """User ids for the invite requests to delete"""
    memberIds: [String]!

    """Team id"""
    teamId: String!
  ): InviteCode
  editGroup(
    """Group"""
    group: GroupInput!

    """Team id"""
    teamId: String!
  ): Group
  editMember(
    """Member"""
    member: MemberInput!

    """Team id"""
    teamId: String!
  ): Member
  generateInviteCode(
    """Team id"""
    teamId: String!
  ): InviteCode
  moveAthletesToTeam(
    """Destination Team id"""
    destinationTeamId: String = null

    """Member ids"""
    memberIds: [String]!

    """Source Team id"""
    sourceTeamId: String = null
  ): MoveAthletesResult
  removeMembers(
    """Member ids"""
    memberIds: [String]!

    """Team id"""
    teamId: String!
  ): Boolean!
  removeSeasonMembers(
    """Member ids"""
    memberIds: [String]!

    """Season id"""
    seasonId: String = null

    """Team id"""
    teamId: String!
  ): Boolean!
  updateInviteRequest(
    """Invite request"""
    inviteRequest: InviteRequestInput!

    """Team id"""
    teamId: String!
  ): InviteCode
}

type Organization {
  """Classification ID of organization"""
  classificationId: Int!

  """Full name of organization"""
  fullName: String

  """The hudl Id of organization"""
  internalId: String

  """Type of organization"""
  organizationType: OrganizationType!
}

enum OrganizationType {
  CLUB
  COLLEGE
  HIGH_SCHOOL
  JUCO
  MIDDLE_SCHOOL
  OTHER
  PRO
  YOUTH
}

"""Information about pagination in a connection."""
type PageInfo {
  """When paginating forwards, the cursor to continue."""
  endCursor: Cursor!

  """When paginating forwards, are there more items?"""
  hasNextPage: Boolean!

  """When paginating backwards, are there more items?"""
  hasPreviousPage: Boolean!

  """When paginating backwards, the cursor to continue."""
  startCursor: Cursor!
}

type Query {
  """Get invite code by id"""
  inviteCode(
    """Invite code id"""
    inviteCodeId: String!
  ): InviteCode

  """Get a list of my teams"""
  myTeams(
    """Show teams that have one of these membership roles"""
    membershipRoles: [String] = null

    """Show teams that have all of these features"""
    requiredFeatures: [String] = null
  ): [Team]

  """Get team by id"""
  team(
    """Team id"""
    id: String = null
  ): Team

  """Determine whether user can join team"""
  validateInviteCodeForUser(
    """Email address"""
    email: String!

    """Invite code id"""
    inviteCodeId: String!
  ): InviteCodeWrapper
}

enum Role {
  ADMINISTRATOR
  COACH
  FAN
  INSIDER
  NONE
  PARTICIPANT
  RECRUITER
  TECHNIQUE
}

type Season {
  description: String
  isMaxPrepsImported: Boolean
  value: String
  year: Int!
}

"""
The `Seconds` scalar type represents a period of time represented as the total number of seconds.
"""
scalar Seconds

enum Sport {
  AUSTRALIAN_RULES_FOOTBALL
  AUSTRALIAN_RULES_FOOTBALL_RECRUITING
  BADMINTON
  BADMINTON_RECRUITING
  BASEBALL
  BASEBALL_RECRUITING
  BASKETBALL
  BASKETBALL_RECRUITING
  CHEER_AND_SPIRIT
  CHEER_AND_SPIRIT_RECRUITING
  CRICKET
  CRICKET_RECRUITING
  CROSS_COUNTRY
  CROSS_COUNTRY_RECRUITING
  CYCLING
  CYCLING_RECRUITING
  DANCE_AND_DRILL
  DANCE_AND_DRILL_RECRUITING
  FENCING
  FENCING_RECRUITING
  FIELD_HOCKEY
  FIELD_HOCKEY_RECRUITING
  FOOTBALL
  FOOTBALL_RECRUITING
  GOLF
  GOLF_RECRUITING
  GYMNASTICS
  GYMNASTICS_RECRUITING
  HANDBALL
  HANDBALL_RECRUITING
  ICE_HOCKEY
  ICE_HOCKEY_RECRUITING
  LACROSSE
  LACROSSE_RECRUITING
  NETBALL
  NETBALL_RECRUITING
  NO_SPORT
  OTHER
  PERFORMING_ARTS
  PERFORMING_ARTS_RECRUITING
  RUGBY
  RUGBY_LEAGUE
  RUGBY_LEAGUE_RECRUITING
  RUGBY_RECRUITING
  RUGBY_UNION
  RUGBY_UNION_RECRUITING
  SAILING_AND_YACHTING
  SAILING_AND_YACHTING_RECRUITING
  SOCCER
  SOCCER_RECRUITING
  SOFTBALL
  SOFTBALL_RECRUITING
  SQUASH
  SQUASH_RECRUITING
  SURFING
  SURFING_RECRUITING
  SWIMMING_AND_DIVING
  SWIMMING_AND_DIVING_RECRUITING
  TENNIS
  TENNIS_RECRUITING
  TENPIN_BOWLING
  TENPIN_BOWLING_RECRUITING
  TRACK
  TRACK_RECRUITING
  VOLLEYBALL
  VOLLEYBALL_RECRUITING
  WATER_POLO
  WATER_POLO_RECRUITING
  WRESTLING
  WRESTLING_RECRUITING
}

type Team {
  """Is the team opted out of Recruit"""
  areAthletesRecruitable: Boolean!

  """Cell Carriers"""
  cellCarriers: [Carrier]

  """The year of the current season."""
  currentSeasonYear: Int

  """The gender of the team."""
  gender: Gender!

  """One Group"""
  group(
    """Group Id"""
    groupId: String = null
  ): Group

  """Team groups"""
  groups(after: String = null, before: String = null, first: Int = null, includeFamilyMembers: Boolean = null, last: Int = null): GroupConnection

  """Has max preps"""
  hasMaxPreps: Boolean!

  """The graphql Id of the team."""
  id: ID!

  """The hudl Id of the team."""
  internalId: String

  """Invite Code"""
  inviteCode: InviteCode

  """The team level"""
  level: TeamLevel!

  """The team logo URL."""
  logoUrl: String

  """Get MaxPreps team"""
  maxPrepsTeam(
    """Season Year (defaults to current)"""
    seasonYear: Int = null
  ): MaxPrepsTeam

  """One member, use member id or internal member id"""
  member(
    """Member id"""
    memberId: String = null

    """Internal Member id"""
    memberInternalId: String = null
  ): Member

  """Team members"""
  members(
    after: String = null
    before: String = null

    """Only Disable members"""
    disabledOnly: Boolean = null
    first: Int = null

    """Group id to filter members"""
    groupId: String = null
    last: Int = null

    """Roles to include"""
    roles: [String] = null
  ): MemberConnection

  """Team Members Additional Fields"""
  membersAdditionalFields: [String]

  """Suggested Team members to remove"""
  membersToRemove(after: String = null, before: String = null, first: Int = null, last: Int = null): MemberConnection

  """The name of the team."""
  name: String

  """Organization"""
  organization: Organization

  """Team positions"""
  positions: [String]

  """The User's roles on the team"""
  role: Role!

  """Seasons"""
  seasons: [Season]

  """The sport that the team plays."""
  sport: Sport!
}

type TeamHeader {
  """Team additional Fields"""
  additionalFields: [String]

  """Cell Carriers"""
  cellCarriers: [Carrier]

  """Current season year"""
  currentSeasonYear: Int

  """Team id"""
  id: String

  """Team logo"""
  logo: String

  """Team name"""
  name: String

  """Team Organization"""
  organizationName: String

  """Positions"""
  positions: [String]
}

enum TeamLevel {
  FRESHMAN
  JUNIOR_VARSITY
  OTHER
  OTHER_NON_HS
  SOPHOMORE
  VARSITY
}

enum UserStatus {
  ACTIVE
  CREATED_WITHOUT_AN_EMAIL_OR_USERNAME
  DISABLED
  HAS_NEVER_LOGGED_IN
}

type VideoActivity {
  """Video watched this week, formatted and with an empty indicator"""
  formattedVideoWatchedThisWeek: String

  """Video Watched This Week"""
  videoWatchedThisWeekInSeconds: Int!

  """Video Watched Today"""
  videoWatchedTodayInSeconds: Int!

  """Video Watched Yesterday"""
  videoWatchedYesterdayInSeconds: Int!
}