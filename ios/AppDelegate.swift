//
//  AppDelegate.swift
//  HudlTicketing
//
//  Created by <PERSON> on 4/18/25.
//

import Expo
import React
import ReactAppDependencyProvider

import HudlJarvis
import react_native_detox_context

@UIApplicationMain
public class AppDelegate: ExpoAppDelegate {
  var window: UIWindow?

  var reactNativeDelegate: ExpoReactNativeFactoryDelegate?
  var reactNativeFactory: RCTReactNativeFactory?

  public override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]? = nil
  ) -> Bool {
#if DEBUG
    let moduleName = "JarvisClient"
    JarvisClientUtil.shared.initialise(launchOptions: launchOptions ?? [:])
    // Uncomment once we move to the new architecture
    // JarvisClientUtil.shared.reloadHandler = self
#else
    let moduleName = "main"
#endif
    
    let delegate = ReactNativeDelegate()
    let factory = ExpoReactNativeFactory(delegate: delegate)
    delegate.dependencyProvider = RCTAppDependencyProvider()

    reactNativeDelegate = delegate
    reactNativeFactory = factory
    bindReactNativeFactory(factory)
    
#if os(iOS) || os(tvOS)
    window = UIWindow(frame: UIScreen.main.bounds)
    factory.startReactNative(
      withModuleName: moduleName,
      in: window,
      initialProperties: DetoxContext.processLaunchArgs(),
      launchOptions: launchOptions)
#endif

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  public override func application(_ application: UIApplication,
                            supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
    // Jarvis ScreenOrientation API
    return OrientationHelper.getOrientation()
  }

  // Linking API
  public override func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey: Any] = [:]
  ) -> Bool {
    // Jarvis Universal Linking Support
    JarvisClientUtil.shared.saveURL(url)
    return super.application(app, open: url, options: options) || RCTLinkingManager.application(app, open: url, options: options)
  }

  // Universal Links
  public override func application(
    _ application: UIApplication,
    continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
  ) -> Bool {
    // Jarvis Universal Linking Support
    JarvisClientUtil.shared.saveUserActivity(userActivity)
    let result = RCTLinkingManager.application(application, continue: userActivity, restorationHandler: restorationHandler)
    return super.application(application, continue: userActivity, restorationHandler: restorationHandler) || result
  }

  // Uncomment once we move to the new architecture
  //
  // Reload RN with different root component
  // Ported from https://github.com/expo/expo/blob/main/packages/expo-dev-launcher/ios/ReactDelegateHandler/ExpoDevLauncherReactDelegateHandler.swift#L53
  /*public func reloadReactNative(moduleName: String, launchOptions: [UIApplication.LaunchOptionsKey : Any]) {
    if self.reactNativeFactory?.delegate?.newArchEnabled() ?? false {
      self.reactNativeFactory?.rootViewFactory.setValue(nil, forKey: "_reactHost")
    } else {
      self.reactNativeFactory?.bridge = nil
      self.reactNativeFactory?.rootViewFactory.bridge = nil
    }

    let rootView = recreateRootView(
      withBundleURL: reactNativeDelegate?.bundleURL(),
      moduleName: moduleName,
      initialProps: DetoxContext.processLaunchArgs(),
      launchOptions: launchOptions
    )
    
    guard let rootViewController = self.reactNativeDelegate?.createRootViewController() else {
      fatalError("Invalid rootViewController returned from ExpoReactDelegate")
    }
    
    rootViewController.view = rootView
    window?.rootViewController = rootViewController
    window?.makeKeyAndVisible()
  }*/
}

class ReactNativeDelegate: ExpoReactNativeFactoryDelegate {
  // Extension point for config-plugins

  override func sourceURL(for bridge: RCTBridge) -> URL? {
    // needed to return the correct URL for expo-dev-client.
    //bridge.bundleURL ?? bundleURL()
    self.bundleURL()
  }
  
  override func bundleURL() -> URL? {
    return JarvisClientUtil.shared.getSourceURL()
  }
  
  override func extraModules(for bridge: RCTBridge) -> [any RCTBridgeModule] {
#if DEBUG
    return [JarvisClientLinkingManager()]
#else
    return []
#endif
  }
}
