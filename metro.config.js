const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');
const { assetExts } = require('metro-config/src/defaults/defaults');
const { withSentryConfig } = require('@sentry/react-native/metro');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */
const config = {
  resolver: {
    // Allows '.cer' files to be available as usable assets via "require('./some/path/someCert.cer')".
    assetExts: [...assetExts, 'cer'],
  },
};

module.exports = withSentryConfig(mergeConfig(getDefaultConfig(__dirname), config));