const IOS_OS_VERSION = '18.5'
const ANDROID_API_LEVEL = 35

const DOWNLOAD_IOS =
  'yarn jarvis install -p ios -a ticketing --local --downloadOnly --mode release';

const DOWNLOAD_ANDROID =
  'yarn jarvis install -p android -a ticketing --local --downloadOnly --includeTestAPK --mode release';

const MODIFY_BINARY_IOS = [
  'yarn jarvis modify-binary .jarvis/release-ios.app',
  '--outputPath .jarvis/modified/release-ios.app',
  '--jsBundlePath ./.bundles/ios/main.jsbundle.hbc',
  '--assetsPath ./.bundles/ios/assets/',
  '&& rm -rf .jarvis/release-ios.app',
  '&& mv .jarvis/modified/release-ios.app .jarvis',
  '&& rm -rf .jarvis/modified'
].join(' ');

const MODIFY_BINARY_ANDROID = [
  'yarn jarvis modify-binary .jarvis/release-android.apk',
  '--outputPath .jarvis/modified/release-android.apk',
  '--jsBundlePath ./.bundles/android/main.jsbundle.hbc',
  '--keystorePath android/app/debug.keystore',
  '--keystoreAlias androiddebugkey',
  '--keystorePassword android',
  '--testBinaryPath .jarvis/release-androidTest.apk',
  '&& npx rimraf .jarvis/release-android.apk',
  '&& mv .jarvis/modified/release-android.apk .jarvis',
  '&& npx rimraf .jarvis/modified'
].join(' ');

module.exports = {
  testRunner: {
      $0: 'jest',
      args: {
        config: 'e2e/jest.config.js',
        _: ['e2e']
      }
    },
    apps: {
      'ios.modify.binary': {
        type: 'ios.app',
        build: `yarn bundle:ios && npx rimraf .jarvis && ${DOWNLOAD_IOS} && ${MODIFY_BINARY_IOS}`,
        binaryPath: '.jarvis/release-ios.app'
      },
      'ios.native': {
        type: 'ios.app',
        build: 'yarn build:ios:release',
        binaryPath: 'ios/build/Build/Products/Release-iphonesimulator/HudlTicketing.app',
      },
      'android.modify.binary': {
        type: 'android.apk',
        build: `yarn bundle:android && npx rimraf .jarvis && ${DOWNLOAD_ANDROID} && ${MODIFY_BINARY_ANDROID}`,
        binaryPath: '.jarvis/release-android.apk',
        testBinaryPath: '.jarvis/release-androidTest.apk'
      },
      'android.native': {
        type: 'android.apk',
        build: `yarn build:android:release && yarn build:androidTest:release`,
        binaryPath: 'android/app/build/outputs/apk/release/app-release.apk',
        testBinaryPath:
          'android/app/build/outputs/apk/androidTest/release/app-release-androidTest.apk',
      },
    },
    devices: {
      simulator: {
        type: 'ios.simulator',
        device: {
          type: 'iPhone 16',
            os: IOS_OS_VERSION
        }
      },
      emulator: {
        type: 'android.emulator',
        device: {
          avdName: `Pixel_API_${ANDROID_API_LEVEL}_AOSP`,
          avdAPILevel: ANDROID_API_LEVEL,
          avdProfile: 'pixel'
        }
      }
    },
    configurations: {
      ios: {
        device: 'simulator',
        app: 'ios.modify.binary',
      },
      'ios.native': {
        device: 'simulator',
        app: 'ios.native',
      },
      android: {
        device: 'emulator',
        app: 'android.modify.binary',
      },
      'android.native': {
        device: 'emulator',
        app: 'android.native',
      }
    },
    behavior: {
      launchApp: false,
      cleanup: {
        shutdownDevice: false
      }
    }
}
