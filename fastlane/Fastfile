FIREBASE_APP_ID_ANDROID = '1:892459069124:android:d9d1e70346420251a3a391'

BUNDLE_ID = ENV['BUNDLE_ID']

platform :android do
  before_all do
    JS_BUNDLE_PATH = './android/app/build/generated/assets/createBundleReleaseJsAndAssets/index.android.bundle'
  end

  desc 'Distribute Android to Firebase'
  lane :distribute_to_firebase do |params|
    # Extract out the apk
    modify_binary_command = 'jarvis modify-binary -o android-output/app-release.aab ' \
    "-j '#{JS_BUNDLE_PATH}' " \
    "-e --keystorePath '~/.android/hudl.keystore' --keystorePassword '#{ENV['KEYSTORE_PASSWORD']}' --keystoreAlias 'hudl-upload' " \
    'android/app/build/outputs/bundle/release/app-release.aab'

    yarn(command: modify_binary_command)

    firebase_params = {
      app_id: FIREBASE_APP_ID_ANDROID,
      android_artifact_type: 'APK',
      groups: 'developers',
      android_artifact_path: 'android-ouput/app-release.apk'
    }

    if params[:groups]
      firebase_params[:groups] = params[:groups]
    end

    if params[:testing_notes]
      firebase_params[:testing_notes] = params[:testing_notes]
    end

    firebase_distribution(firebase_params)
  end

  desc 'Distribute Android to the Play Store'
  lane :distribute_to_playstore do
    playstore_cert = aws_get_secrets(secret_name: "pst/mobile/playstore-service-account-cert").to_json

    supply(
      skip_upload_apk: true,
      package_name: BUNDLE_ID,
      track: "production",
      rollout: "1", #100%
      aab: 'android/app/build/outputs/bundle/release/app-release.aab',
      json_key_data: playstore_cert,
    )
  end

end

platform :ios do
  before_all do
    PROJECT_NAME = 'HudlTicketing'
    XCODEPROJ_PATH = './ios/HudlTicketing.xcodeproj'
    XCWORKSPACE_PATH = './ios/HudlTicketing.xcworkspace'
    XCARCHIVE_PATH = './ios/HudlTicketing.xcarchive'
    PLIST_PATH = 'HudlTicketing/Info.plist'
  end

  desc 'Build and distribute iOS to TestFlight'
  lane :build_and_distribute_to_testflight do |params|
    app_store_api_key = aws_get_secrets(
      secret_name: 'pst/mobile/app-store-connect-api-key'
    )

    # This action sets a SharedValue parameter that other
    # actions can use when authenticated with Apple.
    app_store_connect_api_key(
      key_id: '2N352HFU45',
      issuer_id: '69a6de71-e18d-47e3-e053-5b8c7c11a4d1',
      key_content: app_store_api_key,
      in_house: false
    )

    set_ios_version(is_release = true, ENV['BUILD_NUMBER'])

    provisioning_profile_id = get_uuid_from_profile('hudl.mobileprovision')
    xcodeproj_build_methods(
      xcodeproj: XCODEPROJ_PATH,
      target: PROJECT_NAME,
      provisioning_profile: provisioning_profile_id,
    )

    build_ios(BUNDLE_ID, 'app-store', get_uuid_from_profile('hudl.mobileprovision'))

    # Upload app to TestFlight
    pilot(
      apple_id: '**********',
      app_identifier: BUNDLE_ID,
      distribute_external: true,
      groups: ['Hudl Ticket Reader Testers'],
      demo_account_required: true,
      notify_external_testers: true,
      expire_previous_builds: true,
      reject_build_waiting_for_review: true,
      changelog: params[:testing_notes].gsub('\n', "\n")
    )
  end

  desc 'Build and distribute iOS to the App Store'
  lane :build_and_distribute_to_appstore do
    app_store_api_key = aws_get_secrets(
      secret_name: 'pst/mobile/app-store-connect-api-key'
    )

    app_store_connect_api_key(
      key_id: '2N352HFU45',
      issuer_id: '69a6de71-e18d-47e3-e053-5b8c7c11a4d1',
      key_content: app_store_api_key,
      in_house: false
    )
  
    set_ios_version(is_release = true, ENV['BUILD_NUMBER'])

    provisioning_profile_id = get_uuid_from_profile('hudl.mobileprovision')

    xcodeproj_build_methods(
      xcodeproj: XCODEPROJ_PATH,
      target: PROJECT_NAME,
      provisioning_profile: provisioning_profile_id,
    )

    build_ios(BUNDLE_ID, 'app-store', provisioning_profile_id)

    deliver(
      force: true,
      ipa: "ios-output/HudlTicketing.ipa",
      precheck_include_in_app_purchases: false,
      app_review_attachment_file: "./fastlane/metadata/TicketScannerTesting.pdf"
    )
  end

  def build_ios(bundle_id, export_method, provisioning_profile_id)
    gym_payload = {
      scheme: PROJECT_NAME,
      workspace: XCWORKSPACE_PATH,
      configuration: "Release",
      output_name: PROJECT_NAME,
      buildlog_path: 'fastlane/logs',
      archive_path: XCARCHIVE_PATH,
      export_method: export_method,
      export_options: {
        provisioningProfiles: {
          "#{bundle_id}": provisioning_profile_id
        }
      },
      output_directory: 'ios-output',
      xcargs: "PRODUCT_BUNDLE_IDENTIFIER='#{bundle_id}' OTHER_CODE_SIGN_FLAGS='--keychain=hudl.keychain'",
    }

    if export_method.eql? "enterprise"
      export_payload = {

        codesigning_identity: "iPhone Distribution: Agile Sports Technologies, Inc.",
        export_team_id: "EAP7GNETJU",
      }
    else
      export_payload = {
        codesigning_identity: "iPhone Distribution: Agile Sports Technologies",
        export_team_id: "4M6T2C723P",
      }
    end

    gym_payload = gym_payload.merge(export_payload)

    puts gym_payload
    UI.message("Building Hudl Ticketing for iOS")
    gym(gym_payload)
  end

  def get_uuid_from_profile(file)
    require 'plist'
    plist = `security cms -D -i #{File.join(Dir.home, 'Library/MobileDevice/Provisioning\ Profiles', file)}`
    Plist.parse_xml(plist)['UUID']
  end

  def set_ios_version(is_release = false, build_number)
    version_string = is_release ? ENV['PACKAGE_VERSION'] : "#{ENV['PACKAGE_VERSION']}-#{get_branch}-#{get_sha.strip[0..6]}"
  
    UI.message("Setting 'CFBundleShortVersionString' to: #{version_string}")
    UI.message("Setting 'CFBundleVersion' to: #{build_number}")
    update_info_plist(
      xcodeproj: XCODEPROJ_PATH,
      plist_path: PLIST_PATH,
      block: proc do |plist|
        plist['CFBundleShortVersionString'] = "#{version_string}"
        plist['CFBundleVersion'] = "#{build_number}"
      end
    )
  end
end

lane :firebase_distribution do |options|
  # The firebase_app_distribution action only takes a file path to the service account cert so we need
  # to store the data in a tempfile and cleanup after uploading to App Distribution.
  service_account_file = Tempfile.new(['service_account', '.json'])
  service_account_file.write(ENV['FIREBASE_APP_DIST_SERVICE_ACCOUNT'])
  service_account_file.close

  artifact_type = (options[:artifact_type] ? options[:artifact_type] : "APK")
  testing_notes = "Branch: #{get_branch}\nCommit: #{get_sha}\nCommit Message: #{get_commit_message}"

  if options[:testing_notes]
    testing_notes = options[:testing_notes].gsub('\n', "\n")
  end

  begin
    firebase_app_distribution(
      service_credentials_file: service_account_file.path,
      release_notes: testing_notes,
      groups: options[:groups],
      app: options[:app_id],
      ipa_path: File.join("ios-output", "HudlTicketing.ipa"),
      android_artifact_type: artifact_type,
      android_artifact_path: File.join("android-output", "app-release.#{artifact_type.downcase}")
    )
  ensure
    # Alway cleanup and delete tempfile.
    service_account_file.unlink
  end
end

lane :determine_ios_build_number do |options|
  app_store_api_key = aws_get_secrets(
    secret_name: 'pst/mobile/app-store-connect-api-key'
  )

  app_store_connect_api_key(
    key_id: '2N352HFU45',
    issuer_id: '69a6de71-e18d-47e3-e053-5b8c7c11a4d1',
    key_content: app_store_api_key,
    in_house: false
  )

  latest_build_number = latest_testflight_build_number(
    version: options[:package_version],
    app_identifier: options[:bundle_id]
  )

  build_number = latest_build_number + 1

  puts "BUILD_NUMBER=#{build_number}"
end

def is_push?
  return true if ENV['GITHUB_EVENT_TYPE'].eql?('push')
end

def get_branch
  return is_push? ? ENV['GITHUB_REF_NAME'] : ENV['GITHUB_HEAD_REF']
end

def get_sha
  return is_push? ? ENV['GITHUB_SHA'] : ENV['GITHUB_HEAD_SHA']
end

def get_commit_message
  return `git show -s --format=%s #{get_sha}`.strip
end