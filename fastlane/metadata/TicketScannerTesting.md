# Hudl Ticket Reader App Review Instructions

## Ticket Scanning

### Setup

To log in:

1. Open app  
2. Click **“Get Started”**  
3. Enter any first name and last name
4. Enter a valid format email address (e.g. <EMAIL>)
5. Enter `ETP4X` as Access Code  
6. Choose the **App/Play Store Submission Testing** Event  
7. Choose **Check In** on the bottom navigation bar
8. Click **Scan Tickets** button  
9. Allow camera access when prompted
10. Scan the QR codes below and refer to the notes explaining expected results:

#### Valid Ticket  
This ticket/QR code is considered valid.  
- Triggers the scanner’s **success** state on scan  
- Subsequent scans trigger the **“already scanned”** state  

<img src="https://static.hudl.com/ticketing/apple-app-submission-content/valid.png" alt="Valid Ticket QR Code" width="200" />

#### Invalid Ticket  
This ticket/QR code is not a valid Hudl Ticket.  
- Triggers the scanner’s **“Ticket Not Recognized”** state  

<img src="https://static.hudl.com/ticketing/apple-app-submission-content/invalid.png" alt="Invalid Ticket QR Code" width="200" />

#### Incorrect Event Ticket  
This ticket/QR code is a valid Hudl Ticket but for a **different event**.  
- Triggers the scanner’s **“Incorrect Event”** state  

<img src="https://static.hudl.com/ticketing/apple-app-submission-content/incorrect-event.png" alt="Incorrect Event Ticket QR Code" width="200" />

---

## Accepting Payments (Point of Sale)

### Card Reader Setup
1. Navigate back to the start screen  
2. Hit **Get Started**  
3. Enter any first name and last name
4. Enter a valid format email address (<EMAIL>)
5. Enter `hud1d3v` as Access Code  

This will enable developer mode (and return you to the start screen):

1. Click the **Developer Options** button at the top center of the screen  
2. Click **Set Branch URL**, choose `Prod`, and click **Set Value**  
3. Scroll down and check:  
   - **Simulate Card Reader**  
   - **Simulate Card**  
4. Click **Set Simulated Card Number**, choose `Valid`, and click **Set Value**  
5. Swipe down to close Developer Options  

> Note: Payment acceptance is done with a Stripe hardware reader, but simulation is available for testing.

### Logging Back In
1. Hit **Get Started**
2. Enter any first name and last name
3. Enter a valid format email address (<EMAIL>)
4. Enter `ETP4X` as Access Code  
5. Choose the **App/Play Store Submission Testing** Event  

### Accepting a Payment (Point of Sale - Card)
1. Choose **Sell** on the bottom navigation bar
2. Click **Tickets** on the "What are you selling?" screen
3. Add `1` **Free Admission** ticket by clicking the `+` icon
4. Click **Card** as the payment method
5. The "Connect to a Reader" screen should display
6. Click one of the simulated readers in the list, wait for it to connect
7. Click **Continue**  
8. Click **Card** again as the payment method
9. Click **Process Card Payment**
10. Click **No Thanks** when asked about a receipt  
11. Click **Place Order**  
12. After a loading screen, the **Order Complete** screen should display — indicating success!  
13. Click **Sell More Tickets** to run through the workflow again  

### Accepting a Payment (Point of Sale - Cash)
1. Choose **Sell** on the bottom navigation bar
2. Click **Tickets** on the "What are you selling?" screen
3. Add `1` **Free Admission** ticket by clicking the `+` icon
4. Click **Cash** as the payment method
5. Click **Process Cash Payment**
6. Click **No Thanks** when asked about a receipt  
7. Click **Place Order**  
8. After a loading screen, the **Order Complete** screen should display — indicating success!  
9. Click **Sell More Tickets** to run through the workflow again

---


