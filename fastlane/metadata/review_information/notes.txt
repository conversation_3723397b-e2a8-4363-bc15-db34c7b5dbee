For testing, any value can be used for first name, last name, and email. For the access code please use "etp4x"

Attached is a PDF testing instructions for scanning tickets and simulating purchases.

Linked below is a video demonstrating Hudl Ticket Reader pairing to the hardware device (Stripe M2 Reader) and taking a payment as part of our point of sale workflow.
https://static.hudl.com/ticketing/apple-app-submission-content/HudlTicketReaderAppleDemo.MOV