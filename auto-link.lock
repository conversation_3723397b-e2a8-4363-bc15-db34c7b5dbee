["@hudl/jarvis:17.0.0", "@react-native-async-storage/async-storage:1.24.0", "@react-native-community/netinfo:11.4.1", "@sentry/react-native:6.16.1", "@stripe/stripe-terminal-react-native:0.0.1-beta.25", "expo:53.0.17", "react-native-auth0:3.2.1", "react-native-detox-context:0.3.1", "react-native-device-info:14.0.2", "react-native-fs:2.20.0", "react-native-gesture-handler:2.27.1", "react-native-get-random-values:1.11.0", "react-native-linear-gradient:2.8.3", "react-native-localize:2.2.6", "react-native-permissions:5.3.0", "react-native-reanimated:3.18.0", "react-native-safe-area-context:5.5.1", "react-native-screens:4.11.1", "react-native-snackbar:2.8.0", "react-native-svg:15.11.2", "react-native-system-bars:0.4.0", "react-native-vision-camera:4.7.0", "react-native-webview:13.13.5", "react-native-zip-archive:7.0.1"]