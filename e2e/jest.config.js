module.exports = {
  maxWorkers: 1,
  testTimeout: 120000,
  verbose: true,
  rootDir: '..',
  testMatch: ['<rootDir>/e2e/**/*.e2e.ts'],
  reporters: [
    ['github-actions', { silent: false }],
    'detox/runners/jest/reporter',
    '@hudl/ci-toolkit/jest-reporter',
    ['jest-summary-reporter', { failuresOnly: false }],
    ['jest-ctrf-json-reporter', { outputDir: 'ctrf', outputFile: 'ctrf-report.json' }]
  ],
  globalSetup: 'detox/runners/jest/globalSetup',
  globalTeardown: 'detox/runners/jest/globalTeardown',
  testEnvironment: 'detox/runners/jest/testEnvironment',
};
