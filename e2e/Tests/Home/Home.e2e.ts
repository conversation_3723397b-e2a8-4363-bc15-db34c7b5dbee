import DetoxUtils from '../../Utils/DetoxUtil';
import { HomePage } from '../../Utils/HomeTab/HomeUtil';
import { launchAndSelectEvent } from '../../Utils/TicketingAppUtil';

describe('Home Page', () => {
  beforeEach(async () => {
    await DetoxUtils.launch({});
    await launchAndSelectEvent({
      accessCode: '2CJG2',
      ticketedEventId: 'VGlja2V0ZWRFdmVudDY4OTY2Y2Q0ZWU0MDk4YjdhODNiNDEyNw==',
    });
  });

  it('Home Page Initial State Loads with Selected Event', async () => {
    // Validate Home Page Elements Load with Selected Event
    const eventTitle = 'DO NOT TOUCH - Detox Automation - Free Ticket Checkout';
    await HomePage.homePageLoadsWithSelectedEvent(eventTitle);
  });
});
