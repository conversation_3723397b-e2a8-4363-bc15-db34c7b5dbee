import DetoxUtils, { waitForElementByIdAndText, waitForElementByText } from '../../Utils/DetoxUtil';
import { OrderCompletePage } from '../../Utils/SellTab/OrderCompleteUtil';
import { PointOfSalePage } from '../../Utils/SellTab/PointOfSaleUtil';
import { ReceiptPage } from '../../Utils/SellTab/ReceiptUtil';
import { ReviewOrderPage } from '../../Utils/SellTab/ReviewOrderUtil';
import { SellPage, navigateToSellTab } from '../../Utils/SellTab/SellUtil';
import { launchAndSelectEvent } from '../../Utils/TicketingAppUtil';

describe('Paid Ticket Checkout', () => {
  beforeEach(async () => {
    await DetoxUtils.launch({});
    await launchAndSelectEvent({
      accessCode: '2CJG2',
      ticketedEventId: 'VGlja2V0ZWRFdmVudDY4YTQ4OGFlM2M1ODU5YjdhNWUwNjMyNg==',
    });
    await navigateToSellTab();
  });

  it('Completes Paid Checkout for Tickets with Card', async () => {
    // Validate Sell Page Elements Load
    await SellPage.sellTicketsButton.waitToBeVisible();
    await SellPage.customSalesButton.waitToBeVisible();
    await SellPage.sellTicketsButton.press();

    // Validate Point of Sale Page for Paid Checkout
    const courtSideTicketTypeId = 'VGlja2V0VHlwZTY4YTQ4ODM5NWIxZjQ5ZWI0MDk2ZmE4MA==';
    const clubSeatingTicketTypeId = 'VGlja2V0VHlwZTY4YTQ4ODk1NWIxZjQ5ZWI0MDk2ZmE4MQ==';
    const generalAdmissionTicketTypeId = 'VGlja2V0VHlwZTY4YTQ4ODc1M2M1ODU5YjdhNWUwNjMyNQ==';
    await PointOfSalePage.verifyPaidTicketingPosPageLoads([
      { ticketTypeId: courtSideTicketTypeId, ticketName: 'Courtside', price: '$25.00' },
      { ticketTypeId: clubSeatingTicketTypeId, ticketName: 'Club Seating', price: '$15.00' },
      {
        ticketTypeId: generalAdmissionTicketTypeId,
        ticketName: 'General Admission',
        price: '$10.00',
      },
    ]);

    // Validate limited quantity displays the proper badge and disables counters
    await PointOfSalePage.scrollUntilTicketFullyVisible(courtSideTicketTypeId, 'up');
    await element(PointOfSalePage.ticketedEventListScroll.matcher).scrollTo('top'); // scroll again due to Android visibility issues
    await waitForElementByText('1 ticket left');
    await PointOfSalePage.ticketCounterIncrementButton(courtSideTicketTypeId, false).press();
    await waitForElementByIdAndText(
      PointOfSalePage.ticketCounterInput(courtSideTicketTypeId, false).matcher,
      '1'
    );
    await PointOfSalePage.ticketCounterIncrementButton(
      courtSideTicketTypeId,
      true
    ).waitToBeVisible();
    await PointOfSalePage.ticketCounterDecrementButton(courtSideTicketTypeId, false).press();
    await waitForElementByIdAndText(
      PointOfSalePage.ticketCounterInput(courtSideTicketTypeId, false).matcher,
      '0'
    );
    await PointOfSalePage.ticketCounterIncrementButton(
      courtSideTicketTypeId,
      false
    ).waitToBeVisible();

    // Add 1 Club Seating Ticket to Order
    await PointOfSalePage.ticketCounterIncrementButton(clubSeatingTicketTypeId, false).press();
    await waitForElementByIdAndText(
      PointOfSalePage.ticketCounterInput(clubSeatingTicketTypeId, false).matcher,
      '1'
    );
    await waitForElementByIdAndText(
      PointOfSalePage.orderTotalNumberOfItems.matcher,
      'Order Total: 1 ticket'
    );
    await waitForElementByIdAndText(PointOfSalePage.orderTotalPrice.matcher, '$15.00');

    // Add 2 General Admission Tickets to Order
    await PointOfSalePage.scrollUntilTicketFullyVisible(generalAdmissionTicketTypeId);
    await element(PointOfSalePage.ticketedEventListScroll.matcher).scrollTo('bottom'); // scroll again due to Android visibility issues
    await PointOfSalePage.ticketCounterIncrementButton(generalAdmissionTicketTypeId, false).press();
    await waitForElementByIdAndText(
      PointOfSalePage.ticketCounterInput(generalAdmissionTicketTypeId, false).matcher,
      '1'
    );
    await PointOfSalePage.ticketCounterIncrementButton(generalAdmissionTicketTypeId, false).press();
    await waitForElementByIdAndText(
      PointOfSalePage.ticketCounterInput(generalAdmissionTicketTypeId, false).matcher,
      '2'
    );
    await waitForElementByIdAndText(
      PointOfSalePage.orderTotalNumberOfItems.matcher,
      'Order Total: 3 tickets'
    );
    await waitForElementByIdAndText(PointOfSalePage.orderTotalPrice.matcher, '$35.00');

    // Checkout with Card
    await PointOfSalePage.cardReviewOrderButton(false).press();

    // Connect to Reader Simulator
    await PointOfSalePage.connectToReaderSimulator('Stripe M2 Reader');
    await PointOfSalePage.cardReviewOrderButton(false).press();

    // Review Order Page
    const orderTotal = '$39.11';
    await waitForElementByIdAndText(
      ReviewOrderPage.posItemSelectionHeader.matcher,
      'You may show fans this screen to review their order before payment.'
    );
    await waitForElementByIdAndText(ReviewOrderPage.reviewOrderHeader.matcher, 'Review Order');
    await ReviewOrderPage.reviewOrderItemName(clubSeatingTicketTypeId).waitToBeVisible();
    await waitForElementByIdAndText(
      ReviewOrderPage.reviewOrderItemPrice(clubSeatingTicketTypeId).matcher,
      '$15.00'
    );
    await ReviewOrderPage.reviewOrderItemName(generalAdmissionTicketTypeId).waitToBeVisible();
    await waitForElementByIdAndText(
      ReviewOrderPage.reviewOrderItemPrice(generalAdmissionTicketTypeId).matcher,
      '$20.00'
    );
    await waitForElementByText('Order Transaction Fee');
    await waitForElementByIdAndText(
      ReviewOrderPage.reviewOrderOrderTransactionFee.matcher,
      '$4.11'
    );
    await waitForElementByIdAndText(
      ReviewOrderPage.orderTotalNumberOfItems.matcher,
      'Order Total: 3 tickets'
    );
    await waitForElementByIdAndText(ReviewOrderPage.orderTotalPrice.matcher, orderTotal);
    await waitForElementByIdAndText(
      ReviewOrderPage.orderTotalSubtext.matcher,
      'All sales are non-refundable.'
    );
    await ReviewOrderPage.processPaymentButton.waitToBeVisible();
    await ReviewOrderPage.reviewOrderEditButton.waitToBeVisible();
    await ReviewOrderPage.processPaymentButton.press();

    // Need a Receipt Page Loads
    await ReceiptPage.verifyReceiptPageLoads();
    await ReceiptPage.placeOrderButton(true, true).waitToBeVisible();

    // Place order with no receipt
    await ReceiptPage.receiptSelectionOptionNo(false).press();
    await ReceiptPage.placeOrderButton(false, true).press();

    // Order Complete Page
    const ticketsSoldText = '3 Tickets Sold';
    await OrderCompletePage.orderCompleteFromCheckoutPageLoads('Card', ticketsSoldText, orderTotal);
  });
});
