import DetoxUtils from '../../Utils/DetoxUtil';
import { OrderCompletePage } from '../../Utils/SellTab/OrderCompleteUtil';
import { PointOfSalePage } from '../../Utils/SellTab/PointOfSaleUtil';
import { ReceiptPage } from '../../Utils/SellTab/ReceiptUtil';
import { ReviewOrderPage } from '../../Utils/SellTab/ReviewOrderUtil';
import { SellPage, navigateToSellTab } from '../../Utils/SellTab/SellUtil';
import { launchAndSelectEvent } from '../../Utils/TicketingAppUtil';

describe('Free Ticket Checkout', () => {
  beforeEach(async () => {
    await DetoxUtils.launch({});
    await launchAndSelectEvent({
      accessCode: '2CJG2',
      ticketedEventId: 'VGlja2V0ZWRFdmVudDY4OTY2Y2Q0ZWU0MDk4YjdhODNiNDEyNw==',
    });
    await navigateToSellTab();
  });

  it('Completes Free Checkout for Tickets with Card', async () => {
    // Validate Sell Page Elements Load
    await SellPage.sellTicketsButton.waitToBeVisible();
    await SellPage.customSalesButton.waitToBeVisible();
    await SellPage.sellTicketsButton.press();

    // Validate Point of Sale Page for Free Checkout
    const ticketTypeId = 'VGlja2V0VHlwZTY4OTY2Y2Q0ZWU0MDk4YjdhODNiNDEyNg==';
    await PointOfSalePage.verifyFreePosPageLoads(ticketTypeId);
    await PointOfSalePage.posInfoButton.press();
    await PointOfSalePage.verifyCheckoutTipsDisplays();
    await PointOfSalePage.checkoutCloseInstructionsButton.press();
    await PointOfSalePage.checkoutInstructionsModal.verifyIsNotVisible();

    // Add Free Ticket to Order
    await PointOfSalePage.addFreeTicketToOrder(ticketTypeId);
    await PointOfSalePage.cardReviewOrderButton(false).press();

    // Connect to Reader Simulator
    await PointOfSalePage.connectToReaderSimulator('Stripe M2 Reader');
    await PointOfSalePage.cardReviewOrderButton(false).press();

    // Review Order Page
    await ReviewOrderPage.verifyReviewOrderFreeCheckoutPageLoads(ticketTypeId);
    await ReviewOrderPage.processPaymentButton.press();

    // Need a Receipt Page Loads
    await ReceiptPage.verifyReceiptPageLoads();
    await ReceiptPage.placeOrderButton(true, false).waitToBeVisible();

    // Place order with no receipt
    await ReceiptPage.receiptSelectionOptionNo(false).press();
    await ReceiptPage.placeOrderButton(false, false).press();

    // Order Complete Page
    const ticketsSoldText = '1 Ticket Sold';
    const orderTotal = '$0.00';
    await OrderCompletePage.orderCompleteFromCheckoutPageLoads('Card', ticketsSoldText, orderTotal);
  });
});
