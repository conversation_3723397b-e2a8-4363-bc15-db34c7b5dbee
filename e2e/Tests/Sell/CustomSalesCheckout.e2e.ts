import DetoxUtils, { waitForElementByIdAndText } from '../../Utils/DetoxUtil';
import { CustomSaleCalculatorPage } from '../../Utils/SellTab/CustomSales/CustomSaleCalculatorUtil';
import { CustomSaleSelectionPage } from '../../Utils/SellTab/CustomSales/CustomSaleSelectionUtil';
import { OrderCompletePage } from '../../Utils/SellTab/OrderCompleteUtil';
import { PointOfSalePage } from '../../Utils/SellTab/PointOfSaleUtil';
import { ReceiptPage } from '../../Utils/SellTab/ReceiptUtil';
import { ReviewOrderPage } from '../../Utils/SellTab/ReviewOrderUtil';
import { SellPage, navigateToSellTab } from '../../Utils/SellTab/SellUtil';
import { launchAndSelectEvent } from '../../Utils/TicketingAppUtil';

describe('Custom Sales Checkout', () => {
  beforeEach(async () => {
    await DetoxUtils.launch({});
    await launchAndSelectEvent({
      accessCode: '2CJG2',
      ticketedEventId: 'VGlja2V0ZWRFdmVudDY4OTY2Y2Q0ZWU0MDk4YjdhODNiNDEyNw==',
    });
    await navigateToSellTab();
  });

  it('Completes Custom Sales Checkout with Card', async () => {
    // Validate Sell Page Elements Load
    await SellPage.sellTicketsButton.waitToBeVisible();
    await SellPage.customSalesButton.waitToBeVisible();
    await SellPage.customSalesButton.press();

    // Validate Custom Sales Page Elements Load
    await CustomSaleSelectionPage.verifyCustomSalesSelectionPageLoads();

    // Continue to Checkout for Concessions item
    await CustomSaleSelectionPage.customSaleSelectionContinueButton.press();

    // Validate Input Custom Price Page Elements Load
    await CustomSaleCalculatorPage.verifyCustomSalesCalculatorLoads();

    // Type 5 and validate error messaging
    await CustomSaleCalculatorPage.customSaleCalculatorButton('5').press();
    await waitForElementByIdAndText(
      CustomSaleCalculatorPage.customSaleCalculatorErrorMin.matcher,
      'Sale price must be at least $0.50.'
    );

    // Type $5.00 for the Custom Sale Order
    await CustomSaleCalculatorPage.customSaleCalculatorButton('0').press();
    await CustomSaleCalculatorPage.customSaleCalculatorButton('0').press();
    await CustomSaleCalculatorPage.waitForCardButtonEnabledAndPress();

    // Connect to Reader Simulator
    await PointOfSalePage.connectToReaderSimulator('Stripe M2 Reader');

    // Proceed with Card Checkout
    await CustomSaleCalculatorPage.waitForCardButtonEnabledAndPress();

    // Review Order Page Displays properly
    const ticketTypeId = ''; // Custom Sale has no Ticket Type ID
    const transactionFee = '$0.40'; // Transaction Fee from $5.00 sale
    const orderTotal = '$5.40'; // Total after $5.00 plus $0.40 fee
    await ReviewOrderPage.verifyReviewOrderCustomSalePageLoads(
      ticketTypeId,
      transactionFee,
      orderTotal
    );
    await ReviewOrderPage.processPaymentButton.press();

    // Need a Receipt Page Loads
    await ReceiptPage.verifyReceiptPageLoads();
    await ReceiptPage.placeOrderButton(true, true).waitToBeVisible();

    // Place order with no receipt
    await ReceiptPage.receiptSelectionOptionNo(false).press();
    await ReceiptPage.placeOrderButton(false, true).press();

    // Order Complete Page
    await OrderCompletePage.orderCompleteFromCustomSalePageLoads(orderTotal, 'Card');
  });

  it('Completes Custom Sales Checkout with Cash', async () => {
    // Validate Sell Page Elements Load
    await SellPage.sellTicketsButton.waitToBeVisible();
    await SellPage.customSalesButton.waitToBeVisible();
    await SellPage.customSalesButton.press();

    // Validate Custom Sales Page Elements Load
    await CustomSaleSelectionPage.verifyCustomSalesSelectionPageLoads();

    // Continue to Checkout for Concessions item
    await CustomSaleSelectionPage.customSaleSelectionContinueButton.press();

    // Validate Input Custom Price Page Elements Load
    await CustomSaleCalculatorPage.verifyCustomSalesCalculatorLoads();

    // Type 5 and validate error messaging
    await CustomSaleCalculatorPage.customSaleCalculatorButton('5').press();
    await waitForElementByIdAndText(
      CustomSaleCalculatorPage.customSaleCalculatorErrorMin.matcher,
      'Sale price must be at least $0.50.'
    );

    // Type $5.00 for the Custom Sale Order
    await CustomSaleCalculatorPage.customSaleCalculatorButton('0').press();
    await CustomSaleCalculatorPage.customSaleCalculatorButton('0').press();
    await CustomSaleCalculatorPage.waitForCashButtonEnabledAndPress();

    // Review Order Page Displays properly
    const ticketTypeId = ''; // Custom Sale has no Ticket Type ID
    const transactionFee = '$0.00'; // $0.00 Transaction Fee for Cash Sale
    const orderTotal = '$5.00'; // Total is $5.00 with no Transaction Fee
    await ReviewOrderPage.verifyReviewOrderCustomSalePageLoads(
      ticketTypeId,
      transactionFee,
      orderTotal
    );
    await ReviewOrderPage.processPaymentButton.press();

    // Need a Receipt Page Loads
    await ReceiptPage.verifyReceiptPageLoads();
    await ReceiptPage.placeOrderButton(true, true).waitToBeVisible();

    // Place order with receipt
    await ReceiptPage.receiptSelectionOptionYes(false).press();
    await ReceiptPage.receiptEmailInput.typeText(
      '<EMAIL>'
    );

    // Disable Synchronization before pressing Place Order button if we send Receipt
    await device.disableSynchronization();

    await ReceiptPage.placeOrderButton(false, true).press();

    // Order Complete Page
    await OrderCompletePage.orderCompleteFromCustomSalePageLoads(orderTotal, 'Cash');
  });
});
