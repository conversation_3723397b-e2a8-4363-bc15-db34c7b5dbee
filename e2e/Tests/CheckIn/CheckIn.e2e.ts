import {
  CheckInPage,
  navigateToCheckInTab,
  verifyTicketScanningTipsDisplays,
} from '../../Utils/CheckInTab/CheckInUtil';
import DetoxUtils, { waitForElementByText } from '../../Utils/DetoxUtil';
import { launchAndSelectEvent } from '../../Utils/TicketingAppUtil';

describe('Check In Page', () => {
  beforeEach(async () => {
    await DetoxUtils.launch({});
    await launchAndSelectEvent({
      accessCode: 'YBRF5',
      ticketedEventId: 'VGlja2V0ZWRFdmVudDY0ZWE0ZDcwZmE1OWIwZjY5MDkxYmI0Zg==',
    });
    await navigateToCheckInTab();
  });

  it('Loads Check In Page and Opens Camera to Scan Ticket', async () => {
    // Validate Check In Page Elements Load
    await CheckInPage.scanTicketsButton.waitToBeVisible();
    await CheckInPage.searchForOrderButton.waitToBeVisible();
    await CheckInPage.needHelpButton.waitToBeVisible();

    // Click Scan Tickets
    await CheckInPage.scanTicketsButton.press();

    // Verify QR Scanning Text Displays
    await waitForElementByText('Align QR code with lines');
  });

  it('Ticket Scanning Tips Modal can be Opened', async () => {
    // Open Ticket Scanning Instructions
    await CheckInPage.needHelpButton.waitToBeVisible();
    await CheckInPage.needHelpButton.press();
    await verifyTicketScanningTipsDisplays();

    // Validate modal can be closed
    await CheckInPage.scannerCloseInstructionsButton.press();
    await CheckInPage.scannerInstructionsModal.verifyIsNotVisible();
  });
});
