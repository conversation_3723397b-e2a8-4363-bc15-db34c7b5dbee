import { device } from 'detox';
import { contextArgs } from 'react-native-detox-context';

import { DetoxUtil } from '@hudl/detox-utils';

async function launch({
  cameraPermissionState = 'YES',
  locationPermissionState = 'always',
}: {
  locationPermissionState?: Detox.LocationPermission;
  cameraPermissionState?: Detox.BasicPermissionState;
} = {}): Promise<void> {
  await device.launchApp({
    newInstance: true,
    delete: true,
    permissions: { camera: cameraPermissionState, location: locationPermissionState },
    launchArgs: contextArgs({}),
  });
}

export const waitForElementByText = async (
  text: string,
  timeout: number = 15000
): Promise<Detox.NativeElement> => {
  const el = element(by.text(text));
  await waitFor(el).toBeVisible().withTimeout(timeout);
  return el;
};

export const waitForElementByIdAndText = async (
  matcher: Detox.NativeMatcher,
  text: string,
  timeout = 15000
): Promise<void> => {
  await DetoxUtil.waitToBeVisible(matcher.and(by.text(text)), timeout);
};

const DetoxUtils = {
  launch,
};

export default DetoxUtils;
