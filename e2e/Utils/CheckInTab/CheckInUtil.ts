import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { waitForElementByText } from '../DetoxUtil';
import { HomePage } from '../HomeTab/HomeUtil';

export const CheckInPage = DetoxPage.create({
  scanTicketsButton: by.id(AppTestIDs.scanTicketsMenuButton),
  searchForOrderButton: by.id(AppTestIDs.searchForOrderMenuButton),
  needHelpButton: by.id(AppTestIDs.needHelpButton),
  scannerInstructionsModal: by.id(AppTestIDs.instructionsBottomSheet),
  scannerCloseInstructionsButton: by.id(AppTestIDs.closeInstructionsButton),
});

export const navigateToCheckInTab = async (): Promise<void> => {
  await HomePage.checkInTab.waitToBeVisible();
  await HomePage.checkInTab.press();
};

export const verifyTicketScanningTipsDisplays = async (): Promise<void> => {
  await CheckInPage.scannerInstructionsModal.waitToBeVisible();
  await waitForElementByText('Ticket Scanning Tips');
  await waitForElementByText(`Allow Camera Access by tapping on 'Scan Tickets' Button.`);
  await waitForElementByText('Tell fans to increase device brightness.');
  await waitForElementByText('If WiFi is available, connect before scanning.');
  await waitForElementByText('Align ticket QR codes with scanner lines.');
  await waitForElementByText('Confirm tickets are valid.');
  await waitForElementByText('Refer to the home page for scanned tickets upload status.');
  await waitForElementByText('Close Instructions');
};
