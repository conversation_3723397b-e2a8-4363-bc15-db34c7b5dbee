import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { waitForElementByIdAndText } from '../DetoxUtil';

export const HomePage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      scannerUploadStatus: by.id(AppTestIDs.scannerUploadStatus),
      scannerUploadStatusNoTicketsToUpload: by.id(AppTestIDs.scannerUploadStatusNoTicketsToUpload),
      scannerUploadStatusTicketsToUpload: by.id(AppTestIDs.scannerUploadStatusTicketsToUpload),
      eventTitleText: by.id(AppTestIDs.homePageEventTitle),
      eventDetailsText: by.id(AppTestIDs.homePageEventDetails),
      selectAndSwitchEventButton: by.id(AppTestIDs.homePageSelectAndSwitchEventButton),
      findNearbyReadersButton: by.id(AppTestIDs.homePageFindNearbyReadersButton),
      signOutButton: by.id(AppTestIDs.homePageSignOutButton),
      homeTab: by.id(AppTestIDs.homeTabBottomSheet),
      sellTab: by.id(AppTestIDs.sellTabBottomSheet),
      checkInTab: by.id(AppTestIDs.checkInTabBottomSheet),
      homePageScroll: by.id(AppTestIDs.homePageScroll),
    },
    {} // no Dynamic Selectors
  ),

  // Validates Home Page Loads with Selected Event
  async homePageLoadsWithSelectedEvent(eventTitle: string): Promise<void> {
    await this.scannerUploadStatus.waitToBeVisible();
    await this.scannerUploadStatusNoTicketsToUpload.waitToBeVisible();
    await waitForElementByIdAndText(this.eventTitleText.matcher, eventTitle);
    await this.eventDetailsText.waitToBeVisible();
    await this.selectAndSwitchEventButton.waitToBeVisible();
    await this.homePageScroll.scrollUntilChildVisible(this.findNearbyReadersButton.matcher, 'down');
    await this.signOutButton.waitToBeVisible();
    await this.homeTab.waitToBeVisible();
    await this.sellTab.waitToBeVisible();
    await this.checkInTab.waitToBeVisible();
  },
};
