import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../app/components/AppTestIDs';

const LandingPage = DetoxPage.create({
  getStartedButton: by.id(AppTestIDs.landingGetStarted),
});

const VolunteerInfoPage = DetoxPage.create({
  firstNameInput: by.id(AppTestIDs.volunteerInfoFirstNameInput),
  lastNameInput: by.id(AppTestIDs.volunteerInfoLastNameInput),
  emailInput: by.id(AppTestIDs.volunteerInfoEmailInput),
  accessCodeInput: by.id(AppTestIDs.volunteerInfoAccessCodeInput),
  continueButton: by.id(AppTestIDs.volunteerInfoContinue),
});
export const InputVolunteerInfo = async ({}): Promise<void> => {
  await VolunteerInfoPage.firstNameInput.waitToBeVisible();
  await VolunteerInfoPage.firstNameInput.typeText('Test');
  await VolunteerInfoPage.lastNameInput.typeText('Volunteer');
  await VolunteerInfoPage.emailInput.typeText('<EMAIL>');
};

const EventsPage = DetoxPage.create(
  {},
  {
    eventItem: (id: string) => by.id(AppTestIDs.orgEvent(id)),
  }
);

const EventActionSelectionPage = DetoxPage.create({
  scanTicketsButton: by.id(AppTestIDs.eventActionSelectionScanTickets),
  sellTicketsButton: by.id(AppTestIDs.eventActionSelectionSellTickets),
  checkInFansButton: by.id(AppTestIDs.eventActionSelectionCheckInFans),
});

const EventConfirmationPage = DetoxPage.create({
  confirmButton: by.id(AppTestIDs.confirmEventScanTickets),
});

const ScannerPage = DetoxPage.create({
  overlay: by.id(AppTestIDs.scannerOverlay),
});

const FindNearbyReaderPage = DetoxPage.create({
  findReadersHeader: by.id(AppTestIDs.discoverReadersLandingHeader),
  findReadersButton: by.id(AppTestIDs.discoverReadersLandingFindReadersButton),
});

const ConnectToReaderPage = DetoxPage.create(
  {},
  {
    connectReader: (serialNumber: string, isDisabled: boolean) =>
      by.id(AppTestIDs.connectReaderDiscoveredReader(serialNumber, isDisabled)),
    discoverReaderContinueButton: (isDisabled: boolean) =>
      by.id(AppTestIDs.discoverNearbyReadersContinueButton(isDisabled)),
  }
);

const ItemSelectionPage = DetoxPage.create(
  {},
  {
    itemCounterIncrease: (itemId: string, disabled: boolean) =>
      by.id(AppTestIDs.counterIncrement(itemId, disabled)),
    itemCounterDecrease: (itemId: string, disabled: boolean) =>
      by.id(AppTestIDs.counterDecrement(itemId, disabled)),
    cardReviewOrderButton: (disabled: boolean) =>
      by.id(AppTestIDs.posCardReviewOrderButton(disabled)),
    cashReviewOrderButton: (disabled: boolean) =>
      by.id(AppTestIDs.posCashReviewOrderButton(disabled)),
  }
);

const ReviewOrderPage = DetoxPage.create(
  {
    posReviewHeader: by.id(AppTestIDs.reviewOrderHeader),
    posReviewItemList: by.id(AppTestIDs.reviewOrderItemList),
    posReviewAcceptPaymentsButton: by.id(AppTestIDs.reviewOrderProcessButton),
  },
  {
    reviewOrderItem: (id: string) => by.id(AppTestIDs.reviewOrderItem(id)),
    reviewOrderPrice: (id: string) => by.id(AppTestIDs.reviewOrderItemPrice(id)),
  }
);

const ReceiptPage = DetoxPage.create(
  {},
  {
    noReceipt: (selected: boolean) => by.id(AppTestIDs.receiptSelectionOptionNo(selected)),
  }
);

const PurchaserListPage = DetoxPage.create({
  purchaserList: by.id(AppTestIDs.purchaserListContainer),
  purchaserListEmpty: by.id(AppTestIDs.purchaserListEmptyState),
  purchaserListError: by.id(AppTestIDs.purchaserListErrorState),
});

export const launchAndSelectEvent = async ({
  accessCode,
  ticketedEventId,
}: {
  accessCode: string;
  ticketedEventId: string;
}): Promise<void> => {
  await LandingPage.getStartedButton.waitToBeVisible();
  await LandingPage.getStartedButton.press();

  await InputVolunteerInfo({
    firstName: VolunteerInfoPage.firstNameInput,
    lastName: VolunteerInfoPage.lastNameInput,
    email: VolunteerInfoPage.emailInput,
  });

  await VolunteerInfoPage.accessCodeInput.typeText(accessCode);
  await VolunteerInfoPage.continueButton.press();

  const eventItem = EventsPage.eventItem(ticketedEventId);
  await eventItem.waitToBeVisible();
  await eventItem.press();
};

export default {
  LandingPage,
  VolunteerInfoPage,
  EventsPage,
  EventActionSelectionPage,
  EventConfirmationPage,
  ScannerPage,
  FindNearbyReaderPage,
  ConnectToReaderPage,
  ItemSelectionPage,
  ReviewOrderPage,
  ReceiptPage,
  PurchaserListPage,
};
