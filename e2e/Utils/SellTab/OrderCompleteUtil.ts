import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { waitForElementByIdAndText, waitForElementByText } from '../DetoxUtil';

export const OrderCompletePage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      orderCompleteHeader: by.id(AppTestIDs.orderCompleteHeader),
      orderCompleteSubHeader: by.id(AppTestIDs.orderCompleteSubHeader),
      doneSellingButton: by.id(AppTestIDs.orderCompleteNavigateHome),
      sellMoreTicketsButton: by.id(AppTestIDs.orderCompleteNewOrderButton),
    },
    {} // no Dynamic Selectors
  ),

  // Validates the Order Complete Page loads and displays for Free Checkout
  async orderCompleteFromCheckoutPageLoads(
    paymentMethod: string,
    ticketsSoldText: string,
    orderTotal: string
  ): Promise<void> {
    await this.orderCompleteHeader.waitToBeVisible();
    await waitForElementByIdAndText(this.orderCompleteSubHeader.matcher, ticketsSoldText);
    await waitForElementByText('Order Number');
    await waitForElementByText('Payment Method');
    await waitForElementByText(paymentMethod);
    await waitForElementByText('Total Amount');
    await waitForElementByText(orderTotal);
    await this.doneSellingButton.waitToBeVisible();
    await this.sellMoreTicketsButton.waitToBeVisible();
    await waitFor(element(this.doneSellingButton.matcher.withDescendant(by.text('Done Selling'))))
      .toBeVisible()
      .withTimeout(5000);
    await waitFor(
      element(this.sellMoreTicketsButton.matcher.withDescendant(by.text('Sell More Tickets')))
    )
      .toBeVisible()
      .withTimeout(5000);
  },

  // Validates the Order Complete Page loads and displays for Custom Sale
  async orderCompleteFromCustomSalePageLoads(
    orderTotal: string,
    paymentMethod: string
  ): Promise<void> {
    await device.disableSynchronization();
    await this.orderCompleteHeader.waitToBeVisible();
    await waitForElementByIdAndText(this.orderCompleteSubHeader.matcher, 'Custom Item');
    await waitForElementByText('Order Number');
    await waitForElementByText('Payment Method');
    await waitForElementByText(paymentMethod);
    await waitForElementByText('Total Amount');
    await waitForElementByText(orderTotal);
    await this.doneSellingButton.waitToBeVisible();
    await this.sellMoreTicketsButton.waitToBeVisible();
    await waitFor(element(this.doneSellingButton.matcher.withDescendant(by.text('Done Selling'))))
      .toBeVisible()
      .withTimeout(5000);
    await waitFor(
      element(this.sellMoreTicketsButton.matcher.withDescendant(by.text('Sell More Items')))
    )
      .toBeVisible()
      .withTimeout(5000);
  },
};
