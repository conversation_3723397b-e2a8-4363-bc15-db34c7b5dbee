import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { waitForElementByIdAndText, waitForElementByText } from '../DetoxUtil';

export const ReviewOrderPage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      posItemSelectionHeader: by.id(AppTestIDs.posItemSelectionHeader),
      reviewOrderHeader: by.id(AppTestIDs.reviewOrderHeader),
      processPaymentButton: by.id(AppTestIDs.reviewOrderProcessButton),
      reviewOrderEditButton: by.id(AppTestIDs.reviewOrderEditButton),
      orderTotalNumberOfItems: by.id(AppTestIDs.orderTotalNumberOfItems),
      orderTotalPrice: by.id(AppTestIDs.orderTotalPrice),
      orderTotalSubtext: by.id(AppTestIDs.orderTotalSubtext),
      reviewOrderOrderTransactionFee: by.id(AppTestIDs.reviewOrderOrderTransactionFee),
    },
    {
      // Dynamic Selectors
      reviewOrderItemName: (ticketTypeId: string) =>
        by.id(AppTestIDs.reviewOrderItemName(ticketTypeId)),
      reviewOrderItemPrice: (ticketTypeId: string) =>
        by.id(AppTestIDs.reviewOrderItemPrice(ticketTypeId)),
    }
  ),

  // Validates the Review Order Page loads properly for Free Checkout
  async verifyReviewOrderFreeCheckoutPageLoads(ticketTypeId: string): Promise<void> {
    await waitForElementByIdAndText(
      this.posItemSelectionHeader.matcher,
      'You may show fans this screen to review their order before payment.'
    );
    await waitForElementByIdAndText(this.reviewOrderHeader.matcher, 'Review Order');
    await this.reviewOrderItemName(ticketTypeId).waitToBeVisible();
    await waitForElementByIdAndText(
      this.reviewOrderItemPrice(ticketTypeId).matcher,
      'Free - $0.00'
    );
    await waitForElementByIdAndText(this.orderTotalNumberOfItems.matcher, 'Order Total: 1 ticket');
    await waitForElementByIdAndText(this.orderTotalPrice.matcher, '$0.00');
    await waitForElementByIdAndText(
      this.orderTotalSubtext.matcher,
      'All sales are non-refundable.'
    );
    await this.processPaymentButton.waitToBeVisible();
    await this.reviewOrderEditButton.waitToBeVisible();
  },

  // Validates the Review Order Page loads properly for Custom Sale
  async verifyReviewOrderCustomSalePageLoads(
    ticketTypeId: string,
    transactionFee: string,
    orderTotal: string
  ): Promise<void> {
    await waitForElementByIdAndText(
      this.posItemSelectionHeader.matcher,
      'You may show fans this screen to review their order before payment.'
    );
    await waitForElementByIdAndText(this.reviewOrderHeader.matcher, 'Review Order');
    await this.reviewOrderItemName(ticketTypeId).waitToBeVisible();
    await waitForElementByIdAndText(this.reviewOrderItemPrice(ticketTypeId).matcher, '$5.00');
    await waitForElementByText('Order Transaction Fee');
    await waitForElementByIdAndText(this.reviewOrderOrderTransactionFee.matcher, transactionFee);
    await waitForElementByIdAndText(this.orderTotalNumberOfItems.matcher, 'Order Total: 1');
    await waitForElementByIdAndText(this.orderTotalPrice.matcher, orderTotal);
    await waitForElementByIdAndText(
      this.orderTotalSubtext.matcher,
      'All sales are non-refundable.'
    );
    await this.processPaymentButton.waitToBeVisible();
    await this.reviewOrderEditButton.waitToBeVisible();
  },
};
