import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../../app/components/AppTestIDs';
import { waitForElementByText } from '../../DetoxUtil';

export const CustomSaleSelectionPage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      customSaleSelectionContinueButton: by.id(AppTestIDs.customSaleSelectionContinueButton),
    },
    {
      // Dynamic Selectors
      customSaleSelectionRadioGroupOption: (value: string) =>
        by.id(AppTestIDs.customSaleSelectionRadioGroupOption(value)),
    }
  ),

  // Validates the Custom Sales Selection Page loads
  async verifyCustomSalesSelectionPageLoads(): Promise<void> {
    await waitForElementByText('Select what type of item you are selling');
    await waitFor(
      element(
        this.customSaleSelectionRadioGroupOption('Concessions').matcher.withDescendant(
          by.text('Concessions')
        )
      )
    )
      .toBeVisible()
      .withTimeout(5000);
    await waitFor(
      element(
        this.customSaleSelectionRadioGroupOption('Apparel').matcher.withDescendant(
          by.text('Apparel')
        )
      )
    )
      .toBeVisible()
      .withTimeout(5000);
    await waitFor(
      element(
        this.customSaleSelectionRadioGroupOption('Other').matcher.withDescendant(by.text('Other'))
      )
    )
      .toBeVisible()
      .withTimeout(5000);
    await this.customSaleSelectionContinueButton.waitToBeVisible();
  },
};
