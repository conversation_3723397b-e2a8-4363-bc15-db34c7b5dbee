import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../../app/components/AppTestIDs';
import { waitForElementByIdAndText, waitForElementByText } from '../../DetoxUtil';

export const CustomSaleCalculatorPage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      customSaleCalculatorHeader: by.id(AppTestIDs.customSaleCalculatorHeader),
      customSaleCalculatorTotal: by.id(AppTestIDs.customSaleCalculatorTotal),
      customSaleCalculatorBackButton: by.id(AppTestIDs.headerBackButton),
      customSaleCalculatorCancelOrderButton: by.id(
        AppTestIDs.customSaleCalculatorCancelOrderButton
      ),
      customSaleCalculatorErrorMin: by.id(AppTestIDs.customSaleCalculatorErrorMin),
      customSaleCalculatorScroll: by.id(AppTestIDs.customSaleCalculatorScroll),
      customSaleCalculator: by.id(AppTestIDs.customSaleCalculator),
    },
    {
      // Dynamic Selectors
      customSaleCalculatorButton: (value: string) =>
        by.id(AppTestIDs.customSaleCalculatorButton(value)),
      customSaleCalculatorCheckoutButtonCash: (disabled: boolean) =>
        by.id(AppTestIDs.customSaleCalculatorCheckoutButtonCash(disabled)),
      customSaleCalculatorCheckoutButtonCard: (disabled: boolean) =>
        by.id(AppTestIDs.customSaleCalculatorCheckoutButtonCard(disabled)),
    }
  ),

  // Validates the Custom Sales Calculator loads properly
  async verifyCustomSalesCalculatorLoads(): Promise<void> {
    await waitForElementByIdAndText(this.customSaleCalculatorHeader.matcher, 'Input Custom Price');
    await waitForElementByIdAndText(this.customSaleCalculatorTotal.matcher, '$0.00');

    // Loop through each key and validate they're visible
    for (const key of ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'C'] as const) {
      await this.customSaleCalculatorButton(key).waitToBeVisible();
    }

    await waitForElementByText(
      'Additional fees may be applied at checkout. All sales are non-refundable.'
    );
    await this.customSaleCalculatorScroll.scrollUntilChildVisible(
      this.customSaleCalculatorCancelOrderButton.matcher,
      'down'
    );
    await this.customSaleCalculatorCheckoutButtonCash(true).waitToBeVisible();
    await this.customSaleCalculatorCheckoutButtonCard(true).waitToBeVisible();
    await this.customSaleCalculatorCancelOrderButton.waitToBeVisible();
  },

  async waitForCardButtonEnabledAndPress(): Promise<void> {
    await this.customSaleCalculatorCheckoutButtonCash(false).waitToBeVisible();
    await this.customSaleCalculatorCheckoutButtonCard(false).waitToBeVisible();
    await this.customSaleCalculatorCheckoutButtonCard(false).press();
  },

  async waitForCashButtonEnabledAndPress(): Promise<void> {
    await this.customSaleCalculatorCheckoutButtonCash(false).waitToBeVisible();
    await this.customSaleCalculatorCheckoutButtonCard(false).waitToBeVisible();
    await this.customSaleCalculatorCheckoutButtonCash(false).press();
  },
};
