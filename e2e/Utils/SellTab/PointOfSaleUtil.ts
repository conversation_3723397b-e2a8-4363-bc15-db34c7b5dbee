import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { waitForElementByText, waitForElementByIdAndText } from '../DetoxUtil';

export const PointOfSalePage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      posItemSelectionHeader: by.id(AppTestIDs.posItemSelectionHeader),
      selectableItemName: by.id(AppTestIDs.selectableItemName),
      selectableItemPrice: by.id(AppTestIDs.selectableItemPrice),
      orderTotalNumberOfItems: by.id(AppTestIDs.orderTotalNumberOfItems),
      orderTotalPrice: by.id(AppTestIDs.orderTotalPrice),
      orderTotalSubtext: by.id(AppTestIDs.orderTotalSubtext),
      checkoutInstructionsModal: by.id(AppTestIDs.instructionsBottomSheet),
      checkoutCloseInstructionsButton: by.id(AppTestIDs.closeInstructionsButton),
      posInfoButton: by.id(AppTestIDs.infoIcon),
      readerConnectionWrapperButton: by.id(AppTestIDs.readerConnectionWrapperButton),
      connectReaderDisconnectButton: by.id(AppTestIDs.connectReaderDisconnectButton),
      ticketedEventListScroll: by.id(AppTestIDs.selectableItemListList),
    },
    {
      // Dynamic Selectors
      ticketCounterDecrementButton: (ticketTypeId: string, disabled: boolean) =>
        by.id(AppTestIDs.counterDecrement(ticketTypeId, disabled)),
      ticketCounterInput: (ticketTypeId: string, disabled: boolean) =>
        by.id(AppTestIDs.counterInput(ticketTypeId, disabled)),
      ticketCounterIncrementButton: (ticketTypeId: string, disabled: boolean) =>
        by.id(AppTestIDs.counterIncrement(ticketTypeId, disabled)),
      cashReviewOrderButton: (disabled: boolean) =>
        by.id(AppTestIDs.posCashReviewOrderButton(disabled)),
      cardReviewOrderButton: (disabled: boolean) =>
        by.id(AppTestIDs.posCardReviewOrderButton(disabled)),
    }
  ),

  async scrollUntilTicketFullyVisible(
    ticketTypeId: string,
    dir: Detox.Direction = 'down'
  ): Promise<void> {
    await waitFor(element(this.ticketCounterIncrementButton(ticketTypeId, false).matcher))
      .toBeVisible(100) // require full visibility
      .whileElement(this.ticketedEventListScroll.matcher)
      .scroll(200, dir);
  },

  // Ensures the Point of Sale Page loads for Free Checkout
  async verifyFreePosPageLoads(ticketTypeId: string): Promise<void> {
    await waitForElementByText(
      `Wait to show fans your screen until it's time to review their order.`
    );
    await this.posInfoButton.waitToBeVisible();
    await waitForElementByIdAndText(this.selectableItemName.matcher, 'Free Admission');
    await waitForElementByIdAndText(this.selectableItemPrice.matcher, 'Free - $0.00');
    await this.ticketCounterDecrementButton(ticketTypeId, true).waitToBeVisible();
    await this.ticketCounterInput(ticketTypeId, false).waitToBeVisible();
    await this.ticketCounterIncrementButton(ticketTypeId, false).waitToBeVisible();
    await waitForElementByIdAndText(this.orderTotalNumberOfItems.matcher, 'Order Total: 0 tickets');
    await waitForElementByIdAndText(this.orderTotalPrice.matcher, '$0.00');
    await waitForElementByIdAndText(
      this.orderTotalSubtext.matcher,
      'Additional fees may be applied at checkout. All sales are non-refundable.'
    );
    await this.cashReviewOrderButton(true).waitToBeVisible();
    await this.cardReviewOrderButton(true).waitToBeVisible();
  },

  // Ensures the Checkout Tips Modal Displays
  async verifyCheckoutTipsDisplays(): Promise<void> {
    await this.checkoutInstructionsModal.waitToBeVisible();
    await waitForElementByText('How to Accept Payments for Tickets');
    await waitForElementByText('Select the desired ticket type and quantity.');
    await waitForElementByText(
      'Review the order with the customer: Only show the “Review Order” screen to customers for this step when asked.'
    );
    await waitForElementByText(
      'Card Payment: Use the Stripe Terminal Reader to accept payment (tap to pay or physical card).'
    );
    await waitForElementByText(
      'Cash Payment: After accepting cash payment, process order for payment confirmation.'
    );
    await waitForElementByText('Close Instructions');
  },

  // Adds Free Ticket to Order for Free Checkout
  async addFreeTicketToOrder(ticketTypeId: string): Promise<void> {
    await this.ticketCounterIncrementButton(ticketTypeId, false).press();
    await waitForElementByIdAndText(this.ticketCounterInput(ticketTypeId, false).matcher, '1');
    await waitForElementByIdAndText(this.orderTotalNumberOfItems.matcher, 'Order Total: 1 ticket');
  },

  // Connect to Reader Simulator
  async connectToReaderSimulator(simulatorName: string): Promise<void> {
    await waitFor(element(by.text(simulatorName)))
      .toBeVisible()
      .withTimeout(30000);
    await element(by.text(simulatorName)).tap();
    await this.connectReaderDisconnectButton.waitToBeVisible();
    await this.readerConnectionWrapperButton.press();
    await this.readerConnectionWrapperButton.verifyIsNotVisible();
  },

  async verifyPaidTicketingPosPageLoads(
    tickets: {
      ticketTypeId: string;
      ticketName: string;
      price: string;
    }[]
  ): Promise<void> {
    await waitForElementByText(
      `Wait to show fans your screen until it's time to review their order.`
    );
    await this.posInfoButton.waitToBeVisible();

    // Loop through each ticketType for the event
    for (const ticket of tickets) {
      await this.scrollUntilTicketFullyVisible(ticket.ticketTypeId);
      await waitForElementByIdAndText(this.selectableItemName.matcher, ticket.ticketName);
      await waitForElementByIdAndText(this.selectableItemPrice.matcher, ticket.price);
      await this.ticketCounterDecrementButton(ticket.ticketTypeId, true).waitToBeVisible();
      await this.ticketCounterInput(ticket.ticketTypeId, false).waitToBeVisible();
      await this.ticketCounterIncrementButton(ticket.ticketTypeId, false).waitToBeVisible();
    }

    await waitForElementByIdAndText(this.orderTotalNumberOfItems.matcher, 'Order Total: 0 tickets');
    await waitForElementByIdAndText(this.orderTotalPrice.matcher, '$0.00');
    await waitForElementByIdAndText(
      this.orderTotalSubtext.matcher,
      'Additional fees may be applied at checkout. All sales are non-refundable.'
    );
    await this.cashReviewOrderButton(true).waitToBeVisible();
    await this.cardReviewOrderButton(true).waitToBeVisible();
  },
};
