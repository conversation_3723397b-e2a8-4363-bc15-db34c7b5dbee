import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { HomePage } from '../HomeTab/HomeUtil';

export const SellPage = DetoxPage.create({
  sellTicketsButton: by.id(AppTestIDs.ticketsButton),
  customSalesButton: by.id(AppTestIDs.customSalesButton),
});

// Navigation helpers
export const navigateToSellTab = async (): Promise<void> => {
  await HomePage.sellTab.waitToBeVisible();
  await HomePage.sellTab.press();
};
