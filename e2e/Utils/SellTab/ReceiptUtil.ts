import { DetoxPage } from '@hudl/detox-utils';

import { AppTestIDs } from '../../../app/components/AppTestIDs';
import { waitForElementByIdAndText } from '../DetoxUtil';

export const ReceiptPage = {
  ...DetoxPage.create(
    {
      // Static Selectors
      receiptSelectionHeader: by.id(AppTestIDs.receiptSelectionHeader),
      receiptSelectionSubHeader: by.id(AppTestIDs.receiptSelectionSubHeader),
      receiptEmailInput: by.id(AppTestIDs.receiptSelectionEmailInputInput),
    },
    {
      // Dynamic Selectors
      receiptSelectionOptionYes: (selected: boolean) =>
        by.id(AppTestIDs.receiptSelectionOptionYes(selected)),
      receiptSelectionOptionNo: (selected: boolean) =>
        by.id(AppTestIDs.receiptSelectionOptionNo(selected)),
      placeOrderButton: (disabled: boolean, isPaidTransaction: boolean) =>
        by.id(AppTestIDs.receiptSelectionContinueButton(disabled, isPaidTransaction)),
    }
  ),

  // Validates the Need a Receipt? Page loads properly
  async verifyReceiptPageLoads(): Promise<void> {
    await waitForElementByIdAndText(this.receiptSelectionHeader.matcher, 'Need a Receipt?');
    await waitForElementByIdAndText(
      this.receiptSelectionSubHeader.matcher,
      'Receipts are sent via email.'
    );
    await this.receiptSelectionOptionYes(false).waitToBeVisible();
    await this.receiptSelectionOptionNo(false).waitToBeVisible();
  },
};
