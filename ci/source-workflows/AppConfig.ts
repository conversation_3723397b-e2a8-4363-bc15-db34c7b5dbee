/* eslint-disable @typescript-eslint/no-var-requires */
import path from 'path';

import { Hudl<PERSON> } from '@hudl/ci-toolkit';

const AppConfig: HudlCI.RN.ProjectConfig = {
  adhocProfileSecret: 'HUDL_TICKETING_ADHOC_PROFILE',
  inhouseProfileSecret: 'HUDL_TICKETING_ENTERPRISE_PROFILE',
  keystoreAlias: 'hudl-upload',
  keystoreFilename: 'hudl.keystore',
  passwordFilename: 'passwords.properties',
  appAlias: 'ticketing',
  appName: 'Ticket Reader',
  packageName: require(path.join(process.cwd(), 'package.json')).name,
  platforms: ['android', 'ios'],
  bundleID: 'com.hudl.ticketing',
  inhouseBundleID: 'com.hudl.ticketing.enterprise',
  firebaseAndroidID: '1:************:android:d9d1e70346420251a3a391',
  firebaseIOSID: '1:************:ios:e3b5b1fc62942627a3a391',
  firebaseInhouseID: '1:************:ios:70b690b21a2c26fca3a391',
  firebaseAndroidDevClientID: '1:************:android:2573bd6a197ac898f10059',
  firebaseIOSDevClientID: '1:************:ios:43390ef924d51794f10059',
  firebaseServiceAccountID: 'pst/mobile/ticketing-firebase-app-distribution-a12d2',
  owningBusinessUnit: 'competitive',
};

export default AppConfig;
