/* eslint-disable @typescript-eslint/no-var-requires */
const replace = require('replace-in-file');

// The Stripe RN library is specifying project level configurations for JVM target and toolchain,
// when (in most cases) this should be left up to the consuming project.
module.exports.stripeTerminalReactNativeJVMWorkaround = async () => {
  try {
    console.log('Patching @stripe/stripe-terminal-react-native');

    const fs = require('fs');
    const path = './node_modules/@stripe/stripe-terminal-react-native/android/build.gradle';
    const data = fs.readFileSync(path, 'utf-8');

    if (data.includes('/*compileOptions')) {
      console.log('Breaking Early: Already Ran.');
      return;
    }

    const options = {
      files: [path],
      from: [
        `compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }`,
        `jvmToolchain(17)`,
      ],
      to: [
        `/*compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }*/`,
        `//jvmToolchain(17)`,
      ],
    };

    const results = await replace(options);
    console.log(results);
  } catch (error) {
    console.error('Error occurred:', error);
  }
};
