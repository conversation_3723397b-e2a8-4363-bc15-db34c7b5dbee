import fs from 'fs';

import { DevSettingsConfig } from '../app/DevSettings';

const defaultSettings: DevSettingsConfig = {
  useLocalApolloServer: false,
  localApolloServerURL: 'http://localhost:4000',
  gqlSchemaBaseURL: 'https://master.thorhudl.com',
};

const devJSONPath = './.dev.json';

let existingConfig: DevSettingsConfig = {
  ...defaultSettings,
};

if (fs.existsSync(devJSONPath)) {
  existingConfig = JSON.parse(fs.readFileSync(devJSONPath, 'utf8')) as DevSettingsConfig;
}

const finalConfig: DevSettingsConfig = {
  ...defaultSettings,
  ...existingConfig,
};

fs.writeFileSync(devJSONPath, JSON.stringify(finalConfig, null, 2), 'utf8');
